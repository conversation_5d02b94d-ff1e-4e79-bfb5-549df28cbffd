; OPcache configuration for optimal performance
opcache.enable=1
opcache.enable_cli=0
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.max_wasted_percentage=10
opcache.revalidate_freq=2
opcache.fast_shutdown=1
opcache.enable_file_override=1
opcache.validate_timestamps=1
opcache.save_comments=1

; Preload configuration (PHP 7.4+)
; opcache.preload=/var/www/html/src/preload.php
; opcache.preload_user=www-data

; JIT configuration (PHP 8.0+) 
opcache.jit_buffer_size=100M
opcache.jit=tracing