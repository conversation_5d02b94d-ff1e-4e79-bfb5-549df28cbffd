# Open eLMS Docker Development Environment

Quick setup guide for running Open eLMS locally using Docker.

## Prerequisites

- Docker and Docker Compose installed
- Ports 80, 443, 3306, and 6379 available

## Quick Start

1. **Generate SSL certificates** (first time only):
   ```bash
   ./generate-ssl.sh
   ```

2. **Start all services**:
   ```bash
   docker compose up -d
   ```

3. **Check services are running**:
   ```bash
   docker compose ps
   ```

4. **Access the application**:
   - Open https://localhost in your browser
   - Accept the self-signed certificate warning

## Services

### Service Overview
- **Web Application**: https://localhost
- **MariaDB 10.11**: Database service (matches production)
- **PHP 8.1-FPM**: Web application server with required extensions
- **Nginx**: Web server and reverse proxy
- **Redis 7**: Caching and session storage

### Service Access
- **Web Application**: https://localhost
- **MariaDB**: localhost:3306 (user: root, password: root)
- **Redis**: localhost:6379 (no authentication)

## HTTPS Configuration

- **Local URL**: https://localhost (automatically redirects from HTTP)
- **SSL Certificate**: Self-signed certificate for localhost
- **Security Headers**: HSTS, X-Frame-Options, CSP headers included
- **HTTP Redirect**: All HTTP traffic is redirected to HTTPS

## Container Configuration

- **Database**: `db` service accessible via hostname `db:3306` (credentials: root/root)
- **Redis**: `redis` service accessible via hostname `redis:6379` (no authentication in development)
- **Web files**: Mounted from `../src/` to `/var/www/html/`
- **Network**: Internal Docker network for service communication

**Configuration Requirements:**
- In `src/app/lms_config.php`, ensure `mysqlHostName = "db"` (not localhost)
- In `src/app/lms_config.php`, ensure `redisHostName = "redis"` (not localhost)

## PHP Configuration

- **Memory Limit**: 1024M (required for upgrade scripts)
- **Upload Limits**: 100M max file size, 100M post size (configured for SCORM uploads)
- **Execution Time**: 300 seconds for uploads and processing
- **Error Handling**: Early detection of upload size limit errors with proper JSON responses
- **Session**: HTTPS-only, secure cookies
- **Timezone**: Europe/London (configurable via database)

### PHP Extensions Included
- mysqli, pdo_mysql (database connectivity)
- mbstring, zip, gd (common utilities)
- bcmath, exif, pcntl (specific requirements)

## Database Operations

```bash
# Run database upgrade/migration script
docker compose exec web php crons/upgrade.php

# Run database initialization (fresh install)  
docker compose exec web php crons/db_init.php

# Access MariaDB directly
docker compose exec db mysql -u root -proot surrey

# Import SQL file into database
docker compose exec db sh -c "mysql -u root -proot surrey < /import/filename.sql"
```

## Redis Operations

```bash
# Test Redis connectivity
docker compose exec redis redis-cli ping

# Access Redis CLI
docker compose exec redis redis-cli

# View Redis statistics
docker compose exec redis redis-cli info stats

# Monitor Redis commands in real-time
docker compose exec redis redis-cli monitor
```

## Common Commands

```bash
# View logs
docker compose logs -f

# Stop services
docker compose down

# Rebuild containers
docker compose down && docker compose up --build -d

# Run cron tasks
docker compose exec web php crons/cron-tasks.php

# Process imports
docker compose exec web php crons/import.php

# Process mail queue
docker compose exec web php crons/process_mail_queue.php

# Rebuild assets
docker compose exec web php crons/combine.php
```

## Troubleshooting

### Template Not Found Errors
```bash
# Fix template path issues - ensure paths in lms_config.php match Docker structure
# AppFilePath should be "/var/www/html/" not "/var/www/html/src/"
```

### JavaScript Dependency Errors
```bash
# Recombine JavaScript assets if AngularJS modules fail to load
docker compose exec web php crons/combine.php
```

### Memory Errors During Upgrade
```bash
# Check current memory limit
docker compose exec web php -i | grep memory_limit

# If memory limit is insufficient, rebuild containers (memory limit set to 1024M in Docker)
docker compose down && docker compose up --build -d

# Verify memory limit is now 1024M
docker compose exec web php -i | grep memory_limit

# Run upgrade with proper memory limit
docker compose exec web php crons/upgrade.php
```

### Database Connection Issues
```bash
# Check if database service is running
docker compose ps db

# Verify database configuration in src/app/lms_config.php
# Ensure mysqlHostName = "db" (not localhost)
```

### Redis Connection Issues
```bash
# Verify Redis service status
docker compose exec redis redis-cli ping

# Check configuration in src/app/lms_config.php  
# Ensure redisHostName = "redis" (not localhost)
```