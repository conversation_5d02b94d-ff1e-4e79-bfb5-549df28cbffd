#!/bin/bash

# MySQL Database Import Script
# Usage: ./import-database.sh <database_name> <sql_file>

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to display usage
usage() {
    echo "Usage: $0 <database_name> <sql_file>"
    echo ""
    echo "Parameters:"
    echo "  database_name  - Name of the target database"
    echo "  sql_file       - Path to the SQL file to import"
    echo ""
    echo "Examples:"
    echo "  $0 kirklees kirklees.sql"
    echo "  $0 surrey /path/to/backup.sql"
    exit 1
}

# Check if correct number of parameters provided
if [ $# -ne 2 ]; then
    echo -e "${RED}Error: Incorrect number of parameters${NC}"
    usage
fi

DATABASE_NAME="$1"
SQL_FILE="$2"

# Check if SQL file exists
if [ ! -f "$SQL_FILE" ]; then
    echo -e "${RED}Error: SQL file '$SQL_FILE' not found${NC}"
    exit 1
fi

# Check if Docker compose is running
if ! docker compose ps | grep -q "Up"; then
    echo -e "${RED}Error: Docker services are not running. Please start with 'docker compose up -d'${NC}"
    exit 1
fi

echo -e "${YELLOW}Starting MySQL import process...${NC}"
echo "Database: $DATABASE_NAME"
echo "SQL File: $SQL_FILE"
echo "File size: $(du -h "$SQL_FILE" | cut -f1)"

# Create database if it doesn't exist
echo -e "${YELLOW}Creating database if it doesn't exist...${NC}"
docker compose exec -T db mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS \`$DATABASE_NAME\`;" 2>/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Database '$DATABASE_NAME' is ready${NC}"
else
    echo -e "${RED}✗ Failed to create/access database '$DATABASE_NAME'${NC}"
    exit 1
fi

# Apply aggressive MySQL optimizations for fast import (LOCAL DEVELOPMENT ONLY)
echo -e "${YELLOW}Applying aggressive MySQL optimizations for fast import...${NC}"
echo -e "${YELLOW}(These settings disable safety features - LOCAL DEVELOPMENT ONLY!)${NC}"

# Store original settings for restoration
ORIGINAL_SETTINGS_FILE="/tmp/mysql_original_settings_$(date +%Y%m%d_%H%M%S).sql"

# Backup current settings
docker compose exec -T db mysql -u root -proot -e "
SELECT CONCAT('SET GLOBAL foreign_key_checks = ', @@foreign_key_checks, ';') as restore_cmd
UNION ALL SELECT CONCAT('SET GLOBAL unique_checks = ', @@unique_checks, ';')
UNION ALL SELECT CONCAT('SET GLOBAL innodb_flush_log_at_trx_commit = ', @@innodb_flush_log_at_trx_commit, ';')
UNION ALL SELECT CONCAT('SET GLOBAL innodb_doublewrite = ', @@innodb_doublewrite, ';')
UNION ALL SELECT CONCAT('SET GLOBAL query_cache_type = ', @@query_cache_type, ';')
" > "$ORIGINAL_SETTINGS_FILE" 2>/dev/null

# Apply aggressive import optimizations
docker compose exec -T db mysql -u root -proot -e "
-- SAFETY CHECKS (MAJOR SPEED BOOST)
SET GLOBAL foreign_key_checks = 0;
SET GLOBAL unique_checks = 0;
SET GLOBAL sql_log_bin = 0;

-- PACKET AND BUFFER SIZES
SET GLOBAL max_allowed_packet = 1073741824;
SET GLOBAL bulk_insert_buffer_size = 536870912;
SET GLOBAL key_buffer_size = 536870912;
SET GLOBAL sort_buffer_size = 134217728;
SET GLOBAL read_buffer_size = 67108864;
SET GLOBAL read_rnd_buffer_size = 67108864;
SET GLOBAL myisam_sort_buffer_size = 268435456;

-- INNODB OPTIMIZATIONS
SET GLOBAL innodb_buffer_pool_size = 2147483648;
SET GLOBAL innodb_log_buffer_size = 134217728;
SET GLOBAL innodb_flush_log_at_trx_commit = 0;
SET GLOBAL innodb_doublewrite = 0;
SET GLOBAL innodb_file_per_table = 0;
SET GLOBAL innodb_autoinc_lock_mode = 2;

-- MEMORY AND TEMP TABLES
SET GLOBAL tmp_table_size = 2147483648;
SET GLOBAL max_heap_table_size = 2147483648;
SET GLOBAL table_open_cache = 2048;
SET GLOBAL table_definition_cache = 1024;

-- DISABLE QUERY CACHE
SET GLOBAL query_cache_size = 0;
SET GLOBAL query_cache_type = 0;

-- TIMEOUTS
SET GLOBAL connect_timeout = 3600;
SET GLOBAL wait_timeout = 3600;
SET GLOBAL interactive_timeout = 3600;
SET GLOBAL net_read_timeout = 3600;
SET GLOBAL net_write_timeout = 3600;

-- CONCURRENT OPERATIONS
SET GLOBAL concurrent_insert = 2;
" 2>/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Aggressive MySQL optimizations applied for maximum import speed${NC}"
    echo -e "${GREEN}✓ Original settings backed up to: $ORIGINAL_SETTINGS_FILE${NC}"
else
    echo -e "${YELLOW}⚠ Warning: Could not apply all optimizations (import may still work)${NC}"
fi

# Show key optimization settings
echo -e "${YELLOW}Key import optimization settings:${NC}"
docker compose exec -T db mysql -u root -proot -e "
SELECT 
    'foreign_key_checks' as setting, @@foreign_key_checks as value, 'DISABLED for speed' as note
UNION ALL SELECT 
    'unique_checks', @@unique_checks, 'DISABLED for speed'
UNION ALL SELECT 
    'innodb_flush_log_at_trx_commit', @@innodb_flush_log_at_trx_commit, 'Set to 0 for speed'
UNION ALL SELECT 
    'innodb_doublewrite', @@innodb_doublewrite, 'DISABLED for speed'
UNION ALL SELECT 
    'max_allowed_packet_MB', ROUND(@@max_allowed_packet/1024/1024, 0), '1GB packet size'
UNION ALL SELECT 
    'bulk_insert_buffer_MB', ROUND(@@bulk_insert_buffer_size/1024/1024, 0), 'Large buffer for inserts'
UNION ALL SELECT 
    'innodb_buffer_pool_GB', ROUND(@@innodb_buffer_pool_size/1024/1024/1024, 1), 'Large InnoDB buffer'
;" 2>/dev/null

# Perform the import
echo -e "${YELLOW}Starting import process...${NC}"
echo "This may take several minutes for large files..."

# Create a temporary log file
LOG_FILE="/tmp/mysql_import_$(date +%Y%m%d_%H%M%S).log"

# Function to restore MySQL settings
restore_mysql_settings() {
    echo -e "${YELLOW}Restoring MySQL to safe production settings...${NC}"
    
    docker compose exec -T db mysql -u root -proot -e "
    -- Re-enable safety features
    SET GLOBAL foreign_key_checks = 1;
    SET GLOBAL unique_checks = 1;
    SET GLOBAL sql_log_bin = 1;
    
    -- Restore InnoDB safety settings
    SET GLOBAL innodb_flush_log_at_trx_commit = 1;
    SET GLOBAL innodb_doublewrite = 1;
    SET GLOBAL innodb_file_per_table = 1;
    
    -- Restore query cache
    SET GLOBAL query_cache_type = 1;
    SET GLOBAL query_cache_size = 67108864;
    
    -- Set reasonable timeouts
    SET GLOBAL connect_timeout = 60;
    SET GLOBAL wait_timeout = 28800;
    SET GLOBAL interactive_timeout = 28800;
    SET GLOBAL net_read_timeout = 30;
    SET GLOBAL net_write_timeout = 60;
    
    -- Restore concurrent insert
    SET GLOBAL concurrent_insert = 1;
    " 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ MySQL settings restored to safe production defaults${NC}"
        
        # Clean up backup file
        rm -f "$ORIGINAL_SETTINGS_FILE"
    else
        echo -e "${YELLOW}⚠ Warning: Could not restore all settings. Manual restoration may be needed.${NC}"
        if [ -f "$ORIGINAL_SETTINGS_FILE" ]; then
            echo -e "${YELLOW}Original settings backed up in: $ORIGINAL_SETTINGS_FILE${NC}"
        fi
    fi
}

# Run the import with error capture
if docker compose exec -T db mysql -u root -proot "$DATABASE_NAME" < "$SQL_FILE" 2>"$LOG_FILE"; then
    echo -e "${GREEN}✓ Import completed successfully!${NC}"
    
    # Restore MySQL settings to safe defaults
    restore_mysql_settings
    
    # Show some basic stats
    echo -e "${YELLOW}Database statistics:${NC}"
    docker compose exec -T db mysql -u root -proot -e "
        SELECT 
            COUNT(*) as 'Total Tables',
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Database Size (MB)'
        FROM information_schema.tables 
        WHERE table_schema = '$DATABASE_NAME';
    " 2>/dev/null
    
    echo -e "${YELLOW}Sample of tables created:${NC}"
    docker compose exec -T db mysql -u root -proot -e "SHOW TABLES;" "$DATABASE_NAME" 2>/dev/null | head -10
    
    # Clean up log file if no errors
    rm -f "$LOG_FILE"
    
    echo ""
    echo -e "${GREEN}🚀 Import completed with speed optimizations!${NC}"
    echo -e "${GREEN}✓ MySQL settings have been restored to safe production defaults${NC}"
    
    exit 0
else
    echo -e "${RED}✗ Import failed!${NC}"
    
    # Restore MySQL settings even on failure
    restore_mysql_settings
    
    echo -e "${RED}Error details:${NC}"
    
    # Show error details
    if [ -f "$LOG_FILE" ]; then
        cat "$LOG_FILE"
        echo ""
        echo "Full error log saved to: $LOG_FILE"
    fi
    
    # Check if it's a packet size issue
    if grep -q "max_allowed_packet" "$LOG_FILE" 2>/dev/null; then
        echo -e "${YELLOW}This appears to be a packet size issue. The file may be too large.${NC}"
        echo -e "${YELLOW}Consider splitting the SQL file into smaller chunks.${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}✓ MySQL settings have been restored to safe defaults after failed import${NC}"
    
    exit 1
fi