# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name localhost;
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name localhost;
    root /var/www/html/public;
    index index.php index.html;

    # SSL configuration
    ssl_certificate /etc/nginx/ssl/localhost.crt;
    ssl_certificate_key /etc/nginx/ssl/localhost.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Handle /tpl/ routes first - these should be processed by PHP
    location ^~ /tpl/ {
        try_files $uri $uri/ /index.php?$args;
    }

    # Handle /api/ routes - equivalent to api/.htaccess rewrite rules
    location ^~ /api/ {
        # Serve static files from specific directories directly
        location ^~ /api/data/ {
            try_files $uri $uri/ =404;
        }
        location ^~ /api/explorer/ {
            try_files $uri $uri/ =404;
        }
        location ^~ /api/jackdaw_html5/ {
            try_files $uri $uri/ =404;
        }
        location ^~ /api/metadata/ {
            try_files $uri $uri/ =404;
        }
        location ^~ /api/temp/ {
            try_files $uri $uri/ =404;
        }
        location ^~ /api/vendor/ {
            try_files $uri $uri/ =404;
        }
        
        # Handle PHP files with PATH_INFO using fastcgi_split_path_info
        location ~ ^/api/(.+\.php)(/.+)?$ {
            try_files /api/$1 =404;
            fastcgi_pass web:9000;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root/api/$1;
            fastcgi_param PATH_INFO $2;
            fastcgi_param HTTPS on;
            fastcgi_param HTTP_SCHEME https;
            fastcgi_param REQUEST_URI $request_uri;
            fastcgi_param QUERY_STRING $query_string;
            include fastcgi_params;
            fastcgi_read_timeout 300;
        }
        
        # Handle all API requests - rewrite to index.php (like Apache .htaccess)
        location ~ ^/api/(.*)$ {
            try_files $uri $uri/ @api_fallback;
        }
    }

    # Named location for API fallback to index.php
    location @api_fallback {
        fastcgi_pass web:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/api/index.php;
        fastcgi_param HTTPS on;
        fastcgi_param HTTP_SCHEME https;
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param QUERY_STRING $query_string;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }

    # Handle SCORM file.php with path info - must come before static file handler
    location ^~ /scorm/file.php/ {
        fastcgi_pass web:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/scorm/file.php;
        fastcgi_param PATH_INFO $uri;
        fastcgi_param HTTPS on;
        fastcgi_param HTTP_SCHEME https;
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param QUERY_STRING $query_string;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        fastcgi_pass web:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param HTTPS on;
        fastcgi_param HTTP_SCHEME https;
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param QUERY_STRING $query_string;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }

    location ~ /\.ht {
        deny all;
    }

    # Handle static files (excluding /tpl/ paths)
    location ~* ^(?!/tpl/)\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Increase max upload size
    client_max_body_size 200M;
}