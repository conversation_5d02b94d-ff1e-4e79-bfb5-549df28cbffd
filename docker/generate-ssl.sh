#!/bin/bash

# Create SSL directory if it doesn't exist
mkdir -p ssl

# Generate private key
openssl genrsa -out ssl/localhost.key 2048

# Generate certificate signing request
openssl req -new -key ssl/localhost.key -out ssl/localhost.csr -config <(
cat <<EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=GB
ST=London
L=London
O=Open eLMS
OU=Development
CN=localhost

[v3_req]
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
)

# Generate self-signed certificate valid for 365 days
openssl x509 -req -in ssl/localhost.csr -signkey ssl/localhost.key -out ssl/localhost.crt -days 365 -extensions v3_req -extfile <(
cat <<EOF
[v3_req]
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
)

# Clean up CSR file
rm ssl/localhost.csr

echo "SSL certificates generated successfully!"
echo "Certificate: ssl/localhost.crt"
echo "Private key: ssl/localhost.key"