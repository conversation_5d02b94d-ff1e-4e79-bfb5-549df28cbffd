#!/bin/bash

# Monitor MySQL Import Progress with Timestamps
# Usage: ./monitor-import.sh <database_name> [interval_seconds]

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DATABASE_NAME=${1:-"kirklees"}
INTERVAL=${2:-10}

# Function to display usage
usage() {
    echo "Usage: $0 <database_name> [interval_seconds]"
    echo ""
    echo "Parameters:"
    echo "  database_name     - Name of the database to monitor"
    echo "  interval_seconds  - Check interval in seconds (default: 10)"
    echo ""
    echo "Examples:"
    echo "  $0 kirklees"
    echo "  $0 kirklees 5"
    echo ""
    echo "Press Ctrl+C to stop monitoring"
    exit 1
}

if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    usage
fi

echo -e "${BLUE}=== MySQL Import Monitor ===${NC}"
echo "Database: $DATABASE_NAME"
echo "Check interval: ${INTERVAL}s"
echo "Press Ctrl+C to stop"
echo ""

# Store previous size for growth calculation
PREV_SIZE=0
START_TIME=$(date +%s)

while true; do
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Get current size
    CURRENT_SIZE=$(docker compose exec -T db mysql -u root -proot -e "
    SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2)
    FROM information_schema.tables WHERE table_schema = '$DATABASE_NAME';" 2>/dev/null | tail -n 1)
    
    # Get table count
    TABLE_COUNT=$(docker compose exec -T db mysql -u root -proot -e "
    SELECT COUNT(*) FROM information_schema.tables 
    WHERE table_schema = '$DATABASE_NAME';" 2>/dev/null | tail -n 1)
    
    if [ -n "$CURRENT_SIZE" ] && [ "$CURRENT_SIZE" != "NULL" ]; then
        # Calculate growth since last check
        if [ "$PREV_SIZE" != "0" ]; then
            GROWTH=$(echo "$CURRENT_SIZE - $PREV_SIZE" | bc 2>/dev/null || echo "0")
            GROWTH_RATE=$(echo "scale=2; $GROWTH / $INTERVAL" | bc 2>/dev/null || echo "0")
        else
            GROWTH="0"
            GROWTH_RATE="0"
        fi
        
        # Calculate elapsed time
        CURRENT_TIME=$(date +%s)
        ELAPSED=$((CURRENT_TIME - START_TIME))
        ELAPSED_MIN=$((ELAPSED / 60))
        ELAPSED_SEC=$((ELAPSED % 60))
        
        echo -e "${YELLOW}[$TIMESTAMP]${NC} Size: ${GREEN}${CURRENT_SIZE} MB${NC} | Tables: ${GREEN}${TABLE_COUNT}${NC} | Growth: ${GREEN}+${GROWTH} MB${NC} (${GROWTH_RATE} MB/s) | Elapsed: ${ELAPSED_MIN}m ${ELAPSED_SEC}s"
        
        PREV_SIZE=$CURRENT_SIZE
    else
        echo -e "${YELLOW}[$TIMESTAMP]${NC} Database '$DATABASE_NAME' not found or no data yet..."
    fi
    
    sleep $INTERVAL
done