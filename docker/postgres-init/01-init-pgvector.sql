-- Initialize pgvector extension and create schema for code embeddings

-- Create the vector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create schema for code embeddings
CREATE SCHEMA IF NOT EXISTS code_search;

-- Create the main embeddings table
CREATE TABLE IF NOT EXISTS code_search.code_embeddings (
    id SERIAL PRIMARY KEY,
    file_path TEXT NOT NULL,
    file_type VARCHAR(20),
    content_type VARCHAR(50), -- 'function', 'class', 'method', 'route', 'model', 'comment'
    name VARCHAR(255), -- function/class/method name
    content_chunk TEXT NOT NULL,
    line_start INTEGER,
    line_end INTEGER,
    embedding vector(1536), -- OpenAI ada-002 embeddings dimension
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraint for upserts
CREATE UNIQUE INDEX idx_embeddings_unique ON code_search.code_embeddings(file_path, content_type, COALESCE(name, ''));

-- Create indexes for performance
CREATE INDEX idx_embeddings_file_path ON code_search.code_embeddings(file_path);
CREATE INDEX idx_embeddings_file_type ON code_search.code_embeddings(file_type);
CREATE INDEX idx_embeddings_content_type ON code_search.code_embeddings(content_type);
CREATE INDEX idx_embeddings_name ON code_search.code_embeddings(name);
CREATE INDEX idx_embeddings_metadata ON code_search.code_embeddings USING GIN(metadata);

-- Index for vector similarity search (using IVFFlat for better performance on large datasets)
CREATE INDEX idx_embeddings_vector ON code_search.code_embeddings 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Create a table for storing search history and feedback
CREATE TABLE IF NOT EXISTS code_search.search_history (
    id SERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    query_embedding vector(1536),
    results JSONB,
    feedback_score INTEGER CHECK (feedback_score >= 1 AND feedback_score <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create helper functions
CREATE OR REPLACE FUNCTION code_search.semantic_search(
    query_embedding vector(1536),
    match_threshold FLOAT DEFAULT 0.7,
    match_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id INTEGER,
    file_path TEXT,
    content_type VARCHAR(50),
    name VARCHAR(255),
    content_chunk TEXT,
    similarity FLOAT
)
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ce.id,
        ce.file_path,
        ce.content_type,
        ce.name,
        ce.content_chunk,
        1 - (ce.embedding <=> query_embedding) AS similarity
    FROM code_search.code_embeddings ce
    WHERE 1 - (ce.embedding <=> query_embedding) > match_threshold
    ORDER BY ce.embedding <=> query_embedding
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- Function to search by multiple criteria
CREATE OR REPLACE FUNCTION code_search.hybrid_search(
    query_text TEXT DEFAULT NULL,
    query_embedding vector(1536) DEFAULT NULL,
    file_types TEXT[] DEFAULT NULL,
    content_types TEXT[] DEFAULT NULL,
    match_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id INTEGER,
    file_path TEXT,
    content_type VARCHAR(50),
    name VARCHAR(255),
    content_chunk TEXT,
    similarity FLOAT,
    text_rank FLOAT
)
AS $$
BEGIN
    RETURN QUERY
    WITH text_search AS (
        SELECT 
            ce.id,
            ts_rank(to_tsvector('english', ce.content_chunk), 
                   plainto_tsquery('english', COALESCE(query_text, ''))) AS rank
        FROM code_search.code_embeddings ce
        WHERE query_text IS NOT NULL
    ),
    vector_search AS (
        SELECT 
            ce.id,
            1 - (ce.embedding <=> query_embedding) AS similarity
        FROM code_search.code_embeddings ce
        WHERE query_embedding IS NOT NULL
    )
    SELECT 
        ce.id,
        ce.file_path,
        ce.content_type,
        ce.name,
        ce.content_chunk,
        COALESCE(vs.similarity, 0) AS similarity,
        COALESCE(ts.rank, 0) AS text_rank
    FROM code_search.code_embeddings ce
    LEFT JOIN text_search ts ON ce.id = ts.id
    LEFT JOIN vector_search vs ON ce.id = vs.id
    WHERE 
        (file_types IS NULL OR ce.file_type = ANY(file_types)) AND
        (content_types IS NULL OR ce.content_type = ANY(content_types)) AND
        (ts.rank > 0 OR vs.similarity > 0.5)
    ORDER BY 
        COALESCE(vs.similarity, 0) * 0.7 + COALESCE(ts.rank, 0) * 0.3 DESC
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- Add update trigger for updated_at
CREATE OR REPLACE FUNCTION code_search.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_code_embeddings_updated_at 
    BEFORE UPDATE ON code_search.code_embeddings 
    FOR EACH ROW 
    EXECUTE FUNCTION code_search.update_updated_at_column();

-- Grant permissions (adjust as needed)
GRANT USAGE ON SCHEMA code_search TO lms_vector;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA code_search TO lms_vector;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA code_search TO lms_vector;

-- Add full-text search support
ALTER TABLE code_search.code_embeddings ADD COLUMN IF NOT EXISTS search_vector tsvector;
CREATE INDEX idx_search_vector ON code_search.code_embeddings USING GIN(search_vector);

-- Update search vector when content changes
CREATE OR REPLACE FUNCTION code_search.update_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('english', COALESCE(NEW.name, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.content_type, '')), 'B') ||
        setweight(to_tsvector('english', COALESCE(NEW.content_chunk, '')), 'C');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_search_vector 
    BEFORE INSERT OR UPDATE ON code_search.code_embeddings 
    FOR EACH ROW 
    EXECUTE FUNCTION code_search.update_search_vector();