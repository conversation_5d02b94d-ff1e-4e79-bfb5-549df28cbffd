FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Python dependencies
RUN pip install --no-cache-dir \
    psycopg2-binary \
    pgvector \
    openai \
    numpy \
    lxml \
    fastapi \
    uvicorn \
    pydantic \
    python-multipart \
    tiktoken \
    langchain \
    sentence-transformers \
    tabulate \
    requests

# Install PyTorch CPU version separately
RUN pip install --no-cache-dir torch --index-url https://download.pytorch.org/whl/cpu

# Create directory structure
RUN mkdir -p /app/scripts /app/data

# Copy scripts
COPY python/ /app/scripts/

# Expose port for API if needed
EXPOSE 8000

CMD ["python", "/app/scripts/vectorizer_service.py"]