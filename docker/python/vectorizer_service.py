#!/usr/bin/env python3
"""
Code Vectorizer Service
Processes code from repomix-output.xml and creates vector embeddings for semantic search
"""

import os
import sys
import json
import re
import logging
from typing import List, Dict, Optional
import xml.etree.ElementTree as ET
from datetime import datetime

import psycopg2
from psycopg2.extras import RealDictCursor
import numpy as np
from pgvector.psycopg2 import register_vector
import openai
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
POSTGRES_CONFIG = {
    'host': os.getenv('POSTGRES_HOST', 'postgres'),
    'database': os.getenv('POSTGRES_DB', 'lms_vectors'),
    'user': os.getenv('POSTGRES_USER', 'lms_vector'),
    'password': os.getenv('POSTGRES_PASSWORD', 'lms_vector_pass'),
    'port': 5432
}

OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
if OPENAI_API_KEY:
    openai.api_key = OPENAI_API_KEY

# FastAPI app for API endpoints
app = FastAPI(title="LMS Code Vectorizer", version="1.0.0")

class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 10
    threshold: Optional[float] = 0.7
    file_types: Optional[List[str]] = None
    content_types: Optional[List[str]] = None

class VectorizeRequest(BaseModel):
    file_path: Optional[str] = None
    process_all: Optional[bool] = False

class CodeChunk:
    def __init__(self, file_path: str, content: str, content_type: str, 
                 name: str = None, line_start: int = None, line_end: int = None):
        self.file_path = file_path
        self.content = content
        self.content_type = content_type
        self.name = name
        self.line_start = line_start
        self.line_end = line_end
        self.file_type = os.path.splitext(file_path)[1][1:]

class CodeVectorizer:
    def __init__(self):
        self.conn = None
        self.connect_db()
        
    def connect_db(self):
        """Connect to PostgreSQL with pgvector"""
        try:
            self.conn = psycopg2.connect(**POSTGRES_CONFIG)
            register_vector(self.conn)
            logger.info("Connected to PostgreSQL with pgvector")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def extract_php_chunks(self, content: str, file_path: str) -> List[CodeChunk]:
        """Extract meaningful chunks from PHP code"""
        chunks = []
        
        # Extract functions
        function_pattern = r'(?:public|private|protected|static)*\s*function\s+(\w+)\s*\([^)]*\)\s*(?:{|:)'
        for match in re.finditer(function_pattern, content, re.MULTILINE):
            func_name = match.group(1)
            start_pos = match.start()
            
            # Find the end of the function by counting braces
            brace_count = 0
            in_string = False
            escape_next = False
            i = match.end() - 1
            end_pos = start_pos  # Initialize end_pos
            
            while i < len(content):
                char = content[i]
                
                if escape_next:
                    escape_next = False
                elif char == '\\':
                    escape_next = True
                elif char in ['"', "'"] and not in_string:
                    in_string = char
                elif char == in_string:
                    in_string = False
                elif not in_string:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_pos = i + 1
                            break
                i += 1
            
            if end_pos > start_pos:
                func_content = content[start_pos:end_pos]
                line_start = content[:start_pos].count('\n') + 1
                line_end = content[:end_pos].count('\n') + 1
                
                chunks.append(CodeChunk(
                    file_path=file_path,
                    content=func_content,
                    content_type='function',
                    name=func_name,
                    line_start=line_start,
                    line_end=line_end
                ))
        
        # Extract classes
        class_pattern = r'(?:abstract\s+)?(?:final\s+)?class\s+(\w+)(?:\s+extends\s+\w+)?(?:\s+implements\s+[\w\s,]+)?\s*{'
        for match in re.finditer(class_pattern, content, re.MULTILINE):
            class_name = match.group(1)
            start_pos = match.start()
            line_start = content[:start_pos].count('\n') + 1
            
            # For classes, just store the declaration and first few lines
            end_of_line = content.find('\n', match.end())
            if end_of_line == -1:
                end_of_line = len(content)
            
            class_preview = content[start_pos:end_of_line + 100]  # First 100 chars after class declaration
            
            chunks.append(CodeChunk(
                file_path=file_path,
                content=class_preview,
                content_type='class',
                name=class_name,
                line_start=line_start
            ))
        
        # Extract route definitions (Slim framework)
        route_pattern = r'\$(?:app|group)->(?:get|post|put|delete|patch)\s*\(\s*[\'"]([^\'"]+)[\'"]'
        for match in re.finditer(route_pattern, content):
            route_path = match.group(1)
            start_pos = max(0, match.start() - 50)
            end_pos = min(len(content), match.end() + 200)
            
            route_content = content[start_pos:end_pos]
            line_num = content[:match.start()].count('\n') + 1
            
            chunks.append(CodeChunk(
                file_path=file_path,
                content=route_content,
                content_type='route',
                name=route_path,
                line_start=line_num
            ))
        
        return chunks
    
    def get_embedding(self, text: str, use_openai: bool = True) -> Optional[List[float]]:
        """Get embedding using OpenAI or fallback to sentence-transformers"""
        if use_openai and OPENAI_API_KEY:
            try:
                client = openai.OpenAI(api_key=OPENAI_API_KEY)
                response = client.embeddings.create(
                    model="text-embedding-ada-002",
                    input=text[:8000]  # Limit tokens
                )
                return response.data[0].embedding
            except Exception as e:
                logger.warning(f"OpenAI embedding failed: {e}, falling back to local model")
        
        # Fallback to sentence-transformers
        try:
            from sentence_transformers import SentenceTransformer
            if not hasattr(self, 'local_model'):
                self.local_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Note: This produces 384-dimensional embeddings, not 1536
            embedding = self.local_model.encode(text[:1000])
            # Pad to 1536 dimensions for compatibility
            padded = np.zeros(1536)
            padded[:len(embedding)] = embedding
            return padded.tolist()
        except Exception as e:
            logger.error(f"Local embedding failed: {e}")
            return None
    
    def store_chunk(self, chunk: CodeChunk, embedding: List[float]):
        """Store code chunk with embedding in PostgreSQL"""
        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO code_search.code_embeddings 
                    (file_path, file_type, content_type, name, content_chunk, 
                     line_start, line_end, embedding, metadata)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (file_path, content_type, COALESCE(name, '')) 
                    DO UPDATE SET 
                        content_chunk = EXCLUDED.content_chunk,
                        embedding = EXCLUDED.embedding,
                        updated_at = CURRENT_TIMESTAMP
                """, (
                    chunk.file_path,
                    chunk.file_type,
                    chunk.content_type,
                    chunk.name,
                    chunk.content,
                    chunk.line_start,
                    chunk.line_end,
                    embedding,
                    json.dumps({
                        'length': len(chunk.content),
                        'processed_at': datetime.now().isoformat()
                    })
                ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"Failed to store chunk: {e}")
            self.conn.rollback()
    
    def process_xml_file(self, xml_path: str = '/app/repomix-output.xml'):
        """Process the repomix XML file and vectorize code"""
        if not os.path.exists(xml_path):
            logger.error(f"XML file not found: {xml_path}")
            return
        
        logger.info(f"Processing {xml_path}")
        
        processed = 0
        current_file_path = None
        current_content = []
        in_file_content = False
        
        try:
            with open(xml_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # Look for file start tag
                    if '<file path="' in line:
                        # Process previous file if exists
                        if current_file_path and current_content:
                            self._process_file_content(current_file_path, '\n'.join(current_content))
                            processed += self._get_processed_count(current_file_path, '\n'.join(current_content))
                            
                            if processed % 50 == 0:
                                logger.info(f"Processed {processed} chunks from {current_file_path}")
                        
                        # Extract file path
                        start = line.find('<file path="') + 12
                        end = line.find('">', start)
                        if end > start:
                            current_file_path = line[start:end]
                            current_content = []
                            in_file_content = True
                            continue
                    
                    # Look for file end tag
                    elif '</file>' in line:
                        if current_file_path and current_content:
                            self._process_file_content(current_file_path, '\n'.join(current_content))
                            processed += self._get_processed_count(current_file_path, '\n'.join(current_content))
                        current_file_path = None
                        current_content = []
                        in_file_content = False
                        continue
                    
                    # Collect file content
                    elif in_file_content and current_file_path:
                        current_content.append(line.rstrip('\n'))
                
                # Process last file if exists
                if current_file_path and current_content:
                    self._process_file_content(current_file_path, '\n'.join(current_content))
                    processed += self._get_processed_count(current_file_path, '\n'.join(current_content))
                        
        except Exception as e:
            logger.error(f"Error processing repomix file: {e}")
            return
        
        logger.info(f"Total chunks processed: {processed}")
    
    def _process_file_content(self, file_path: str, content: str):
        """Process a single file's content"""
        if not content or not file_path.endswith('.php'):
            return
        
        # Extract chunks
        chunks = self.extract_php_chunks(content, file_path)
        
        for chunk in chunks:
            # Get embedding
            embedding = self.get_embedding(chunk.content)
            if embedding:
                self.store_chunk(chunk, embedding)
    
    def _get_processed_count(self, file_path: str, content: str) -> int:
        """Get count of chunks that would be processed for a file"""
        if not content or not file_path.endswith('.php'):
            return 0
        
        chunks = self.extract_php_chunks(content, file_path)
        return len([c for c in chunks if self.get_embedding(c.content)])
    
    def search(self, query: str, limit: int = 10, threshold: float = 0.7,
               file_types: List[str] = None, content_types: List[str] = None) -> List[Dict]:
        """Search for similar code using vector similarity"""
        embedding = self.get_embedding(query)
        if not embedding:
            return []
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Build query
                sql = """
                    SELECT 
                        id,
                        file_path,
                        file_type,
                        content_type,
                        name,
                        content_chunk,
                        line_start,
                        line_end,
                        1 - (embedding <=> %s::vector) as similarity
                    FROM code_search.code_embeddings
                    WHERE 1 - (embedding <=> %s::vector) > %s
                """
                
                params = [embedding, embedding, threshold]
                
                if file_types:
                    sql += " AND file_type = ANY(%s)"
                    params.append(file_types)
                
                if content_types:
                    sql += " AND content_type = ANY(%s)"
                    params.append(content_types)
                
                sql += " ORDER BY embedding <=> %s::vector LIMIT %s"
                params.extend([embedding, limit])
                
                cur.execute(sql, params)
                return cur.fetchall()
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

# Create global vectorizer instance
vectorizer = CodeVectorizer()

# API Endpoints
@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "code-vectorizer"}

@app.post("/search")
def search_code(request: SearchRequest):
    """Search for code using semantic similarity"""
    results = vectorizer.search(
        query=request.query,
        limit=request.limit,
        threshold=request.threshold,
        file_types=request.file_types,
        content_types=request.content_types
    )
    return {"query": request.query, "results": results}

@app.post("/vectorize")
def vectorize_code(request: VectorizeRequest):
    """Vectorize code from XML file"""
    try:
        if request.process_all:
            vectorizer.process_xml_file()
            return {"status": "success", "message": "Vectorization started"}
        else:
            return {"status": "error", "message": "Specify process_all=true to start vectorization"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
def get_stats():
    """Get statistics about stored embeddings"""
    try:
        with vectorizer.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    COUNT(*) as total_chunks,
                    COUNT(DISTINCT file_path) as total_files,
                    COUNT(DISTINCT file_type) as file_types,
                    COUNT(DISTINCT content_type) as content_types
                FROM code_search.code_embeddings
            """)
            return cur.fetchone()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    # Run the FastAPI server
    logger.info("Starting Code Vectorizer Service")
    uvicorn.run(app, host="0.0.0.0", port=8000)