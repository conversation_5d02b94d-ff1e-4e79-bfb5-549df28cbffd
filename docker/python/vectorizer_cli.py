#!/usr/bin/env python3
"""
CLI tool for code vectorization and search
"""

import argparse
import requests
import json
from tabulate import tabulate

API_BASE = "http://python_vectorizer:8000"

def search_code(query, limit=10):
    """Search for code snippets"""
    response = requests.post(f"{API_BASE}/search", json={
        "query": query,
        "limit": limit,
        "threshold": 0.3  # Lower threshold for more results
    })
    
    if response.status_code == 200:
        data = response.json()
        results = data['results']
        
        if results:
            # Format results for display
            table_data = []
            for r in results:
                table_data.append([
                    r['file_path'].split('/')[-1],  # Just filename
                    r['content_type'],
                    r['name'] or 'N/A',
                    f"{r['similarity']:.3f}",
                    f"L{r['line_start']}" if r['line_start'] else 'N/A'
                ])
            
            print(f"\nFound {len(results)} results for: '{query}'\n")
            print(tabulate(table_data, 
                         headers=['File', 'Type', 'Name', 'Similarity', 'Line'],
                         tablefmt='grid'))
            
            # Show top result details
            if results:
                top = results[0]
                print(f"\nTop result ({top['file_path']}):")
                print("-" * 80)
                print(top['content_chunk'][:500] + "..." if len(top['content_chunk']) > 500 else top['content_chunk'])
        else:
            print(f"No results found for: '{query}'")
    else:
        print(f"Error: {response.status_code}")

def vectorize_all():
    """Start vectorization of all code"""
    response = requests.post(f"{API_BASE}/vectorize", json={
        "process_all": True
    })
    print(response.json())

def get_stats():
    """Get statistics"""
    response = requests.get(f"{API_BASE}/stats")
    if response.status_code == 200:
        stats = response.json()
        print("\nCode Search Statistics:")
        print("-" * 30)
        for key, value in stats.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
    else:
        print(f"Error: {response.status_code}")

def main():
    parser = argparse.ArgumentParser(description='Code Search CLI')
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # Search command
    search_parser = subparsers.add_parser('search', help='Search for code')
    search_parser.add_argument('query', help='Search query')
    search_parser.add_argument('-l', '--limit', type=int, default=10, help='Result limit')
    
    # Vectorize command
    vectorize_parser = subparsers.add_parser('vectorize', help='Vectorize all code')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show statistics')
    
    args = parser.parse_args()
    
    if args.command == 'search':
        search_code(args.query, args.limit)
    elif args.command == 'vectorize':
        vectorize_all()
    elif args.command == 'stats':
        get_stats()
    else:
        parser.print_help()

if __name__ == "__main__":
    main()