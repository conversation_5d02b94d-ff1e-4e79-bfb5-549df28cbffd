<?php
/*
	ILR fields tree and validation logic goes here, used for export, will be used for import also.
	For individual Learner only

	https://assets.publishing.service.gov.uk/government/uploads/system/uploads/attachment_data/file/715492/ILRSpecification2018-2019v3.pdf

	https://assets.publishing.service.gov.uk/government/uploads/system/uploads/attachment_data/file/800443/ILR_Specification_2019-2020v2.pdf

	https://guidance.submitlearnerdatabeta.fasst.org.uk/ilr
*/

$ilr_UUID = '662db9a7-940a-474f-a297-526b208465c2';

$ilr_groups = [
	'header' => [
		'name' => '',
		'type' => 'well',
		'fieldGroup' => 'ilr',
	],
	'learner' => [
		'name' => 'Learner',
		'type' => 'tab',
		'fieldGroup' => 'ilr',
	],
	'employment-statuses' => [
		'name' => 'Employment Statuses',
		'type' => 'tab',
		'fieldGroup' => 'ilr',
	],
	'learning-deliveries' => [
		'name' => 'Learning Deliveries',
		'type' => 'tab',
		'fieldGroup' => 'ilr',
	],
];

$ilr_fields = [
	'UKPRN' => [
		'name' => 'UK provider reference number (UKPRN)',
		'required' => true,
		'pattern' => '^[1-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$',
		'pattern_error' => 'Must contain a value in the range 10000000 to 99999999',
		'type' => 'number',
		'field' => 'number',
		'group' => 'header',
	],
	'LearnRefNumber' => [
		'name' => 'Learner reference number',
		'required' => true,
		'pattern' => '^[A-Za-z0-9 ]{1,12}$',
		'pattern_error' => 'Any combination of up to 12 alphabetic characters, numeric digits or spaces',
		'group' => 'header',
	],
	'PrevLearnRefNumber' => [
		'name' => 'Learner reference number in previous year',
		'required' => false,
		'pattern' => '^[A-Za-z0-9 ]{1,12}$',
		'pattern_error' => 'Any combination of up to 12 alphabetic characters, numeric digits or spaces',
		'group' => 'header',
	],
	'exclude_from_ilr_export' => [
		'name' => 'Exclude Export',
		'required' => false,
		'pattern' => '^\d{1}$',
		'pattern_error' => 'Must be integer, one characters',
		'type' => 'boolean',
		'group' => 'learner',
		'field' => 'checkbox',
		'choice' => [
			'name' => 'Exclude from any ILR export',
			'value' => true
		]
	],
	'PrevUKPRN' => [
		'name' => 'UKPRN in previous year',
		'required' => false,
		'pattern' => '^[1-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$',
		'pattern_error' => 'Must contain a value in the range 10000000 to 99999999',
		'type' => 'number',
		'field' => 'number',
		'group' => 'header',
	],
	'PMUKPRN' => [
		'name' => 'Pre-merger UKPRN',
		'required' => false,
		'pattern' => '^[1-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$',
		'pattern_error' => 'Must contain a value in the range 10000000 to 99999999',
		'type' => 'number',
		'field' => 'number',
		'group' => 'header',
	],
	'ULN' => [
		'name' => 'Unique learner number',
		'required' => true,
		'pattern' => '^[1-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$',
		'pattern_error' => 'Must contain a value in the range 1000000000 to 9999999999',
		'type' => 'number',
		'field' => 'number',
		'group' => 'header',
	],
	'CampId' => [
		'name' => 'Campus Identifier',
		'required' => false,
		'pattern' => '^[A-Za-z0-9]{1,8}$',
		'pattern_error' => 'Must be in a valid look up. This is a 8 - digit identifier using alphanumeric characters',
		'type' => 'text',
		'group' => 'header',
	],
	'OTJHours' => [
		'name' => 'Off-the-job training hours',
		'definition' => 'Cumulative total of actual off-the-job training hours (as defined in the funding rules) delivered to date, to the individual apprentice, in the academic year by the training provider, sub-contracted training providers and the employer.',
		'reason_required' => 'To provide information about the quantum of off-the-job training delivered. To help demonstrate compliance with the funding rules.',
		'required' => false,
		'pattern' => '^[0-9]{1,4}$',
		'pattern_error' => 'Must contain a value in the range of 0 to 9999',
		'type' => 'number',
		'field' => 'number',
		'group' => 'header',
	],
	'lname' => [ // FamilyName
		'name' => 'Family name',
		'required' => false,
		'pattern' => '^[^0-9\r\n\t|"]{1,100}$',
		'pattern_error' => 'Limit is 100 characters, no "Carriage returns", no "Line feeds", no "Double quotes", no "Pipes", no "Numerical characters"',
		'type' => 'text',

		'group' => 'learner',
	],
	'fname' => [ // GivenNames
		'name' => 'Given name',
		'required' => false,
		'pattern' => '^[^0-9\r\n\t|"]{1,100}$',
		'pattern_error' => 'Limit is 100 characters, no "Carriage returns", no "Line feeds", no "Double quotes", no "Pipes", no "Numerical characters"',
		'type' => 'text',

		'group' => 'learner',

	],
	'DateOfBirth' => [
		'name' => 'Date of birth',
		'required' => false,
		'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
		'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
		'field' => 'date', //YYYY-MM-DD
		'type' => 'date',

		'group' => 'learner',
	],
	'Ethnicity' => [
		'name' => 'Ethnicity',
		'required' => true,
		'pattern' => '^\d{2}$',
		'pattern_error' => 'Incorrect format, must be 2 digits.',
		'type' => 'number',

		'group' => 'learner',
		'field' => 'select',
		'choices' => [
			['name' => 'English / Welsh / Scottish / Northern Irish / British', 'value' => 31],
			['name' => 'Irish', 'value' => 32],
			['name' => 'Gypsy or Irish Traveller', 'value' => 33],
			['name' => 'Any Other White background', 'value' => 34],
			['name' => 'White and Black Caribbean', 'value' => 35],
			['name' => 'White and Black African', 'value' => 36],
			['name' => 'White and Asian', 'value' => 37],
			['name' => 'Any Other Mixed / multiple ethnic background', 'value' => 38],
			['name' => 'Indian', 'value' => 39],
			['name' => 'Pakistani', 'value' => 40],
			['name' => 'Bangladeshi', 'value' => 41],
			['name' => 'Chinese', 'value' => 42],
			['name' => 'Any other Asian background', 'value' => 43],
			['name' => 'African', 'value' => 44],
			['name' => 'Caribbean', 'value' => 45],
			['name' => 'Any other Black / African / Caribbean background', 'value' => 46],
			['name' => 'Arab', 'value' => 47],
			['name' => 'Any other ethnic group', 'value' => 98],
			['name' => 'Not provided', 'value' => 99],
		],
	],
	'Sex' => [
		'name' => 'Sex',
		'required' => true,
		'pattern' => '^(F|M)$',
		'pattern_error' => 'Must contain "F" or "M".',

		'group' => 'learner',
		'field' => 'select',
		'choices' => [
			[
				'name' => 'Female',
				'value' => 'F'
			],
			[
				'name' => 'Male',
				'value' => 'M'
			]
		],
	],
	'LLDDHealthProb' => [
		'name' => 'LLDD and health problem',
		'required' => true,
		'pattern' => '^(1|2|9)$',
		'pattern_error' => 'Must be 1, 2 or 9',

		'group' => 'learner',
		'field' => 'select',
		'choices' => [
			[
				'name' => 'Learner considers himself or herself to have a learning difficulty and/or disability and/or health problem.',
				'value' => 1
			],
			[
				'name' => 'Learner does not consider himself or herself to have a learning difficulty and/or disability and/or health problem.',
				'value' => 2
			],
			[
				'name' => 'No information provided by the learner.',
				'value' => 9
			]
		],
	],
	'NINumber' => [
		'name' => 'National Insurance number',
		'required' => true,
		'pattern' => '^(?:(?![DFIQUV])[A-Z]){1}(?:(?![DFIOQUV])[A-Z]){1}\d{6}([ABCD ]){1}$',
		'pattern_error' => 'A valid national insurance number in the format XXnnnnnnX, where X is alphabetic and n is numeric. The first character of the NI number must not be D, F, I, Q, U or V, the second character must not be D, F, I, O, Q, U or V, characters 3 to 8 must be numeric and character 9 must be A, B, C, D or space.',
		'group' => 'learner',
	],
	'PriorAttain' => [
		'name' => 'Prior Attainment',
		'type' => 'json',
		'add' => true,
		'description' => 'The learner\'s prior attainment when a new learning agreement has been agreed between the learner and the provider.',
		'group' => 'learner',
		'limit' => 22,
		'required' => false,
		'children' => [
			'PriorLevel' => [
				'name' => 'Prior attainment',
				'required' => false,
				'pattern' => '^\d{1,2}$',
				'pattern_error' => 'Must be integer, one or two characters',
				'type' => 'number',

				'group' => 'learner',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Entry level',
						'value' => 1
					],
					[
						'name' => 'Level 1',
						'value' => 2
					],
					[
						'name' => 'Level 2',
						'value' => 3
					],
					[
						'name' => 'Full Level 2',
						'value' => 4
					],
					[
						'name' => 'Level 3',
						'value' => 5
					],
					[
						'name' => 'Full Level 3',
						'value' => 6
					],
					[
						'name' => 'Level 4',
						'value' => 7
					],
					[
						'name' => 'Level 5',
						'value' => 8
					],
					[
						'name' => 'Level 6',
						'value' => 9
					],
					[
						'name' => 'Level 7 and above',
						'value' => 10
					],
					[
						'name' => 'Other qualification, level not known',
						'value' => 97
					],
					[
						'name' => 'Not known',
						'value' => 98
					],
					[
						'name' => 'No qualifications',
						'value' => 99
					],
				],
			],
			'DateLevelApp' => [
				'name' => 'Date Level applies from',
				'description' => 'The date the level of prior attainment applies from. ',
				'required' => false,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			]
		]
	],
	'PriorAttainLegacy' => [
		'name' => 'Prior attainment (untill 2021 incl.)',
		'required' => false,
		'pattern' => '^\d{1,2}$',
		'pattern_error' => 'Must be integer, one or two characters',
		'type' => 'number',

		'group' => 'learner',
		'field' => 'select',
		'choices' => [
			[
				'name' => 'Entry level',
				'value' => 9
			],
			[
				'name' => 'Other qualifications below level 1',
				'value' => 7
			],
			[
				'name' => 'Level 1',
				'value' => 1
			],
			[
				'name' => 'Full level 2',
				'value' => 2
			],
			[
				'name' => 'Full level 3',
				'value' => 3
			],
			[
				'name' => 'Level 4 (valid to 31/07/2013)',
				'value' => 4
			],
			[
				'name' => 'Level 5 and above (valid to 31/07/2013)',
				'value' => 5
			],
			[
				'name' => 'Level 4',
				'value' => 10
			],
			[
				'name' => 'Level 5',
				'value' => 11
			],
			[
				'name' => 'Level 6',
				'value' => 12
			],
			[
				'name' => 'Level 7 and above',
				'value' => 13
			],
			[
				'name' => 'Other qualification, level not known',
				'value' => 97
			],
			[
				'name' => 'Not known',
				'value' => 98
			],
			[
				'name' => 'No qualifications',
				'value' => 99
			]
		],
	],
	'Accom' => [
		'name' => 'Accommodation',
		'required' => false,
		'pattern' => '^\d{1}$',
		'pattern_error' => 'Must be integer, one characters',
		'type' => 'number',

		'group' => 'learner',
		'field' => 'checkbox',
		'choice' => [
			'name' => 'Is the learner living away from home in accommodation owned or managed by the provider? (Y/N) ',
			'value' => 5
		]
	],
	'ALSCost' => [
		'name' => 'Learning Support Cost (£)',
		'required' => false,
		'pattern' => '^\d{1,6}$',
		'pattern_error' => 'Must contain a value in the range 0 to 999999',
		'type' => 'number',
		'field' => 'number',

		'group' => 'learner',
	],
	'PlanLearnHours' => [
		'name' => 'Planned learning hours',
		'required' => false,
		'pattern' => '^\d{1,4}$',
		'pattern_error' => 'Must contain a value in the range 0 to 9999',
		'type' => 'number',
		'field' => 'number',

		'group' => 'learner',
	],
	'PlanEEPHours' => [
		'name' => 'Planned employability, enrichment and pastoral hours',
		'required' => false,
		'pattern' => '^[\d]{1,4}$',
		'pattern_error' => 'Must contain a value in the range 0 to 9999',
		'type' => 'number',
		'field' => 'number',

		'group' => 'learner',
	],
	'MathGrade' => [
		'name' => 'GCSE maths qualification grade',
		'required' => false,
		'pattern' => '^[A-Z0-9]{1,4}$',
		'pattern_error' => 'A valid GCSE grade or the value of \'NONE\'',
		'group' => 'learner',
	],
	'EngGrade' => [
		'name' => 'GCSE English qualification grade',
		'required' => false,
		'pattern' => '^[A-Z0-9]{1,4}$',
		'pattern_error' => 'A valid GCSE grade or the value of \'NONE\'',

		'group' => 'learner',
	],
	'PostcodePrior' => [
		'name' => 'Postcode prior to enrolment',
		'required' => true,
		'pattern' => '^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Z][0-9]{1,2})|(([A-Z][A-HJ-Y][0-9]{1,2})|(([A-Z][0-9][A-Z])|([A-Z][A-HJ-Y][0-9]?[A-Z]))))\s?[0-9][A-Z]{2})$',
		'pattern_error' => 'A valid postcode which must be in upper case',

		'group' => 'learner',
	],
	'Postcode' => [
		'name' => 'Postcode',
		'required' => true,
		'pattern' => '^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Z][0-9]{1,2})|(([A-Z][A-HJ-Y][0-9]{1,2})|(([A-Z][0-9][A-Z])|([A-Z][A-HJ-Y][0-9]?[A-Z]))))\s?[0-9][A-Z]{2})$',
		'pattern_error' => 'A valid postcode which must be in upper case',

		'group' => 'learner',
	],
	'AddLine1' => [
		'name' => 'Address line 1',
		'required' => false,
		'pattern' => '^[A-Za-z0-9 ~!@&\'\\()\*\+\,\-\.\/\:\;]{1,50}$',
		'pattern_error' => 'Valid entries are alphabetic characters, numeric digits and the following characters only: [~!@&\'\()*+,-./:;] only',

		'group' => 'learner',
	],
	'AddLine2' => [
		'name' => 'Address line 2',
		'required' => false,
		'pattern' => '^[A-Za-z0-9 ~!@&\'\\()\*\+\,\-\.\/\:\;]{1,50}$',
		'pattern_error' => 'Valid entries are alphabetic characters, numeric digits and the following characters only: [~!@&\'\()*+,-./:;] only',

		'group' => 'learner',
	],
	'AddLine3' => [
		'name' => 'Address line 3',
		'required' => false,
		'pattern' => '^[A-Za-z0-9 ~!@&\'\\()\*\+\,\-\.\/\:\;]{1,50}$',
		'pattern_error' => 'Valid entries are alphabetic characters, numeric digits and the following characters only: [~!@&\'\()*+,-./:;] only',

		'group' => 'learner',
	],
	'AddLine4' => [
		'name' => 'Address line 4',
		'required' => false,
		'pattern' => '^[A-Za-z0-9 ~!@&\'\\()\*\+\,\-\.\/\:\;]{1,50}$',
		'pattern_error' => 'Valid entries are alphabetic characters, numeric digits and the following characters only: [~!@&\'\()*+,-./:;] only',

		'group' => 'learner',
	],
	'phone' => [ // TelNo
		'name' => 'Telephone number',
		'required' => false,
		'pattern' => '^[0-9]{1,18}$',
		'pattern_error' => 'String of numeric digits, must not include brackets, must not contain any spaces at all including between the STD code and main number.',

		'group' => 'learner',
	],
	'email' => [
		'name' => 'Email address',
		'required' => false,
		'pattern' => '^.+@.+$',
		'pattern_error' => 'Must contain at least an @ sign and a dot (.). The @ must not be the first character of the email address and the last dot must be at least one character after the @ sign',

		'group' => 'learner',
	],

	// ther eis additional logic that needs to be i mplemented where RUI(1-5) or PMC(1-3) is allowed only.
	'ContactPreference' => [
		'name' => 'Learner Contact Preference',
		'type' => 'json',
		'add' => true,

		'group' => 'learner',
		'limit' => 5,
		'required' => false,
		'children' => [
			'ContPrefType' => [
				'name' => 'Contact preference type',
				'required' => true,
				'pattern' => '^(RUI|PMC)$',
				'pattern_error' => 'RUI or PMC is valid value only.',
				'type' => 'text',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Restricted use indicator',
						'value' => 'RUI'
					],
					[
						'name' => 'Preferred method of contact',
						'value' => 'PMC'
					]
				],
			],
			'ContPrefCode' => [
				'name' => 'Contact preference code',
				'required' => true,
				'pattern' => '^\d{1}$',
				'pattern_error' => 'Integer 1 to 7 is valid value only.',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Learner does not wish to be contacted about courses or learning opportunities (valid to 25/05/2018)',
						//'short_name' => 'About courses or learning opportunities',
						'show_checkbox' => false, // this is unique feature for ILR page, where this choice will be shown as checkbox
						'key' => 'ContPrefCode',
						'value' => 1,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'RUI',
					],
					[
						'name' => 'Learner does not wish to be contacted for survey and research (valid to 25/05/2018)',
						//'short_name' => 'For surveys and research',
						'show_checkbox' => false,
						'key' => 'ContPrefCode',
						'value' => 2,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'RUI',
					],
					[
						'name' => 'Learner is not to be contacted, for example where a learner has died, or suffered severe illness during the programme (valid to 31/07/2013)',
						//'short_name' => '',
						'show_checkbox' => false,
						'key' => 'ContPrefCode',
						'value' => 3,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'RUI',
					],
					[
						'name' => 'Learner is not to be contacted, for example where a learner has suffered severe illness during the programme or other circumstance',
						//'short_name' => '',
						'show_checkbox' => false,
						'key' => 'ContPrefCode',
						'value' => 4,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'RUI',
					],
					[
						'name' => 'Learner is not to be contacted - learner has died',
						//'short_name' => '',
						'show_checkbox' => false,
						'key' => 'ContPrefCode',
						'value' => 5,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'RUI',
					],
					[
						'name' => 'Learner agrees to be contacted about courses or learning opportunities',
						//'short_name' => '',
						'show_checkbox' => false,
						'key' => 'ContPrefCode',
						'value' => 6,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'RUI',
					],
					[
						'name' => 'Learner agrees to be contacted for survey and research',
						//'short_name' => '',
						'show_checkbox' => false,
						'key' => 'ContPrefCode',
						'value' => 7,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'RUI',
					],
					[
						'name' => 'Learner does not wish to be contacted by post (valid to 25/05/2018)',
						//'short_name' => 'By post',
						'show_checkbox' => true,
						'key' => 'ContPrefCode',
						'value' => 1,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'PMC',
					],
					[
						'name' => 'Learner does not wish to be contacted by telephone (valid to 25/05/2018)',
						//'short_name' => 'By phone',
						'show_checkbox' => true,
						'key' => 'ContPrefCode',
						'value' => 2,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'PMC',
					],
					[
						'name' => 'Learner does not wish to be contacted by e-mail (valid to 25/05/2018)',
						//'short_name' => 'By e-mail',
						'show_checkbox' => true,
						'key' => 'ContPrefCode',
						'value' => 3,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'PMC',
					],
					[
						'name' => 'Learner agrees to be contacted by post',
						'show_checkbox' => true,
						'key' => 'ContPrefCode',
						'value' => 4,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'PMC',
					],
					[
						'name' => 'Learner agrees to be contacted by telephone',
						'show_checkbox' => true,
						'key' => 'ContPrefCode',
						'value' => 5,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'PMC',
					],
					[
						'name' => 'Learner agrees to be contacted by e-mail',
						'show_checkbox' => true,
						'key' => 'ContPrefCode',
						'value' => 6,
						'condition_key' => 'ContPrefType',
						'condition_value' => 'PMC',
					]
				],
			]
		]
	],

	'LLDDandHealthProblem' => [
		'name' => 'LLDD and Health Problem',
		'type' => 'json',
		'add' => true,

		'group' => 'learner',
		'limit' => 22,
		'required' => false,
		'children' => [
			'LLDDCat' => [
				'name' => 'LLDD and health problem category',
				'required' => false,
				'pattern' => '^\d{1,2}$',
				'pattern_error' => 'Integer 1 or 2 integer characters.',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Emotional/behavioural difficulties (valid to 31/07/2015)',
						'value' => 1,
						'choice' => 1,
					],
					[
						'name' => 'Multiple disabilities (valid to 31/07/2015)',
						'value' => 2,
						'choice' => 1,
					],
					[
						'name' => 'Multiple learning difficulties (valid to 31/07/2015)',
						'value' => 3,
						'choice' => 2,
					],
					[
						'name' => 'Vision impairment',
						'value' => 4,
						'choice' => 1,
					],
					[
						'name' => 'Hearing impairment',
						'value' => 5,
						'choice' => 1,
					],
					[
						'name' => 'Disability affecting mobility',
						'value' => 6,
						'choice' => 1,
					],
					[
						'name' => 'Profound complex disabilities',
						'value' => 7,
						'choice' => 1,
					],
					[
						'name' => 'Social and emotional difficulties',
						'value' => 8,
						'choice' => 1,
					],
					[
						'name' => 'Mental health difficulty',
						'value' => 9,
						'choice' => 1,
					],
					[
						'name' => 'Moderate learning difficulty ',
						'value' => 10,
						'choice' => 2,
					],
					[
						'name' => 'Severe learning difficulty ',
						'value' => 11,
						'choice' => 2,
					],
					[
						'name' => 'Dyslexia',
						'value' => 12,
						'choice' => 2,
					],
					[
						'name' => 'Dyscalculia ',
						'value' => 13,
						'choice' => 2,
					],
					[
						'name' => 'Autism spectrum disorder',
						'value' => 14,
						'choice' => 2,
					],
					[
						'name' => 'Asperger\'s syndrome (valid to 31/07/2025)',
						'value' => 15,
						'choice' => 1,
					],
					[
						'name' => 'Temporary disability after illness (for example post-viral) or accident',
						'value' => 16,
						'choice' => 1,
					],
					[
						'name' => 'Speech, Language and Communication Needs',
						'value' => 17,
					],
					[
						'name' => 'Down Syndrome',
						'value' => 18,
					],
					[
						'name' => 'Other physical disability',
						'value' => 93,
						'choice' => 1,
					],
					[
						'name' => 'Other specific learning difficulty (e.g. Dyspraxia)',
						'value' => 94,
						'choice' => 2,
					],
					[
						'name' => 'Other medical condition (for example epilepsy, asthma, diabetes)',
						'value' => 95,
						'choice' => 1,
					],
					[
						'name' => 'Other learning difficulty',
						'value' => 96,
						'choice' => 2,
					],
					[
						'name' => 'Other disability',
						'value' => 97,
						'choice' => 1,
					],
					[
						'name' => 'Prefer not to say',
						'value' => 98,
						'choice' => 2,
					],
					[
						'name' => 'Not provided',
						'value' => 99,
						'choice' => 2,
					],

				],
			],
			'PrimaryLLDD' => [
				'name' => 'Primary LLDD and health problem',
				'required' => false,
				'pattern' => '^\d{1}$',
				'pattern_error' => 'Integer, 1 characters.',
				'type' => 'number',
				'field' => 'checkbox',
				'choice' => [
					'name' => 'The learner\'s primary learning difficulty, disability or health problem',
					'value' => 1
				]
			]
		]
	],

	'LearnerFAM' => [
		'name' => 'Learner Funding and Monitoring',
		'type' => 'json',
		'add' => true,

		'group' => 'learner',
		'limit' => 17,
		'required' => false,
		'children' => [
			'LearnFAMType' => [
				'name' => 'Learner funding and monitoring type',
				'required' => true,
				'pattern' => '^[A-Z]{3}$',
				'pattern_error' => 'String, length of 3.',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'High needs students',
						'value' => 'HNS'
					],
					[
						'name' => 'Education Health Care plan',
						'value' => 'EHC',
						'LLDD' => true,
						'code' => 1,
						'codeKey' => 'LearnFAMCode',
					],
					[
						'name' => 'Disabled students allowance',
						'value' => 'DLA',
						'LLDD' => true,
						'code' => 1,
						'codeKey' => 'LearnFAMCode',
					],
					[
						'name' => 'Learner support reason',
						'value' => 'LSR'
					],
					[
						'name' => 'Special educational needs',
						'value' => 'SEN'
					],
					[
						'name' => 'National learner monitoring',
						'value' => 'NLM'
					],
					[
						'name' => 'Eligibility for 16-19 (excluding Apprenticeships) disadvantage funding',
						'value' => 'EDF'
					],
					[
						'name' => 'GCSE Maths condition of funding',
						'value' => 'MCF'
					],
					[
						'name' => 'GCSE English condition of funding',
						'value' => 'ECF'
					],
					[
						'name' => 'Free Meals Eligibility',
						'value' => 'FME'
					],
					[
						'name' => 'Pupil premium funding eligibility',
						'value' => 'PPE'
					],
					[
						'name' => 'English Minimum Hours',
						'value' => 'EMH'
					],
					[
						'name' => 'Maths Minimum Hours',
						'value' => 'MMH'
					],
				],
			],
			'LearnFAMCode' => [
				'name' => 'Learner funding and monitoring code',
				'required' => true,
				'pattern' => '^\d{1,3}$',
				'pattern_error' => 'Integer, up to 3 characters.',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Learner is a high needs student in receipt of element 3 \'top-up\' funding from the local authority',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'HNS',
					],
					[
						'name' => 'Learner has an Education Health Care plan',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'EHC',
					],
					[
						'name' => 'Learner is in receipt of disabled students allowance',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'DLA',
					],
					[
						'name' => 'Care to Learn',
						'value' => 36,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => '16-19 Bursary Fund - learner is a member of a vulnerable group',
						'value' => 55,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => '16-19 Bursary Fund - learner has been awarded a discretionary bursary',
						'value' => 56,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => 'Residential support',
						'value' => 57,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => '19+ Hardship (Adult Skills or Advanced Learner Loan funded learners only)',
						'value' => 58,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => '20+ Childcare (Adult Skills or Advanced Learner Loan funded learners only)',
						'value' => 59,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => '19+ Residential Access Fund (Adult Skills or Advanced Learner Loan funded learners only)',
						'value' => 60,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => 'ESF funded learner receiving childcare support',
						'value' => 61,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => 'Unassigned',
						'value' => 62,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'LSR',
					],
					[
						'name' => 'Special educational needs',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'SEN',
					],
					[
						'name' => 'Learner migrated as part of provider merger',
						'value' => 17,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'NLM',
					],
					[
						'name' => 'Learner moved as a result of Minimum Contract Level',
						'value' => 18,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'NLM',
					],
					[
						'name' => 'Learner in receipt of 16-19 tuition fund',
						'value' => 21,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'NLM',
					],
					[
						'name' => 'Learner repeating up to one full final year of 16-19 funded provision',
						'value' => 22,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'NLM',
					],
					[
						'name' => 'Learner has not achieved a maths GCSE (at grade A*-C/9-4) by the end of year 11',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'EDF',
					],
					[
						'name' => 'Learner has not achieved an English GCSE (at grade A*-C/9-4) by the end of year 11',
						'value' => 2,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'EDF',
					],
					[
						'name' => 'Learner is exempt from GCSE maths condition of funding due to a learning difficulty',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'MCF',
					],
					[
						'name' => 'Learner is exempt from GCSE maths condition of funding as they hold an equivalent overseas qualification',
						'value' => 2,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'MCF',
					],
					[
						'name' => 'Learner has met the GCSE maths condition of funding as they hold an approved equivalent UK qualification',
						'value' => 3,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'MCF',
					],
					[
						'name' => 'Learner has met the GCSE maths condition of funding by undertaking/completing a valid maths GCSE or equivalent qualification at another institution through collaboration with the home institution',
						'value' => 4,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'MCF',
					],
					[
						'name' => 'Learner holds a pass grade for functional skills level 2 in mathematics',
						'value' => 5,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'MCF',
					],
					[
						'name' => 'Learner is exempt from GCSE English condition of funding due to a learning difficulty',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'ECF',
					],
					[
						'name' => 'Learner is exempt from GCSE English condition of funding as they hold an equivalent overseas qualification',
						'value' => 2,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'ECF',
					],
					[
						'name' => 'Learner has met the GCSE English condition of funding as they hold an approved equivalent UK qualification',
						'value' => 3,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'ECF',
					],
					[
						'name' => 'Learner has met the GCSE English condition of funding by undertaking/completing a valid English GCSE or equivalent qualification at another institution through collaboration with the home institution',
						'value' => 4,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'ECF',
					],
					[
						'name' => 'Learner holds a pass grade for functional skills level 2 in English',
						'value' => 5,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'ECF',
					],
					[
						'name' => '14-15 year old learner is eligible for free meals ',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'FME',
					],
					[
						'name' => '16-19 year old learner is eligible for and in receipt of free meals',
						'value' => 2,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'FME',
					],
					[
						'name' => 'Learner is eligible for Service Child premium',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'PPE',
					],
					[
						'name' => 'Learner is eligible for Adopted from Care premium',
						'value' => 2,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'PPE',
					],
					[
						'name' => 'Unassigned',
						'value' => 3,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'PPE',
					],
					[
						'name' => 'Student has at least the minimum classroom teaching hours planned for an eligible English qualification',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'EMH',
					],
					[
						'name' => 'Student does not have at least the minimum classroom teaching hours planned for an eligible English qualification',
						'value' => 2,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'EMH',
					],
					[
						'name' => 'Student has at least the minimum classroom teaching hours planned for an eligible maths qualification',
						'value' => 1,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'MMH',
					],
					[
						'name' => 'Student does not have at least the minimum classroom teaching hours planned for an eligible maths qualification',
						'value' => 2,
						'condition_key' => 'LearnFAMType',
						'condition_value' => 'MMH',
					],
				]
			],
		]
	],

	'ProviderSpecLearnerMonitoring' => [
		'name' => 'Learner Provider Specified Monitoring',
		'type' => 'json',
		'add' => true,

		'group' => 'learner',
		'limit' => 2,
		'required' => false,
		'children' => [
			'ProvSpecLearnMonOccur' => [
				'name' => 'Provider specified learner monitoring occurrence',
				'notes' => '
					This field is used to identify data stored in each of the occurrences of this field.
					If the Provider specified learner monitoring fields are completed then an occurrence code must be returned.
				',
				'required' => false,
				'pattern' => '^(A|B)$',
				'pattern_error' => 'String, A or B.',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'A occurrence',
						'value' => 'A'
					],
					[
						'name' => 'B occurrence',
						'value' => 'B'
					],
				]
			],
			'ProvSpecLearnMon' => [
				'name' => 'Provider specified learner monitoring',
				'required' => false,
				'pattern' => '^[^\*\?\%\_]{1,20}$',
				'pattern_error' => 'String, up to 20 characters, "*, ?, %, _" removed.',
			],
		]
	],

	'LearnerEmploymentStatus' => [
		'name' => 'Learner Employment Status',
		'type' => 'json',
		'add' => true,

		'group' => 'employment-statuses',
		//'limit' => 5,
		'required' => false,
		'children' => [
			'EmpStat' => [
				'name' => 'Employment status',
				'required' => true,
				'pattern' => '^\d{2}$',
				'pattern_error' => 'Integer, 2 characters.',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'In paid employment',
						'value' => 10
					],
					[
						'name' => 'Not in paid employment, looking for work and available to start work',
						'value' => 11
					],
					[
						'name' => 'Not in paid employment, not looking for work and/or not available to start work',
						'value' => 12
					],
					[
						'name' => 'Not known / not provided',
						'value' => 98
					],
				]
			],
			'DateEmpStatApp' => [
				'name' => 'Date employment status applies',
				'required' => true,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'EmpId' => [
				'name' => 'Employer identifier',
				'required' => false,
				'pattern' => '^\d{9}$',
				'pattern_error' => 'Integer, 9 characters.',
				'type' => 'number',
				'field' => 'number',
			],
			'AgreeId' => [
				'name' => 'Agreement identifier',
				'definition' => 'The identifier associates a legal entity (organisation) to anemployer’s apprenticeship account.',
				'reason_required' => 'To enable the association of an apprentice to a legal entity within an employer’s apprenticeship account.',
				'required' => false,
				'pattern' => '^[A-Za-z0-9]{1,6}$',
				'pattern_error' => 'A valid AgreementID within the employer’s Apprenticeship Service account. This is a six-digit identifier using alphanumeric characters.',
				'type' => 'text',
			],
			'EmploymentStatusMonitoring' => [
				'name' => 'Learner Employment Status Monitoring',
				'type' => 'json',
				'add' => true,
				//'fieldGroup' => 'ilr',
				'limit' => 10,
				'required' => false,
				'children' => [
					'ESMType' => [
						'name' => 'Employment status monitoring type',
						'required' => true,
						'pattern' => '^[A-Z]{3}$',
						'pattern_error' => 'String, length of 3.',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Self employment indicator',
								'value' => 'SEI',
								'code_value' => 1,
							],
							[
								'name' => 'Employment intensity indicator',
								'value' => 'EII'
							],
							[
								'name' => 'Length of unemployment',
								'value' => 'LOU'
							],
							[
								'name' => 'Length of employment',
								'value' => 'LOE'
							],
							[
								'name' => 'Benefit status indicator',
								'value' => 'BSI'
							],
							[
								'name' => 'Was the learner in full time education or training prior to enrolment?',
								'value' => 'PEI',
								'code_value' => 1,
							],
							[
								'name' => 'Small Employer',
								'value' => 'SEM',
								'code_value' => 1,
							],
							[
								'name' => 'Other Employment Type',
								'value' => 'OET'
							],
						]
					],
					'ESMCode' => [
						'name' => 'Employment status monitoring code',
						'required' => true,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, 1 character',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Learner is self employed',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'SEI',
							],
							[
								'name' => 'Learner is employed for 16 hours or more per week (valid to 31/07/2013)',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner is employed for less than 16 hours per week (valid to 31/07/2018)',
								'value' => 2,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner is employed for 16 - 19 hours per week (valid to 31/07/2018)',
								'value' => 3,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner is employed for 20 hours or more per week (valid to 31/07/2018)',
								'value' => 4,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner is employed for 0 to 10 hours per week',
								'value' => 5,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner is employed for 11 to 20 hours per week',
								'value' => 6,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner is employed for 21 to 30 hours per week',
								'value' => 7,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner is employed for 31+ hours per week',
								'value' => 8,
								'condition_key' => 'ESMType',
								'condition_value' => 'EII',
							],
							[
								'name' => 'Learner has been unemployed for less than 6 months',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOU',
							],
							[
								'name' => 'Learner has been unemployed for 6-11 months',
								'value' => 2,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOU',
							],
							[
								'name' => 'Learner has been unemployed for 12-23 months',
								'value' => 3,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOU',
							],
							[
								'name' => 'Learner has been unemployed for 24-35 months',
								'value' => 4,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOU',
							],
							[
								'name' => 'Learner has been unemployed for 36 months or more',
								'value' => 5,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOU',
							],
							[
								'name' => 'Learner has been employed for up to 3 months',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOE',
							],
							[
								'name' => 'Learner has been employed for 4 months - 6 months',
								'value' => 2,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOE',
							],
							[
								'name' => 'Learner has been employed for 7 months - 12 months',
								'value' => 3,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOE',
							],
							[
								'name' => 'Learner has been employed for more than 12 months',
								'value' => 4,
								'condition_key' => 'ESMType',
								'condition_value' => 'LOE',
							],
							[
								'name' => 'Learner is in receipt of Job Seekers Allowance (JSA)',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'BSI',
							],
							[
								'name' => 'Learner is in receipt of Employment and Support Allowance - Work Related Activity Group (ESA WRAG)',
								'value' => 2,
								'condition_key' => 'ESMType',
								'condition_value' => 'BSI',
							],
							[
								'name' => 'Learner is in receipt of another state benefit other than JSA, Universal Credit or ESA (WRAG)',
								'value' => 3,
								'condition_key' => 'ESMType',
								'condition_value' => 'BSI',
							],
							[
								'name' => 'Learner is in receipt of Universal Credit',
								'value' => 4,
								'condition_key' => 'ESMType',
								'condition_value' => 'BSI',
							],
							[
								'name' => 'Unassigned',
								'value' => 5,
								'condition_key' => 'ESMType',
								'condition_value' => 'BSI',
							],
							[
								'name' => 'Learner was in full - time education or training prior to enrolment',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'PEI',
							],
							[
								'name' => 'Small employer',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'SEM',
							],
							[
								'name' => 'Learner has been made redundant',
								'value' => 1,
								'condition_key' => 'ESMType',
								'condition_value' => 'OET',
							],
							[
								'name' => 'Small or Medium Employer',
								'value' => 2,
								'condition_key' => 'ESMType',
								'condition_value' => 'OET',
							],
							[
								'name' => 'Employer has changed',
								'value' => 3,
								'condition_key' => 'ESMType',
								'condition_value' => 'OET',
							],
						]
					],
				]
			]
		]
	],

	'LearnerHE' => [
		'name' => 'Learner HE',
		'type' => 'json',
		'add' => true,

		'group' => 'learner',
		'limit' => 1,
		'required' => false,
		'children' => [
			'UCASPERID' => [
				'name' => 'UCAS personal identifier',
				'required' => false,
				'pattern' => '^[0-9]{10}$',
				'pattern_error' => 'Must contain a value in the range 0000000001 to 9999999999',
			],
			'TTACCOM' => [
				'name' => 'Term Time Accommodation',
				'required' => false,
				'pattern' => '^\d{1}$',
				'pattern_error' => 'Integer, 1 character',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Institution-maintained property',
						'value' => 1
					],
					[
						'name' => 'Parental/guardian home',
						'value' => 2
					],
					[
						'name' => 'Own home (Valid To date of 31/07/2008)',
						'value' => 3
					],
					[
						'name' => 'Other',
						'value' => 4
					],
					[
						'name' => 'Not known',
						'value' => 5
					],
					[
						'name' => 'Not in attendance at the institution',
						'value' => 6
					],
					[
						'name' => 'Own residence',
						'value' => 7
					],
					[
						'name' => 'Other rented accommodation',
						'value' => 8
					],
					[
						'name' => 'Private sector halls',
						'value' => 9
					],
				]
			],
			'LearnerHEFinancialSupport' => [
				'name' => 'Learner HE Financial Support',
				'type' => 'json',
				'add' => true,
				//'fieldGroup' => 'ilr',
				'limit' => 4,
				'required' => false,
				'children' => [
					'FINTYPE' => [
						'name' => 'Financial support type',
						'required' => true,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, 1 character',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Cash',
								'value' => 1
							],
							[
								'name' => 'Near Cash',
								'value' => 2
							],
							[
								'name' => 'Accommodation discount',
								'value' => 3
							],
							[
								'name' => 'Other',
								'value' => 4
							],
						]
					],
					'FINAMOUNT' => [
						'name' => 'Financial support amount',
						'required' => true,
						'pattern' => '^\d{0,6}$',
						'pattern_error' => 'Integer, 1 character',
						'type' => 'number',
						'field' => 'number',
					],
				]
			],
		]
	],

	// Learning Delivery!
	'LearningDelivery' => [
		'name' => 'Learning Delivery',
		'type' => 'json',
		'add' => true,

		'group' => 'learning-deliveries',
		//'limit' => 5,
		'required' => true,
		'children' => [
			'LearnAimRefTitle' => [
				'name' => 'Learning aim reference title',
				'required' => false,
			],
			'LearnAimRef' => [
				'name' => 'Learning aim reference',
				'required' => false,
				'pattern' => '^.{0,8}$',
				'pattern_error' => 'Text, up to 8 characters',
			],
			'AimType' => [
				'name' => 'Aim type',
				'required' => false,
				'pattern' => '^\d{1}$',
				'pattern_error' => 'Integer, 1 character',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Programme aim',
						'value' => 1
					],
					[
						'name' => 'Component learning aim within a programme',
						'value' => 3
					],
					[
						'name' => 'Learning aim that is not part of a programme',
						'value' => 4
					],
					[
						'name' => 'Core aim – EFA funded learning aims only',
						'value' => 5
					],
				]
			],
			'AimSeqNumber' => [
				'name' => 'Aim sequence number',
				'required' => true,
				'pattern' => '^([1-9]|[0-8][0-9]|9[0-8])$',
				'pattern_error' => 'Must contain a value in the range 1 to 98',
				'type' => 'number',
				'field' => 'number',
			],
			'LearnStartDate' => [
				'name' => 'Learning start date',
				'required' => true,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'OrigLearnStartDate' => [
				'name' => 'Original learning start date',
				'required' => false,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'LearnPlanEndDate' => [
				'name' => 'Learning planned end date',
				'required' => true,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'FundModel' => [
				'name' => 'Funding model',
				'description' => 'Identifies the funding model to be applied when calculating funding for this learning aim.',
				'required' => false,
				'pattern' => '^\d{2}$',
				'pattern_error' => 'Integer, 2 character',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Community Learning (valid to 31/07/2024)',
						'value' => 10
					],
					[
						'name' => 'Tailored Learning (non-formula-funded provision)',
						'value' => 11
					],
					[
						'name' => '16-19 (excluding Apprenticeships)',
						'value' => 25
					],
					[
						'name' => 'Adult skills (valid to 31/07/2024)',
						'value' => 35
					],
					[
						'name' => 'Apprenticeships (from 1 May 2017)',
						'value' => 36
					],
					[
						'name' => 'Skills Bootcamps',
						'value' => 37
					],
					[
						'name' => 'Adult Skills Fund (formula-funded provision)',
						'value' => 38
					],
					[
						'name' => 'ESF funded (co-financed by the Education and Skills Funding Agency)',
						'value' => 70
					],
					[
						'name' => 'Other Adult',
						'value' => 81
					],
					[
						'name' => 'Other 16-19',
						'value' => 82
					],
					[
						'name' => 'Non-funded',
						'value' => 99
					]
				],
			],
			'PHours' => [
				'name' => 'Planned hours',
				'required' => false,
				'collection_requirements' => 'Collected for Apprenticeships (Funding Model 36)',
				'pattern' => '^(?:[1-9][0-9]{3}|[1-9][0-9]{2}|[1-9][0-9]|[0-9])$',
				'pattern_error' => 'Must contain a value in the range of 0 to 9999',
				'type' => 'number',
				'field' => 'number',
			],
			'OTJActHours' => [
				'name' => 'Actual Hours for Off the Job Training',
				'required' => false,
				'collection_requirements' => 'Aim Type 1 Apprenticeships (FundModel 36)',
				'pattern' => '^(?:[1-9][0-9]{3}|[1-9][0-9]{2}|[1-9][0-9]|[0-9])$',
				'pattern_error' => 'Must contain a value in the range of 0 to 9999',
				'type' => 'number',
				'field' => 'number',
			],
			'ProgType' => [
				'name' => 'Programme type',
				'required' => false,
				'pattern' => '^\d{1,2}$',
				'pattern_error' => 'Integer, maximum 2 character',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Advanced Level Apprenticeship (removed 2025)',
						'value' => 2
					],
					[
						'name' => 'Intermediate Level Apprenticeship (removed 2025)',
						'value' => 3
					],
					[
						'name' => 'Higher Apprenticeship - level 4 (removed 2025)',
						'value' => 20
					],
					[
						'name' => 'Higher Apprenticeship - level 5 (removed 2025)',
						'value' => 21
					],
					[
						'name' => 'Higher Apprenticeship - level 6 (removed 2025)',
						'value' => 22
					],
					[
						'name' => 'Higher Apprenticeship - level 7+ (removed 2025)',
						'value' => 23
					],
					[
						'name' => 'Traineeship',
						'value' => 24
					],
					[
						'name' => 'Apprenticeship standard',
						'value' => 25
					],
					[
						'name' => 'T Level Foundation Year',
						'value' => 30
					],
					[
						'name' => 'T Level programme',
						'value' => 31
					],
					[
						'name' => 'Skills Bootcamps',
						'value' => 32
					],
					[
						'name' => 'Combined Authorities',
						'value' => 33
					],
				],
			],
			'FworkCode' => [
				'name' => 'Framework code',
				'required' => false,
				'pattern' => '^\d{1,3}$',
				'pattern_error' => 'Integer, maximum 3 character',
				'type' => 'number',
				'field' => 'number',
			],
			'PwayCode' => [
				'name' => 'Pathway',
				'description' => 'The pathway of the framework being undertaken.',
				'required' => false,
				'pattern' => '^\d{1,4}$',
				'pattern_error' => 'Integer, maximum 4 character',
				'type' => 'number',
				'field' => 'number',
			],
			'StdCode' => [
				'name' => 'Apprenticeship standard code',
				'required' => false,
				'pattern' => '^\d{1,5}$',
				'pattern_error' => 'Integer, maximum 5 character',
				'type' => 'number',
				'field' => 'number',
			],
			'PartnerUKPRN' => [
				'name' => 'Subcontracted or partnership UKPRN',
				'required' => false,
				'pattern' => '^[1-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$',
				'pattern_error' => 'Must contain a value in the range 10000000 to 99999999',
				'type' => 'number',
				'field' => 'number',
			],
			'DelLocPostCode' => [
				'name' => 'Delivery location postcode',
				'required' => false,
				'pattern' => '^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Z][0-9]{1,2})|(([A-Z][A-HJ-Y][0-9]{1,2})|(([A-Z][0-9][A-Z])|([A-Z][A-HJ-Y][0-9]?[A-Z]))))\s?[0-9][A-Z]{2})$',
				'pattern_error' => 'A valid postcode which must be in upper case',
			],
			'LSDPostcode' => [
				'name' => 'Learning Start Date Postcode',
				'definition' => 'The residencypostcode of the learner for the purposes of funding',
				'required' => false,
				'pattern' => '^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Z][0-9]{1,2})|(([A-Z][A-HJ-Y][0-9]{1,2})|(([A-Z][0-9][A-Z])|([A-Z][A-HJ-Y][0-9]?[A-Z]))))\s?[0-9][A-Z]{2})$',
				'pattern_error' => 'A valid postcode thatmust be in upper case',
			],
			'AddHours' => [
				'name' => 'Additional delivery hours',
				'required' => false,
				'pattern' => '^\d{0,4}$',
				'pattern_error' => 'Must contain a value in the range 0 to 9999',
				'type' => 'number',
				'field' => 'number',
			],
			'PriorLearnFundAdj' => [
				'name' => 'Funding adjustment for prior learning',
				'required' => false,
				'pattern' => '^\d{0,2}$',
				'pattern_error' => 'Must contain a value in the range 0 to 99',
				'type' => 'number',
				'field' => 'number',
			],
			'OtherFundAdj' => [
				'name' => 'Other funding adjustment',
				'required' => false,
				'pattern' => '^\d{0,3}$',
				'pattern_error' => 'Must contain a value in the range 0 to 999',
				'type' => 'number',
				'field' => 'number',
			],
			'ConRefNumber' => [
				'name' => 'Contract reference number',
				'required' => false,
				'pattern' => '^ESF-\d{0,20}$',
				'pattern_error' => 'For ESF delivery this number will be in the format of \'ESF-xxxxxxx\' where \'x\' is numerical.',
			],
			'EPAOrgID' => [
				'name' => 'End point assessment organisation',
				'required' => false,
				'pattern' => '^EPA\d{4}$',
				'pattern_error' => 'A valid end point assessment organisation IDfrom the register of EPA organisations in the format EPAXXXX where X is an integer from 0-9',
			],
			'EmpOutcome' => [
				'name' => 'Employment outcome',
				'required' => false,
				'pattern' => '^\d{1}$',
				'pattern_error' => 'Integer, 1 character',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Employment outcome (with training) gained on eligible funded programme',
						'value' => 1
					],
					[
						'name' => 'Employment outcome (without training) gained on eligible funded programme',
						'value' => 2
					],
				]
			],
			'CompStatus' => [
				'name' => 'Completion status',
				'required' => false,
				'pattern' => '^\d{1}$',
				'pattern_error' => 'Integer, 1 character',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'The learner is continuing or intending to continue the learning activities leading to the learning aim',
						'value' => 1
					],
					[
						'name' => 'The learner has completed the learning activities leading to the learning aim',
						'value' => 2
					],
					[
						'name' => 'The learner has withdrawn from the learning activities leading to the learning aim',
						'value' => 3
					],
					[
						'name' => 'Learner has temporarily withdrawn from the aim due to an agreed breakin learning',
						'value' => 6
					],
				]
			],
			'LearnActEndDate' => [
				'name' => 'Learning actual end date',
				'description' => 'Forapprenticeship standards on a programme aim, where the learner has completed all learning activities and begun the End point assessment period prior to 1st August 2019, the Learning Actual End date in the 19/20 ILR will continue to include the end point assessment (as per the 18/19 ILR ILR specification for 2019 to 2020 –Version 2Page 3of 234specification). For apprenticeship standards on a  programme aim, where the learner has not completed all learning activities by 1st August 2019, the Learning Actual End Date will be completed after all learning has taken place and does NOT include the end point assessment period.',
				'required' => false,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'WithdrawReason' => [
				'name' => 'Withdrawal reason',
				'required' => false,
				'pattern' => '^\d{1,2}$',
				'pattern_error' => 'Integer, 1 to 2 characters',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Learner has transferred to another provider',
						'value' => 2
					],
					[
						'name' => 'Learner injury / illness',
						'value' => 3
					],
					[
						'name' => 'Learner has transferred between providers due to intervention by or with the written agreement of the ESFA',
						'value' => 7
					],
					[
						'name' => 'OLASS learner withdrawn due to circumstances outside the providers\' control',
						'value' => 28
					],
					[
						'name' => 'Learner has been made redundant',
						'value' => 29
					],
					[
						'name' => 'Learner has transferred to a new learning aim with the same provider',
						'value' => 40
					],
					[
						'name' => 'Learner has transferred to another provider to undertake learning that meets a specific government strategy',
						'value' => 41
					],
					[
						'name' => 'Academic failure/left in bad standing/not permitted to progress - HE learning aims only',
						'value' => 42
					],
					[
						'name' => 'Financial reasons',
						'value' => 43
					],
					[
						'name' => 'Other personal reasons',
						'value' => 44
					],
					[
						'name' => 'Written off after lapse of time - HE learning aims only',
						'value' => 45
					],
					[
						'name' => 'Exclusion',
						'value' => 46
					],
					[
						'name' => 'Learner has transferred to another provider due to merger',
						'value' => 47
					],
					[
						'name' => 'Industrial placement learner has withdrawn due to circumstances outside the providers’ control',
						'value' => 48
					],
					[
						'name' => 'Other',
						'value' => 97
					],
					[
						'name' => 'Reason not known',
						'value' => 98
					],
				]
			],
			'WithdrawDate' => [
				'name' => 'Withdrawal date',
				'description' => '',
				'required' => false,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'Outcome' => [
				'name' => 'Outcome',
				'required' => false,
				'pattern' => '^\d{1}$',
				'pattern_error' => 'Integer, 1 character',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Achieved',
						'value' => 1
					],
					[
						'name' => 'Partial achievement',
						'value' => 2
					],
					[
						'name' => 'No achievement',
						'value' => 3
					],
					[
						'name' => 'Learning activities are complete but the outcome is not yet known',
						'value' => 8
					],
				]
			],
			'AchDate' => [
				'name' => 'Achievement date',
				'required' => false,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'OutGrade' => [
				'name' => 'Outcome grade',
				'required' => false,
				'pattern' => '^.{0,6}$',
				'pattern_error' => 'Text, up to 6 characters',
			],
			'SWSupAimId' => [
				'name' => 'Software supplier aim identifier',
				'required' => false,
				'pattern' => '^.{32,36}$',
				'pattern_error' => '32 character hexadecimal ID or 36 character hexadecimal ID with hyphens that adheres to the universally unique identifier (UUID) standard',
			],
			'TLOut' => [ // Tailored Learning (FundModel 11)
				'name' => 'Tailored Learning Outcome',
				'description' => 'The outcome of the learning for the learner when they have completed or withdrawn from the planned learning activities.',
				'required' => false,
				'pattern' => '^\d{1,2}$',
				'pattern_error' => 'Integer, 1 to 2 characters',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Increased confidence',
						'value' => 1
					],
					[
						'name' => 'Improved skills for progressing to further learning',
						'value' => 2
					],
					[
						'name' => 'Improved skills for work',
						'value' => 3
					],
					[
						'name' => 'Improved essential skills',
						'value' => 4
					],
					[
						'name' => 'Improved ability to support a child’s learning',
						'value' => 5
					],
					[
						'name' => 'Improved physical health',
						'value' => 6
					],
					[
						'name' => 'Improved mental health and well-being',
						'value' => 7
					],
					[
						'name' => 'Improved skills to participate in community life',
						'value' => 8
					],
					[
						'name' => 'Increased understanding of democratic values',
						'value' => 9
					],
					[
						'name' => 'Improved skills for Independent Living',
						'value' => 10
					],
					[
						'name' => 'No outcome area 1-10 achieved',
						'value' => 11
					],
				]
			],
			'LearningDeliveryFAM' => [
				'name' => 'Learning Delivery Funding and Monitoring',
				'type' => 'json',
				'add' => true,
				//'fieldGroup' => 'ilr',
				//'limit' => 17,
				'required' => false,
				'children' => [
					'LearnDelFAMType' => [
						'name' => 'Learning delivery funding and monitoring type',
						'required' => false,
						'pattern' => '^.{3}$',
						'pattern_error' => 'Text, up to 3 characters',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Source of funding',
								'value' => 'SOF',
								'limit' => 1,
							],
							[
								'name' => 'Full or co-funding indicator',
								'value' => 'FFI',
								'limit' => 1,
							],
							[
								'name' => 'Eligibility for enhanced apprenticeship funding',
								'value' => 'EEF',
								'limit' => 1,
							],
							[
								'name' => 'Restart indicator',
								'value' => 'RES',
								'code_value' => 1,
								'limit' => 1,
							],
							[
								'name' => 'Learning support funding',
								'value' => 'LSF',
								'code_value' => 1,
							],
							[
								'name' => 'Advanced Learner Loans indicator',
								'value' => 'ADL',
								'code_value' => 1,
								'limit' => 1,
							],
							[
								'name' => 'Advanced Learner Loans Bursary funding',
								'value' => 'ALB'
							],
							[
								'name' => 'Community Learning provision type',
								'value' => 'ASL',
								'limit' => 1,
							],
							[
								'name' => 'Family English, Maths and Language',
								'value' => 'FLN',
								'limit' => 1
							],
							[
								'name' => 'Learning delivery monitoring',
								'value' => 'LDM',
								'limit' => 6,
							],
							[
								'name' => 'Devolved Area Monitoring',
								'value' => 'DAM',
								'limit' => 4,
							],
							[
								'name' => 'Apprenticeship contract type',
								'value' => 'ACT'
							],
							[
								'name' => 'Tailored Learning purpose',
								'value' => 'ACL'
							],
							[
								'name' => 'Community Learning which is Family learning',
								'value' => 'AFL'
							],
							[
								'name' => 'National Skills Academy indicator',
								'value' => 'NSA',
								'limit' => 1,
							],
							[
								'name' => 'Work programme participation',
								'value' => 'WPP',
								'limit' => 1,
							],
							[
								'name' => 'Percentage of online delivery',
								'value' => 'POD',
								'limit' => 1,
							],
							[
								'name' => 'HE monitoring',
								'value' => 'HEM',
								'limit' => 3,
							],
							[
								'name' => 'Household situation',
								'value' => 'HHS',
								'limit' => 2,
							],
							[
								'name' => 'Event Indicator',
								'value' => 'EVI',
							],
						]
					],
					'LearnDelFAMCode' => [
						'name' => 'Learning delivery funding and monitoring code',
						'required' => false,
						'pattern' => '^.{1,5}$',
						'pattern_error' => 'Text, up to 5 characters',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Office for Students (OfS)',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Adult funded',
								'value' => 105,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => '16-19 funded',
								'value' => 107,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Local authority (Community Learning funds) (removed 2025)',
								'value' => 108,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Greater Manchester Combined Authority',
								'value' => 110,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Liverpool City Region Combined Authority',
								'value' => 111,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'West Midlands Combined Authority',
								'value' => 112,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'West of England Combined Authority',
								'value' => 113,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Tees Valley Combined Authority',
								'value' => 114,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Cambridgeshire and Peterborough Combined Authority',
								'value' => 115,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Greater London Authority',
								'value' => 116,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'North of Tyne Combined Authority (valid to 31/07/2024)',
								'value' => 117,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'South Yorkshire Mayoral Combined Authority',
								'value' => 118,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'West Yorkshire Combined Authority',
								'value' => 119,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'North East Mayoral Combined Authority',
								'value' => 120,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'East Midlands Combined County Authority',
								'value' => 121,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'York and North Yorkshire Combined Authority',
								'value' => 122,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Cornwall council',
								'value' => 123,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Unassigned',
								'value' => 124,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Unassigned',
								'value' => 125,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Unassigned',
								'value' => 126,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Unassigned',
								'value' => 127,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Unassigned',
								'value' => 128,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Unassigned',
								'value' => 129,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Unassigned',
								'value' => 130,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Other',
								'value' => 998,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'SOF',
							],
							[
								'name' => 'Fully funded learning aim',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'FFI',
							],
							[
								'name' => 'Co funded learning aim',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'FFI',
							],
							[
								'name' => 'Entitlement to 16-18 apprenticeship funding, where the learner is 19 or over',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EEF',
							],
							[
								'name' => 'Entitlement to 19-23 apprenticeship funding, where the learner is 24 or over',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EEF',
							],
							[
								'name' => 'Entitlement to extended funding for apprentices',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EEF',
							],
							[
								'name' => 'Learning aim restarted',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'RES',
							],
							[
								'name' => 'Learning support funding',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 2',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 3',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 4',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 5',
								'value' => 5,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 6',
								'value' => 6,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 7',
								'value' => 7,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 8',
								'value' => 8,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 9',
								'value' => 9,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 10',
								'value' => 10,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Unassigned - 11',
								'value' => 11,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LSF',
							],
							[
								'name' => 'Aim is financed by an Advanced Learner Loan',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ADL',
							],
							[
								'name' => 'Advanced Learner Loan Bursary funding - rate 1',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ALB',
							],
							[
								'name' => 'Advanced Learner Loan Bursary funding - rate 2',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ALB',
							],
							[
								'name' => 'Advanced Learner Loan Bursary funding - rate 3',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ALB',
							],
							[
								'name' => 'Personal and community development learning (valid to 31/07/2023)',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ASL',
							],
							[
								'name' => 'Neighbour learning in deprived communities (valid to 31/07/2023)',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ASL',
							],
							[
								'name' => 'Family English Maths and Language (valid to 31/07/2023)',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ASL',
							],
							[
								'name' => 'Wider family learning (valid to 31/07/2023)',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ASL',
							],
							[
								'name' => 'Wider family learning (valid to 31/07/2023)',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ASL',
							],
							[
								'name' => 'Family English, Maths or Language learning aim formula funded through the Adult Education Budget',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'FLN',
							],
							[
								'name' => 'Apprenticeship or Advanced Apprenticeship delivered through a programme led apprenticeship funded from the learner responsive funding stream',
								'value' => 31,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 34,
								'name' => 'OLASS – Offenders in custody (valid to 31/07/2022)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 85,
								'name' => 'Skills for Jobs',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 86,
								'name' => 'OLASS – Offenders in the community (valid to 31/07/2023)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 105,
								'name' => 'European Social Fund 2007/13',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 107,
								'name' => 'Employer engagement co-funded scheme',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 116,
								'name' => 'Apprenticeship Grant for Employers',
							],
							[
								'value' => 117,
								'name' => 'Apprenticeship supported or funded by ESF',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 118,
								'name' => 'Proxy Learning Aim',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 119,
								'name' => 'Offenders in custody with mainstream funding',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 123,
								'name' => 'Diversity in Apprenticeships',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 124,
								'name' => 'ESF Apprenticeship Grant (19-24)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 125,
								'name' => 'Non-workplace learning in employer responsive funding model (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 126,
								'name' => 'Progression Funding',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 127,
								'name' => 'Access to Apprenticeships',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 128,
								'name' => 'Enhanced Learning Credit Scheme for Armed Forces',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 129,
								'name' => 'Group Training Association (GTA)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 130,
								'name' => 'Apprenticeship Training Agency (ATA) (valid to 31/07/2023)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 131,
								'name' => 'NEET Apprenticeship starts',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 132,
								'name' => 'Apprenticeship Grant for Employers for 16 to 24 year olds (AGE 16-24) - Provider Payment Trigger',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 133,
								'name' => 'Apprenticeship Grant for Employers for 16 to 24 year olds (AGE 16 to 24) - Strategic Partner',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 135,
								'name' => 'Greater Manchester Commitment to Youth Employment',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 136,
								'name' => 'Youth Contract',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 300,
								'name' => 'Sunderland\'s Community Learning Trust',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 301,
								'name' => 'Blackburn with Darwen (Sustainable Neighbourhood Services)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 302,
								'name' => 'CLCumbria (CLC)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 303,
								'name' => 'Birmingham CLT (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 304,
								'name' => 'Derby Community Learning Trust',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 305,
								'name' => 'Trust in Learning - new curriculum, in new places for new learners in Exeter',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 306,
								'name' => 'Community Learning in Cheshire (CLiC) (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 307,
								'name' => 'West of England Community Learning Trust',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 308,
								'name' => 'Liberate',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 309,
								'name' => 'Kent Community Learning Trust (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 310,
								'name' => 'Brighton and Hove Community Learning Trust BHCLT (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 311,
								'name' => 'The Solihull Source',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 312,
								'name' => 'Learning-for-All',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 313,
								'name' => 'The Luton Trust',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 314,
								'name' => 'Sheffield Community Learning Trust',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 315,
								'name' => 'Norfolk Apprenticeships Subsidy',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 317,
								'name' => 'Learners in mainstream FE funded by the LLDD Placement budget',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 318,
								'name' => 'Mandation to Skills Training',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 319,
								'name' => 'Employer Ownership Pilot - Employer defined programme',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 320,
								'name' => '14-16 Direct Funded Students in FE',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 321,
								'name' => '14-16 Home Educated Students',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 322,
								'name' => 'Residential Courses (valid to 31/07/2024)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 323,
								'name' => 'Traineeships',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 324,
								'name' => 'Personalised learning programme for learners with learning difficulties and/or disabilities',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 325,
								'name' => 'Tunnelling procurement (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 326,
								'name' => 'Skills Made Easy-Sheffield City Deal',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 327,
								'name' => '327 Unassigned',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 328,
								'name' => 'RoTL',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 329,
								'name' => 'ESOL Plus (Mandation)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 330,
								'name' => 'Skills Training Innovation and Employment (STRIVE) pilot for the homeless',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 331,
								'name' => 'King\'s Trust TEAM Programme',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 332,
								'name' => '332 Unassigned',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 333,
								'name' => 'Apprenticeship Grant for Employers for 16 to 24 year olds (AGE 16 to 24) - Provider Payment Trigger from  1 Jan 2015 to 31 Mar 2016',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 334,
								'name' => 'Apprenticeship Grant for Employers for 16 to 24 year olds (AGE 16 to 24) - Strategic Partner from 1 Jan 2015 to 31 Dec 2015',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 335,
								'name' => 'ESF AGE Enhancement 1-49',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 336,
								'name' => '336 ESOL QCF additional learning (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 337,
								'name' => '337 Unassigned',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 338,
								'name' => 'ESF AGE Enhancements 50-249',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 339,
								'name' => '339 Solent Skills for Growth Programme (removed)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 340,
								'name' => 'Community learning mental health pilot',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 341,
								'name' => '341 Unassigned',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 342,
								'name' => 'BBC Make it Digital Traineeship Programme',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 344,
								'name' => 'Greater Manchester Skills for Employment Pilot Programme (Growth Deal)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 345,
								'name' => 'Sheffield City Region Skills Bank (Growth Deal)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 346,
								'name' => 'Armed forces leavers',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 347,
								'name' => 'Steel Industries Redundancy Training',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 348,
								'name' => 'Apprenticeship seasonal worker pilot',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 349,
								'name' => 'Apprenticeship Grant for Employers (AGE) - Provider Payment Trigger from 1 April 2016 to 31 July 2017',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 350,
								'name' => 'Non-apprenticeship workplace learning originally funded under the employer responsive funding model',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 351,
								'name' => 'Former Employer Ownership Pilot Provision',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 352,
								'name' => 'HESA-generated ILR file (valid to 31/07/2022)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 353,
								'name' => 'Non-apprenticeship Sporting Excellence Award',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 354,
								'name' => 'Non-apprenticeship Theatre and live events',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 355,
								'name' => 'Non-apprenticeship Sea Fishing (valid to 31/07/2024)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 356,
								'name' => 'Apprenticeship being delivered to own employees',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 357,
								'name' => 'Procured Adult Education Budget (AEB) (valid to 31/07/2021)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 358,
								'name' => 'Extended Allocated Non-Levy Apprenticeships (removed 2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 359,
								'name' => 'Career Learning Pilot',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 360,
								'name' => 'Flexible Learning Fund',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 361,
								'name' => 'Waiver to record payment records for apprenticeships',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 362,
								'name' => 'Apprentice care leavers',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 363,
								'name' => 'Learners in receipt of low wages (valid to 31/07/2024)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 364,
								'name' => 'Adult education budget run-down contract for services -continuing learners from 2017/18',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 365,
								'name' => 'Institute of Technology Provision',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 366,
								'name' => 'Non-levy non-procured contract for authorised apprentices',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 367,
								'name' => 'De-merger monitoring',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 368,
								'name' => 'COVID-19 temporary or permanent withdrawals (valid to 31/07/2022)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 369,
								'name' => 'COVID-19 new programme starts (valid to 31/07/2022)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 370,
								'name' => 'Short Term Funding Initiative (STFI) 1',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 371,
								'name' => 'Short Term Funding Initiative (STFI) 2',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 372,
								'name' => 'Short Term Funding Initiative (STFI) 3',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 373,
								'name' => 'Short Term Funding Initiative (STFI) 4',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 374,
								'name' => 'Apply amended earnings calculation for authorised apprentices, to address specific scenarios with negotiated prices.',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 375,
								'name' => 'sector based work academies pre-employment training',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 376,
								'name' => 'Classroom based 18/19 Offer',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 377,
								'name' => '19-24 Traineeship (2020 procurement) (removed 2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 378,
								'name' => 'Level 3 Free Courses for Jobs Offer',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 379,
								'name' => 'Adult Education Budget procurement 2021 (valid to 31/07/2023)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 380,
								'name' => 'Apprentice authorised by ESFA to have amended band limit applied as part of ESFA intervention',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 381,
								'name' => '16-24 year old continuing a 14-16 valid aim started when the learner was aged 14 or 15',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 382,
								'name' => 'Free Courses for Jobs (Level 3 Offer) for Low Wage Learners (valid to 31/07/2024)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 383,
								'name' => 'Portable flexi-job Apprenticeships pilot (valid to 31/10/2023)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 384,
								'name' => 'T Levels for Adults Pilot (removed 2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 385,
								'name' => 'Multiply Programme (valid to 31/03/2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 386,
								'name' => 'Flexi-Job Apprenticeship Agencies (FJAAs)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 387,
								'name' => 'SME Apprenticeship Brokerage Pathfinder (valid to 31/10/2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 388,
								'name' => 'Adult Education Budget procurement 2023 (valid to 31/07/2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 389,
								'name' => 'LSIF – Local Skills Improvement Fund – Additionality (valid to 31/03/2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 390,
								'name' => 'LSIF – Local Skills Improvement Fund – Delivery (valid to 31/03/2025)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 391,
								'name' => 'Earnings Threshold',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 392,
								'name' => 'LDD Apprenticeships English and Maths flexibilities',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 393,
								'name' => 'Accelerated delivery',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 394,
								'name' => 'Multiply Randomised Control Trials (valid to 31/03/2026)',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'value' => 395,
								'name' => 'Project S Pilot for Adult Skills Fund Providers',
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'LDM',
							],
							[
								'name' => 'Fashion Retail (valid to 31/07/2016)',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Manufacturing (valid to 31/07/2016)',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Financial Services (valid to 31/07/2016)',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Construction (valid to 31/07/2016)',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Food and Drink Manufacturing (valid to 31/07/2016)',
								'value' => 5,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Nuclear (valid to 31/07/2016)',
								'value' => 6,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Process Industries (valid to 31/07/2016)',
								'value' => 7,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Creative and Cultural (valid to 31/07/2016)',
								'value' => 8,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Hospitality (valid to 31/07/2016)',
								'value' => 9,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Sport and Active Leisure (valid to 31/07/2016)',
								'value' => 10,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Retail (valid to 31/07/2016)',
								'value' => 11,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Materials, Production and Supply (valid to 31/07/2016)',
								'value' => 12,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'National Enterprise Academy (valid to 31/07/2016)',
								'value' => 13,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Social Care (valid to 31/07/2016)',
								'value' => 14,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Information Technology (valid to 31/07/2016)',
								'value' => 15,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Power (31/07/2016)',
								'value' => 16,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Rail Engineering (31/07/2016)',
								'value' => 17,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Environmental Technologies (valid to 31/07/2016)',
								'value' => 18,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Logistics (valid to 31/07/2016)',
								'value' => 19,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'Health (valid to 31/07/2016)',
								'value' => 20,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'NSA',
							],
							[
								'name' => 'DWP work Programme',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'WPP',
							],
							[
								'name' => '0%',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'POD',
							],
							[
								'name' => '1 - 9%',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'POD',
							],
							[
								'name' => '10 - 24%',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'POD',
							],
							[
								'name' => '25 - 49%',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'POD',
							],
							[
								'name' => '50 - 74%',
								'value' => 5,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'POD',
							],
							[
								'name' => '75 - 99%',
								'value' => 6,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'POD',
							],
							[
								'name' => '100%',
								'value' => 7,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'POD',
							],
							[
								'name' => 'Student is funded by HEFCE using the old funding regime (only for learning aims starting on or after 1 September 2012)',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HEM',
							],
							[
								'name' => 'Student has received an award under the National Scholarship programme for this learning aim',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HEM',
							],
							[
								'name' => 'Student\'s qualifications and grades prior to enrolment are included in the student number control exemption list according to HEFCE',
								'value' => 5,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HEM',
							],
							[
								'name' => 'Postcode validation exclusion',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Procured Devolved Adult Education',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Devolved AEB Innovation Fund',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Devolved AEB Innovation Fund',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Test and Learn Pilot English and Maths',
								'value' => 5,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Test and Learn Pilot ESOL',
								'value' => 6,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Test and Learn Pilot Digital',
								'value' => 7,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Sector Specific Monitoring',
								'value' => 8,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Devolved AEB British Sign Language Entitlement',
								'value' => 9,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Devolved AEB Low Wage Pilot',
								'value' => 10,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Level 2 contingency',
								'value' => 11,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Level 3 contingency',
								'value' => 12,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Sector Based Work Academy Programme (valid to 31/07/2026)',
								'value' => 13,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Sector Based Work Academy Programme Work Experience',
								'value' => 14,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Sector Based Work Academy Programme Job Outcome (valid to 31/07/2026)',
								'value' => 15,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Sector Based Work Academy Component Learning Aim',
								'value' => 16,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Innovation Engagement Payment',
								'value' => 17,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Innovation On-Programme Payment',
								'value' => 18,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Innovation Outcome Payment Type 1',
								'value' => 19,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Innovation Outcome Payment Type 2',
								'value' => 20,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Innovation Outcome Payment Type 3',
								'value' => 21,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Non-Standard Contract Management',
								'value' => 22,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'MCA/GLA Delivery Exclusion',
								'value' => 23,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Workplace-based provision',
								'value' => 24,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Category of Adult Education: Engagement',
								'value' => 25,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Test and learn Pilot Innovation',
								'value' => 26,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Targeted In work progression Initiative',
								'value' => 27,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Category of Adult Education: Employability',
								'value' => 28,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Category of Adult Education: Foundation Skills for Work',
								'value' => 29,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Category of Adult Education: Intermediate/Advanced Skills for Work',
								'value' => 30,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Category of Adult Education: Learning for Personal Development',
								'value' => 31,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Commonwealth Games',
								'value' => 32,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Coventry City of Culture',
								'value' => 33,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Construction Project',
								'value' => 34,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Level 3 expansion initiative',
								'value' => 35,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Level 2 expansion initiative',
								'value' => 36,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Test and Learn pilot for residents aged over 50',
								'value' => 37,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Upskilling of staff delivering to SEND learners',
								'value' => 38,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'High Value Courses Premium Devolved Area Extension',
								'value' => 39,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Devolved Area Skills Projects',
								'value' => 40,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Progress to Work Extended Programme',
								'value' => 41,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Sector Gateway Component aim',
								'value' => 42,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Learning aim authorised as exempt from learner-level capping',
								'value' => 43,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Payment on Actuals',
								'value' => 44,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Co-funded fee waived',
								'value' => 45,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Formula Funded ACL',
								'value' => 46,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'LOT 1 Procured activity – priority provision for the unemployed',
								'value' => 47,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'LOT 3 – Hidden NEETS – Category 1',
								'value' => 48,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'LOT 3 – Hidden NEETS – Category 2',
								'value' => 49,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'LOT 4 – Local integrated Employability Model (25+)',
								'value' => 50,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => '19-23 Entitlement Uplift',
								'value' => 51,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Priority Sector Level 4 Offer',
								'value' => 52,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Priority Sector Second Funded Level 3',
								'value' => 53,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Vocational ESOL Programme',
								'value' => 54,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Short Term Monitoring Code 1',
								'value' => 55,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Short Term Monitoring Code 2',
								'value' => 56,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Short Term Monitoring Code 3',
								'value' => 57,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Short Term Monitoring Code 4',
								'value' => 58,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Short Term Monitoring Code 5',
								'value' => 59,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Short Term Monitoring Code 6',
								'value' => 60,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Health and Social Care Plan',
								'value' => 61,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'NTCA AEB Carry-in allocation from AY 2020-21',
								'value' => 62,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Local approved flexibilities',
								'value' => 63,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Software Driven Distance Learning',
								'value' => 73,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'LOT 1 Procured activity – priority provision',
								'value' => 74,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'Residential Courses (devolved)',
								'value' => 75,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'LOT 2 Procured activity – priority provision',
								'value' => 76,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'DAM',
							],
							[
								'name' => 'No household member is in employment and the household includes one or more dependent children',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HHS',
							],
							[
								'name' => 'No household member is in employment and the household does not include any dependent children ',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HHS',
							],
							[
								'name' => 'Learner lives in a single adult household with dependent children',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HHS',
							],
							[
								'name' => 'Learner has withheld this information',
								'value' => 98,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HHS',
							],
							[
								'name' => 'None of HHS1, HHS2 or HHS3 applies',
								'value' => 99,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'HHS',
							],
							[
								'name' => 'Apprenticeship funded through a contract for services with the employer',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACT',
							],
							[
								'name' => 'Apprenticeship funded through a contract for services with the Education and Skills Funding Agency',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACT',
							],
							[
								'name' => 'Unassigned',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACT',
							],
							[
								'name' => 'Engaging and/or building confidence',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACL',
							],
							[
								'name' => 'Preparation for further learning',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACL',
							],
							[
								'name' => 'Preparation for employment',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACL',
							],
							[
								'name' => 'Improving essential skills including English, ESOL, Maths and Digital',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACL',
							],
							[
								'name' => 'Equipping parents/carers to support children\'s learning',
								'value' => 5,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACL',
							],
							[
								'name' => 'Health and well-being',
								'value' => 6,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACL',
							],
							[
								'name' => 'Developing stronger communities',
								'value' => 7,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'ACL',
							],
							[
								'name' => 'Family Learning provision type',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'AFL',
							],
							[
								'name' => 'Offer of an interview for a role which matches skills acquired',
								'value' => 1,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Offer of a new role or added responsibilities with existing employer',
								'value' => 2,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Receipt of plan from learner to acquire new self-employment opportunities or contracts',
								'value' => 3,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Checked HGV Provisional licence',
								'value' => 4,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Passed HGV ADR (dangerous goods) exam',
								'value' => 5,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Passed HGV PDP (petroleum driver passport) exam',
								'value' => 6,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Passed HGV Part 4 of the driver certificate of professional competence',
								'value' => 7,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Received HGV Evidence that CPC has been brought up to date',
								'value' => 8,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Taken HGV Part 3b of the driver certificate of professional competence: On-Road Driving',
								'value' => 9,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
							[
								'name' => 'Passed HGV Part 3b of the driver certificate of professional competence: On-Road Driving',
								'value' => 10,
								'condition_key' => 'LearnDelFAMType',
								'condition_value' => 'EVI',
							],
						]
					],
					'LearnDelFAMDateFrom' => [
						'name' => 'Date applies from',
						'required' => false,
						'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
						'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
						'field' => 'date', //YYYY-MM-DD
						'type' => 'date',
					],
					'LearnDelFAMDateTo' => [
						'name' => 'Date applies to',
						'required' => false,
						'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
						'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
						'field' => 'date', //YYYY-MM-DD
						'type' => 'date',
					],
				]
			],
			'LearningDeliveryWorkPlacement' => [
				'name' => 'Learning Delivery Work Placement',
				'type' => 'json',
				'add' => true,

				//'limit' => 17,
				'required' => false,
				'children' => [
					'WorkPlaceStartDate' => [
						'name' => 'Work placement start date',
						'required' => false,
						'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
						'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
						'field' => 'date', //YYYY-MM-DD
						'type' => 'date',
					],
					'WorkPlaceEndDate' => [
						'name' => 'Work placement end date',
						'required' => false,
						'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
						'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
						'field' => 'date', //YYYY-MM-DD,
						'type' => 'date',
					],
					'WorkPlaceHours' => [
						'name' => 'Work placement hours',
						'required' => false,
						'pattern' => '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9])$',
						'pattern_error' => 'Integer, 1 character',
						'type' => 'number',
						'field' => 'number',
					],
					'WorkPlaceMode' => [
						'name' => 'Work placement mode',
						'required' => false,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, 1 character',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Internal (simulated) work placement',
								'value' => 1
							],
							[
								'name' => 'External work placement',
								'value' => 2
							],
						]
					],
					'WorkPlaceEmpId' => [
						'name' => 'Work placement employer identifier',
						'required' => false,
						'pattern' => '^\d{9}$',
						'pattern_error' => 'A valid Employer ID number from the Employer Data Service (EDS). This is a nine digit number',
						'type' => 'number',
						'field' => 'number',
					],
				]
			],
			'AppFinRecord' => [
				'name' => 'Apprenticeship Financial Record',
				'type' => 'json',
				'add' => true,
				//'fieldGroup' => 'ilr',
				'limit' => 1,
				'required' => false,
				'children' => [
					'AFinType' => [
						'name' => 'Apprenticeship financial type',
						'required' => false,
						'pattern' => '^.{3}$',
						'pattern_error' => '3 character text',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Total negotiated price',
								'value' => 'TNP'
							],
							[
								'name' => 'Payment record',
								'value' => 'PMR'
							],
						]
					],
					'AFinCode' => [
						'name' => 'Apprenticeship financial code',
						'required' => false,
						'pattern' => '^\d{1,2}$',
						'pattern_error' => 'Integer, 1 to 2 characters.',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Total training price',
								'value' => 1,
								'condition_key' => 'AFinType',
								'condition_value' => 'TNP',
							],
							[
								'name' => 'Total assessment price',
								'value' => 2,
								'condition_key' => 'AFinType',
								'condition_value' => 'TNP',
							],
							[
								'name' => 'Residual training price',
								'value' => 3,
								'condition_key' => 'AFinType',
								'condition_value' => 'TNP',
							],
							[
								'name' => 'Residual assessment price',
								'value' => 4,
								'condition_key' => 'AFinType',
								'condition_value' => 'TNP',
							],
							[
								'name' => 'Unassigned',
								'value' => 5,
								'condition_key' => 'AFinType',
								'condition_value' => 'TNP',
							],
							[
								'name' => 'Training payment',
								'value' => 1,
								'condition_key' => 'AFinType',
								'condition_value' => 'PMR',
							],
							[
								'name' => 'Assessment payment',
								'value' => 2,
								'condition_key' => 'AFinType',
								'condition_value' => 'PMR',
							],
							[
								'name' => 'Employer payment reimbursed by provider',
								'value' => 3,
								'condition_key' => 'AFinType',
								'condition_value' => 'PMR',
							],
							[
								'name' => 'Unassigned',
								'value' => 4,
								'condition_key' => 'AFinType',
								'condition_value' => 'PMR',
							],
						]
					],
					'AFinDate' => [
						'name' => 'Apprenticeship financial record date',
						'required' => false,
						'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
						'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
						'field' => 'date', //YYYY-MM-DD
						'type' => 'date',
					],
					'AFinAmount' => [
						'name' => 'Apprenticeship financial amount',
						'required' => false,
						'pattern' => '^\d{1,6}$',
						'pattern_error' => 'Must contain a value in the range 0 to 999999',
						'type' => 'number',
						'field' => 'number',
					],
				]
			],
			'ProviderSpecDeliveryMonitoring' => [
				'name' => 'Learning Delivery Provider Specified Monitoring',
				'type' => 'json',
				'add' => true,
				//'fieldGroup' => 'ilr',
				'limit' => 4,
				'required' => false,
				'children' => [
					'ProvSpecDelMonOccur' => [
						'name' => 'Provider specified delivery monitoring occurrence',
						'required' => false,
						'pattern' => '^.{1}$',
						'pattern_error' => '1 character text',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'A Occurrence',
								'value' => 'A'
							],
							[
								'name' => 'B Occurrence',
								'value' => 'B'
							],
							[
								'name' => 'C Occurrence',
								'value' => 'C'
							],
							[
								'name' => 'C Occurrence',
								'value' => 'C'
							],
						]
					],
					'ProvSpecDelMon' => [
						'name' => 'Provider specified delivery monitoring',
						'required' => false,
						'pattern' => '^.{0,20}$',
						'pattern_error' => 'String, up to 20 characters, "*, ?, %, _" removed.',
					],
				]
			],
			'LearningDeliveryHE' => [
				'name' => 'Learning Delivery HE',
				'type' => 'json',
				'add' => true,
				//'fieldGroup' => 'ilr',
				'limit' => 1,
				'required' => false,
				'children' => [
					'NUMHUS' => [
						'name' => 'Student instance identifier',
						'required' => false,
						'pattern' => '^.{0,20}$',
						'pattern_error' => 'String, up to 20 characters, "*, ?, %, _" removed.',
					],
					'SSN' => [
						'name' => 'Student Support Number',
						'required' => false,
						'pattern' => '^[A-Z]{4}\d{8}[A-Z]{1}$',
						'pattern_error' => 'The SSN is 13 characters long. The first four characters are alpha. The next 8 characters are numeric. The last character is alpha, which is a check character. For example Student Support Number = WADM46891352A',
					],
					'QUALENT3' => [
						'name' => 'Qualification on Entry',
						'required' => false,
						'pattern' => '^.{0,3}$',
						'pattern_error' => 'String, up to 3 characters',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Certificate of Higher Education',
								'value' => 'C20'
							],
							[
								'name' => 'Higher National Certificate (including BTEC and SQA equivalents)',
								'value' => 'C30'
							],
							[
								'name' => 'Higher Apprenticeships (Level 4) (valid to 31/07/2013)',
								'value' => 'C44'
							],
							[
								'name' => 'Other Qualification at Level C',
								'value' => 'C80'
							],
							[
								'name' => 'Undergraduate credits',
								'value' => 'C90'
							],
							[
								'name' => 'Other qualification at level D',
								'value' => 'D80'
							],
							[
								'name' => 'UK Doctorate degree',
								'value' => 'DUK'
							],
							[
								'name' => 'Non-UK Doctorate degree',
								'value' => 'DZZ'
							],
							[
								'name' => 'First degree leading to QTS',
								'value' => 'H11'
							],
							[
								'name' => 'Professional Graduate Certificate in Education',
								'value' => 'H71'
							],
							[
								'name' => 'Other Qualification at Level H',
								'value' => 'H80'
							],
							[
								'name' => 'UK First degree',
								'value' => 'HUK'
							],
							[
								'name' => 'Non-UK first degree',
								'value' => 'HZZ'
							],
							[
								'name' => 'Foundation degree',
								'value' => 'J10'
							],
							[
								'name' => 'Diploma of Higher Education',
								'value' => 'J20'
							],
							[
								'name' => 'Higher National Diploma (including BTEC and SQA equivalents)',
								'value' => 'J30'
							],
							[
								'name' => 'Certificate or diploma of education (i.e. non-graduate initial teacher training qualification)',
								'value' => 'J48'
							],
							[
								'name' => 'Foundation course at HE level',
								'value' => 'J49'
							],
							[
								'name' => 'Other Qualification at Level J',
								'value' => 'J80'
							],
							[
								'name' => 'UK ordinary (non-honours) first degree',
								'value' => 'JUK'
							],
							[
								'name' => 'Integrated undergraduate/postgraduate taught Masters degree on the enhanced / extended pattern',
								'value' => 'M2X'
							],
							[
								'name' => 'Diploma at Level M (Postgraduate Diploma)',
								'value' => 'M41'
							],
							[
								'name' => 'Certificate at Level M (Postgraduate Certificate)',
								'value' => 'M44'
							],
							[
								'name' => 'Postgraduate Certificate of Education or Professional Graduate Diploma in Education',
								'value' => 'M71'
							],
							[
								'name' => 'Other Qualification at Level M',
								'value' => 'M80'
							],
							[
								'name' => 'Postgraduate credits',
								'value' => 'M90'
							],
							[
								'name' => 'UK Masters degree',
								'value' => 'MUK'
							],
							[
								'name' => 'Non-UK Masters degree',
								'value' => 'MZZ'
							],
							[
								'name' => 'Diploma at Level 3',
								'value' => 'P41'
							],
							[
								'name' => 'Certificate at Level 3',
								'value' => 'P42'
							],
							[
								'name' => 'Award at Level 3',
								'value' => 'P46'
							],
							[
								'name' => 'AQA Baccalaureate',
								'value' => 'P47'
							],
							[
								'name' => 'GCE and VCE A/AS Level',
								'value' => 'P50'
							],
							[
								'name' => '14-19 Advanced Diploma (Level 3)',
								'value' => 'P51'
							],
							[
								'name' => 'Scottish Baccalaureate',
								'value' => 'P53'
							],
							[
								'name' => 'Scottish Highers / Advance Highers',
								'value' => 'P54'
							],
							[
								'name' => 'T Levels',
								'value' => 'P55'
							],
							[
								'name' => 'International Baccalaureate (IB) Diploma',
								'value' => 'P62'
							],
							[
								'name' => 'International Baccalaureate (IB) Certificate',
								'value' => 'P63'
							],
							[
								'name' => 'Cambridge Pre-U Diploma',
								'value' => 'P64'
							],
							[
								'name' => 'Cambridge Pre-U Certificate',
								'value' => 'P65'
							],
							[
								'name' => 'Welsh Baccalaureate Advanced Diploma (Level 3)',
								'value' => 'P68'
							],
							[
								'name' => 'Cambridge Pre-U Diploma (valid to 31/07/2013)',
								'value' => 'P69'
							],
							[
								'name' => 'Professional Qualification at Level 3 (valid to 31/07/2013)',
								'value' => 'P70'
							],
							[
								'name' => 'Other Qualification at Level 3',
								'value' => 'P80'
							],
							[
								'name' => 'Mixed Level 3 qualifications of which some or all are subject to Tariff (valid to 31/07/2014)',
								'value' => 'P91'
							],
							[
								'name' => 'Mixed Level 3 qualifications of which none are subject to Tariff',
								'value' => 'P92'
							],
							[
								'name' => 'Level 3 qualifications of which all are subject to UCAS Tariff',
								'value' => 'P93'
							],
							[
								'name' => 'Level 3 qualifications of which some are subject to UCAS Tariff',
								'value' => 'P94'
							],
							[
								'name' => '14-19 Higher Diploma (Level 2)',
								'value' => 'Q51'
							],
							[
								'name' => 'Welsh Baccalaureate Intermediate Diploma (Level 2)',
								'value' => 'Q52'
							],
							[
								'name' => 'Other Qualification at Level 2',
								'value' => 'Q80'
							],
							[
								'name' => '14-19 Foundation Diploma (Level 1)',
								'value' => 'R51'
							],
							[
								'name' => 'Welsh Baccalaureate Foundation Diploma (Level 1)',
								'value' => 'R52'
							],
							[
								'name' => 'Other Qualification at Level 1',
								'value' => 'R80'
							],
							[
								'name' => 'HE Access Course, QAA recognised',
								'value' => 'X00'
							],
							[
								'name' => 'HE Access Course, not QAA recognised',
								'value' => 'X01'
							],
							[
								'name' => 'Mature student admitted on basis of previous experience and/or admissions test',
								'value' => 'X02'
							],
							[
								'name' => 'Mature students admitted on basis of previous experience (without formal APEL/APL and/or institution\'s own entrance examinations)',
								'value' => 'X03'
							],
							[
								'name' => 'Other qualification level not known',
								'value' => 'X04'
							],
							[
								'name' => 'Student has no formal qualification',
								'value' => 'X05'
							],
							[
								'name' => 'Not known',
								'value' => 'X06'
							],
							[
								'name' => 'Higher National Certificate (HNC)',
								'value' => 'C31'
							],
							[
								'name' => 'Higher National Diploma (HND)',
								'value' => 'J31'
							],
							[
								'name' => 'Other qualification or mixture of qualifications at Level 4',
								'value' => 'C82'
							],
							[
								'name' => 'Qualification at Level 8',
								'value' => 'D82'
							],
							[
								'name' => 'Other Qualification at Level 6',
								'value' => 'H82'
							],
							[
								'name' => 'Other qualification or mixture of qualifications at Level 5',
								'value' => 'J82'
							],
							[
								'name' => 'Other qualification or mixture of qualifications at Level 7',
								'value' => 'M82'
							],
							[
								'name' => 'Other qualification or mixture of qualifications at Level 3',
								'value' => 'P82'
							],
							[
								'name' => 'Level 2 qualification(s)',
								'value' => 'Q82'
							],
							[
								'name' => 'Level 1 qualification(s)',
								'value' => 'R82'
							],
							[
								'name' => 'A/AS Level',
								'value' => 'P60'
							],
							[
								'name' => 'Art and Design foundation course at HE level',
								'value' => 'J51'
							],
							[
								'name' => 'International Baccalaureate (IB) Diploma/Certificate',
								'value' => 'P66'
							],
							[
								'name' => 'HE Access course',
								'value' => 'X07'
							],
						]
					],
					'SOC2000' => [
						'name' => 'Occupation Code (SOC2000)',
						'required' => false,
						'pattern' => '^\d{4}$',
						'pattern_error' => 'Integer, 4 characters',
						'type' => 'number',
						'field' => 'number',
					],
					'SOC' => [
						'name' => 'Occupation Code',
						'required' => false,
						'pattern' => '^\d{4}$',
						'pattern_error' => 'Integer, 4 characters',
						'type' => 'number',
						'field' => 'number',
					],
					'SEC' => [
						'name' => 'Socio-Economic Classification',
						'required' => false,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, one numeric digit',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Higher managerial and professional occupations',
								'value' => 1
							],
							[
								'name' => 'Lower managerial and professional occupations',
								'value' => 2
							],
							[
								'name' => 'Intermediate occupations',
								'value' => 3
							],
							[
								'name' => 'Small employers and own-account workers',
								'value' => 4
							],
							[
								'name' => 'Lower supervisory and technical occupations',
								'value' => 5
							],
							[
								'name' => 'Semi-routine occupations',
								'value' => 6
							],
							[
								'name' => 'Routine occupations',
								'value' => 7
							],
							[
								'name' => 'Never worked and long term unemployed',
								'value' => 8
							],
							[
								'name' => 'Not classified',
								'value' => 9
							],
						]
					],
					'UCASAPPID' => [
						'name' => 'UCAS application code (legacy)',
						'required' => false,
						'pattern' => '^[a-zA-Z]{2}([0-9]{2}|[0-9]{9})$',
						'pattern_error' => 'Two alphabetic characters followed by two numeric digits, or nine numeric digits.',
					],
					'UCASSCHEMECODE' => [
						'name' => 'UCAS Scheme Code',
						'required' => false,
						'pattern' => '^[a-zA-Z]{2}([0-9]{2}|[0-9]{9})$',
						'pattern_error' => 'Two alphabetic characters followed by two numeric digits, or nine numeric digits.',
					],
					'TYPEYR' => [
						'name' => 'Type of Instance Year',
						'required' => false,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, one numeric digit',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Year of instance contained within the reporting period 01 August to 31 July',
								'value' => 1
							],
							[
								'name' => 'Year of instance not contained within the reporting period 01 August to 31 July',
								'value' => 2
							],
							[
								'name' => 'Learner commencing a year of instance of a course running across reporting periods',
								'value' => 3
							],
							[
								'name' => 'Learner mid-way through a learning aim running across reporting periods',
								'value' => 4
							],
							[
								'name' => 'Learner finishing a year of instance of a course running across reporting periods',
								'value' => 5
							],
						]
					],
					'MODESTUD' => [
						'name' => 'Mode of Study',
						'required' => false,
						'pattern' => '^\d{1,2}$',
						'pattern_error' => 'Integer, up to two numeric digits',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Full-time and sandwich',
								'value' => 1
							],
							[
								'name' => 'Sandwich year-out',
								'value' => 2
							],
							[
								'name' => 'Part-time',
								'value' => 3
							],
							[
								'name' => 'Not in Early Statistics/HESES population. Valid to: 31 July 2022',
								'value' => 99
							],
						]
					],
					'FUNDLEV' => [
						'name' => 'Level Applicable to Funding Council HESES',
						'required' => false,
						'pattern' => '^\d{2}$',
						'pattern_error' => 'Integer, two numeric digits',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Undergraduate',
								'value' => 10
							],
							[
								'name' => 'Long undergraduate',
								'value' => 11
							],
							[
								'name' => 'Postgraduate taught',
								'value' => 20
							],
							[
								'name' => 'Long postgraduate taught',
								'value' => 21
							],
							[
								'name' => 'Postgraduate research',
								'value' => 30
							],
							[
								'name' => 'Long postgraduate research',
								'value' => 31
							],
							[
								'name' => 'Not in HEIFES population',
								'value' => 99
							],
						]
					],
					'FUNDCOMP' => [
						'name' => 'Completion of Year of Instance',
						'required' => false,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, one numeric digits',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Completed the current year of programme of study',
								'value' => 1
							],
							[
								'name' => 'Did not complete the current year of programme of study',
								'value' => 2
							],
							[
								'name' => 'Year of programme of study not yet completed, but has not failed to complete',
								'value' => 3
							],
							[
								'name' => 'Not in HEIFES population',
								'value' => 9
							],
						]
					],
					'STULOAD' => [
						'name' => 'Student Instance FTE',
						'required' => false,
						'pattern' => '^(([0-9]\.[1-9]|[1-9]\.[0-9]|([1-9][0-9]|[1-2][0-9][0-9])\.[0-9])|300.0)$',
						'pattern_error' => 'Must contain a value in the range 0.1 to 300.0',
						'type' => 'decimal',
					],
					'YEARSTU' => [
						'name' => 'Year of Student on this Instance',
						'required' => false,
						'pattern' => '^([1-9]|[0-8][0-9]|9[0-8])$',
						'pattern_error' => 'Must contain a value in the range 1 to 98',
						'type' => 'number',
						'field' => 'number',
					],
					'MSTUFEE' => [
						'name' => 'Major Source of Tuition Fees',
						'required' => false,
						'pattern' => '^\d{1,2}$',
						'pattern_error' => 'Integer, up to two numeric digits',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'No award or financial backing',
								'value' => 1
							],
							[
								'name' => 'English or Welsh LEA award (valid to 31/07/2024)',
								'value' => 2
							],
							[
								'name' => 'Student Awards Agency for Scotland (SAAS)',
								'value' => 3
							],
							[
								'name' => 'DELNI/Northern Ireland Education and Library Boards',
								'value' => 4
							],
							[
								'name' => 'Institutional waiver of support costs',
								'value' => 5
							],
							[
								'name' => 'Local government - Channel Islands and Isle of Man',
								'value' => 6
							],
							[
								'name' => 'Fee waiver under government unemployed learners scheme',
								'value' => 7
							],
							[
								'name' => 'British Academy',
								'value' => 8
							],
							[
								'name' => 'Part-time graduate apprentice study programme',
								'value' => 9
							],
							[
								'name' => 'Student Loans Company (SLC)',
								'value' => 10
							],
							[
								'name' => 'Research council - BBSRC',
								'value' => 11
							],
							[
								'name' => 'Research council - MRC',
								'value' => 12
							],
							[
								'name' => 'Research council - NERC',
								'value' => 13
							],
							[
								'name' => 'Research council - EPSRC',
								'value' => 14
							],
							[
								'name' => 'Arts and Humanities Research Council',
								'value' => 17
							],
							[
								'name' => 'Science and Technology Facilities Council (STFC)',
								'value' => 18
							],
							[
								'name' => 'Research council - not specified',
								'value' => 19
							],
							[
								'name' => 'International agency',
								'value' => 22
							],
							[
								'name' => 'Cancer Research UK',
								'value' => 23
							],
							[
								'name' => 'Wellcome Trust',
								'value' => 24
							],
							[
								'name' => 'Other AMRC charity',
								'value' => 25
							],
							[
								'name' => 'Other charitable foundation',
								'value' => 26
							],
							[
								'name' => 'Departments of Health/NHS/Social Care',
								'value' => 31
							],
							[
								'name' => 'Departments of Social Services',
								'value' => 32
							],
							[
								'name' => 'BIS',
								'value' => 33
							],
							[
								'name' => 'Other HM government departments/public bodies',
								'value' => 34
							],
							[
								'name' => 'Scholarship of HM forces',
								'value' => 35
							],
							[
								'name' => 'Scottish Enterprise/Highlands and Islands Enterprise/Training Enterprise Council/Local Enterprise Company',
								'value' => 36
							],
							[
								'name' => 'LEA training grants scheme',
								'value' => 37
							],
							[
								'name' => 'Department of Agriculture and Rural Development for Northern Ireland (DARD)',
								'value' => 38
							],
							[
								'name' => 'Scottish Local Authority discretionary award',
								'value' => 39
							],
							[
								'name' => 'EU Commission (EC)',
								'value' => 41
							],
							[
								'name' => 'Overseas learner award from HM government/British Council',
								'value' => 42
							],
							[
								'name' => 'Overseas government',
								'value' => 43
							],
							[
								'name' => 'Overseas Development Administration',
								'value' => 44
							],
							[
								'name' => 'Overseas institution',
								'value' => 45
							],
							[
								'name' => 'Overseas industry or commerce',
								'value' => 46
							],
							[
								'name' => 'Other overseas funding',
								'value' => 47
							],
							[
								'name' => 'Other overseas - repayable loan',
								'value' => 48
							],
							[
								'name' => 'ORSAS',
								'value' => 49
							],
							[
								'name' => 'Mix of learner and SLC',
								'value' => 52
							],
							[
								'name' => 'Mix of learner and SAAS/SLC',
								'value' => 53
							],
							[
								'name' => 'Mix of learner and DELNI/NIELB',
								'value' => 54
							],
							[
								'name' => 'UK industry/commerce',
								'value' => 61
							],
							[
								'name' => 'Absent for a year',
								'value' => 71
							],
							[
								'name' => 'Learner\'s employer',
								'value' => 81
							],
							[
								'name' => 'FE student New Deal',
								'value' => 96
							],
							[
								'name' => 'Other',
								'value' => 97
							],
							[
								'name' => 'No fees',
								'value' => 98
							],
							[
								'name' => 'Not known',
								'value' => 99
							],
							[
								'name' => 'Research council',
								'value' => 92
							],
							[
								'name' => 'Charitable foundation',
								'value' => 93
							],
							[
								'name' => 'UK armed forces',
								'value' => 94
							],
							[
								'name' => 'Overseas funding',
								'value' => 95
							],
						]
					],
					'PCOLAB' => [
						'name' => 'Percentage not taught by this Institution',
						'required' => false,
						'pattern' => '^(([0-9]\.[1-9]|[1-9]\.[0-9]|([1-9][0-9])\.[0-9])|100.0)$',
						'pattern_error' => 'Must contain a value in the range 0.1 to 100.0',
						'type' => 'decimal',
					],
					'PCFLDCS' => [
						'name' => 'Percentage taught in first LDCS subject',
						'required' => false,
						'pattern' => '^(([0-9]\.[1-9]|[1-9]\.[0-9]|([1-9][0-9])\.[0-9])|100.0)$',
						'pattern_error' => 'Must contain a value in the range 0.1 to 100.0',
						'type' => 'decimal',
					],
					'PCSLDCS' => [
						'name' => 'Percentage taught in second LDCS subject',
						'required' => false,
						'pattern' => '^(([0-9]\.[1-9]|[1-9]\.[0-9]|([1-9][0-9])\.[0-9])|100.0)$',
						'pattern_error' => 'Must contain a value in the range 0.1 to 100.0',
						'type' => 'decimal',
					],
					'PCTLDCS' => [
						'name' => 'Percentage taught in third LDCS subject',
						'required' => false,
						'pattern' => '^(([0-9]\.[1-9]|[1-9]\.[0-9]|([1-9][0-9])\.[0-9])|100.0)$',
						'pattern_error' => 'Must contain a value in the range 0.1 to 100.0',
						'type' => 'decimal',
					],
					'SPECFEE' => [
						'name' => 'Special Fee Indicator',
						'required' => false,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, one numeric digit',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Standard/Prescribed fee',
								'value' => 0
							],
							[
								'name' => 'Sandwich placement',
								'value' => 1
							],
							[
								'name' => 'Language year abroad and not full-year outgoing ERASMUS+/Turing Scheme',
								'value' => 2
							],
							[
								'name' => 'Full-year outgoing ERASMUS+/Turing Scheme',
								'value' => 3
							],
							[
								'name' => 'Final year of full-time course lasting less than 15 weeks',
								'value' => 4
							],
							[
								'name' => 'Final year of a full-time course lasting more than 14 weeks but less than 24 weeks',
								'value' => 5
							],
							[
								'name' => 'Other fee',
								'value' => 9
							],
						]
					],
					'NETFEE' => [
						'name' => 'Net Tuition Fee',
						'required' => false,
						'pattern' => '^\d{1,6}$',
						'pattern_error' => 'Must contain a value in the range 0 to 999999',
						'type' => 'number',
						'field' => 'number',
					],
					'GROSSFEE' => [
						'name' => 'Gross Tuition Fee',
						'required' => false,
						'pattern' => '^\d{1,6}$',
						'pattern_error' => 'Must contain a value in the range 0 to 999999',
						'type' => 'number',
						'field' => 'number',
					],
					'DOMICILE' => [
						'name' => 'Domicile (legacy)',
						'required' => false,
						'pattern' => '^.{2}$',
						'pattern_error' => 'Two-character alphabetic code from Appendix D',
					],
					'PERMADDCOUNTRY' => [
						'name' => 'Country of Permanent Address',
						'required' => false,
						'pattern' => '^.{2}$',
						'pattern_error' => 'Two-character alphabetic code from Appendix D',
					],
					'ELQ' => [
						'name' => 'Equivalent or Lower Qualification',
						'required' => false,
						'pattern' => '^\d{1}$',
						'pattern_error' => 'Integer, one numeric digit',
						'type' => 'number',
						'field' => 'select',
						'choices' => [
							[
								'name' => 'Non-exempt ELQ',
								'value' => 1
							],
							[
								'name' => 'Exempt ELQ',
								'value' => 2
							],
							[
								'name' => 'Not ELQ',
								'value' => 3
							],
							[
								'name' => 'Not required',
								'value' => 9
							],
						]
					],
					'HEPostCode' => [
						'name' => 'HE Centre Location Postcode',
						'required' => false,
						'pattern' => '^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Z][0-9]{1,2})|(([A-Z][A-HJ-Y][0-9]{1,2})|(([A-Z][0-9][A-Z])|([A-Z][A-HJ-Y][0-9]?[A-Z]))))\s?[0-9][A-Z]{2})$',
						'pattern_error' => 'A valid postcode which must be in upper case',
					],
				]
			],
		]
	],
	// Adding "DPOutcome" here as it will be used to generate LearnerDestinationandProgression if these values will be filled
	'DPOutcome' => [
		'name' => 'DPOutcome Entity Definition',
		'description' => 'The destination of the learner when they have completed or withdrawn from the activities on their original learning agreement or plan.',
		'type' => 'json',
		'add' => true,
		'required' => false,
		'limit' => 1,
		'children' => [
			'OutType' => [
				'name' => 'Outcome type',
				'required' => true,
				'pattern' => '^.{3}$',
				'pattern_error' => '3 character text',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Education',
						'value' => 'EDU'
					],
					[
						'name' => 'In Paid Employment',
						'value' => 'EMP'
					],
					[
						'name' => 'Gap Year',
						'value' => 'GAP'
					],
					[
						'name' => 'Not in Paid Employment',
						'value' => 'NPE'
					],
					[
						'name' => 'Other',
						'value' => 'OTH'
					],
					[
						'name' => 'Social Destination (High needs student only)',
						'value' => 'SDE'
					],
					[
						'name' => 'Voluntary Work',
						'value' => 'VOL'
					],
				]
			],
			'OutCode' => [
				'name' => 'Outcome code',
				'description' => 'The type of destination or progression outcome being recorded',
				'required' => false,
				'pattern' => '^\d{1,3}$',
				'pattern_error' => 'Integer, 1 to 3 characters.',
				'type' => 'number',
				'field' => 'select',
				'choices' => [
					[
						'name' => 'Traineeship',
						'value' => 1,
						'condition_key' => 'OutType',
						'condition_value' => 'EDU',
					],
					[
						'name' => 'Apprenticeship',
						'value' => 2,
						'condition_key' => 'OutType',
						'condition_value' => 'EDU',
					],
					[
						'name' => 'Supported Internship',
						'value' => 3,
						'condition_key' => 'OutType',
						'condition_value' => 'EDU',
					],
					[
						'name' => 'Other FE* (Full-time)',
						'value' => 4,
						'condition_key' => 'OutType',
						'condition_value' => 'EDU',
					],
					[
						'name' => 'Other FE* (Part-time)',
						'value' => 5,
						'condition_key' => 'OutType',
						'condition_value' => 'EDU',
					],
					[
						'name' => 'HE',
						'value' => 6,
						'condition_key' => 'OutType',
						'condition_value' => 'EDU',
					],
					[
						'name' => 'In paid employment for 16 hours or more per week',
						'value' => 1,
						'condition_key' => 'OutType',
						'condition_value' => 'EMP',
					],
					[
						'name' => 'In paid employment for less than 16 hours per week',
						'value' => 2,
						'condition_key' => 'OutType',
						'condition_value' => 'EMP',
					],
					[
						'name' => 'Self employed',
						'value' => 3,
						'condition_key' => 'OutType',
						'condition_value' => 'EMP',
					],
					[
						'name' => 'Self-employed for 16 hours or more per week',
						'value' => 4,
						'condition_key' => 'OutType',
						'condition_value' => 'EMP',
					],
					[
						'name' => 'Self-employed for less than 16 hours per week',
						'value' => 5,
						'condition_key' => 'OutType',
						'condition_value' => 'EMP',
					],
					[
						'name' => 'Gap year before starting HE',
						'value' => 1,
						'condition_key' => 'OutType',
						'condition_value' => 'GAP',
					],
					[
						'name' => 'Not in paid employment, looking for work and available to start work',
						'value' => 1,
						'condition_key' => 'OutType',
						'condition_value' => 'NPE',
					],
					[
						'name' => 'Not in paid employment, not looking for work and/or not available to start work (including retired)',
						'value' => 2,
						'condition_key' => 'OutType',
						'condition_value' => 'NPE',
					],
					[
						'name' => 'Other outcome - not listed',
						'value' => 1,
						'condition_key' => 'OutType',
						'condition_value' => 'OTH',
					],
					[
						'name' => 'Not reported',
						'value' => 2,
						'condition_key' => 'OutType',
						'condition_value' => 'OTH',
					],
					[
						'name' => 'Unable to contact learner',
						'value' => 3,
						'condition_key' => 'OutType',
						'condition_value' => 'OTH',
					],
					[
						'name' => 'Not known',
						'value' => 4,
						'condition_key' => 'OutType',
						'condition_value' => 'OTH',
					],
					[
						'name' => 'Supported independent living',
						'value' => 1,
						'condition_key' => 'OutType',
						'condition_value' => 'SDE',
					],
					[
						'name' => 'Independent living',
						'value' => 2,
						'condition_key' => 'OutType',
						'condition_value' => 'SDE',
					],
					[
						'name' => 'Learner returning home',
						'value' => 3,
						'condition_key' => 'OutType',
						'condition_value' => 'SDE',
					],
					[
						'name' => 'Long term residential placement',
						'value' => 4,
						'condition_key' => 'OutType',
						'condition_value' => 'SDE',
					],
					[
						'name' => 'Voluntary work',
						'value' => 1,
						'condition_key' => 'OutType',
						'condition_value' => 'VOL',
					]

				]
			],
			'OutStartDate' => [
				'name' => 'Outcome start date',
				'description' => 'The date that the learner commenced the destination or progression outcome recorded.',
				'required' => true,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'OutEndDate' => [
				'name' => 'Outcome end date',
				'description' => 'The date that the learner finished the destination or progression outcome recorded, if applicable.',
				'required' => false,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			],
			'OutCollDate' => [
				'name' => 'Outcome collection date',
				'description' => 'The date that the outcome data was collected from the learner.',
				'required' => true,
				'pattern' => '^[0-9][0-9][0-9][0-9]-([1][0-2]|[0][0-9])-([0-2][0-9]|[3][0-1])$',
				'pattern_error' => 'A valid date, using the date pattern YYYY-MM-DD',
				'field' => 'date', //YYYY-MM-DD
				'type' => 'date',
			]
		]
	],
];

// List of default learning delivery templates that are added into user's profile if user is assigned to standard with default learning delivery
$ilr_learning_deliveries = [
	'diploma_programme' => [
		'name' => 'Apprenticeship or 14-19 Diploma Programme',
		'template' => '{"LearnAimRef":"","open":false,"AimType":1,"LearnStartDate":null,"LearnPlanEndDate":null,"record":"diploma_programme","FundModelAllow":[10,25,35,70,81,82,99],"AimTypeAllow":[1],"AimTypeDisabled":true}'
	],
	'component_aim' => [
		'name' => '',
		'template' => ''
	],
	'efa_study' => [
		'name' => 'EFA Study Programme',
		'template' => '{"LearnAimRef":"","open":false,"AimType":4,"LearnStartDate":null,"LearnPlanEndDate":null,"record":"efa_study","FundModelAllow":[25,82],"AimTypeAllow":[4,5]}'
	],
	'adult_skills' => [
		'name' => 'Adult Skills',
		'template' => '{"LearnAimRef":"","open":false,"AimType":4,"LearnStartDate":null,"LearnPlanEndDate":null,"record":"adult_skills","FundModelAllow":[35,81],"AimTypeAllow":[4],"AimTypeDisabled":true}'
	],
	'esf' => [
		'name' => 'ESF',
		'template' => '{"LearnAimRef":"","open":false,"AimType":4,"LearnStartDate":null,"LearnPlanEndDate":null,"FundModel":70,"record":"esf","FundModelDisabled":true,"FundModelAllow":[70],"AimTypeAllow":[4],"AimTypeDisabled":true}'
	],
	'community_learning' => [
		'name' => 'Community Learning',
		'template' => '{"LearnAimRef":"","open":false,"AimType":4,"LearnStartDate":null,"LearnPlanEndDate":null,"FundModel":10,"record":"community_learning","FundModelDisabled":true,"FundModelAllow":[10],"AimTypeAllow":[4],"AimTypeDisabled":true}'
	],
	'non_funded' => [
		'id' => 'non_funded',
		'name' => 'Non Funded',
		'template' => '{"LearnAimRef":"","open":false,"AimType":4,"LearnStartDate":null,"LearnPlanEndDate":null,"FundModel":99,"record":"non_funded","FundModelDisabled":true,"FundModelAllow":[99],"AimTypeAllow":[4],"AimTypeDisabled":true}'
	]
];
?>