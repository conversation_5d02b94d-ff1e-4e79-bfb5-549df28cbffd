<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/smcr-committee-role",  function ($group) {

	// Get spcific SMCR committee role
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$committee_role = \Models\SmcrCommitteeRole::find($args["id"]);

		$response->getBody()->write(json_encode($committee_role));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'select'));

	// Update existing SMCR committee role
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$committee_role = \Models\SmcrCommitteeRole::find($args["id"]);
		$data = $request->getParsedBody();

		$committee_role->name = $data["name"];
		if (isset($data["description"])) {
			$committee_role->description = $data["description"];
		}
		$committee_role->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-roles', 'update'));


	// Disable SMCR committee role
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$committee_role = \Models\SmcrCommitteeRole::find($args["id"]);
		$committee_role->status = 0;
		$committee_role->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-roles', 'disable'));

	// Enable SMCR committee role
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$committee_role = \Models\SmcrCommitteeRole::find($args["id"]);
		$committee_role->status = 1;
		$committee_role->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-roles', 'disable'));

	// Get all list of enabled SMCR functions role
	$group->get('/all', function (Request $request, Response $response) {
		$query = \Models\SmcrCommitteeRole
			::where("status", true)
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'select'));


	// Add new SMCR committee role
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$committee_role = new \Models\SmcrCommitteeRole;

		$committee_role->name = $data["name"];
		$committee_role->smcr_committee_id = $data["smcr_committee_id"];
		if (isset($data["description"])) {
			$committee_role->description = $data["description"];
		}
		$committee_role->status = true;
		$committee_role->save();


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-roles', 'insert'));

	// Get paginated list of cimittee roles
   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\SmcrCommitteeRole
			::where('id', '>', 0)
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name",
				"Description" => "description",
			];


			$download_file_name = uniqid("smcr-committees.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-roles', 'select'));
});