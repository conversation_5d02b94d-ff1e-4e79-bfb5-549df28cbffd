<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/country-group",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$country = \Models\CountryGroup::find($args["id"]);
		$country->status = 0;
		$country->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$country = \Models\CountryGroup::find($args["id"]);
		$country->status = 1;
		$country->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$country = \Models\CountryGroup::find($args["id"]);

		$response->getBody()->write(json_encode($country));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$country = new \Models\CountryGroup();

		if (isset($data["name"]))
		{
			$country->name = $data["name"];
            $country->position =$data["position"];
            $country->status = 1;
            $country->save();
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'insert'));

	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$country = \Models\CountryGroup::find($args["id"]);
        $country->name = $data["name"];
        $country->position =$data["position"];
		$country->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		$data = \Models\CountryGroup::where("status",">",0)->get();

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionRegisterCheck());

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\CountryGroup::where("id", ">", "0");

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}


		if (isset($args["download"]) && $args["download"] == "/download")
		{
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
					"ID" => "id",
					"Group Name" => "name",
					"Position" => "position",
			];


			$download_file_name = uniqid("country-group.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
					);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		}
		else
		{
			$p = \APP\SmartTable::searchPaginate($params, $query);
			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


		}

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'select'));
});
