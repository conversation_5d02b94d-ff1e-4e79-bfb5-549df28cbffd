<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/custom-programme-status",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$custom_programme_status = \Models\CustomProgrammeStatus::find($args["id"]);
		$custom_programme_status->status = 0;
		$custom_programme_status->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$custom_programme_status = \Models\CustomProgrammeStatus::find($args["id"]);
		$custom_programme_status->status = 1;
		$custom_programme_status->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$custom_programme_status = \Models\CustomProgrammeStatus::find($args["id"]);

		$response->getBody()->write(json_encode($custom_programme_status));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$custom_programme_status = new \Models\CustomProgrammeStatus;

		$fields = [
			"name", "description", "order", "colour_code"
		];

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$custom_programme_status->$field = $data[$field]; // != '---' ? $data[$field] : null;
			}

		}

		$custom_programme_status->status = 1;
		$custom_programme_status->created_by = \APP\Auth::getUserId();
		$custom_programme_status->slug = \APP\Tools::safeName($data['name']);
		$custom_programme_status->updated_by = \APP\Auth::getUserId();

		$custom_programme_status->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'insert'));

	// Update competency
	$group->POST('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$custom_programme_status = \Models\CustomProgrammeStatus::find($args["id"]);

		 $fields = [
			"name", "description", "order", "colour_code"
		];

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$custom_programme_status->$field = $data[$field]; // != '---' ? $data[$field] : null;
			} else {
				if ($field == 'badge') {
					$custom_programme_status->$field = null;
				}
			}
		}

		$custom_programme_status->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$data = [];
		if (\APP\Auth::checkStructureAccess('system-setup-custom-programme-statuses', 'select')) {
			$data = \Models\CustomProgrammeStatus::where("status",">",0)->get();
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());



	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\CustomProgrammeStatus::where("id", ">", "0");

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name",
				// "Order" =>"order",
				"Description" => "description",
			];


			$download_file_name = uniqid("competencies.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'select'));
});