<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/jamboard",  function ($group) use($app) {

	$group->get('', function (Request $request, Response $response) use ($app) {

        $params = $request->getQueryParams();

        $jamboard = new \APP\Jamboard();

        if (isset($params["code"])){

            $jamboard->setTokenFromCode($params["code"]);

            $name = isset($_SESSION["event_name"]) ?  $_SESSION["event_name"] . " jamboard": "Jamboard";

            $jamboard_lm = $jamboard->createJamboard($name);

            $_SESSION["jamboard_join_url"] = $jamboard_lm->material->link;
            $_SESSION["jamboard_manager_url"] = $jamboard_lm->material->manager_link;
            $_SESSION["jamboard_resource_id"] = $jamboard_lm->id;

            ?>
            <script>
                window.name = "close";
                setTimeout(function(){ window.close(); }, 1000);
            </script>
            Jamboard created.
            <?php
        } else {
            if (isset($params["event_name"])) {
                $_SESSION["event_name"] = $params["event_name"];
            }

            setcookie("LMSSite", $this->get('settings')["LMSUrl"], time()+3600, "/");

            return $response->withStatus(302)->withHeader('Location', $jamboard->getAuthLink());
        }
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	$group->get('/retrieve', function (Request $request, Response $response) {
        $return_vars = [
            "join_url",
            "manager_url",
            "resource_id",
        ];
        $data = [];
        foreach ($return_vars as $key => $return_var) {
            if (isset($_SESSION["jamboard_" . $return_var])) {
                $data[$return_var] = $_SESSION["jamboard_" . $return_var];
            }
        }

        $response->getBody()->write(json_encode($data));
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));
});