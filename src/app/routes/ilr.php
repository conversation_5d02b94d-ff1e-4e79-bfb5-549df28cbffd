<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Illuminate\Database\Capsule\Manager as DB;


$app->group("/ilr",  function ($group) use ($app) {

	$group->get("/progression-outcome-code/all", function (Request $request, Response $response, array $args) {
		$query = \Models\IlrProgressionOutcomeCode
			::where('status', true)
			->get()
		;
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	$group->get("/progression-outcome-type/all", function (Request $request, Response $response, array $args) {
		$query = \Models\IlrProgressionOutcomeType
			::where('status', true)
			->get()
		;
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	$group->group('/learning-delivery', function($group) {
		// Get list of all outcomes, available to anyone.
		$group->get("/outcome/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryOutcome
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());

		// Get list of all Tailored Learning Outcomes, available to anyone.
		$group->get("/tailored-learning-outcome/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryTailoredLearningOutcome
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
			return
				$response
					->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());


		$group->get("/completion-status/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryCompletionStatus
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());

		$group->get("/aim-type/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryAimType
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());

		$group->get("/programme-type/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryProgrammeType
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());


		$group->get("/funding-model/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryFundingModel
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());


		$group->get("/financial-record-type/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryFinancialRecordType
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());

		$group->get("/financial-record-code/all", function (Request $request, Response $response, array $args) {
			$query = \Models\IlrLearningDeliveryFinancialRecordCode
				::where('status', true)
				->get()
			;
			$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getSessionCheck());

		$group->get("/look-up-title/{learn_aim_ref:.+}", function (Request $request, Response $response, array $args) {
			$learningdelivery = \Models\LarsLearningDelivery
				::where('LearnAimRef', $args["learn_aim_ref"])
				->first()
			;

			if ($learningdelivery) {
				$response = $response
					->write($learningdelivery->LearnAimRefTitle)
				;
			} else {
				$response = $response
					->withStatus(404)
				;
			}

			return
				$response
			;

        })->add(\APP\Auth::getSessionCheck());
        $group->get('/ilr_learning_delivery_fam_types',function(Request $request,Response $response){
            $query = \Models\IlrLearningDeliveryFamTypes::select('name','id')->get();
            $response->getBody()->write(json_encode($query));
			return $response->withHeader('Content-Type', 'application/json');

        })->add(\APP\Auth::getSessionCheck());
        $group->get('/ilr_learning_delivery_fam_codes',function(Request $request,Response $response){
            $query = \Models\IlrLearningDeliveryFamCodes::select('name','id')->get();
            $response->getBody()->write(json_encode($query));
			return $response->withHeader('Content-Type', 'application/json');
        })->add(\APP\Auth::getSessionCheck());
        $group->get('/ilr_learning_delivery_monitorings_codes',function(Request $request,Response $response){
            $query = \Models\IlrLearningDeliveryFamCodes::select('name','id','condition_value')->where('condition_value','LDM')->get();
            $response->getBody()->write(json_encode($query));
			return $response->withHeader('Content-Type', 'application/json');
        })->add(\APP\Auth::getSessionCheck());

    });
    $group->get('/prior_level',function(Request $request,Response $response){
        $query = \Models\IlrUserPriorAttainmentLevel::select('name','value as id')->get();
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getSessionCheck());
});
