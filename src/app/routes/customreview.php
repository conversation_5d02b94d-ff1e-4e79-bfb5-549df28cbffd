<?php

use APP\Controllers\CustomReportController;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
$app->group("/customreview",  function ($group) {

	// Get specific review
	$group->get("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$review = \Models\CustomReview
			::where('id', $args['id'])
			->where('status', 1)
			->first()
		;

		$response->getBody()->write(json_encode($review));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('review', 'select'));

	// Save tableState for custom review
	$group->put("/tablestate/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$review = \Models\CustomReview
			::where('id', $args['id'])
			->where('status', 1)
			->first()
		;
		$review->table_state = json_encode($data['table_state']);
		$review->save();

		return
			$response
				->withHeader('Content-Type', 'application/json')
				//->write(json_encode($review))
		;
	})->add(\APP\Auth::getStructureAccessCheck('review', 'update'));

	// List all added custom reviews
	$group->get("/{group:[\/a-z]*}/list", function (Request $request, Response $response, array $args) {
		$reviews = \Models\CustomReview
			::where('custom_reviews.group', $args['group'])
			->where('custom_reviews.status', 1)
			->withCount(['UnreadBatchReports' => function($query) {
				$query
					->where('batch_report_data.status', true)
					->where('batch_reports.status', true)
					->where('batch_report_data.unread', true)
				;
			}])
			->where(function ($query) {
				if (!\APP\Auth::isAdmin()) {
					$query
						->whereDoesntHave('UserVisibility', function ($query) {
							$query->whereHas('User', function ($query) {
								$query->validUser();
							});
						})
						->orWhereHas('UserVisibility', function ($query) {
							$query
								->where("user_custom_reviews.user_id", \APP\Auth::getUserId())
							;
						})
					;
				}
			})
			->get()
		;


		$response->getBody()->write(json_encode($reviews));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('review', 'select'));

	// List all custom filters for custom reviews
	$group->get("/{group:[\/a-z]*}/filter-list", function (Request $request, Response $response, array $args) {
		$filters = \Models\CustomReviewsFilter
			::where(function ($query) use ($args) {
				$query
					->where('group', $args['group'])
					->orWhereNull('group')
				;
			})
			->where('status', 1)
		;

		// Hide apprentix filters, if not apprentix
		if (!$this->get('settings')['licensing']['isApprentix']) {
			$filters = $filters->where('is_apprentix', false);
		}

		// Hide SMCR filters if not SMCR
		if (!$this->get('settings')['licensing']['isSMCR']) {
			$filters = $filters->where('is_smcr', false);
		}

		$filters = $filters->get();

		// https://bitbucket.org/emilrw/scormdata/issues/1140/volvo-general-business-one
		// If the version of the program is Open Elms for Business or Open Elms for Schools can we list the “ILR Data” category at the end of these dropdown lists.
		// Need to loop filters and replace "option_group_order" to 9999.
		if (
			$this->get('settings')["licensing"]['version'] == 'openelmsbusiness' ||
			$this->get('settings')["licensing"]['version'] == 'openelmsschools'
		) {
			foreach ($filters as $key => $filter) {
				if ($filter->option_group == 'ILR Data') {
					$filter->option_group_order = 9999;
				}
			}
		}

		$response->getBody()->write(json_encode($filters));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('review', 'select'));


	// Add new custom review
	$group->post("/{group:[\/a-z]*}/new", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$review =  new \Models\CustomReview;
		$review->name = $data['name'];
		$review->group = $args['group'];
		$review->key = 'custom-review';
		$review->filter_list = json_encode($data['filter_list']);
		$review->display_list = json_encode($data['display_list']);
		$review->coach_trainers = isset($data['coach_trainers']) ? true : false;
		$review->option_group = 'Custom';
		$review->created_by = \APP\Auth::getUserId();
		$review->status = true;
		$review->save();


		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($filters))
		;
	})->add(\APP\Auth::getStructureAccessCheck('review', 'insert'));


	// Update custom review
	$group->put("/{group:[\/a-z]*}/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$review =  \Models\CustomReview::find($args['id']);
		$review->name = $data['name'];
		//$review->group = $args['group'];
		//$review->key = 'custom-review';
		$review->filter_list = json_encode($data['filter_list']);
		$review->display_list = json_encode($data['display_list']);
		$review->coach_trainers = $data['coach_trainers'];
		$review->table_state = '';
		//$review->created_by = \APP\Auth::getUserId();
		//$review->status = true;
		$review->save();


		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($filters))
		;
	})->add(\APP\Auth::getStructureAccessCheck('review', 'update'));

	// retrieve custom review data
	$group->post("/list{option:[\/a-z]*}", function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$params_original = $params;
		$review_id = $params["search"]["review_id"];
		$query_id = 'customreviewList';
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		$option = isset($args["option"]) ? $args["option"] : "";

		$user_custom_review = \Models\UserCustomReview
			::where("custom_review_id", $review_id)
			->get()
			->count()
		;

		if (
			$user_custom_review > 0 &&
			!\APP\Auth::isAdmin()
		) {
			$current_user = \APP\Auth::getUserId();
			$current_user_custom_review = \Models\UserCustomReview
				::where("custom_review_id", $review_id)
				->where("user_id", $current_user)
				->get()
				->count()
			;
			if ($current_user_custom_review <= 0) {
				$data = [];
				$response->getBody()->write(json_encode($data));
				return $response->withHeader('Content-Type', 'application/json');
			}
		}

		session_write_close();

		switch($args["option"]) {
			case "/download":
				// $data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false, false, false, true);


				// $custom_review_settings = \Models\CustomReview::exportFields($custom_review, $data);
				// $download_file_name = $custom_review_settings['download_file_name'];
				// $export_fields = $custom_review_settings['export_fields'];
				// foreach ($export_fields as $key => $value) {
				// 	if ($value === "sub_departments_compact") {
				// 		$export_fields[$key] = "SubDepartmentsCompact";
				// 		break;
				// 	}
				// }

				// \APP\Tools::generateExcelDownload(
				// 	$data,
				// 	$export_fields,
				// 	$this->get('settings')["LMSTempPath"] . $download_file_name,
				// 	$params_original
				// );


				return \Models\CustomReview::streamDownloadFile(
					$review_id,
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);


				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
			break;
			case "/email":

				$custom_review = \Models\CustomReview
					::where('id', $review_id)
					->firstOrFail()
				;

				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					\APP\Tools::safeName($custom_review->name), // Template name
					['id'], // where to look for user's ID in this query
					$args
				);

			break;
		}

		$p = \APP\SmartTable::searchPaginate($params, $query, false, true, true);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('review', 'select'));

	// Disable custom review(do not delete it)
	$group->delete("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$review = \Models\CustomReview
			::where('id', $args['id'])
			->where('status', 1)
			->where('created_by', '>', 0) // 0 is created by ststem
			->firstOrFail()
		;


		$review->status = false;
		$review->save();

		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($reviews))
		;
	})->add(\APP\Auth::getStructureAccessCheck('review', 'disable'));

	$group->post("/download", function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		if (empty($params['id'])) {
			return \APP\Tools::returnCode($request, $response, 400, "Custom review ID is required");
		}

		$review = \Models\CustomReview
			::where('id', $params['id'])
			->where('status', 1)
			->first()
		;
		if (!$review) {
			return \APP\Tools::returnCode($request, $response, 404, "Custom review not found");
		}

		$user_download = new \Models\UserDownload();
		$user_download->name = $review['name'];
		$user_download->user_id = \APP\Auth::getUserId();
		$user_download->type = 'Legacy Custom Reports';
		$user_download->args = json_encode($review);
		$user_download->params = json_encode($params);
		$user_download->save();

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('review', 'select'));

});
