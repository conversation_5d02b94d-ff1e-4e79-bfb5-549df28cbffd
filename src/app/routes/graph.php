<?php

use APP\Controllers\GraphController;

$app->group("/graph", function ($group){
  //temporary route for power bi TODO: remove this route
  $group->post('/',GraphController::class.':create')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->post('/list',GraphController::class.':list')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/all',GraphController::class.':all')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->put('/{id:[0-9]+}',GraphController::class.':edit')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->put('/enable/{id:[0-9]+}',GraphController::class.':enable')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->put('/disable/{id:[0-9]+}',GraphController::class.':disable')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/{id:[0-9]+}',GraphController::class.':get')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/config',GraphController::class.':getConfig')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/render/{id:[0-9]+}/[{preview}]',GraphController::class.':renderGraph')->Add(\APP\Auth::getStructureAccessCheck(['review','custom-report-data'], 'select'));
  $group->post('/preview',GraphController::class.':getPreview')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
});
