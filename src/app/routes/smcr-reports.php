<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/smcr-reports",  function ($group) {

	// Get spcific SMCR report
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$report = \Models\SmcrReport
			::where('id', $args["id"])
			->first()
		;

		$response->getBody()->write(json_encode($report));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-smcr-reports', 'select'));


	// Disable SMCR report
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$report = \Models\SmcrReport::find($args["id"]);
		$report->status = 0;
		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-smcr-reports', 'disable'));

	// Enable SMCR report
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$report = \Models\SmcrReport::find($args["id"]);
		$report->status = 1;
		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-smcr-reports', 'disable'));

	// Delete SMCR report
	$group->delete('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$report = \Models\SmcrReport::find($args["id"]);
		$report->deleted_by = \APP\Auth::getUserId();
		$report->save();
		$report->delete();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-smcr-reports', 'disable'));

	// Get all list of SMCR report for logged in user!
	$group->get('/user-list', function (Request $request, Response $response) {
		$query = \Models\SmcrReport
			::where("status", true)
			->with('Type')
			->with('CertifiedBy')
			->where('user_id', \APP\Auth::getUserId())
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// update Report
	$group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$report = \Models\SmcrReport::find($args["id"]);
		if (isset($data['expire_at'])) {
			$report->expire_at = \Carbon\Carbon::parse($data['expire_at']);
			if ($report->completion_status != 'Archived') {
				// Also update users next expire at date
				$user = \Models\User::find($report->user_id);
				$user->next_completion_date = $report->expire_at;
				$user->save();
			}

			// Find if newer certificate exists and change its start_at
			$new_report = \Models\SmcrReport
				::where('id', '>', $report->id)
				->where('user_id', $report->user_id)
				->where('type_id', $report->type_id)
				->orderBy('id', 'asc')
				->first()
			;
			if ($new_report) {
				$new_report->start_at = $report->expire_at;
				$new_report->save();
			}
		}

		if (isset($data['start_at'])) {
			$report->start_at = \Carbon\Carbon::parse($data['start_at']);
		}

		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-smcr-reports', 'update'));

	// update Report, full edit!
	$group->POST('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$report = \Models\SmcrReport::find($args["id"]);
		$fields = [
			"start_at", "expire_at", "sign_off_date", "self_attested_date", "approved_authority", "comments", "completion_status"
		];

		\APP\Tools::setObjectFields($report, $fields, $data, true);

		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-smcr-reports', 'update'));

	// insert new Report, full edit!
	$group->POST('/new', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$report = new \Models\SmcrReport;
		$fields = [
			"user_id", "type_id", "start_at", "expire_at", "sign_off_date", "self_attested_date", "approved_authority", "comments", "completion_status"
		];

		\APP\Tools::setObjectFields($report, $fields, $data, true);

		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-smcr-reports', 'update'));


	$group->GET('/types/all', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$reports = [];
		if (\APP\Auth::checkStructureAccess('misc-permissions-smcr-reports', 'select')) {
			$reports = \Models\SmcrReportType::get();
		}

		$response->getBody()->write(json_encode($reports));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

});