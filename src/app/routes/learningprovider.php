<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/learningprovider",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningprovider = \Models\LearningProvider::find($args["id"]);
		$learningprovider->status = 0;
		$learningprovider->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-providers', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningprovider = \Models\LearningProvider::find($args["id"]);
		$learningprovider->status = 1;
		$learningprovider->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-providers', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningprovider = \Models\LearningProvider::find($args["id"]);

		$response->getBody()->write(json_encode($learningprovider));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-providers', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$learningprovider = new \Models\LearningProvider;

		$fields = [
			"company", "contactname", "email", "phone", "address"
		];

		foreach($fields as $field)
		{
			if (isset($data[$field]))
			{
				$learningprovider->$field = $data[$field];
			}
		}

		$learningprovider->status = 1;
		$learningprovider->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-providers', 'insert'));

	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$learningprovider = \Models\LearningProvider::find($args["id"]);

		$fields = [
			"company", "contactname", "email", "phone", "address"
		];

		foreach($fields as $field)
		{
			if (isset($data[$field]))
			{
				$learningprovider->$field = $data[$field];
			}
		}

		$learningprovider->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-providers', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$data = [];

		if (\APP\Auth::checkStructureAccess('system-setup-learning-providers', 'select')) {
			$data = \Models\LearningProvider
				::where("status", true)
				->get()
			;
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\LearningProvider::where("id", ">", "0");
		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-providers', 'select'));
});