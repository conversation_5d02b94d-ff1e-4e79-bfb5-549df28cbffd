<?php

use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Message\ResponseInterface as Response;
use Models\DocumentTemplate;
use Models\DocumentTemplateBinding;


$app->group("/document-template", function ($group) {
    $group->post('/', function (Request $request, Response $response) {
        $body=$request->getParsedBody();
        if(!empty($body['name']) && !empty($body['template']) && count($body['params'])>0){
            $documentTemplate=DocumentTemplate::create(['name'=>$body['name'],'template'=>$body['template'], 'is_custom_report' => $body['customReport']]);
            foreach ($body['params'] as  $value) {
                DocumentTemplateBinding::create(['document_template_id'=>$documentTemplate->id,
                                            'form_id'=>isset($value['forms'])?$value['forms']['id']:null,
                                            'form_field_id'=>isset($value['field'])?$value['field']['id']:null,
                                            'is_signature'=>$value['is_signature'],
                                            'form_signoff_role_id'=>isset($value['role'])?$value['role']['id']:null,
                                            'is_report'=>$value['is_report'],
                    'type'=>isset($value['type'])?$value['type']:null,

                                            'custom_report_id'=>isset($value['report_table'])?$value['report_table']['id']:null,
                                            'custom_report_field_id'=>isset($value['report_header'])?$value['report_header']['id']:null,
                                            'report_row_count'=>isset($value['report_row_count'])?$value['report_row_count']:0,
                                            'report_type'=>isset($value['report_type'])?$value['report_type']:null,
                                            'is_graph'=>$value['is_graph'],
                                            'custom_graph_id'=>isset($value['graph'])?$value['graph']['id']:null,
                                            'binding_field'=>$value['binding_field']['value']]);
            }
            $response->getBody()->write(json_encode($documentTemplate));

        }else{
            $response->getBody()->write(json_encode(["status"=>0,"message"=>"Please fill the mandatory fields- Name, Template And Associated Fields"]));
        }

        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));
    $group->post('/list', function (Request $request, Response $response) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        $query= DocumentTemplate::with('document_template_bindings.form')
        ->where("id", ">", 0)
        ;



        if (isset($params["search"]["custom_report"])) {
            if($params["search"]["custom_report"]=='1'){
                $query=$query->whereHas("document_template_bindings", function ($query) {
                    $query =  $query->where("type","is_graph")
                    ->orWhere("type","is_report");
                });
            }else{
                // $query=$query->whereHas("document_template_bindings", function ($query) {
                //     $query =  $query->where("type","is_form")
                //     ->orWhere("type","is_signature");
                // });
//                $query=$query->whereHas("document_template_bindings");
            }
            $query=$query->where('is_custom_report', $params["search"]["custom_report"]);
            unset($params["search"]["custom_report"]);
        }




        $data = \APP\SmartTable::searchPaginate($params, $query, false);
        //form list
        $form_list=[];
        foreach($data AS $dataval){
            foreach($dataval['document_template_bindings'] AS $key=>$document_template_binding){
                if(isset($document_template_binding["form"])){
                    $form_list[]=$document_template_binding["form"]["name"];
                }
            }
            $dataval["forms"]=array_values(array_unique($form_list));
            $form_list=[];
        }
        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

    $group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $documentTemplate = DocumentTemplate::find($args["id"]);
        $documentTemplate->status = 0;
        $documentTemplate->save();
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

    $group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $documentTemplate  = DocumentTemplate::find($args["id"]);
        $documentTemplate->status = 1;
        $documentTemplate->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

    $group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        if (
            \APP\Auth::isAdmin() ||
            \APP\Auth::checkStructureAccess(['custom-report-data'], 'select') ||
            (
                \APP\Auth::getUserId() &&
                \APP\Controllers\AssignmentController::access('users', \APP\Auth::getUserId(), 'document_templates', $args['id'])
            )
        ) {
            $documentTemplate=DocumentTemplate
                ::with(['document_template_bindings'=>function($query){
                    $query
                        ->with('form')
                        ->with('form_field.Field')
                        ->where(function($query) {
                            $query
                                ->whereNull("custom_report_id")
                                ->orWhere(function($query) {
                                    $query
                                        ->whereHas("report")
                                    ;
                                })
                            ;
                        })
                    ;
                }])
                ->find($args['id'])
            ;
            $response->getBody()->write(json_encode($documentTemplate));
		    return $response->withHeader('Content-Type', 'application/json');
        } else {
            \APP\Tools::returnCode($request, $response, '401');
        }
    })->add(\APP\Auth::getSessionCheck());

    $group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $body= $request->getParsedBody();
        if(!empty($body['name']) && !empty($body['template']) && count($body['params'])>0){
            $documentTemplate=DocumentTemplate::find($args['id']);
            $documentTemplate->name=$body['name'];
            $documentTemplate->template=$body['template'];
            $documentTemplate->save();
            $bindings=[];
            foreach ($body['params'] as  $value) {
                $bindings[]=$value['binding_field']['value'];
                DocumentTemplateBinding::updateOrCreate(
                    ['document_template_id'=>$documentTemplate->id,'binding_field'=>$value['binding_field']['value']],
                    ['document_template_id'=>$documentTemplate->id,
                    'form_id'=>isset($value['forms'])?$value['forms']['id']:null,
                    'form_field_id'=>isset($value['field'])?$value['field']['id']:null,
                    'form_signoff_role_id'=>isset($value['role'])?$value['role']['id']:null,
                    'type'=>isset($value['type'])?$value['type']:null,
                    'is_signature'=>$value['is_signature'],
                    'is_report'=>$value['is_report'],
                    'custom_report_id'=>isset($value['report_table'])?$value['report_table']['id']:null,
                    'custom_report_field_id'=>isset($value['report_header'])?$value['report_header']['id']:null,
                    'report_row_count'=>isset($value['report_row_count'])?$value['report_row_count']:0,
                    'report_type'=>isset($value['report_type'])?$value['report_type']:null,
                    'is_graph'=>$value['is_graph'],
                    'custom_graph_id'=>isset($value['graph'])?$value['graph']['id']:null,
                    'binding_field'=>$value['binding_field']['value']]
                );
            }
            DocumentTemplateBinding::whereNotIn('binding_field', $bindings)->where('document_template_id', $documentTemplate->id)->delete();
            $response->getBody()->write(json_encode($documentTemplate));

        }else{
            $response->getBody()->write(json_encode(["status"=>0,"message"=>"Please fill the mandatory fields- Name, Template And Associated Fields"]));
        }
		return $response->withHeader('Content-Type', 'application/json');
    });
    $group->get('/list', function (Request $request, Response $response) {

        $data= DocumentTemplate::enabled()
            ->with('document_template_bindings.form')
            ->whereHas("document_template_bindings")
            ->get();
        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));



    $group->put('/form-duplicate/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		try{
            $form = \Models\DocumentTemplate::find($args["id"]);
            $slug = \APP\Tools::safeName($form->name);

            $form_template_clone = $form->replicate()->fill(
                [
                    'name' => "copy of ".$form->name
                ]
            );
            $form_template_clone->save();



            $bindings=DocumentTemplateBinding::where("document_template_id",$form->id)->get();
            /*Replicate Form Field*/
            foreach ($bindings as  $value) {
                $document_bindings_clone = $value->replicate()->fill(
                    [
                        'document_template_id' => $form_template_clone->id,
                    ]
                );
                $document_bindings_clone->save();
            }

            $response->getBody()->write(
                json_encode(
                    [
                        'l_name' => $form_template_clone->name,
                        'l_id' => $form_template_clone->id,
                    ]
                )
            );
            return $response->withHeader('Content-Type', 'application/json');

        }catch(\Exception $e){
            print_r($e->getMessage());
        }
	});

    // Update DocumentTemplate property
    $group->put("/{id:[0-9]+}/property/{property:[\/a-z_-]*}", function (Request $request, Response $response, array $args) {
        $data = $request->getParsedBody();

        $entry = \Models\DocumentTemplate::find($args['id']);
        if (isset($entry[$args['property']])) {
            $entry[$args['property']] = $data['value'];
        }
        $entry->save();


        return
            $response
        ;
    })->add(\APP\Auth::getStructureAccessCheck('custom-report-data', 'update'));
});
