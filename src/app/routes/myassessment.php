<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;


$app->group("/myassessment",  function ($group) {

	// Update task status and comment.
	$group->put("/task/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$task = \Models\Assessment\Task::find($args["id"]);

		if (
			!(
				$task->user_id == \APP\Auth::getUserId() ||
				$task->reporter_id == \APP\Auth::getUserId() ||
				\APP\Auth::isManagerOf($task->user_id) ||
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners()
			)
		) {
			$response->getBody()->write("access denied");
			$response = $response->withStatus(403);
			return $response;
		}

		if (isset($data["new_comment"])) {
			$comment = new \Models\Assessment\TaskComment;
			$comment->assessment_task_id = $task->id;
			$comment->user_id = \APP\Auth::getUserId();
			$comment->comment = $data["new_comment"];
			$comment->save();
		}

		if (isset($data["status"]) && ($task->status != $data["status"])) {
			$new_status_str = "";

			switch(intval($data["status"]))
			{
				case 2:
					$new_status_str = "%%assessment_task_status__deferred%%";
					break;
				case 3:
					$new_status_str = "%%assessment_task_status__in_progress%%";
					break;
				case 4:
					$new_status_str = "%%assessment_task_status__completed%%";
					break;
				case 1:
				default:
					$new_status_str = "%%assessment_task_status__not_started%%";
					break;
			}
			$translator = \APP\Templates::getTranslator();
			$new_status_str = $translator->replaceVersionLabels($new_status_str);

			$comment = new \Models\Assessment\TaskComment;
			$comment->assessment_task_id = $task->id;
			$comment->user_id = \APP\Auth::getUserId();
			$comment->comment = "Status changed to '{$new_status_str}'";
			$comment->save();
		}

		$fields = [
			"reporter_id", "status"
		];

		\APP\Tools::setObjectFields($task, $fields, $data);

		$task->save();

		// check if any more assesments left that are not completed, if 0, run course refresh logic on this!
		$assesments = \Models\Assessment\Task
			::where('status', '!=', 4)
			->where('user_id', $task->user_id)
			->where('course_id', $task->course_id)
			->where('assessment_data_id', $task->assessment_data_id)
			->count()
		;
		if ($assesments == 0) {
			$send_email = \APP\Tools::getConfig('sendRefreshEmail');
			\APP\Refresh::refreshResults(false, [$task->user_id], [$task->course_id], $send_email, false, 'Myassessment task update, if no tasks are left, refresh learning.');
		}

		/*
			$join
				->on("assessment_data.user_id", "=", "learning_results.user_id")
				->on("assessment_data.course_id", "=", "learning_results.learning_module_id")
				->where("assessment_data.status", "<>", 4)
				->whereNotNull("assessment_data.submitted_at")
			;
		*/

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-task-assesments', 'trainee-learning-results'], 'update'));

	$group->get('/completeall/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$tasks = \Models\Assessment\Task
			::where("assessment_tasks.assessment_data_id", "=", $args["id"])
			->where('status', '<>', '4')
			->where(function($query) {
				// As I understand, logic below will filter out current user or user you are manager of, if you are a admin, you should see all assesments.
				if (
					!\APP\Auth::isAdmin() &&
					!\APP\Auth::accessAllLearners()
				) {
					$query
						->orWhere("assessment_tasks.user_id", "=", \APP\Auth::getUserId())
						->orWhere("assessment_tasks.reporter_id", "=", \APP\Auth::getUserId())
						->orWhereIn("assessment_tasks.user_id", function($query) {
							$query
								->select("user_id")
								->from("manager_users")
								->where("manager_id", "=", \APP\Auth::getUserId())
								->whereNull('manager_users.deleted_at')
							;
						})
					;
				}
			})
			->get();

		$refresh_task = new \stdClass();

		foreach($tasks as $task) {

			$translator = \APP\Templates::getTranslator();
			$new_status_str = $translator->replaceVersionLabels("%%assessment_task_status__completed%%");

			$comment = new \Models\Assessment\TaskComment;
			$comment->assessment_task_id = $task->id;
			$comment->user_id =  \APP\Auth::getUserId();
			$comment->comment = "Status changed to '" . $new_status_str . "'";
			$comment->save();

			$task->status = 4;
			$task->save();

			$refresh_task->user_id = $task->user_id;
			$refresh_task->course_id = $task->course_id;
			$refresh_task->assessment_data_id = $task->assessment_data_id;
		}


		// check if any more assesments left that are not completed, if 0, run course refresh logic on this!
		$assesments = \Models\Assessment\Task
			::where('status', '!=', 4)
			->where('user_id', $refresh_task->user_id)
			->where('course_id', $refresh_task->course_id)
			->where('assessment_data_id', $refresh_task->assessment_data_id)
			->count()
		;
		if ($assesments == 0) {
			$send_email = \APP\Tools::getConfig('sendRefreshEmail');
			\APP\Refresh::refreshResults(false, [$refresh_task->user_id], [$refresh_task->course_id], $send_email, false, 'Myassessment tasks, complete all pressed and no tasks left.');
		}

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-task-assesments', 'trainee-learning-results'], 'update'));

	$group->get("/task/{id:[0-9]+}", function (Request $request, Response $response, $args) {

		$task = \Models\Assessment\Task
			::select(
					"assessment_tasks.id",
					"assessment_tasks.user_id",
					"assessment_questions.title",
					"learning_modules.name",
					"assessment_answers.task_description",
					"assessment_tasks.created_at",
					"assessment_answers.actionByID",
					"assessment_tasks.status",
					"assessment_answers.score",
					"assessment_tasks.reporter_id",
					"assessment_tasks.user_comment",
					"learning_modules.is_skillscan AS is_skillscan",
					"assessment_answers.order as order"
			)
			->with("user", "comments.user")
			->with(['Files' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->where("assessment_tasks.id", "=", $args["id"])
			->join("learning_modules", function($join) {
				$join
					->on("assessment_tasks.course_id", "=", "learning_modules.id")
				;
			})
			->join("assessment_questions", function($join) {
				$join
					->on("assessment_tasks.question_id", "=", "assessment_questions.question_id")
					->on("assessment_tasks.course_id", "=", "assessment_questions.course_id")
				;
			})
			->join("assessment_answers", function($join) {
				$join
					->on("assessment_tasks.question_id", "=", "assessment_answers.question_id")
					->on("assessment_tasks.answer_id", "=", "assessment_answers.answer_id")
					->on("assessment_tasks.course_id", "=", "assessment_answers.course_id")
				;
			})
			->firstOrFail()
		;

		if (!
			(
				$task->user_id == \APP\Auth::getUserId() ||
				$task->reporter_id == \APP\Auth::getUserId() ||
				\APP\Auth::isManagerOf($task->user_id) ||
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners()
			)
		)
		{
			$response->getBody()->write("access denied");
			$response = $response->withStatus(403);
			return $response;
		}

		$response->getBody()->write($task->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-task-assesments', 'trainee-learning-results'], 'select'));

	$group->post('/list/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\Assessment\Task
			::select(
				"assessment_tasks.*",
				"assessment_questions.title",
				"learning_modules.name AS learning_name",
				"learning_modules.is_skillscan AS is_skillscan",
				"assessment_answers.task_description",
				"assessment_answers.actionByID",
				"assessment_answers.score",
				"assessment_answers.order as order" // For skill scan, order is the score!
			)

			// https://bitbucket.org/emilrw/scormdata/issues/1018/smcr-view-task-assessment-changes
			// 2) This is not just an SMCR task but across all versions - if a task is set to Completed/Resolved then the Priority level is set to zero
			->selectRaw("
				(
					CASE
						WHEN
							assessment_tasks.status = 4
						THEN
							0
						ELSE
							assessment_answers.score
					END
				) as assessment_answers_score
			")
			->where("assessment_tasks.assessment_data_id", "=", $args["id"])
			->where(function($query) {
				// As I understand, logic below will filter out current user or user you are manager of, if you are a admin, you should see all assesments.
				if (
					!\APP\Auth::isAdmin() &&
					!\APP\Auth::accessAllLearners()
				) {
					$query
						->orWhere("assessment_tasks.user_id", "=", \APP\Auth::getUserId())
						->orWhere("assessment_tasks.reporter_id", "=", \APP\Auth::getUserId())
						->orWhereIn("assessment_tasks.user_id", function($query) {
							$query
								->select("user_id")
								->from("manager_users")
								->where("manager_id", "=", \APP\Auth::getUserId())
								->whereNull('manager_users.deleted_at')
							;
						})
					;
				}
			})
//			->where("assessment_tasks.status", "<>", 4)
			->join("learning_modules", function($join) {
				$join
					->on("assessment_tasks.course_id", "=", "learning_modules.id")
				;
			})
			->join("assessment_questions", function($join) {
				$join
					->on("assessment_tasks.question_id", "=", "assessment_questions.question_id")
					->on("assessment_tasks.course_id", "=", "assessment_questions.course_id")
				;
			})
			->join("assessment_answers", function($join) {
				$join
					->on("assessment_tasks.question_id", "=", "assessment_answers.question_id")
					->on("assessment_tasks.answer_id", "=", "assessment_answers.answer_id")
					->on("assessment_tasks.course_id", "=", "assessment_answers.course_id")
				;
			})
		;

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManager() ||
			\APP\Auth::accessAllLearners()
		) {
			$query
				->with("user")
				->join("users", function($join) {
					$join
						->on("assessment_tasks.user_id", "=", "users.id")
					;
				})
			;
		}

		if (isset($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			if (isset($params["search"]["assessment_answers__actionByID"])) {
				if ($params["search"]["assessment_answers__actionByID"] == 0) {
					$query->whereNotIn("assessment_answers.actionByID", [1, 2, 4]);
					unset($params["search"]["assessment_answers__actionByID"]);
				}
			}
			if (isset($params["search"]["assessment_tasks__status"]))
			{
				if ($params["search"]["assessment_tasks__status"] == 0)
				{
					$query->whereNotIn("assessment_tasks.status", [2, 3, 4]);
					unset($params["search"]["assessment_tasks__status"]);
				}
			}
		}
		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-task-assesments', 'trainee-learning-results'], 'select'));

});