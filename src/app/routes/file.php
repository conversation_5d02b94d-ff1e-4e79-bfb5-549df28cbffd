<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;

$app->group("/file",  function ($group) {


	$group->post("/new", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (
			isset($_FILES['file']) // there is file
			&& $_FILES['file']['name'] // and file has a name
		) {

			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSFilePath"], true);
			$upload = new \Upload\File('file', $storage);

			$hash = bin2hex(random_bytes(16));

			$file_name = $upload->getNameWithExtension();
			$extension = $upload->getExtension();
			$upload->setName($hash);

			$fileSizeValidation = new \Upload\Validation\Size('800M');
			$fileTypeValidation = new \Upload\Validation\Mimetype(\APP\Tools::documentMime());
			$fileExtValidation = new \Upload\Validation\Extension(\APP\Tools::allowExtensions());

			$fileTypeValidation->setMessage("Invalid file type: " . $upload->getMimetype());
			$upload->addValidations([
				$fileTypeValidation,
				$fileSizeValidation,
				$fileExtValidation
			]);

			// basic security checks for learner
			if (
				\APP\Auth::isLearner() &&
				isset($data['table_name']) &&
				$data['table_name'] &&
				isset($data['table_row_id'])
			) {
				$can_add = true;
				switch ($data['table_name']) {
					case 'learning_results_comments':
						$comment = \Models\LearningResultsComment
							::where('id', $data['table_row_id'])
							->where('comment_by_user_id', \APP\Auth::getUserId())
							->first()
						;
						if (!$comment) {
							$can_add = false;
						}
						if (!\APP\Auth::attachFilesToComments()) {
							$can_add = false;
						}
						break;
				}
				if (!$can_add) {
					return \APP\Tools::returnCode($request, $response, '403');
				}
			}


			try {
				$upload->upload();
				// Hard-coded/initial logic to check maximum file size per table_row_id, for specific tables
				if (
					isset($data['table_name']) &&
					$data['table_name'] == 'email_templates' &&
					isset($data['table_row_id'])
				) {
					// Get limit for email attachments
					$emailAttachmentsSize = \APP\Tools::getConfig('emailAttachmentsSize');

					$total_file_size = 0; // MB
					$total_file_size = $total_file_size + number_format($upload->getSize() / 1048576, 2);

					//
					$existing_files = \Models\File
						::where('table_name', 'email_templates')
						->where('table_row_id', $data['table_row_id'])
						->get()
					;
					foreach ($existing_files as $key => $existing_file) {
						$filename = $this->get('settings')["LMSFilePath"] . $existing_file->hash . '.' . $existing_file->extension;
						if (is_file($filename)) {
							$total_file_size = $total_file_size + number_format(filesize($filename) / 1048576, 2);
						}
					}

					if ($total_file_size > $emailAttachmentsSize) {
						return \APP\Tools::returnCode($request, $response, '403', 'Combined attachment size exceeds ' . $emailAttachmentsSize . 'MB for this Template');
					}
				}

				$file = new \Models\File;
				$file->table_row_id = isset($data['table_row_id']) ? $data['table_row_id'] : null;
				$file->table_name = isset($data['table_name']) ? $data['table_name'] : null;
				$file->group = isset($data['group']) ? $data['group'] : null;
				$file->file = $file_name;
				$file->hash = $hash;
				$file->extension = $extension;
				$file->added_by = \APP\Auth::getUserId();
				$file->added_for = isset($data['added_for']) ? $data['added_for'] : null;
				$file->save();

				$response
					->getBody()
					->write($upload->getNameWithExtension())
				;
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $upload->getErrors();
				return \APP\Tools::returnCode($request, $response, '500', implode("\n", $errors));
			}

			return $response;
		}
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-file-uploads'], 'insert'));

	$group->delete("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$file = \Models\File::find($args['id']);
		if (
			$file &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				$file->added_by == \APP\Auth::getUserId() ||
				\APP\Auth::isManagerOf($file->added_by)
			)
		) {

			if (
				\APP\Auth::isLearner() &&
				$file->table_name == 'learning_results_comments' &&
				!\APP\Auth::learnerCanDeleteFilesFromComments()
			) {
				return \APP\Tools::returnCode($request, $response, '403');
			}

			$filename = $this->get('settings')["LMSFilePath"] . $file->hash . '.' . $file->extension;
			if (is_file($filename)) {
				unlink($filename);
			}
			$file->delete();
		} else {
			return \APP\Tools::returnCode($request, $response, '404');
		}


		return $response;

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-file-uploads', 'lessons-and-learning-resources'], 'disable'));

	// Get file, hash is to identify file and file name is just for saving file purposes.
	$group->get("/{hash}/{file}", function (Request $request, Response $response, array $args) {

		if ($this->get('settings')["DisableFileDownload"]) {
			return \APP\Tools::returnCode($request, $response, '403');
		}

		$file = \Models\File
			::where('hash', $args['hash'])
			->where('status', true)
			->where('file', $args['file'])
			->first()
		;
		if ($file) {
			$filename = $this->get('settings')["LMSFilePath"] . $file->hash . '.' . $file->extension;
		}

		if (
			$file &&
			is_file($filename)
		) {

			// Need to check if user requesting this file is assigned to schedule event and file is attached against schedule event
			$schedule = false;
			if (\APP\Tools::getConfig('enableSchedule')) {
				// check if there is schedule this file is attached to and current user is on same schedule
				$schedule = \Models\Schedule
					::whereIn('id',
						\Models\File
							::select('table_row_id')
							->where('id', $file->id)
							->where('table_name', 'schedules')
							->get()
					)
					->where('status', true)
					->where(function ($query) {
						$query
							->whereIn('id',
								\Models\ScheduleLink
									::select('schedule_id')
									->where('type', 'users')
									->where('link_id', \APP\Auth::getUserId())
									->get()
							)
							->orWhereIn('id',
								\Models\ScheduleLink
									::select('schedule_id')
									->where('type', 'managers')
									->where('link_id', \APP\Auth::getUserId())
									->get()
							)
						;
					})
					->first()
				;
			}

			// Check if file is linked to lesson that user is assigned to!
			$lesson = \Models\LearningModule
				::whereIn('id',
					\Models\File
						::select('table_row_id')
						->where('id', $file->id)
						->where('table_name', 'learning_modules')
						->get()
				)
				->where('status', true)
				->where('is_course', 1)
				->whereIn('id',
					\Models\UserLearningModule
						::select('learning_module_id')
						->where('user_id', \APP\Auth::getUserId())
						->get()
				)
				->first()
			;

			if (
				\APP\Auth::isManager() ||
				\APP\Auth::isManagerOf($file->added_for) ||
				\APP\Auth::isAdmin() ||
				\APP\Auth::isAdmin(true) ||
				\APP\Auth::accessAllLearners() ||
				$file->added_for == \APP\Auth::getUserId() ||
				$file->added_by == \APP\Auth::getUserId() ||
				$schedule ||
				$lesson
			) {
				$fileStream = new OpenStream($filename, 'r');
				$finfo = finfo_open(FILEINFO_MIME_TYPE);

				$response = $response
					//->withHeader('Content-Transfer-Encoding', 'Binary')
					//->withHeader('Content-Disposition', 'attachment; filename="' . basename($filename) . '"')
					//->withHeader('Expires', '0')
					//->withHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
					//->withHeader('Pragma', 'public')
					->withHeader('Content-Length', filesize($filename))
					//->withHeader('Content-Type', FILEINFO_MIME_TYPE)

					//->withHeader('Content-Type', 'application/force-download')
					//->withHeader('Content-Type', 'application/octet-stream')
					//->withHeader('Content-Type', 'application/download')
					//->withHeader('Content-Description', 'File Transfer')
					->withHeader('Content-Type', finfo_file($finfo, $filename))
					->withBody($fileStream)
				;
			} else {
				return \APP\Tools::returnCode($request, $response, 403, 'You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.');
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 404);
		}
		return $response;
	})->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));


	// Retrieve list of files for specific article
	$group->get("/list/{table_name}/{table_row_id:[0-9]+}[/{group}]", function (Request $request, Response $response, array $args) {
		$files = \Models\File
			::where('table_name', $args['table_name'])
			->where('table_row_id', $args['table_row_id'])
			->where('status', true)
			->with(['AddedBy' => function($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id'
					)
					->with('role')
				;
			}])
		;

		if (!empty($args['group'])) {
			$files = $files
				->where('group', $args['group'])
			;
		}

		if (\APP\Auth::isLearner()) {
			$files = $files
				->where(function ($query) {
					$query
						->where('added_by', \APP\Auth::getUserId())
						->orWhere('added_for', \APP\Auth::getUserId())
					;
				})
			;
		}

		$files = $files
			->get()
		;
		$response->getBody()->write(json_encode($files));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));

});
