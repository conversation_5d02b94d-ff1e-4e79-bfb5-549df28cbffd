<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/taskassessmentreports",  function ($group) {

	$group->put('/updatetasks', function (Request $request, Response $response) {
		$tasks = $request->getParsedBody();
		foreach($tasks as $task) {
			$asm_task = \Models\Assessment\Task::find($task["id"]);
			$asm_task->status = $task["status"];
			$asm_task->reporter_id = $task["reporter_id"];
			$asm_task->save();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-resources-data', 'update'));


	$group->post('/audit/list/{assessment_id:[0-9]+}{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'taskassessmentreportsauditList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);


		switch($args["option"]) {
			case "/download":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::returnDownloadFile(
					'taskassessmentreportsaudit',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);

				break;


			case "/powerbi":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::exportToPowerBi(
					'taskassessmentreportsaudit',
					$params["export_config"],
					$data,
					$response
				);
			break;

			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'task assessment audit reports tpl', // Template name
					['user', 'id'], // where to look for user's ID in this query
					$args
				);
				break;
		}




		// group response by year/month if request is for making a chart.
		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$query->groupBy(DB::raw('
				year, month
			'));
			$p = $query->get();
		} else {
			// fire pagination
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	});

	$group->post('/latest/list/{assessment_id:[0-9]+}{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'taskassessmentreportslatestList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);


		switch($args["option"]) {
			case "/download":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::returnDownloadFile(
					'taskassessmentreportslatest',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);

				break;

			case "/powerbi":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::exportToPowerBi(
					'taskassessmentreportslatest',
					$params["export_config"],
					$data,
					$response
				);
			break;

			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'task assessment latest reports tpl', // Template name
					['user', 'id'], // where to look for user's ID in this query
					$args
				);
				break;
		}


		// group response by year/month if request is for making a chart.
		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$query->groupBy(DB::raw('
				year, month
			'));
			$p = $query->get();
		} else {
			// fire off pagination
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	});

	$group->post('/todo/list/{assessment_id:[0-9]+}{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'taskassessmentreportstodoList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);


		switch($args["option"]) {
			case "/download":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::returnDownloadFile(
					'taskassessmentreportstodo',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);

				break;

			case "/powerbi":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::exportToPowerBi(
					'taskassessmentreportstodo',
					$params["export_config"],
					$data,
					$response
				);
			break;

			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'task assessment outstanding reports tpl', // Template name
					['user', 'id'], // where to look for user's ID in this query
					$args
				);

				break;
		}

		// group response by year/month if request is for making a chart.
		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$query->groupBy(DB::raw('
				year, month
			'));
			$p = $query->get();
		} else {
			// pagination
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	});

})->add(\APP\Auth::getStructureAccessCheck('review-learning-resources-data', 'select'));