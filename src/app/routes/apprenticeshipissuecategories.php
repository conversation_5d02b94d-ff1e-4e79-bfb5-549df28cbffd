<?php

use Models\ApprenticeshipIssuesLearningModules;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/apprenticeshipissuecategories",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$apprenticeshipcategory = \Models\ApprenticeshipIssueCategories::find($args["id"]);

		$response->getBody()->write(json_encode($apprenticeshipcategory));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$apprenticeshipcategory = \Models\ApprenticeshipIssueCategories::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$apprenticeshipcategory->name = $data["name"];
		}

		if (isset($data["standard_id"])) {
			$apprenticeshipcategory->standard_id = $data["standard_id"];
		}

		if (isset($data["minimum_required_credits"])) {
			$apprenticeshipcategory->minimum_required_credits = $data["minimum_required_credits"];
		}

		$apprenticeshipcategory->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Remove category
	//		find all modules, look if same modules are in other categories, if they are not, remove association, detach from users, remove issues and remove category.
	$group->delete("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		// get to be deleted category
		$apprenticeshipcategory = \Models\ApprenticeshipIssueCategories::find($args["id"]);

		// Get all users assigned to standard
		$standard_users = \Models\ApprenticeshipStandardUser
			::where('standard_id', '=', $apprenticeshipcategory->standard_id)
			->get()
		;

		// get all modules from to be deleted category if they do not exist in other categories.
		$category_modules = \Models\ApprenticeshipIssuesLearningModules
			// Get all modules in specified category
			::whereIn('id',
				\Models\ApprenticeshipIssuesLearningModules
					::whereIn('apprenticeship_issues_id',
						\Models\ApprenticeshipIssues
							::where('issue_category_id', '=', $args['id'])
							->where('status', '=', 1)
							->whereIn('issue_category_id',
								\Models\ApprenticeshipIssueCategories
									::where('standard_id', '=', $apprenticeshipcategory->standard_id)
									->where('status', '=', 1)
									->select('id')
									->get()
							)
							->select('id')
							->get()
					)
					->select('id')
					->get()
			)
			// Exclude all modules that are in other categories for same standard
			->whereNotIn('learning_modules_id',
				\Models\ApprenticeshipIssuesLearningModules
					::whereIn('apprenticeship_issues_id',
						\Models\ApprenticeshipIssues
							::where('issue_category_id', '!=', $args['id'])
							->where('status', '=', 1)
							->whereIn('issue_category_id',
								\Models\ApprenticeshipIssueCategories
									::where('standard_id', '=', $apprenticeshipcategory->standard_id)
									->where('status', '=', 1)
									->select('id')
									->get()
							)
							->select('id')
							->get()
					)
					->select('learning_modules_id')
					->get()
			)

			->orderBy('learning_modules_id')
			->get()
		;
    ApprenticeshipIssuesLearningModules::deleteModuleUserWorkflow(false,$category_modules->pluck('learning_modules_id'),$args['id'],$apprenticeshipcategory->standard_id);
		//detach modules from users
		foreach ($standard_users as $key => $standard_user) {
			$user = \Models\User::find($standard_user->user_id);
			foreach ($category_modules as $key => $category_module) {
				$module_ids = \APP\Learning::getAllModuleIds([$category_module->learning_modules_id]);
				\Models\UserLearningModule::unlinkResources($user->id, $module_ids, 'programme - remove outcome - de-assign outcome resources - ' . $apprenticeshipcategory->id);
			}
		}

		// Delete Modules from issue
		foreach ($category_modules as $key => $category_module) {
			\Models\ApprenticeshipIssuesLearningModules
				::where('id', '=', $category_module->id)
				->delete()
			;
		}

		// Disable category issues
		$category_issues = \Models\ApprenticeshipIssues
			::where('issue_category_id', '=', $args['id'])
			->update(['status' => 0]);
		;

		// disable category
		$apprenticeshipcategory->status = 0;
		$apprenticeshipcategory->save();


		return
			$response
				->withHeader('Content-Type', 'application/json')
				//->write(json_encode($category_modules))
		;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$apprenticeshipcategory = \Models\ApprenticeshipIssueCategories::find($args["id"]);
		$apprenticeshipcategory->status = 0;
		$apprenticeshipcategory->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$apprenticeshipcategory = \Models\ApprenticeshipIssueCategories::find($args["id"]);
		$apprenticeshipcategory->status = 1;
		$apprenticeshipcategory->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$apprenticeshipcategory = new \Models\ApprenticeshipIssueCategories;

		if (isset($data["name"])) {
			$apprenticeshipcategory->name = $data["name"];
		}


		if (isset($data["minimum_required_credits"])) {
			$apprenticeshipcategory->minimum_required_credits = $data["minimum_required_credits"];
		}

		if (isset($data["standard_id"])) {
			$apprenticeshipcategory->standard_id = $data["standard_id"];
		}

		$apprenticeshipcategory->status = 1;
		$apprenticeshipcategory->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));

	$group->get('/all', function (Request $request, Response $response) {

		$data = \Models\ApprenticeshipIssueCategories
			::with("Standard")
			->where("status",">",0)
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\ApprenticeshipIssueCategories::with(['standard' => function($query) {

			}])
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Apprenticeship Issue Category Name" => "name",
				"Standard Name" => "apprenticeship_standards.name",
			];


			$download_file_name = uniqid("apprenticeshipissuecategories.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Update Outcome sort order
	$group->put("/updatesort" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		foreach ($data as $key => $value) {
			$apprenticeshipoutcomes = \Models\ApprenticeshipIssueCategories::where('id', '=', $value['id'])
				->update(["sort" => $value['sort']])
			;
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Optional outcome groups update!
	// This will overwrite existing data
	$group->put("/optional-outcome" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$safe_ids = [];
		foreach ($data['optional_outcomes'] as $key => $optional_outcome) {
			$entry = \Models\ApprenticeshipOptionalOutcome
				::firstOrCreate(
					[
						'outcome_id' => $data['outcome_id'],
						'standard_id' => $data['standard_id'],
						'group' => $optional_outcome['group']
					]
				)
			;
			$entry->save();
			$safe_ids[] = $entry->id;
		}
		$delete_entries = \Models\ApprenticeshipOptionalOutcome
			::where('outcome_id', $data['outcome_id'])
			->whereNotIn('id', $safe_ids)
			->get()
		;
		foreach ($delete_entries as $key => $delete_entry) {
			$delete_entry->deleted_by = \APP\Auth::getUserId();
			$delete_entry->save();
			$delete_entry->delete();
		}



		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

});
