<?php

use APP\Auth;
use Models\ConfigurationCategory;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Configuration;
use Models\FormWorkflow;
use Slim\Http\Request as HttpRequest;
use Slim\Http\Response as HttpResponse;

$app->group("/configuration",  function ($group) {


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$configuration = \Models\Configuration::find($args["id"]);
		$configuration->status = 0;
		$configuration->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$configuration = \Models\Configuration::find($args["id"]);
		$configuration->status = 1;
		$configuration->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$configuration = \Models\Configuration
			::select(
				'*',
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.value
						END
					) as value
				"),
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.previousValue
						END
					) as previousValue
				")
			)
			->find($args["id"])
		;

		if($configuration->table_name) {
			if($configuration->table_name == 'form_workflow') {
				$configuration->select_values = FormWorkflow::getFormWorflowNameById();
			}
		}

		$response->getBody()->write(json_encode($configuration));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'select'));

	// Get by key
	$group->get('/key/{key}', function (Request $request, Response $response, $args) {
		$configuration = \Models\Configuration
			::select(
				'*',
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.value
						END
					) as value
				"),
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.previousValue
						END
					) as previousValue
				")
			)
			->where('key', $args["key"])
			->first()
		;

		if (
			!$configuration->public &&
			!\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'select')
		) {
			return \APP\Tools::returnCode($request, $response, 403, 'You do not have permission to access this configuration');
		}

		$response->getBody()->write(json_encode($configuration));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		if (
			$data["key"] == 'version' &&
			(
				!isset($data["password"]) ||
				$data["password"] !== 'updateConfiguration'
			)
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$configuration = new \Models\Configuration;

		if (isset($data["name"])) {
			$configuration->name = $data["name"];
		}
		if (isset($data["key"])) {
			$configuration->key = $data["key"];
		}
		if (isset($data["value"])) {
			if ($configuration->previousValue != $data["value"]) {
				$configuration->previousValue = $configuration->value;
			}
			$configuration->value = $data["value"];
		}

		if (isset($data["description"])) {
			$configuration->description = $data["description"];
		}
		if (isset($data["type"])) {
			$configuration->type = $data["type"];
		}
		$configuration->created_by = \APP\Auth::getUserId();
		$configuration->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'insert'));

	// update configuration
	$group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$redirect = false; // if this will be true, js will be recombined and user will be redirected to start/index

		if (
			$data["key"] == 'version' &&
			(
				!isset($data["password"]) ||
				$data["password"] !== 'updateConfiguration'
			)
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$configuration = \Models\Configuration::find($args["id"]);

		// Update version labels/roles/preferences if
		if (
			isset($data["key"]) &&
			$data["key"] == 'version' &&
			isset($data["value"])
		) {
			\APP\Licensing::updateVersion($configuration->value, $data["value"], $this->get('settings')["licensing"]);
			$redirect = true;
		}

		// Remove previously set color schemes when default configuration key is 'isBlackColourScheme'.
		if (
			isset($data["key"]) &&
			$data["key"] == 'isBlackColourScheme'
		) {
			\Models\TableExtension::where('name', 'color_scheme')->where('table', 'users')->delete();
		}


		if (isset($data["name"])) {
			$configuration->name = $data["name"];
		}
		if (isset($data["key"])) {
			$configuration->key = $data["key"];
		}
		if (isset($data["value"])) {
			if ($configuration->value != $data["value"]) {
				$configuration->previousValue = $configuration->value;
			}

            if($configuration->type === 'list-definition') {
                $prevValue = json_decode($configuration->value, true);
                $currValue = $data['value'];

                $differences = array_diff($prevValue, $currValue);

                if (!empty($differences)) {
                    $configuration->previousValue = json_encode($prevValue);
                }
            }

			// If configuration is secure, save value only if legit empty or actual data is provided, 8* are just filler.
			if (
				(
					$configuration->secure &&
					(
						empty($data["value"]) ||
						$data["value"] != '********'
					)
				) ||
				!$configuration->secure
			) {
				$configuration->value = $data["value"];
			}
		}

		if (isset($data["description"])) {
			$configuration->description = $data["description"];
		}
		if (isset($data["type"])) {
			$configuration->type = $data["type"];
		}
		if (isset($data["table_name"])) {
			$configuration->table_name = $data["table_name"];
		}
		$configuration->updated_by = \APP\Auth::getUserId();
		$configuration->save();

		/*Check and update sendCivicaData CRON based on CivicaFrequencyDays*/
		if ($configuration->key == "CivicaFrequencyDays") {
			$cron = \Models\Cron::where('function','=','sendCivicaData')->first();
			if ($cron) {
				$cron->frequency = 60*($configuration->value * 24); /*days to minutes*/
				$cron->save();
			}
		}

		if ($redirect) {
			\APP\Tools::combineJsAssets($this->get('settings'));
			$bump_version = new \Models\Version;
			$bump_version->save();
			$response->getBody()->write('redirect');
			return $response->withHeader('Content-Type', 'text/html');
		}


		// If schedule is enabled, apply permission upgrade to managers, so they can see groups
		if (
			$data["key"] == 'enableSchedule' &&
			$data["value"] == "1"
		) {
			$sctructure = \Models\Structure::where('key', 'system-setup-organisation-groups')->first();
			$manager_roles = \Models\Role
				::where('is_manager', true)
				->get()
			;
			foreach ($manager_roles as $key => $manager_role) {
				$role_structure = \Models\RoleStructure
					::firstOrNew(
						[
							'role_id' => $manager_role->id,
							'structure_id' => $sctructure->id,
						]
					)
				;
				$role_structure->select = true;
				$role_structure->save();
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		$data = \Models\Configuration
			::select(
				'*',
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.value
						END
					) as value
				")
			)
			->where("status", 1)
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'select'));

	$group->post('/list{download:[\/a-z-]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Configuration
			::select(
				'*',
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.value
						END
					) as value
				"),
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.previousValue
						END
					) as previousValue
				")
			)
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}


		if (isset($args["download"]) && $args["download"] == "/download") {
            $data = \APP\SmartTable::searchPaginate($params, $query, false, false);

            $export_fields = [
                "ID" => "id",
                "Configuration Name" => "name"
            ];


            $download_file_name = uniqid("configuration.list.") . ".xlsx";

            \APP\Tools::generateExcelDownload(
                $data,
                $export_fields,
                $this->get('settings')["LMSTempPath"] . $download_file_name
            );

            $response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
        } else if (isset($args["download"]) && $args["download"] == "/payment-engines") {
            $query->where('is_payment_configuration',true);
            $p = \APP\SmartTable::searchPaginate($params, $query);
            $response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

        } else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		}
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'select'));

	$group->get('/data-from-table/{table_name:[A-Za-z_]+}', function (Request $request, Response $response, array $args) {

        $selectValues = [];
	    if($args['table_name'] == 'form_workflow') {
		   $selectValues = FormWorkflow::getFormWorflowNameById();
        }

        $response->getBody()->write(json_encode($selectValues));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'select'));

    $group->post('/category/{type:[A-Za-z]+}',function(Request $request, Response $response,array $args){
        $params = $request->getParsedBody();
        $name = null;
        if(isset($params['search']['refresh'])){
            unset($params['search']['refresh']);
        }
        if(isset($params['search']['name'])){
            $name = $params['search']['name'];
            unset($params['search']['name']);
        }
        $query = ConfigurationCategory::select('configuration_categories.*')->where('type',$args['type']);
        if((Auth::isManager() && !Auth::isAdmin()) || (isset($params['search']) && isset($params['search']['company_id']))){
            $query->whereHas('Configuration',function($query){
                $query->where('visible_manager',1);
                $query->orWhere('editable_manager',1);
            })->where('status',1);
        }
        if($name){
            $query->where(function($query)use($name){
                $query->where('name','like',"%$name%");
                $query->orWhere('description','like',"%$name%");
                $query->orWhereHas('Configuration',function($query)use($name){
                    $query->where('name','like',"%$name%");
                    $query->orWhere('description','like',"%$name%");
                    $query->orWhere('key','like',"%$name%");
                });
            });
        }
        if(isset($params['search']) && isset($params['search']['company_id'])){
            unset($params['search']['company_id']);
        }
        $category = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($category->toJson());
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'select'));

    $group->get('/list/{id:[0-9]+}',function(Request $request, Response $response,array $args){
        $params = $request->getQueryParams();
        $select = ['*',
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.value
						END
					) as value
				"),
				DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.previousValue
						END
					) as previousValue
        ")];
        if ((Auth::isManager() && !Auth::isAdmin()) || isset($params['company_id'])){
            $select = ['configuration.*',
                DB::raw("(
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.previousValue
						END
					) as previousValue
                "),
                DB::raw(
                "(CASE WHEN company_configurations.value IS NULL THEN
                    (
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								configuration.value
						END
					)
                ELSE
                (
						CASE
							WHEN
								configuration.secure = 1
							THEN
								'********'
							ELSE
								company_configurations.value
						END
				)
                END) as value"
            )];
        }
        $configuration = Configuration::select($select)
            ->orderBy('name')
			->where('hidden',0) //make sure hidden configurations are not shown
            ->where('category_id',$args['id'])
            ->where(function($query){
                if((Auth::isManager() && !Auth::isAdmin())|| (isset($params['company_id']))){
                    $query->where('visible_manager',1);
                    $query->orWhere('editable_manager',1);
                }
            });
        if((Auth::isManager() && !Auth::isAdmin()) || isset($params['company_id'])){
            $company_id = isset($params['company_id']) ? $params['company_id'] : Auth::getUser()->company_id;
            $configuration->leftJoin('company_configurations',function($join) use($company_id){
                $join->on('company_configurations.configuration_id','=','configuration.id');
                $join->where('company_configurations.company_id',$company_id);
            });
        }
		$response->getBody()->write(json_encode($configuration->get()));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'select'));

    $group->put('/category/status/{id:[0-9]+}',function(Request $request, Response $response,array $args){
        $category = ConfigurationCategory::find($args['id']);
        $category->status = !$category->status;
        $category->save();
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'disable'));

    $group->put('/save',function(Request $request, Response $response){
        return ConfigurationCategory::updateConfiguration($request, $response, $this->get('settings'));
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'update'));

	//this function update the OpeneLMSCreatorMenu from Media Library
	$group->post('/updateOpeneLMSCreatorMenu',function(Request $request, Response $response){
		$data = $request->getParsedBody();
		$configuration = \Models\Configuration::where('key','OpeneLMSCreatorMenu')->first();
		$configuration->value = $data['value'];
		$configuration->save();
		return $response;
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccessMediaLibrary"));
});
