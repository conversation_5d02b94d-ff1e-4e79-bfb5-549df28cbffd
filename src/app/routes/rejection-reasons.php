<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/rejection-reasons",  function ($group) {

    $group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $rejectionReason = \Models\RejectionReason::query()->find($args['id']);

        $response->getBody()->write(json_encode($rejectionReason));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-rejection-reasons', 'select'));

    $group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {

        $data = $request->getParsedBody();

        $rejectionReason = \Models\RejectionReason::query()->find($args['id']);
        $rejectionReason->name = $data['name'] ?? $rejectionReason->name;
        $rejectionReason->description = $data['description'] ?? $rejectionReason->description;
        $rejectionReason->save();

        $response->getBody()->write(json_encode($rejectionReason));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-rejection-reasons', 'select'));

    $group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        $query= \Models\RejectionReason::query();
        ;

        $data = \APP\SmartTable::searchPaginate($params, $query, false);

        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-rejection-reasons', 'select'));

    $group->post('/new', function (Request $request, Response $response) {
        $data = $request->getParsedBody();

        $rejection = new \Models\RejectionReason;
        $rejection->name = $data["name"] ?? $rejection->name;
        $rejection->description = $data["description"] ?? $rejection->description;
        $rejection->status = 1;
        $rejection->save();

        return $response;

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-rejection-reasons', 'insert'));

    $group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $rejectionReason = \Models\RejectionReason::query()->find($args["id"]);
        $rejectionReason->status = 0;
        $rejectionReason->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-rejection-reasons', 'disable'));

    $group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $rejectionReason = \Models\RejectionReason::query()->find($args["id"]);
        $rejectionReason->status = 1;
        $rejectionReason->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-rejection-reasons', 'disable'));

    $group->get('/all', function (Request $request, Response $response, $args) {
        $rejectionReason = \Models\RejectionReason::query()->where('status', '=', \Models\RejectionReason::REJECTION_REASON_ENABLE)->get();

        $response->getBody()->write(json_encode($rejectionReason));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getSessionCheck());
});