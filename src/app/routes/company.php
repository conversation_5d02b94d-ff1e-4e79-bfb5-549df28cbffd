<?php

use APP\Form;
use Models\Company;
use Models\Field;
use Models\FieldCategory;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;


$app->group("/company", function($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$company = \Models\Company::find($args["id"]);
		$company->status = 0;
		$company->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$company = \Models\Company::find($args["id"]);
		$company->status = 1;
		$company->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'disable'));

	// Return interested resources against company
	$group->get('/{company_id:[0-9]+}/resource', function (Request $request, Response $response, $args) {

		if (!\APP\Auth::isAdminInterface()) {
			return false;
		}

		$resources = \Models\CompanyResourceInterestedIn
			::where('status', true)
			->where('company_id', $args['company_id'])
			->with(['Resource' => function($query) {
				$query
					->select(
						'id',
						'name'
					)
				;
			}])
		;

		$resources = $resources
			->get()
		;

		$response->getBody()->write(json_encode($resources));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// Attach resource against company and interested people!
	$group->post('/{company_id:[0-9]+}/resource', function (Request $request, Response $response, $args) {

		if (!\APP\Auth::isAdminInterface()) {
			return false;
		}

		$data = $request->getParsedBody();

		$query = new \Models\CompanyResourceInterestedIn;
		$query->company_id = $args['company_id'];
		$query->people = $data['people'];
		$query->user_id = \APP\Auth::getUserId();
		$query->learning_module_id = $data['learning_module_id'];
		$query->status = true;
		$query->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));


	// Delete linked resource
	$group->delete('/{company_id:[0-9]+}/resource/{comment_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if (!\APP\Auth::isAdminInterface()) {
			return false;
		}

		$query = \Models\CompanyResourceInterestedIn
			::where('id', $args['comment_id'])
			->where('company_id', $args['company_id'])
			->first()
		;
		$query->status = false;
		$query->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// Return comments against company
	$group->get('/{company_id:[0-9]+}/comment', function (Request $request, Response $response, $args) {

		if (!\APP\Auth::isAdminInterface()) {
			return false;
		}

		$comments = \Models\CompanyComment
			::where('status', true)
			->where('company_id', $args['company_id'])
			->with(['User' => function($query) {
				$query
					->select(
						'id',
						'company_id',
						'fname',
						'lname',
						'email',
						'phone'
					)
				;
			}])
		;

		$comments = $comments
			->get()
		;

		$response->getBody()->write(json_encode($comments));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// Delete comment against company-user
	$group->delete('/{company_id:[0-9]+}/comment/{comment_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if (!\APP\Auth::isAdminInterface()) {
			return false;
		}

		$comments = \Models\CompanyComment
			::where('id', $args['comment_id'])
			->where('company_id', $args['company_id'])
			->first()
		;
		$comments->status = false;
		$comments->save();

		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($comments))
			;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// Add comment against company!
	$group->post('/{company_id:[0-9]+}/comment', function (Request $request, Response $response, $args) {
        if (!\APP\Auth::isAdminInterface()) {
            return false;
        }

        $data = $request->getParsedBody();
		if (
			isset($data['comment']) &&
			isset($data['company_id']) &&
			isset($data['user_id'])
		) {
			$query = new \Models\CompanyComment;
			$query->comment = $data['comment'];
			$query->company_id = $data['company_id'];
			$query->user_id = $data['user_id'];
			$query->status = true;
			$query->save();
		}

		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($users))
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// Return list of users assigned to companY
	$group->get('/{company_id:[0-9]+}/users', function (Request $request, Response $response, $args) {

		if (!\APP\Auth::isAdminInterface()) {
			return false;
		}

		$users = \Models\User
			::where('status', true)
			->where('company_id', $args['company_id'])
			->select(
				'id',
				'company_id',
				'fname',
				'lname',
				'phone',
				'email'
			)
		;

		$users = $users
			->get()
		;

		$response->getBody()->write(json_encode($users));
		return $response->withHeader('Content-Type', 'application/json');
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));


	// Return individual company!
	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$company = \Models\Company
			::where('id', $args["id"])
			->with(['Files' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
		;

		$company = $company
			->first()
		;

		\Models\TableExtension::returnAllFields('companies', $company->id, $company);

		$response->getBody()->write(json_encode($company));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// Delete images of company
	$group->delete('/{type:[logo|login_bg|learner_bg|e_learning_thumbnail]+}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$company = \Models\Company::find($args["id"]);

        if (!\APP\Auth::isAdminInterface()) {
            return false;
        }

		$image_list = [
			'logo' => $this->get('settings')["CompanyLogosPath"],
			'login_bg' => $this->get('settings')["CompanyLoginBgPath"],
			'learner_bg' => $this->get('settings')["CompanyLearnerBgPath"],
			'e_learning_thumbnail' => $this->get('settings')["CompanyThumbnailPath"],
		];

		if (is_file($image_list[$args["type"]] . $company->{$args["type"]})) {
			unlink($image_list[$args["type"]] . $company->{$args["type"]});
		}
		$company->{$args["type"]} = null;
		$company->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// Update company details
	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

        if (!\APP\Auth::isAdminInterface()) {
            return false;
        }

		$company = \Models\Company::find($args["id"]);
		if (!$company) {
			return \APP\Tools::returnCode($request, $response, 404);
		}
        if(isset($data['custom-field'])){
			Form::saveCustomForm($data['custom-field'],'company',$company->id);
       }
		$fields = [
			"name", "email", "phone", "reference_number", "address", "urlextension", "message", "max_users", "next_contact_date", "discount_percentage", "e_learning_button_style",
			'email_from', 'email_from_name', 'email_footer','email_username','make_all_enrollable'
		];
        if (\APP\Tools::getConfig('sharedClients')) {
            // Check if urlextension exists in other companies, if not, remove it.
            $urlextension_check = \Models\Company
            ::where('urlextension', $data['urlextension'])
            ->where('id', '!=', $args["id"])
            ->first();
            if ($urlextension_check) {
                $data['urlextension'] = $company->urlextension;
            }
        }

		\APP\Tools::setObjectFields($company, $fields, $data, true);

		// List of image files uploaded
		$image_list = [
			'logo' => $this->get('settings')["CompanyLogosPath"],
			'login_bg' => $this->get('settings')["CompanyLoginBgPath"],
			'learner_bg' => $this->get('settings')["CompanyLearnerBgPath"],
			'e_learning_thumbnail' => $this->get('settings')["CompanyThumbnailPath"],
		];
		foreach ($image_list as $image_list_key => $image_list_item) {
			$company->{$image_list_key} = \APP\Tools::uploadImage($image_list_item, $image_list_key, $company->{$image_list_key});
		}

		$company->save();

		$users = \Models\User::where('company_id', $company->id)->pluck('id');
		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				if (
					isset($field_name) &&
					$field_name === "learner_theme"
				) {
					\Models\TableExtension::where('name', 'color_scheme')->where('table', 'users')->whereIn('table_id', $users)->delete();
				}
				\Models\TableExtension::updateField('companies', $company->id, $field_name, $value);
			}
		}

		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($company))
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	$group->post('/new', function (Request $request, Response $response) {

		$data = $request->getParsedBody();

		$company = new \Models\Company;

		$fields = [
			"name", "email", "phone", "reference_number", "address", "urlextension", "message", "max_users", "next_contact_date", "discount_percentage",
			"e_learning_button_style", "email_from", "email_from_name", "email_footer", "email_username",
		];
        if (\APP\Tools::getConfig('sharedClients')) {
            // Check if urlextension exists in other companies, if not, remove it.
            $urlextension_check = \Models\Company
            ::where('urlextension', $data['urlextension'])
            ->first();
            if ($urlextension_check) {
                unset($data['urlextension']);
            }
        }
        if(isset($data['make_all_enrollable'])){
            $company->make_all_enrollable = filter_var($data['make_all_enrollable'], FILTER_VALIDATE_BOOLEAN);
        }

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$company->$field = $data[$field];
			}
		}

		$image_list = [
			'logo' => $this->get('settings')["CompanyLogosPath"],
			'login_bg' => $this->get('settings')["CompanyLoginBgPath"],
			'learner_bg' => $this->get('settings')["CompanyLearnerBgPath"],
			'e_learning_thumbnail' => $this->get('settings')["CompanyThumbnailPath"],
		];
		foreach ($image_list as $image_list_key => $image_list_item) {
			$company->{$image_list_key} = \APP\Tools::uploadImage($image_list_item, $image_list_key, $company->{$image_list_key});
		}

		$company->status = 1;
		$company->save();
		if (isset($data['custom-field'])) {
			Form::saveCustomForm($data['custom-field'], 'company', $company->id);
		}

		$response->getBody()->write(json_encode($company->id));
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'insert'));

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Company::where("id", ">", "0");

		if (
			isset($params["search"]) &&
			is_array($params["search"])
		){
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}

			foreach($params["search"] as $field => $value)
			{
				if (is_int($value))
				{
					$query->where($field, "=", $value);
				}
				else
				{
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"]))
		{
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download")
		{
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
					"ID" => "id",
					"Company Name" => "name",
					"Email" => "email",
					"Phone" => "phone",
					"Address" => "address",
			];


			$download_file_name = uniqid("companies.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
					);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		}
		else
		{
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();

			$query = \Models\Company
				::where("status", true)
			;
			if (
				!\APP\Auth::isAdmin() &&
				(
					\APP\Auth::isLearner() ||
					!\APP\Auth::accessAllCompanies()
				)
			) {
				$query = $query
					->whereIn('id', [\APP\Auth::getUserCompanyId()])
				;
				$query = $query->get();
			} else {
				$query = cache()->remember('company_all', 600, function () use ($query) {
					return $query->get()->toArray();
				});
			}


		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'select'));

	// For registration page
	$group->get('/register/all', function (Request $request, Response $response) {
		$query = [];
		if (\APP\Tools::getConfig('registerShowCompany')) {
			$query = \Models\Company
				::where("status", true)
				->get()
			;
		}
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionRegisterCheck());

    $group->get('/verify',function(Request $request,Response $response){
        if(isset($_GET['company_id'])){
            $_SESSION['company_id'] = $_GET['company_id'];
        }
        if(!isset($_GET['code'])){
            $return_url  = Company::getRedirectUrl();
            $response->getBody()->write($return_url);
			return $response->withHeader('Content-Type', 'text/html');
       }else{
           $data = Company::getAccessToken($_GET['code']);
           if($data){
               $response
               	->getBody()
               	->write('<script>
     localStorage.setItem("token_details",1);
     window.close();
    </script>');
           }
           return $response;
        }
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'insert'));


    $group->delete('/verify/{id:[0-9]+}',function(Request $request,Response $response,$args){
        $company = Company::where('id',$args['id'])->first();
        if (!$company){
        	return \APP\Tools::returnCode($request, $response, 400);
        }
        $company->access_token = "";
        $company->refresh_token = "";
        $company->save();
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'insert'));

	$group->group("/validate", function ($group) {

		$group->get('/urlextension/[{urlextension: .+}]', function (Request $request, Response $response, $args) {

			$urlextension = $args["urlextension"];

			$params = $request->getQueryParams();

			if (empty($params["exclude_id"])) {
				$params["exclude_id"] = 0;
			}

			if (isset($params["exclude_id"])) {
				if (\Models\Company::where("urlextension", $urlextension)->where("id", "<>", $params["exclude_id"])->first()) {
					return $response->withStatus(409)->write('Already exists');
				}
			}

			$response->getBody()->write("ok");
			return $response;

		});

	})->add(\APP\Auth::getSessionRegisterCheck());


	$group->put('/{company_id:[0-9]+}/resource/{resource_id:[0-9]+}', function (Request $request, Response $response, $args) {
        try {
            if (array_key_exists('company_id', $args) && array_key_exists('resource_id', $args)) {
                $company = \Models\Company::find($args["company_id"]);
                $resource = \Models\LearningModule::find($args["resource_id"]);
                if ($company && $resource) {
                    $companyModuleEnrollment = new \Models\CompanyModuleEnrollment();
                    $companyModuleEnrollment->company_id = $company->id;
                    $companyModuleEnrollment->learning_module_id = $resource->id;
                    $companyModuleEnrollment->save();

					$response->getBody()->write(json_encode(['status'=>true]));
                }
            }
        }catch (Exception $exception)
        {
			$response->getBody()->write(json_encode(['status'=>false]));
        }

		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'insert'));

    $group->delete('/resource/{company_id:[0-9]+}/{resource_id:[0-9]+}', function (Request $request, Response $response, $args) {
        try{
            if (array_key_exists('company_id', $args) && array_key_exists('resource_id', $args)) {
                $companyModuleEnrollment=\Models\CompanyModuleEnrollment::where(
                    ['company_id'=>$args['company_id'],'learning_module_id'=>$args['resource_id']]
                )->first();
                if($companyModuleEnrollment)
                {
                    $companyModuleEnrollment->delete();
                    $response->getBody()->write(json_encode(['status'=>true]));
                }
            }
        }catch (Exception $exception)
        {
            $response->getBody()->write(json_encode(['status'=>false]));
        }

		$response->getBody()->write(json_encode(['status'=>false]));
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-companies', 'insert'));
});
