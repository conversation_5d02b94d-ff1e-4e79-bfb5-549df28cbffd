<?php
use Illuminate\Database\Capsule\Manager as DB;

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use IMSGlobal\LTI\ToolProvider;
use IMSGlobal\LTI\ToolProvider\DataConnector;

$app->group("/lti/1.3",  function ($group) {


    $group->post("/login" , function (Request $request, Response $response, $args) {

        //\APP\Tools::updatePHPSessionCookie();

        try
        {
            \IMSGlobal\LTI\LTI_OIDC_Login::new(new \APP\LTI())
                ->do_oidc_login_redirect($this->get('settings')['LMSUri'] . "/lti1.3/launch")
                ->do_redirect();
        }
        catch(\IMSGlobal\LTI\LTI_Exception $ex){
            $response->getBody()->write("LTI error: " . $ex->getMessage());
        }

        exit(0);
    });

	$group->post("/launch" , function (Request $request, Response $response, $args) {

        //\APP\Tools::updatePHPSessionCookie();

        try
        {
            $lti = new \APP\LTI();
            $launch = \IMSGlobal\LTI\LTI_Message_Launch::new($lti)->validate();

            $lms_user = $lti->get_lms_user($launch);

            \APP\Auth::login($lms_user->username, $lms_user->password, true);

            if ($launch->is_deep_link_launch()) {
                //deep link
                $dl = $launch->get_deep_link();

                if ($lti->is_instructor($launch) || $lti->is_admin($launch)){
                    $lti_role = "instructor/admin";
                    $lrs = \Models\LearningResult
                        ::where("user_id", "=", 1)
                        ->with("module")
                        ->where("refreshed" , "=", 0)
                        ->get()
                    ;
                } else {
                    //trainee
                    $lti_role = "trainee";
                    $lrs = \Models\LearningResult
                        ::where("user_id", "=", $lms_user->id)
                        ->with("module")
                        ->where("refreshed" , "=", 0)
                        ->get()
                    ;
                }

                $dl_resources = [];

                $GLOBALS["LOGGER"]->addInfo("Returnin DL resources for {$lti_role}");

                $count = 0;

                foreach($lrs as $lr){
                    if (in_array($lr->module->type->slug, ["e-learning", "youtube", "vimeo"]))
                    {
                        if ($lti->is_instructor($launch) || $lti->is_admin($launch)){
                            $url = $lti->get_preview_learning_url($lr->module, $this->get('settings')["LMSUrl"]);
                        } else {
                            $url = $this->get('settings')["LMSUrl"] . $lti->get_learning_uri($lr->module);
                        }


                        $dl_resources[] = \IMSGlobal\LTI\LTI_Deep_Link_Resource::new()
                            ->set_url($url)
                            ->set_title($lr->name)
                        ;

                        $GLOBALS["LOGGER"]->addInfo("DL resource: \"{$lr->module->name}\", {$url}");

                        if ($count++ > 5) {
                            break;
                        }
                    }
                    //
                }

                $output = $dl->output_response_form($dl_resources);

                exit(0);

            }
            if ($launch->is_resource_launch()) {

                $_SESSION['LTI_launch_id'] = $launch->get_launch_id();
                $learning = $lti->get_learning_resource($launch);
                $lti->assign_lms_learning($lms_user, $learning);
                if ($lti->is_instructor($launch)){
                    if ($launch->has_nrps()) {
                        $nrps = $launch->get_nrps();
                        $members = $nrps->get_members();

                        foreach($members as $member) {
                            if ($member["status"] != "Active") {
                                continue;
                            }
                            $member_roles = [];
                            foreach($member["roles"] as $role) {
                                $member_roles[] = $role;
                            }
                            $member_roles = implode(", ", $member_roles);
                            $member_info = "{$member['name']} ({$member['user_id']}, {$member_roles}) enrolled at {$member['context_id']}";
                            $GLOBALS["LOGGER"]->addInfo("Member retrieved: $member_info");

                        }
                    }
                    if ($launch->has_ags())
                    {
                        //Grading service is enabled so launch course in testing mode
                        $uri = $lti->get_learning_uri($learning);
                        return $response
                            ->withHeader('Location', $this->get('settings')["LMSUrl"] . $uri)
                            ->withStatus(302)
                        ;
                    } else {
                        //Launch larning in preview mode
                        $url = $lti->get_preview_learning_url($learning, $this->get('settings')["LMSUrl"]);
                        return $response
                            ->withHeader('Location', $url)
                            ->withStatus(302)
                        ;
                    }
                } else {
                    //trainee
                    $uri = $lti->get_learning_uri($learning);
                    return $response
                        ->withHeader('Location', $this->get('settings')["LMSUrl"] . $uri)
                        ->withStatus(302)
                    ;
                }

            }
        }
        catch(\IMSGlobal\LTI\LTI_Exception $ex){
            $error = "LTI error: " . $ex->getMessage();
            $GLOBALS["LOGGER"]->addError($error);
            $response = $response->withHeader($error, 400);
        }

        return $response;
    });

});

$app->group("/lti",  function ($group) {


	$group->post('/{learning_module_id:[0-9]+}', function (Request $request, Response $response, $args) {

        //\APP\Tools::updatePHPSessionCookie();

        $tool = new \APP\LTIProvider($args["learning_module_id"], $this->get('settings')["settings"]["database"]);

        try {
            $tool->handleRequest();
        }
        catch(\APP\LTIException $lti_exception){
			$response
				->getBody()
				->write($lti_exception->message . "\n" . $lti_exception->reason);
            ;
            switch($lti_exception->code){
                case \APP\LTIErrorCodes::LTIError:
                    $response = $response->withStatus(500);
            }

            return $response;
        }

        $module = \Models\LearningModule::find($args["learning_module_id"]);

        if ($module && in_array($module->type->slug, ["youtube", "vimeo", "e_learning"])) {
            $learning_link = "";

            switch($module->type->slug) {
                case "youtube":
                    $video_id = \APP\Tools::getVideoId($module->material->link, "youtube");
                    $controls = \APP\Tools::getConfig('disableVideoClicks') ? 0 : 1;
                    $learning_link =
                        $this->get('settings')['LMSUri'] .
                        "tincan/play_tincan.php?" .
                        "user_id=" . \APP\Auth::getUserId() . "&" .
                        "course_id=" . $module->id . "&" .
                        "controls={$controls}&" .
                        "fname=" . \APP\Auth::getUser()->fname . "&" .
                        "lname=" . \APP\Auth::getUser()->lname . "&" .
                        "email=" . \APP\Auth::getUser()->email . "&" .
                        "video_type=youtube&" .
                        "video_id=" . $video_id . "&" .
                        "session_id=" . session_id()
                    ;
                break;
                case "vimeo":
                    $video_id = \APP\Tools::getVideoId($module->material->link, "vimeo");
                    $learning_link =
                        $this->get('settings')['LMSUri'] .
                        "tincan/play_tincan.php?" .
                        "user_id=" . \APP\Auth::getUserId() . "&" .
                        "course_id=" . $module->id . "&" .
                        "controls=1&" .
                        "fname=" . \APP\Auth::getUser()->fname . "&" .
                        "lname=" . \APP\Auth::getUser()->lname . "&" .
                        "email=" . \APP\Auth::getUser()->email . "&" .
                        "video_type=vimeo&" .
			"video_id=" . $video_id . "&" .
			"video_url=" . $module->material->link . "&" .
                        "session_id=" . session_id()
                    ;
                break;
                case "e_learning":
                    $scorm_data = \APP\Learning::launchScorm($module->id);
                    $learning_link =
                        $this->get('settings')['LMSUri'] .
                        "/scorm/play_scorm.php?" .
                        "a=" . $scorm_data["scorm"]["id"] . "&" .
                        "scoid=" . $scorm_data["scorm"]["start_module_id"] . "&" .
                        "fname=" . $scorm_data["user"]["fname"] . "&" .
                        "lname=" . $scorm_data["user"]["lname"] . "&" .
                        "email=" . $scorm_data["user"]["email"] . "&" .
                        "username=" . $scorm_data["user"]["username"] . "&" .
                        "user_id=" . $scorm_data["user"]["id"] . "&" .
                        "session_id=" . session_id()
                    ;
                break;
            }

            return
                $response
                    ->withStatus(302)
                    ->withHeader('Location', $learning_link);
        } else {
            return $response
                ->withStatus(404)
                ->getBody()->write("The requested learning is not found or can't be run via LTI.");
        }

        return $response;
    });

});
