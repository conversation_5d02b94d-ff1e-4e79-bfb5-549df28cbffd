<?php

use APP\Auth;
use APP\Controllers\PowerBIController;
use APP\SmartTable;
use Models\PowerBiAccessToken;
use Models\PowerBiDashboard;
use Models\User;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/powerbi-dashboards", function ($group) use ($app) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$dashboard = \Models\PowerBiDashboard::find($args["id"]);
		$dashboard->status = 0;
		\Models\PowerBiDataset::where('powerbi_dashboard_id', $dashboard->id)->update(['status' => false]);
		\Models\PowerBiReport::where('powerbi_dashboard_id', $dashboard->id)->update(['status' => false]);
		$dashboard->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$dashboard = \Models\PowerBiDashboard::find($args["id"]);
		$dashboard->status = 1;
		\Models\PowerBiDataset::where('powerbi_dashboard_id', $dashboard->id)->update(['status' => true]);
		\Models\PowerBiReport::where('powerbi_dashboard_id', $dashboard->id)->update(['status' => true]);
		$dashboard->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'disable'));

	$group->post('/list', function (Request $request, Response $response) {

		$params = $request->getParsedBody();

		$query = \Models\PowerBiDashboard
			::selectRaw("powerbi_dashboards.*")
			->selectRaw("designations.name AS designation_name")
			->selectRaw("smcr_staff_types.name AS staff_type_name")
			->leftJoin('powerbi_dashboard_roles', 'powerbi_dashboards.id', '=', 'powerbi_dashboard_roles.powerbi_dashboard_id')
			->leftJoin('roles', 'powerbi_dashboard_roles.role_id', '=', 'roles.id')
			->with('roles')
			->with('courseSet')
			->distinct()
			->leftJoin("designations", function ($join) {
				$join
					->on("powerbi_dashboards.designation_id", "=", "designations.id");
			})
			->leftJoin("smcr_staff_types", function ($join) {
				$join
					->on("powerbi_dashboards.staff_type_id", "=", "smcr_staff_types.id");
			});
		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'select'));

	$group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {

		$dashboard = \Models\PowerBiDashboard::with(["designation", "stafftype", "roles"])->find($args["id"]);
		$response->getBody()->write($dashboard->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'select'));

	$group->post("/new", function (Request $request, Response $response) {

		$data = $request->getParsedBody();

		$dashboard = new \Models\PowerBiDashboard();
		$dashboard->name = $data["name"];
		$dashboard->powerbi_report_id = $data["powerbi_report_id"];
		$dashboard->visibility = $data["visibility"];
		$dashboard->powerbi_course_set_id = $data["powerbi_course_set_id"];
		$dashboard->include_data_for = $data["include_data_for"];
		$dashboard->display_order = $data["display_order"];
		if (isset($data["apllies_to"])) {
			if ($data["apllies_to"] == "job") {
				$dashboard->designation_id = $data["designation_id"];
			}
			if ($data["apllies_to"] == "staff_type") {
				$dashboard->staff_type_id = $data["staff_type_id"];
			}
		}
		$dashboard->save();

		$rolesList = $data['roleList'] ?? [];
		if ($dashboard->id) {
			foreach ($rolesList as $key => $value) {
				\Models\PowerbiDashboardRole::updateOrCreate([
					'powerbi_dashboard_id' => $dashboard->id,
					'role_id' => $value['id']
				], [
					'powerbi_dashboard_id' => $dashboard->id,
					'role_id' => $value['id']
				]);
			}
		}


		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'insert'));

	$group->put("/{id:[0-9]+}", PowerBIController::class . ':updateDashboard')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'insert'));

	$group->post('/{dashboard_id:[0-9]+}/duplicate', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$parentDashboard = \Models\PowerBiDashboard::find($args["dashboard_id"]);

		$dashboard = new \Models\PowerBiDashboard();
		$dashboard->name = $data["name"];
		$dashboard->powerbi_report_id = $parentDashboard->powerbi_report_id;
		$dashboard->visibility = $parentDashboard->visibility;
		$dashboard->include_data_for = $parentDashboard->include_data_for;

		$dashboard->designation_id = $parentDashboard->designation_id;
		$dashboard->powerbi_course_set_id = $parentDashboard->powerbi_course_set_id;
		$dashboard->staff_type_id = $parentDashboard->staff_type_id;
		$dashboard->save();

		$roles = $parentDashboard->roles()->get()->pluck("id")->toArray();
		$dashboard->roles()->sync($roles);
		// $learningModules = $parentDashboard->courses()->get()->pluck("id")->toArray();
		// $dashboard->courses()->sync($learningModules);

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'select'));

	$group->get('', PowerBIController::class . ':ssoLogin')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-dashboards', 'insert'));


	$group->get('/hastoken', function (Request $request, Response $response) {
		try {
			$token = PowerBiAccessToken::where('status', 1)->first();
			if (!$token) return $response->withStatus(404);
			return $response;
		} catch (\Exception $e) {
			return $response->withStatus(404);
		}
	})->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));

	$group->get('/removetoken', function (Request $request, Response $response) {
		$token = PowerBiAccessToken::where('status', 1)->first();
		$token->delete();
	})->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));

	// client side
	$group->get("/home", PowerBIController::class . ':getPowerBIDashboard')->add(\APP\Auth::getSessionCheck());

	$group->get('/refresh-data/{id:[0-9]+}', PowerBIController::class . ':refreshData')->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));
    $group->delete('/{dashboard_id:[0-9]+}/course/{course_id:[0-9]+}', function (Request $request, Response $response, $args) {

        \Models\PowerbiDashboardLearningModules
            ::where("dashboard_id", $args["dashboard_id"])
            ->where("learning_module_id", $args["course_id"])
            ->delete();

        // $c_users = \Models\LearningModule::where("course_id", $args["course_id"])->get();
        //
        // \Models\PowerbiDashboardLearningModules
        //     ::whereIn("learning_module_id",
        //         $c_users->pluck('id')->toArray()
        //     )
        //     ->where('dashboard_id', $args["dashboard_id"])
        //     ->delete()
        // ;
        //
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'disable'));

});
