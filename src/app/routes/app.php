<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;

use Dflydev\FigCookies\FigRequestCookies;
use Dflydev\FigCookies\FigResponseCookies;
use Dflydev\FigCookies\Cookie;
use Dflydev\FigCookies\SetCookie;
use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Support\Str;
use Endroid\QrCode\QrCode;
use Monolog\Logger;
use Middleware\RateLimitMiddleware;

$app->get("/", function (Request $request, Response $response, array $args) {
	$redirect_uri = \APP\Auth::getRedirectUri($this->get('settings'));
	return $response->withHeader('Location', $this->get('settings')["LMSAppUri"] . $redirect_uri)->withStatus(302);
})->add(\APP\Auth::getLoginCheck($settings["LMSUri"] . "login"));


$app->group("/forgottenpassword", function($group) {

	$group->post("/{token_id:.+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		try {
			$fp_token = \Models\ForgottenPasswordToken
				::where("token", $args["token_id"])
				->where("status", 1)
				->firstOrFail()
			;

			if (preg_match('/' . $this->get('settings')["LMSPasswordPattern"] .'/', $data["new_password"])) {
				$password_check_response = \APP\Tools::passwordStrengthCheck($data["new_password"], $request, $response);
				if ($password_check_response) {
					return $password_check_response;
				}
				$fp_token->user->password = password_hash($data["new_password"], PASSWORD_BCRYPT, ['cost' => 12]);
				$fp_token->user->password_attempts = 0;
				$fp_token->user->save();
				$fp_token->status = false;
				$fp_token->save();
			} else {
				return \APP\Tools::returnCode($request, $response, 406, 'The password should be minimum 8 characters in length, should contain 1 uppercase letter, 1 lower case letter and 1 number.');
			}

			return $response;
		}
		catch(Exception $e) {
			return \APP\Tools::returnCode($request, $response, 401, 'Invalid password recovery token.');
		}
	})->add(new RateLimitMiddleware(60, 10));

	$group->get("/{token_id:.+}", function (Request $request, Response $response, array $args) {

		try {
			$fp_token = \Models\ForgottenPasswordToken
				::where("token", $args["token_id"])
				->where("status", true)
				->firstOrFail()
			;

			return $this->get('view')->render($response, 'html/recoverpassword.html', [
				"LMSUrl" => $this->get('settings')["LMSUrl"],
				"LMSUri" => $this->get('settings')["LMSUri"],
				"LMSAppUri" => $this->get('settings')["LMSAppUri"],
				"LMSTplsUri" => $this->get('settings')["LMSTplsUri"],
				"LMSTplsUriHTML" => $this->get('settings')["LMSTplsUriHTML"],
				"LMSTplsUriCombined" => $this->get('settings')["LMSTplsUriCombined"],
				"LMSPasswordPattern" => $this->get('settings')["LMSPasswordPattern"],
				"password_pattern_error_message" => \APP\Templates::translate('%%password_pattern_error_message%%'),
				"FPToken" => $fp_token->token,
			]);
		}
		catch(Exception $e) {
			return \APP\Tools::returnCode($request, $response, 401, 'Invalid or expired password recovery link');
		}
	})->add(new RateLimitMiddleware(60, 10));

	$group->post("", function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		try {
			$user = \Models\User
				::where("username", "=", $data["username_email"])
				->orWhere("email", "=", $data["username_email"])
				->where('status', 1)
				->where(function($query) {
					$query
						->whereNull("expiration_dt")
						->orWhere("expiration_dt", ">", \Carbon\Carbon::now())
					;
				})
			;

			if (\APP\Tools::getConfig('PasswordResetAnonymousMessage')) {
				$user = $user
					->first()
				;
				$response_text = \APP\Templates::translate('%%password_reset_anonymous_message%%');
			} else {
				$user = $user
					->firstOrFail()
				;
				$response_text = "We have emailed a password recovery link to " . $user->email . ". Please check your inbox in a few minutes for further instructions.";
			}

			if ($user) {
				$template = \Models\EmailTemplate::getTemplate('Forgotten Password Link');
				if ($template) {

					$fp_token = new \Models\ForgottenPasswordToken;
					$fp_token->user_id = $user->id;
					$fp_token->token = uniqid("fp", true);
					$fp_token->status = true;
					$fp_token->save();

					$email_queue = new \Models\EmailQueue;
					$email_queue->email_template_id = $template->id;
					$email_queue->recipients = [$user->id];
					$email_queue->from = null;
					$email_queue->custom_variables = json_encode([
						'FORGOTTENPASSWORDID' => $fp_token->token,
					]);
					$email_queue->save();
				}
			}

			$response
				->getBody()
				->write($response_text)
			;
			return $response;
		} catch(Exception $e) {
			return \APP\Tools::returnCode($request, $response, 401, 'We couldn\'t find an account with the given username or email.');
		}
	})->add(new RateLimitMiddleware(60, 10));
});

$app->group("/password", function($group) {
	$group->get("/reset", function (Request $request, Response $response, array $args) {
		return $this->get('view')->render($response, 'html/resetpassword.html', [
			"LMSUrl" => $this->get('settings')["LMSUrl"],
			"LMSUri" => $this->get('settings')["LMSUri"],
			"LMSAppUri" => $this->get('settings')["LMSAppUri"],
			"LMSTplsUri" => $this->get('settings')["LMSTplsUri"],
			"LMSTplsUriHTML" => $this->get('settings')["LMSTplsUriHTML"],
			"LMSTplsUriCombined" => $this->get('settings')["LMSTplsUriCombined"],
			"LMSPasswordPattern" => $this->get('settings')["LMSPasswordPattern"],
			"passwordResetPageCustomCode" => \APP\Tools::getConfig('passwordResetPageCustomCode'),
		]);
	});


	$group->put("/reset-password", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (!isset($data['username'])) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$user = \Models\User
			::where("username", "=", $data["username"])
			->orWhere("email", "=", $data["username"])
			->where('status', 1)
			->where(function($query) {
				$query
					->whereNull("expiration_dt")
					->orWhere("expiration_dt", ">", \Carbon\Carbon::now())
				;
			})
			->first()
		;

		if (!$user) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		if (
			$data["username"] &&
			$user
		) {
			$old_password = isset($data["old_password"]) ? $data["old_password"] : false;
			$new_password = $data["new_password"];
			if (
				(
					(
						$old_password &&
						$user->reset_password != 1 &&
						password_verify($old_password, $user->password)
					) ||
					$user->reset_password == 1
				) &&
				preg_match('/' . $this->get('settings')["LMSPasswordPattern"] . '/', $new_password)
			) {
				$password_check_response = \APP\Tools::passwordStrengthCheck($new_password, $request, $response);
				if ($password_check_response) {
					return $password_check_response;
				}
				$user->password = password_hash($new_password, PASSWORD_BCRYPT, ['cost' => 12]);
				$user->password_attempts = 0 ;
				$user->password_changed_at = \Carbon\Carbon::now();
				$user->password_force_reset = 0 ;
				$user->reset_password = 0 ;
				$user->save();
				return $response;
			} else {
				return \APP\Tools::returnCode($request, $response, 403, \APP\Templates::translate('%%password_pattern_error_message%%'));
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403, "Error, Please contact the administrator");
		}

	})->add(new RateLimitMiddleware());
});
$app->group("/login", function($group) {

	$group->get("/sso/{user_id:.+}/{signature:.+}",  function (Request $request, Response $response, array $args) {

		$key = $this->get('settings')["TokenLoginKey"] ?? false;
		if (\APP\Auth::loginByToken($args["user_id"], $args["signature"], $key)){

			// redirect to original URL that was used to come into site, problem is, when admin logs in, he should not be reidirected to learner and when trainee logs in, he should not be redirected to any admin site.
			$is_trainee = \APP\Auth::isLearner();
			$redirect_uri = $this->get('settings')["LMSAppUri"] . \APP\Auth::getRedirectUri($this->get('settings'));
			$session_url = isset($_SESSION["original_url"]) && $_SESSION["original_url"] ? $_SESSION["original_url"] : false;

			// check if you are trainee and original url contains learner in it, if yes, redirect to that url
			$redirect_uri = $is_trainee && $session_url && strpos($session_url, 'learner') ? $session_url : $redirect_uri;
			// if you are admin, check if url does not contain learner, then redirect to url or redirect to managament choice, default url.
			$redirect_uri = !$is_trainee && $session_url && !strpos($session_url, 'learner') ? $session_url : $redirect_uri;

			unset($_SESSION["original_url"]);

			return $response->withStatus(302)->withHeader('Location', $redirect_uri);

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
	});

	$group->get("/google/{accesstoken:.+}",  function (Request $request, Response $response, array $args) {
		$access_token = $args["accesstoken"];

		$client_id = "571380418952-0hfqag42p9aqi7e5290d75htbb900ujq.apps.googleusercontent.com";

		if ($token_info = file_get_contents("https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=" . $access_token))
		{
			$token = json_decode($token_info);

			if ($token->aud == $client_id) {
				try {
					$user = \APP\Autoregister::loginByEmailOrRegister(
						$token->email,
						"password123",
						$token->given_name,
						$token->family_name,
						"google"
					);
					if (!\APP\Auth::login($user->username, $user->password, true)) {
						throw new Exception("Can't login!");
					}
				} catch(Exception $ex) {
					$response->getBody()->write("Unable to login by Google.");
					$response->getBody()->write($ex->getMessage());
					$response = $response->withStatus(500);
					return $response;
				}

			} else {
				return \APP\Tools::returnCode($request, $response, 401, "Client id doesn't match");
			}

		} else {
			return \APP\Tools::returnCode($request, $response, 401, "Can't access Google API");
		}

		// redirect to original URL that was used to come into site, problem is, when admin logs in, he should not be reidirected to learner and when trainee logs in, he should not be redirected to any admin site.
		$is_trainee = !\APP\Auth::isAdmin() || !\APP\Auth::isManager();
		$redirect_uri = $this->get('settings')["LMSAppUri"] . \APP\Auth::getRedirectUri($this->get('settings'));
		$session_url = isset($_SESSION["original_url"]) && $_SESSION["original_url"] ? $_SESSION["original_url"] : false;

		// check if you are trainee and original url contains learner in it, if yes, redirect to that url
		$redirect_uri = $is_trainee && $session_url && strpos($session_url, 'learner') ? $session_url : $redirect_uri;
		// if you are admin, check if url does not contain learner, then redirect to url or redirect to managament choice, default url.
		$redirect_uri = !$is_trainee && $session_url && !strpos($session_url, 'learner') ? $session_url : $redirect_uri;

		unset($_SESSION["original_url"]);

		$response->getBody()->write($redirect_uri);
		return $response;

	});

	$group->get("/fb/{accesstoken:.+}",  function (Request $request, Response $response, array $args) {

		$access_token = $args["accesstoken"];

		$fb = new  \Facebook\Facebook([
				'app_id' => '1964984646850851',
				'app_secret' => '0246d1651d75859126d078262b84b1c6',
				'default_graph_version' => 'v2.10',
				'http_client_handler' => 'stream',
				//'default_access_token' => '{access-token}', // optional
		]);

		try {
			$fb_response = $fb->get('/me?fields=id,first_name,last_name,email', $access_token);
		} catch(\Facebook\Exceptions\FacebookResponseException $e) {
			return \APP\Tools::returnCode($request, $response, 401);
		} catch(\Facebook\Exceptions\FacebookSDKException $e) {
			return \APP\Tools::returnCode($request, $response, 401);
		}


		$me = $fb_response->getGraphUser();

		try {
			$user = \APP\Autoregister::loginByEmailOrRegister(
					$me->getField("email"),
					"password123",
					$me->getField("first_name"),
					$me->getField("last_name"),
					"facebook"
				)
			;
			if (!\APP\Auth::login($user->username, $user->password, true)) {
				throw new Exception("Can't login!");
			}
		} catch(Exception $ex) {
			$response->getBody()->write("Unable to login by Facebook.");
			$response->getBody()->write($ex->getMessage());
			$response = $response->withStatus(500);
			return $response;
		}

		// redirect to original URL that was used to come into site, problem is, when admin logs in, he should not be reidirected to learner and when trainee logs in, he should not be redirected to any admin site.
		$is_trainee = !\APP\Auth::isAdmin() || !\APP\Auth::isManager();
		$hideIntro = FigRequestCookies::get($request, 'hideIntro', false)->getValue();
		$redirect_uri = $this->get('settings')["LMSAppUri"] . \APP\Auth::getRedirectUri($hideIntro);
		$session_url = isset($_SESSION["original_url"]) && $_SESSION["original_url"] ? $_SESSION["original_url"] : false;

		// check if you are trainee and original url contains learner in it, if yes, redirect to that url
		$redirect_uri = $is_trainee && $session_url && strpos($session_url, 'learner') ? $session_url : $redirect_uri;
		// if you are admin, check if url does not contain learner, then redirect to url or redirect to managament choice, default url.
		$redirect_uri = !$is_trainee && $session_url && !strpos($session_url, 'learner') ? $session_url : $redirect_uri;

		unset($_SESSION["original_url"]);

		$response->getBody()->write($redirect_uri);
		return $response;
	});

	$group->get("/config",  function (Request $request, Response $response, array $args) {
		unset($_SESSION["original_url"]);
		$response
			->getBody()
			->write(\APP\Tools::getConfig('SingleSignOnButtonURL'))
		;
		return
			$response
				->withHeader('Content-Type', 'text/html')
		;
	});

	$group->get("{urlextension:\/?.*}",  function (Request $request, Response $response, array $args) {


		$DefaultLoginBg = $this->get('settings')["LMSUri"] . 'images/licensing/' . $this->get('settings')["licensing"]['version'] . 'bg.jpg';
		$DefaultLogo = $this->get('settings')["DefaultLogo"];

		// url extension query in companies table, used in URL's format like: "http://openlms/login/pfpg"

		$company = false;
		if (!empty($args['urlextension'])) {
			$company = \Models\Company
				::where('urlextension', str_replace('/', '', $args['urlextension']))
				->first()
			;
		}

		$branded_company_urlextension = '';
		$branded_company_welcome = false;
		$branded_company_login_bg = false;
		if ($company) {
			$branded_company_urlextension = $company->urlextension;
			if (
				!empty($company->logo) &&
				is_file($this->get('settings')["CompanyLogosPath"] . $company->logo)
			) {
				$DefaultLogo = $this->get('settings')["LMSCompanyLogosUri"] . $company->logo;
			}

			if (
				!empty($company->login_bg) &&
				is_file($this->get('settings')["CompanyLoginBgPath"] . $company->login_bg)
			) {
				$DefaultLoginBg = $this->get('settings')["LMSCompanyLoginBgUri"] . $company->login_bg;
			}
			if (
				!empty($company->message) &&
				$company->message != null &&
				$company->message != 'null'
			) {
				$branded_company_welcome = $company->message;
			}
		}

		// Check if registraton is allowed
		$allowRegistration = \APP\Tools::getConfig('allowRegistration');

		$DefaultWelcomeText = $this->get('settings')["DefaultWelcomeText"];
		// Preserve custom welcome messages from config file
		if (
			$DefaultWelcomeText == 'Welcome to Open eLMS Pro - the business focussed Learning Content Management System from Open eLMS.'
		) {
			$DefaultWelcomeText = $this->get('settings')["licensing"]['welcomeMessage'];
		}
		if (\APP\Tools::getConfig('DefaultWelcomeText')) {
			$DefaultWelcomeText = \APP\Tools::getConfig('DefaultWelcomeText');
		}

		if ($branded_company_welcome) {
			$DefaultWelcomeText = $branded_company_welcome;
		}

		if (!\APP\Tools::getConfig('allowIframeEmbedFromDomains')) {
			$response = $response
				->withHeader('Content-Security-Policy', 'frame-ancestors \'self\'')
			;
		}
		$ResetPasswordText = \APP\Tools::getConfig('ResetPasswordText');
		$ResetPasswordLogo = \APP\Tools::getConfig('ResetPasswordLogo');

		if ($ResetPasswordText === null || $ResetPasswordText == '') {
			$ResetPasswordText = $DefaultWelcomeText;
		}

		if ($ResetPasswordLogo === null || $ResetPasswordLogo == '') {
			if (isset($DefaultLogo)) {
				$ResetPasswordLogo = $DefaultLogo;
			}
		}


		$vars = \APP\Templates::getVariables($this->get('settings'));
		$vars_merged = array_merge($vars, [
			"registration" => $allowRegistration,
			"DefaultLoginBg" => $DefaultLoginBg,
			"DefaultWelcomeText" => $DefaultWelcomeText,
			"ResetPasswordText" => $ResetPasswordText,
			"ResetPasswordLogo" => $ResetPasswordLogo,
			"branded_company_urlextension" => $branded_company_urlextension,
            "company_id" => $company ? $company->id : false,
			"title" => \APP\Templates::translate('%%title__login%%'),
		]);
		
		// Ensure our URL-based company logo takes precedence over session-based logo
		$vars_merged["DefaultLogo"] = $DefaultLogo;
		return $this->get('view')->render(
			$response,
			$this->get('settings')["customLoginPage"] && is_file($this->get('settings')["LMSTplsPath"] . 'html/custom/' . $this->get('settings')["customLoginPage"]) ? 'html/custom/' . $this->get('settings')["customLoginPage"] : 'html/login.html',
			$vars_merged
		);
	});

	$group->post("/mobile",  function (Request $request, Response $response) {
		$data = $request->getParsedBody();


		if (\APP\Auth::login($data["username"], $data["password"])) {
			print \APP\Auth::getSessionId();
		} else {
			return \APP\Tools::returnCode($request, $response, 401, 'No such user');
		}
	});

	$group->post("/demo",  function (Request $request, Response $response) {
		$data = $request->getParsedBody();


		if (\APP\Auth::isDemoEnabled() && \APP\Auth::loginDemo()) {
			$redirect_uri = $this->get('settings')["LMSAppUri"] . \APP\Auth::getRedirectUri();
			$response->getBody()->write($redirect_uri);
			return $response;
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
	});

	$group->post("",  function (Request $request, Response $response) {

		$data = $request->getParsedBody();

		$redirect = false;
		if (
			isset($data['referer']) &&
			$data['referer'] == 'openelms.com' &&
			isset($data['username']) &&
			isset($data['password'])
		) {
			$data["user.username"] = $data['username'];
			$data["user.password"] = $data['password'];
			$redirect = true;
		}

		// for mobile app log in
		$response = \APP\Tools::cors($response);

		$google_2FA_code = false;

		if (isset($data["user.enable_google_2FA"])) {
			$google_2FA_code = str_replace(" ", "", trim($data["user.enable_google_2FA"]));
			if (empty($google_2FA_code)) {
				$google_2FA_code = false;
			}
		}

		if (
			empty($data["user.username"]) ||
			empty($data["user.password"])
		) {
			return \APP\Tools::returnCode($request, $response, 400, 'Username and password are required');
		}

		if (\APP\Auth::login($data["user.username"], $data["user.password"], false, false, $google_2FA_code)) {

			// If user logged in using company url extension, set that into session and overwrite defaults with company settings, if set.
			if (
				(
					!empty($data["user.branded_company_urlextension"]) &&
					$data["user.branded_company_urlextension"] > ''
				) ||
				\APP\Auth::getUserCompanyId()
			) {
				$company_id = false;
				if (
					!empty($data["user.branded_company_urlextension"]) &&
					$data["user.branded_company_urlextension"] > ''
				) {
					$company = \Models\Company
						::where('urlextension', $data["user.branded_company_urlextension"])
						->first()
					;
					if ($company) {
						$company_id = $company->id;
					}
				}

				// Check if user is assigned to company, then overwrite branded company to his!
				$_SESSION["branded_company"] = (\APP\Auth::getUserCompanyId() ? \APP\Auth::getUserCompanyId() : $company_id);
			} else {
				unset($_SESSION["branded_company"]);
			}

			// redirect to original URL that was used to come into site, problem is, when admin logs in, he should not be reidirected to learner and when trainee logs in, he should not be redirected to any admin site.
			$is_trainee = \APP\Auth::isLearner();
			$redirect_uri = $this->get('settings')["LMSAppUri"] . \APP\Auth::getRedirectUri($this->get('settings'));
			$session_url = isset($_SESSION["original_url"]) && $_SESSION["original_url"] ? $_SESSION["original_url"] : false;


			// check if you are trainee and original url contains learner in it, if yes, redirect to that url
			$redirect_uri = $is_trainee && $session_url && strpos($session_url, 'learner') ? $session_url : $redirect_uri;
			// if you are admin, check if url does not contain learner, then redirect to url or redirect to managament choice, default url.
			$redirect_uri = !$is_trainee && $session_url && !strpos($session_url, 'learner') ? $session_url : $redirect_uri;



			unset($_SESSION["original_url"]);


			if ($redirect) {
				return $response->withHeader('Location', $redirect_uri)->withStatus(302);
			} else {
				$response->getBody()->write($redirect_uri);
				return $response;
			}
		} else {
			$error = \APP\Auth::getError() ?? "No such user";
			if($error == "forcePasswordReset")
			{
                $user = \Models\User::where('username', $data["user.username"])->select('reset_password')->first();
				$redirect_url = $this->get('settings')["LMSUrl"] . "password/reset";

                if ($user->reset_password == 1) {
                    $error = json_encode(["code" => $error, "resetURL" => $redirect_url, "username"=>$data["user.username"], 'reset_password' => true]);

                } else {
                    $error = json_encode(["code" => $error, "resetURL" => $redirect_url, "username"=>$data["user.username"]]);

                }

			}
			if($error == "maxPasswordAttempts")
			{
				$redirect_url = $this->get('settings')["LMSUrl"] . "password/reset";
				$error = json_encode(["code" => $error, "resetURL" => $redirect_url, "username"=>$data["user.username"]]);

			}
			if (in_array($error, ["no2FACode", "wrong2FACode"])){
				if ($user = \APP\Auth::getUser()){
					if (!$user->enabled_google_2FA) {
						//2FA not enabled, show QR code
						$g = new \Google\Authenticator\GoogleAuthenticator();
						$secret = $g->generateSecret();
						$user->google_2FA_secret = $secret;
						$user->save();

						// Your existing variables
						$username = $user->username; // Assuming this is the user's username
						$issuer = urlencode($this->get('settings')["LMSName"] . $this->get('settings')["LMSUri"]); // The issuer, urlencoded to handle spaces and special characters
						$secret = $user->google_2FA_secret; // The user's 2FA secret key

						// The otpauth string that represents the 2FA details
						$otpAuthString = "otpauth://totp/{$username}@{$issuer}?secret={$secret}&issuer={$issuer}";

						// Create the QR code instance
						$qrCode = new QrCode($otpAuthString);
						$qrCode->setSize(200);

						// Set other options as needed
						$qrCode->setMargin(0);

						// Write the QR code data to a string
						$qrCodeData = $qrCode->writeString();

						// Base64 encode the data
						$googleAuthQRUrl = base64_encode($qrCodeData);


					} else {
						//2FA already enabled
						$googleAuthQRUrl = false;
					}
					$error = json_encode(["code" => $error, "googleAuthQRUrl" => $googleAuthQRUrl, "secret"]);
				}
			}
			if ($redirect) {
				return $response
					->withHeader('Location', $this->get('settings')["LMSUrl"] . "login?show_register=1")
					->withStatus(302)
				;
			} else {
				if (!$error) {
					$error = "Invalid login credentials. Please check your email and password and try again.";
				}
				return \APP\Tools::returnCode($request, $response, 401, $error, ['type' => 'manual log in']);
			}

		}
	})->add(new RateLimitMiddleware(60, 20, 'Login temporarily disabled due to multiple failed attempts. Please try again later.'));

	/**
	 * Handles POST requests to the "/v2/mobile" endpoint for mobile authentication.
	 *
	 * This function processes incoming requests to authenticate a user based on
	 * their phone number, token, and device ID. It checks if the user exists,
	 * validates the token and its expiration, and ensures the request is from
	 * the authorized device. If the token is valid, the user is logged in and
	 * a session hash is returned. If no token is provided, a new short-lived
	 * token is generated and stored for the user.
	 *
	 * @param Request $request The incoming HTTP request.
	 * @param Response $response The HTTP response to be returned.
	 *
	 * @return Response JSON response with authentication status and relevant data.
	 */
	$group->post("/v2/mobile", function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$phone = $data['phone_number'] ?? null;
		$token = $data['token'] ?? null;
		$deviceId = $data['device_id'] ?? null;

		// Default response structure
		$basicResponse = [
			'status' => false,
			'response_code' => '200'
		];

		// Check if user exists in LMS
		$user = \Models\User::where('phone', $phone)->first();

		if (!$user) {
			$response->getBody()->write(json_encode(['status' => false, 'response_code' => '404 No such user']));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(404);
		}

		// If token exists, validate token & check expiration
		if (!is_null($token)) {
			// Retrieve user's token
			$authToken = $user->centralAuthTokens;

			if (!$authToken || $authToken->token !== $token) {
				$response->getBody()->write(json_encode(['status' => false, 'response_code' => '401 Invalid Token']));
            	return $response->withHeader('Content-Type', 'application/json')->withStatus(401);
			}

			// Check if token is expired
			if ($authToken->expires_at < time()) {
				$response->getBody()->write(json_encode(['status' => false, 'response_code' => '401 Token Expired']));
            	return $response->withHeader('Content-Type', 'application/json')->withStatus(401);
			}

			// Validate if the request is from the same device
			if ($authToken->device_id != $deviceId) {
				$response->getBody()->write(json_encode(['status' => false, 'response_code' => '403 Unauthorized Device']));
            	return $response->withHeader('Content-Type', 'application/json')->withStatus(403);
			}
			// Proceed with login if token is valid
			if (\APP\Auth::loginMobile($phone, null, false, true)) {

				$responseData = [
					'status' => true,
					'LMSSessionHash' => \APP\Auth::getSessionId(),
					'sid' => session_id(),
				];

				// Close session early if no updates are needed
				// session_write_close();

				$response->getBody()->write(json_encode($responseData));
            	return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
			}

			// remove central auth token once it has been validated
			$user->centralAuthTokens()->delete();

			$response->getBody()->write(json_encode(['status' => false, 'response_code' => '401 Unable to log in']));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(401);
		}

		// Generate a new short-lived token
		$newToken = bin2hex(random_bytes(32));
		$expiresAt = time() + (5 * 60); // 5 minutes expiration

		// Store or update the token, user can have only one token at a time
		$user->centralAuthTokens()->updateOrCreate(
			['user_id' => $user->id],
			['token' => $newToken, 'expires_at' => $expiresAt,  'device_id' => $deviceId]
		);

		$response->getBody()->write(json_encode([
			'status' => true,
			'centralAuthToken' => $newToken,
			'response_code' => '200'
		]));

		return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
	});
});

$app->get("/logout[/{state}]", function (Request $request, Response $response, array $args) {
	$state = isset($args["state"]) && $args["state"] ? $args["state"] : 'logout';
	\APP\Auth::logout($state);
	return $response;
});

$app->post("/register",  function (Request $request, Response $response) {

	$data = $request->getParsedBody();

	// if no-password option is passed, generate random password and send user an e-mail to reset password.
	// No password means that user registered for free demo, from: https://www.openelms.com/page/open-elms-lms-meets-netflix
	// Log-in user and redirect

	// Start output buffering at the beginning
	if (ob_get_length() === false) {
		ob_start();
	}

	if (
		isset($data['no_password']) &&
		$data['no_password'] == true
	) {
		// Weird way to do it, but it works.
		echo '<body style="text-align:center; color:#fff; background-color: #333; align-items: center; justify-content: center;"><div style="display: flex; flex-direction: column; align-items: center;"><div><?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0px" y="0px" viewBox="0 0 400 400" id="svg6" width="256" height="256"><path d="m 257.63882,266.38164 -28.57008,-17.82469 c 0.25285,-2.65477 0.37926,-5.18309 0.37926,-7.83782 0,-2.65477 -0.12641,-5.3095 -0.37926,-7.96422 l 28.57008,-17.82469 c 1.89625,-1.26419 2.78116,-3.6661 2.14908,-5.81517 l -4.55099,-14.15863 c -0.75849,-2.14907 -2.78116,-3.66606 -5.18306,-3.41325 l -33.62674,2.27551 c -2.78116,-4.55097 -5.81515,-8.84914 -9.3548,-12.76805 l 12.64162,-31.22482 c 0.88492,-2.14911 0.12641,-4.55098 -1.64341,-5.94157 l -12.00955,-8.72274 c -1.89624,-1.39058 -4.42456,-1.26414 -6.19439,0.25305 l -25.78893,21.6172 c -4.80382,-2.0227 -9.86047,-3.6661 -15.04354,-4.93028 l -8.21706,-32.61537 c -0.50566,-2.27551 -2.52833,-3.7925 -4.93023,-3.7925 h -14.91713 c -2.27549,0 -4.29815,1.51699 -4.93024,3.7925 l -8.21706,32.61537 c -5.18306,1.13778 -10.23971,2.78118 -15.04353,4.93028 l -25.788927,-21.6172 c -1.769828,-1.51699 -4.298154,-1.51699 -6.194401,-0.25305 l -12.009548,8.7227 c -1.896244,1.39059 -2.528326,3.7925 -1.643411,5.94157 l 12.641631,31.22486 c -3.41324,3.91891 -6.573648,8.21708 -9.354805,12.76805 l -33.626738,-2.27551 c -2.275494,-0.12633 -4.42457,1.26418 -5.183068,3.41325 l -4.550987,14.03223 c -0.758498,2.14906 0.126414,4.55097 2.149076,5.81512 l 28.570086,17.8247 c -0.252813,2.65476 -0.379258,5.18308 -0.379258,7.83781 0,2.65476 0.126406,5.30949 0.379258,7.83782 l -28.570086,17.82469 c -1.896244,1.26418 -2.781158,3.66609 -2.149076,5.81516 l 4.550987,14.15863 c 0.758498,2.14907 2.781158,3.66606 5.183068,3.41326 l 33.626738,-2.27551 c 2.781157,4.55097 5.815147,8.84913 9.354805,12.76804 L 57.142573,331.2332 c -0.884915,2.14906 -0.126407,4.55098 1.643411,5.94156 l 12.009548,8.72274 c 1.896247,1.39055 4.424573,1.26414 6.194401,-0.25266 l 25.788927,-21.61719 c 4.80382,2.02265 9.86047,3.66609 15.04353,4.93024 l 8.21706,32.6154 c 0.50567,2.2755 2.52833,3.7925 4.93024,3.7925 h 14.91713 c 2.27549,0 4.29815,-1.517 4.93023,-3.7925 l 8.21706,-32.6154 c 5.18307,-1.13775 10.23972,-2.78115 15.04354,-4.93024 l 25.78893,21.61719 c 1.76983,1.51699 4.29815,1.51699 6.19439,0.25266 l 12.00955,-8.72274 c 1.89625,-1.39058 2.52833,-3.7925 1.64341,-5.94156 l -12.64162,-31.22483 c 3.41324,-3.91891 6.57364,-8.21707 9.3548,-12.76804 l 33.62674,2.27551 c 2.27549,0.12632 4.42457,-1.26419 5.18306,-3.41326 l 4.55099,-13.90581 c 0.63208,-2.27548 -0.12641,-4.55099 -2.14908,-5.81513 z m -119.21057,31.09842 c -31.47766,0 -56.88733,-25.40971 -56.88733,-56.88733 0,-31.47768 25.40967,-56.88737 56.88733,-56.88737 31.47766,0 56.88733,25.40969 56.88733,56.88737 0,31.47762 -25.40967,56.88733 -56.88733,56.88733 z" id="gearBig" style="fill:#f79420;stroke-width:1.26416302"><animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 138 241" to="36 138 241" dur="0.75" repeatCount="indefinite" /></path><path d="m 381.5268,166.76558 -25.40968,-24.52474 c 0.37926,-2.78117 0.63208,-5.56235 0.63208,-8.34348 0,-2.78117 -0.25281,-5.56235 -0.63208,-8.34348 L 381.5268,101.0291 c 1.64341,-1.643398 2.02266,-4.171714 0.88491,-6.067968 l -7.45856,-12.894457 c -1.13775,-2.022657 -3.53965,-2.90758 -5.68873,-2.275509 l -34.00599,9.734068 c -4.29815,-3.413245 -9.22839,-6.320825 -14.41145,-8.343481 l -8.59632,-34.258805 c -0.50565,-2.275509 -2.52832,-3.792502 -4.93023,-3.792502 h -14.91712 c -2.2755,0 -4.29816,1.516993 -4.93024,3.792502 l -8.59631,34.258805 c -5.18307,2.022656 -10.11331,4.930236 -14.41145,8.343481 l -34.00599,-9.734068 c -2.27549,-0.632071 -4.55099,0.379374 -5.68874,2.275509 l -7.45856,12.894457 c -1.13774,2.022657 -0.75849,4.550982 0.88492,6.067968 l 25.40967,24.52478 c -0.37925,2.78113 -0.63208,5.56231 -0.63208,8.34348 0,2.78113 0.25282,5.56231 0.63208,8.34348 l -25.53609,24.52474 c -1.64341,1.64344 -2.02266,4.17176 -0.88491,6.06797 l 7.45856,12.89446 c 1.13774,2.0227 3.53966,2.90759 5.68873,2.27551 l 34.00598,-9.73407 c 4.29816,3.41325 9.2284,6.32082 14.41146,8.34348 l 8.59632,34.25884 c 0.50566,2.27548 2.52832,3.79247 4.93023,3.79247 h 14.91712 c 2.2755,0 4.29815,-1.51699 4.93024,-3.79247 l 8.59631,-34.25884 c 5.18307,-2.02266 10.1133,-4.93023 14.41146,-8.34348 l 34.00598,9.73407 c 2.2755,0.63208 4.55099,-0.37938 5.68873,-2.27551 l 7.45856,-12.89446 c 1.26417,-1.89621 0.88492,-4.42453 -0.75849,-6.06797 z m -81.66494,-4.67738 c -15.67561,0 -28.31725,-12.64165 -28.31725,-28.31728 0,-15.67559 12.64164,-28.31724 28.31725,-28.31724 15.67562,0 28.31726,12.64165 28.31726,28.31724 0,15.67563 -12.76805,28.31728 -28.31726,28.31728 z" id="gearSmall" style="fill:#74bf44;stroke-width:1.26416302"><animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 300 134" to="-60 300 134" dur="0.75" repeatCount="indefinite" /></path></svg></div><div style="color: white; font-weight: 700; font-family: Arial, Helvetica, sans-serif;">Creating site - this will take a few seconds</div></div></body>';
		// Flush output buffer safely
		if (ob_get_length() > 0) {
			ob_end_flush();
			flush();
		}

		ob_start();

		$data["password"] = "Ul" . bin2hex(openssl_random_pseudo_bytes(10)) . "1";
        $data["reset_password"] = true;
	}

	// If registration is not allowed, fail
	if (
		!\APP\Tools::getConfig('allowRegistration') &&
		!\APP\Tools::getConfig('allowRemoteRegistration')
	) {
		return
			$response
				->withStatus(403)
		;
	}

    if (isset($data['is_learner'])){
        $config_key = 'defaultLearnerRole';
    }else{
        $config_key = 'defaultRegisterRole';
    }

	// If jackdaw is enabled AND defaultJackdawRegisterRole is set up correctly, register users as jackdaw role.
	if (
		$this->get('settings')['licensing']['isJackdawCloud'] &&
		\APP\Tools::getConfig('defaultJackdawRegisterRole') > 0
	) {
		$config_key = 'defaultJackdawRegisterRole';
	}

	$defaultRole = \APP\Tools::getConfig($config_key);

	if (!$defaultRole) {
		return
			$response
				->withStatus(403)
		;
	}

    $createSubscription = false;
	if (
		\APP\Tools::getConfig('sharedClients') &&
		$this->get('settings')['defaultClientManagerRoleID'] &&
        !isset($data['is_learner'])
	) {
		$defaultRole = $this->get('settings')['defaultClientManagerRoleID'];
		$createSubscription = true;
		$role_check = \Models\Role
			::find($defaultRole)
		;
		// Fail registration if default role is admin or manager or role is disabled
		if (
			$role_check->status == false ||
			$role_check->is_admin == true ||
			(
				$role_check->is_manager == true &&
				!$this->get('settings')["RegisterManagerialRole"]
			)
		) {
			return \APP\Tools::returnCode($request, $response, 403, 'Configuration problem, please contact your manager.');
		}
	}


	if (
			isset($data["password"]) &&
			$data["password"] &&
			(
				(
					isset($data['username']) &&
					$data['username']
				) ||
				(
					isset($data['email']) &&
					$data['email'] &&
					filter_var($data['email'], FILTER_VALIDATE_EMAIL)
				)
			) &&
			preg_match('/' . $this->get('settings')["LMSPasswordPattern"] .'/', $data["password"])
	) {

		if (empty($data['username'])) {
			$data['username'] = $data['email'];
		}

		// Check if username or/and email exists
		$username_check = \Models\User::where('username', $data['username'])->first();
		$email_check = \Models\User::where('email', $data['email'])->first();

		if (
			\APP\Tools::getConfig('uniqueEmailPerUser') &&
			$email_check
		){
				// No password indicates that this is auto register, display some sort of information what went wrong and how to proceed.
				if (
					isset($data['no_password']) &&
					$data['no_password'] == true
				) {
					$response
						->getBody()
						->write('<h1 style="color: #dc3545;">❌ E-mail is already taken.</h1><br> Please visit <a href="' .  $this->get('settings')["LMSUri"] . '" style="color:#a9aafd;"><strong>login page</strong></a> and log in with your credentials, or <a href="' .  $this->get('settings')["LMSUri"] . 'login?reset-password=1" style="color:#a9aafd;"><strong>Password reset page</strong></a> to recover your password.')
					;
				} else {
					$response
						->getBody()
						->write('E-mail is already taken.')
					;
				}

				return $response
					->withStatus(400)
					->withHeader('Content-Type', 'text/html')
				;

		}
		if (
			\APP\Tools::getConfig('uniqueUsernamePerUser') &&
			$username_check
		) {
				// No password indicates that this is auto register, display some sort of information what went wrong and how to proceed.
				if (
					isset($data['no_password']) &&
					$data['no_password'] == true
				) {
					$response
						->getBody()
						->write('<h1 style="color: #dc3545;">❌ Username is already taken.</h1><br> Please visit <a href="' .  $this->get('settings')["LMSUri"] . '" style="color:#a9aafd;"><strong>login page</strong></a> and log in with your credentials, or <a href="' .  $this->get('settings')["LMSUri"] . 'login?reset-password=1" style="color:#a9aafd;"><strong>Password reset page</strong></a> to recover your password.')
					;
				} else {
					$response
						->getBody()
						->write('Username is already taken.')
					;
				}
				return $response
					->withStatus(400)
					->withHeader('Content-Type', 'text/html')
				;
		}

		$user = new \Models\User;
		$user->username = $data['username'];
		$user->creation_notes = 'register from login screen';
		if (isset($data['no_password']) && $data['no_password'] == true) {
			$user->creation_notes = 'user registrated from remote location';
		} else {
			$password_check_response = \APP\Tools::passwordStrengthCheck($data["password"], $request, $response);
			if ($password_check_response) {
				return $password_check_response;
			}
		}
		//$user->usercode = time(); // User code needs to be generated too, so I pre-fill this with time, as this fields needs to be unique too.
		$user->password = password_hash($data["password"], PASSWORD_BCRYPT, ['cost' => 12]);
        if(isset($data["reset_password"]) && $data["reset_password"] == true){
            $user->reset_password = 1;
        }
		$defaultFieldList = $this->get('settings')['defaultRegistrationFieldList'];
		$customConfigData = (object) json_decode(\APP\Tools::getConfig('registrationFormFields'));

		if (json_last_error() === JSON_ERROR_NONE && isset($customConfigData->fields))	// if no errors, merging custom configuration in to the default one
		{
			$customFieldList = $customConfigData->fields;
			foreach ($defaultFieldList as $key => $firstItem)
			{
				$matchingItem = array_filter($customFieldList, function ($secondItem) use ($firstItem) { return $secondItem->slug == $firstItem['slug']; });
				if (!empty($matchingItem))
				{
					$matchingItem = reset($matchingItem);
					$defaultFieldList[$key] = array_merge((array) $firstItem, (array) $matchingItem);
				}
			}
		}

		$enabledFields = [];
		foreach ($defaultFieldList as $key => $item) if (isset($item['enabled'])) $enabledFields[$item['slug']] = $item['enabled'];

		if (isset($data['firstName']) && $enabledFields['firstName']) {
			$user->fname = $data['firstName'];
		}
		if (isset($data['lastName']) && $enabledFields['lastName']) {
			$user->lname = $data['lastName'];
		}
		if (isset($data['email']) && $enabledFields['email']) {
			$user->email = $data['email'];
		}
		if (isset($data['phone']) && $enabledFields['phone']) {
			$user->phone = $data['phone'];
		}
		if (isset($data['country']) && $enabledFields['country']) {
			$user->country_id = $data['country'];
		}
		if (isset($data['city']) && $data['city'] && $enabledFields['city']) {
			$user->city_id = $data['city'];
		}
		if (isset($data['school']) && $data['school'] && $enabledFields['school']) {
			$user->school = $data['school'];
		}
		if (
			isset($data['company']) &&
			$data['company'] &&
			$enabledFields['company'] && empty($data['company_slug'])
		) {
			$user->company_id = $data['company'];
		}
		if (isset($data['department']) && $data['department'] && $enabledFields['department']) {
			$user->department_id = $data['department'];
		}
		if (isset($data['job']) && $data['job'] && $enabledFields['job']) {
			$user->designation_id = $data['job'];
		}
		if (isset($data['location']) && $data['location'] && $enabledFields['location']) {
			$user->location_id = $data['location'];
		}
		if (isset($data['receiveEmails']) && $enabledFields['receiveEmails']) {
			$user->exclude_from_emails = !$data['receiveEmails'];
        }
        if(isset($data['dateOfBirth']) && $enabledFields['dateOfBirth']){
            $user->DateOfBirth = $data['dateOfBirth'];
        }
        if(isset($data['Postcode']) && $enabledFields['Postcode']){
            $user->Postcode = $data['Postcode'];
        }
        if(isset($data['address1']) && $enabledFields['address1']){
            $user->AddLine1 = $data['address1'];
        }
        if(isset($data['address2']) && $enabledFields['address2']){
            $user->AddLine2 = $data['address2'];
        }
        if(isset($data['address3']) && $enabledFields['address3']){
            $user->AddLine3 = $data['address3'];
        }
       if(isset($data['usercode']) && $enabledFields['usercode']){
            $user->usercode = $data['usercode'];
        }
		if (isset($data['NINumber']) && $data['NINumber'] && $enabledFields['NINumber']) {
			$user->NINumber = $data['NINumber'];
		}

		if (
			isset($data['company_text']) &&
			\APP\Tools::getConfig('RegisterCompanyText') && empty($data['company_slug'])
		) {
			$company_text = strtolower($data['company_text']);
			$company = \Models\Company::where(DB::raw('LOWER(name)'), '=', $company_text)->first();

			if (
				$company &&
				!\APP\Tools::getConfig('sharedClients') // With shared clients, new registration will allways create a new company
			) {
				$user->company_id = $company->id;
			} else {
				$company = \Models\Company
					::create(
						[
							'name' => $data['company_text'],
							'slug' => \APP\Tools::safeName($data['company_text'], '_', true) . '_' . uniqid(),
							'status' => 1,
						]
					)
				;

				// upload company logo if present and set color scheme for learners
				if (\APP\Tools::getConfig('sharedClients')) {
					$company->logo = \APP\Tools::uploadImage($this->get('settings')["CompanyLogosPath"], 'logo', $company->logo);
                    $company->urlextension = $company->slug;
                    $company->make_all_enrollable = true;

					// update learner_theme, dark/light
					$learner_theme = 'dark';
					if (isset($data['user_branding'])) {
						if (strpos($data['user_branding'], 'Light') !== false) {
							$learner_theme = 'light';
						}
					}
					\Models\TableExtension::updateField('companies', $company->id, 'learner_theme', $learner_theme);

					$company->save();
				}

				$user->company_id = $company->id;
			}
		}

		$user->role_id = $defaultRole;

		$user->status = true;
		$user->approval_status = 'In Progress';

		if (!empty($data['company_slug'])) {
			$companyId = \Models\Company::where('slug', $data['company_slug'])->first();
			$user->company_id = $companyId->id;
		}
		$user->save();

		// If user autoregistered, check from where it happened and assign demo data if condition is met.
		if (
			isset($data['no_password']) &&
			$data['no_password'] == true
		) {
			if (isset($data['registered_from'])) {
				switch ($data['registered_from']) {
					case 'apprentice':
						// assign programme(5) - from demo_business site to user
						$programme_check = \Models\ApprenticeshipStandard::find(5);
						if (
							$programme_check &&
							$user->id
						) {
							\Models\ApprenticeshipStandardUser::assignToStandard($user->id, 5, \Carbon\Carbon::now());
						}
						break;

					default:
						// assign programme(83) - from demo_business site to user
						$programme_check = \Models\ApprenticeshipStandard::find(83);
						if (
							$programme_check &&
							$user->id
						) {
							\Models\ApprenticeshipStandardUser::assignToStandard($user->id, 83, \Carbon\Carbon::now());
						}
						break;
				}
			}
		}

		if (
			$user->id &&
			\APP\Tools::getConfig('isApproveLearners')
		) {
			$response
				->getBody()
				->write(
					json_encode(
						[
							'status' => false,
							'msg' => "You have been successfully registered!. Please wait for the admin approval",
						]
					)
				)
			;
		 }

		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {
			$allowed_fields = [
				'Salutation', 'Gender__c', 'Health_Consent__c', 'Employment_Status__c', 'Contact_Source__c', 'Contact_Relationship__c', 'Year_of_Onset__c', 'SMILE_Relationship__c', 'MPC_Email_Opt_in__c',
			];

			foreach ($data["extended"] as $field_name => $value) {
				if (in_array($field_name, $allowed_fields)) {
					\Models\TableExtension::updateField('users', $user->id, $field_name, $value);
				}
			}
		}

		//If NRAS and allowRemoteRegistration, connect to Salesforce and sync data from there
		if (
			\APP\Tools::getConfig('allowRemoteRegistration') &&
			$this->get('settings')["licensing"]['version'] == 'nras' &&
			$user->email
		) {
			\APP\Api::salesforceUpdateLocalUser($user, true);

			// Send welcome email to user!
			$template = \Models\EmailTemplate::getTemplate('Registration Email');
			if ($template) {
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$user->id];
				$email_queue->from = null;
				$email_queue->save();
			}
		}

        // create subscription for user
        if ($createSubscription){
            if (\APP\Tools::getConfig('enableStripePayment') && \APP\Tools::getConfig('stripePublishableKey') != '') {
                $stripeController = new \APP\Controllers\StripeController($this);
                $stripeController->createDefaultCredits($user);
                // $stripeController->createDefaultSubscription($user);
            }
        }


		if (
			isset($data['no_password']) &&
			$data['no_password'] == true
		) {
			// if no_password was used, it means user registered directly with POST request, log him in and redirect to learner interface, send e-mail explaining about re-setting password.

			\APP\Auth::login($user->username, $data["password"]);

			$redirect_uri = $this->get('settings')["LMSAppUri"] . \APP\Auth::getRedirectUri();

			// Send e-mail, depending where registration came from, curently it should be either Jackdaw on open elms.
			if ($config_key == 'defaultJackdawRegisterRole') {
				$template = \Models\EmailTemplate::getTemplate('Jackdaw Licence Activated');
			} else {
				$template = \Models\EmailTemplate::getTemplate('%%registration_email%%');
			}

			if ($template) {
				$fp_token = new \Models\ForgottenPasswordToken;
				$fp_token->user_id = $user->id;
				$fp_token->token = uniqid("fp", true);
				$fp_token->status = true;
				$fp_token->save();

				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$user->id];
				$email_queue->from = null;
				$email_queue->custom_variables = json_encode([
					'FORGOTTENPASSWORDID' => $fp_token->token,
				]);
				$email_queue->save();
            }
            if($user->reset_password){
                $redirect_uri = $this->get('settings')["LMSUrl"] . "password/reset";
                echo "<script>localStorage.setItem('passedUserName', '$user->username');localStorage.setItem('passedErrorCode','');localStorage.setItem('resetPassword',true)</script>";
            }
			echo "<script>setTimeout(\"location.href = '$redirect_uri';\",100);</script>";
		}
	} else {
		return \APP\Tools::returnCode($request, $response, 400);
	}


	// log in user and redirect him!

	return $response;
})->add(new RateLimitMiddleware(60, 10));

$app->post("/register/mobile",  function (Request $request, Response $response, Logger $logger) {
	$data = $request->getParsedBody();

	try
	{
		foreach([
			"password" => "password",
			"fname" => "first name",
			"lname" => "last name",
			"username" => "username",
			"email" => "email",
		] as $field_id => $field_name){
			if (empty($data[$field_id]))
			{
				throw new Exception("No {$field_name} specified");
			}
		}

		\APP\Autoregister::registerUser(
				$data["username"],
				$data["email"],
				$data["password"],
				$data["fname"],
				$data["lname"],
				"mobile");

		return $response;

	}
	catch(Exception $ex)
	{
        $logger->error("User registration failed: " . $ex->getMessage(), ['exception' => $ex]);

        $error = [
            'message' => 'Unable to register user.',
            'error' => $ex->getMessage(),
        ];

        $response->getBody()->write(json_encode($error));
        return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
	}

})->add(new RateLimitMiddleware(60, 5));

$app->get("/checksession", function (Request $request, Response $response, array $args) {
	if (\APP\Auth::isSessionExpired()) {
		return \APP\Tools::returnCode($request, $response, 403);
	}

	// Return remaining session time for timeout warnings
	$remainingMinutes = \APP\Auth::getSessionRemainingMinutes();
	$data = [
		'status' => 'active',
		'remainingMinutes' => $remainingMinutes
	];

	$response->getBody()->write(json_encode($data));
	return $response->withHeader('Content-Type', 'application/json');
});

$app->get("/extend-session", function (Request $request, Response $response, array $args) {
	if (!\APP\Auth::checkSession()) {
		return \APP\Tools::returnCode($request, $response, 403);
	}

	// Update session timestamp to extend session
	if (isset($_SESSION['LMSUserId']) && isset($_SESSION['LMSSessionHash'])) {
		\Models\Session::where('user_id', $_SESSION['LMSUserId'])
			->where('hash', $_SESSION['LMSSessionHash'])
			->update(['updated_at' => new \DateTime()]);
	}

	$data = ['status' => 'extended'];
	$response->getBody()->write(json_encode($data));
	return $response->withHeader('Content-Type', 'application/json');
});

$app->get("/download/{type:[a-z]+}/{file}[/{auth_redirect}]", function (Request $request, Response $response, array $args) {

	if (!\APP\Auth::checkSession()) {
		if (isset($args["auth_redirect"]) && $args["auth_redirect"] == "auth") {
			$redirect_url = $this->get('settings')["LMSUri"] . 'login?redirect_url=' . urlencode($this->get('settings')["LMSUrl"] . 'download/' . $args["type"] . '/' . $args["file"]);
			return $response
				->withHeader('Location', $redirect_url)
				->withStatus(302)
			;
		}
		return \APP\Tools::returnCode($request, $response, 403);
	}

	if (!is_file($this->get('settings')["LMSTempPath"] . $args["file"])) {
		return \APP\Tools::returnCode($request, $response, 404);
	}

	switch($args["type"]) {
		case "excel":
				$response
					->getBody()
					->write(file_get_contents($this->get('settings')["LMSTempPath"] . $args["file"]))
				;

				return $response
					->withHeader('Content-Type', 'application/vnd.ms-excel')
					->withHeader('Content-Disposition','attachment;filename="' . $args["file"] . '"')
					->withHeader('Cache-Control','max-age=0')
					->withHeader('Cache-Control','max-age=1')
					->withHeader('Expires','Mon, 26 Jul 1997 05:00:00 GMT')
					->withHeader('Last-Modified',gmdate('D, d M Y H:i:s').' GMT')
					->withHeader('Cache-Control','cache, must-revalidate')
					->withHeader('Pragma','public')
				;
			break;
		case "ilr":
				$response
					->getBody()
					->write(file_get_contents($this->get('settings')["LMSTempPath"] . $args["file"]))
				;
				return $response
					->withHeader('Content-Type', 'application/xml')
					->withHeader('Content-Disposition','attachment;filename="' . $args["file"] . '"')
					->withHeader('Cache-Control','max-age=0')
					->withHeader('Cache-Control','max-age=1')
					->withHeader('Expires','Mon, 26 Jul 1997 05:00:00 GMT')
					->withHeader('Last-Modified',gmdate('D, d M Y H:i:s').' GMT')
					->withHeader('Cache-Control','cache, must-revalidate')
					->withHeader('Pragma','public')
				;
			break;
		default:
				return \APP\Tools::returnCode($request, $response, 403);
			break;
	}

})->add(new RateLimitMiddleware(60, 5));

$app->get("/reload", function (Request $request, Response $response, array $args) {

	if (\APP\Auth::isAdmin()) {
		\APP\Tools::combineJsAssets($this->get('settings'));
		\DB\CustomReviews::update();
		\DB\CustomReviews::updateFilters($this->get('settings'));

		$query = new \Models\Version;
		$query->save();
	}
	return $response;

})->add(\APP\Auth::getSessionCheck());

$app->group("/app", function($group) {
	$group->get("{page:\/.*|\/?}",  function (Request $request, Response $response, array $args)
	{
		if (Str::startsWith($args['page'], '/learner'))	// checking if a user who accesses learner content has a learner role active. if not active, but role available, switching it automatically.
		{
			$user = \APP\Auth::getUser();
			if (!$user) {
				return \APP\Tools::returnCode($request, $response, 403);
			}
			if (!\APP\Auth::isLearner())
			{
				$learnerRoles = \Models\Role::where('is_learner', true)->where('status', 1)->pluck('id')->toArray();
				$role = \Models\Role::find($user->role_id);

				if ($role->is_admin)
				{
					$learnerRoleID = $learnerRoles[0];
				}
				else
				{

					$accessibleRoles = \APP\Cache\RoleAccessCache::getAccessIds($user->role_id);
					$accessibleLearnerRoles = array_intersect($learnerRoles, $accessibleRoles);

					if (count($accessibleLearnerRoles) > 0)
					{
						$accessibleLearnerRoles = array_values($accessibleLearnerRoles);
						$learnerRoleID = $accessibleLearnerRoles[0];
					}
					else
					{
						header( "refresh:4;url=" . $this->get('settings')["LMSUri"] );
						echo "<h1>You have no access to this resource!</h1><small>You will be redirected back in 4 seconds...</small>";
						return \APP\Tools::returnCode($request, $response, 403);
					}
				}

				$user->shadow_role_id = $learnerRoleID;
				$user->save();
			}
		}

		$vars = \APP\Templates::getVariables($this->get('settings'));
		return $this->get('view')->render($response, 'html/app.html', $vars);
	});
})->add(\APP\Auth::getLoginCheck($settings["LMSUri"] . "login"));

/*
	Redirect user to his manage URL, if he is not learner
*/
$app->get("/manage", function (Request $request, Response $response, array $args) {
	$this->get('settings')['shadow'] = 1;
	$url = \Models\Structure::firstLink($this->get('settings'));
	return $response->withHeader('Location', $url)->withStatus(302);

})->add(\APP\Auth::getSessionCheck());


/*
	Will have eventually think about routing any static files trough application, for security reasons.
	Someday....
*/
$app->get("/file/{file}", function (Request $request, Response $response, array $args) {

	$files = [
		'1.jpg' => $this->get('settings')['LMSPublicPath'] . 'api/data/Sample course/images/thumbs/1.jpg',
		'intro.swf' => $this->get('settings')['LMSPublicPath'] . 'api/data/Sample course/intro.swf',
	];

	if (
		isset($args['file']) &&
		isset($files[$args['file']]) &&
		$files[$args['file']]
	) {
		$file = $files[$args['file']];
		if (is_file($file)) {
			$imageStream = new OpenStream($file, 'r');
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$response = $response
				->withHeader('Content-Length', filesize($file))
				->withHeader('Content-Type', finfo_file($finfo, $file))
				->withBody($imageStream)
			;
		} else {
			return \APP\Tools::returnCode($request, $response, 404);
		}
	} else {
		return \APP\Tools::returnCode($request, $response, 500, "Missing file or no permission to view");

	}
	return $response;

})->add(new RateLimitMiddleware())->add(\APP\Auth::getSessionCheck());



