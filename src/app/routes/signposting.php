<?php

use APP\Tools;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Signpost;

$app->group("/signposting", function ($group) {

    $group->post('/list', function (Request $request, Response $response) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        $query = \Models\Signpost::where("id", '>', 0);

        $data = \APP\SmartTable::searchPaginate($params, $query, false);

        $response->getBody()->write($data->toJson());
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-sign-posting', 'select'));

    $group->put('/update-status/{status:enable|disable}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $signpost = \Models\Signpost::find($args['id']);

        if ($args['status'] == 'enable') {
            $signpost->status = 1;
        } else {
            $signpost->status = 0;
        }
        $signpost->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-sign-posting', 'disable'));

    $group->post('/save', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();
        $keywords = implode(',', $params['keywords']);
        $data = [
            'name' => $params['name'],
            'keywords' => $keywords,
        ];
        $LMSUrl = $this->get('settings')["LMSUrl"];
        $signpost = \Models\Signpost::create($data);
        Signpost::updatedDomainList($params['domains'], '');
        $signpost->domains = $params['domains'];
        $signpost->code = "<div id='signpost_button'></div>\n<script src='{$LMSUrl}signposting/javascript/" . $signpost->id . "/signpost_button'></script>";
        $signpost->save();
        $learning_resources = $params['learning_resources'] ?? [];
        if ($signpost) {
            foreach ($learning_resources as $key => $value) {
                \Models\SignpostsLearningResource::updateOrCreate(['signpost_id' => $signpost->id, 'learning_module_id' => $value['id']], ['signpost_id' => $signpost->id, 'learning_module_id' => $value['id']]);
            }
        }

        $response->getBody()->write(json_encode($signpost));
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-sign-posting', 'insert'));

    $group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {

        $data = \Models\Signpost::with('LearningResources')->find($args['id']);

        $response->getBody()->write(json_encode($data));
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-sign-posting', 'select'));

    $group->put('/update/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();
        $keywords = implode(',', $params['keywords']);
        $data = [
            'name' => $params['name'],
            'keywords' => $keywords,
            'domains' => $params['domains']
            // 'code' => $params['code']
        ];
        $signpost = \Models\Signpost::where('id', $args['id'])->first();
          Signpost::updatedDomainList($params['domains'], $signpost->domains);
         \Models\Signpost::where('id', $args['id'])->update($data);
        $learning_resources = $params['learning_resources'] ?? [];

        if ($signpost) {
            \Models\SignpostsLearningResource::where('signpost_id', $args['id'])->delete();

            foreach ($learning_resources as $key => $value) {
                \Models\SignpostsLearningResource::updateOrCreate(['signpost_id' => $args['id'], 'learning_module_id' => $value['id']], ['signpost_id' => $args['id'], 'learning_module_id' => $value['id']]);
            }
        }

        $response->getBody()->write(json_encode($signpost));
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-sign-posting', 'insert'));

    $group->get('/javascript/{id:[0-9]+}/{div_id}', function (Request $request, Response $response, $args) {
        $signpost = \Models\Signpost::with('LearningResources')->find($args['id']);
        if (!$signpost) {
            $response->getBody()->write("<h1>Record doesn't exists</h1>");
            return $response;
        }
        $learningResource = $signpost->LearningResources()->first();
        if (!$learningResource) {
            $response->getBody()->write("<h1>No learning Resource attached</h1>");
            return $response;
        }

        $LMSUrl = $this->get('settings')["LMSUrl"];
        //get the URL of your website
        $website_url = "{$LMSUrl}app/learner/resources/".$learningResource->id;
        $div_id = $args['div_id'] ?? 'signpost_button';

        //generate the JavaScript snippet
        $script = <<<EOT
(function() {
    //set the URL of the iframe to load
    var iframe_url = "{$website_url}";

    //create the button element
    var button = document.createElement("button");
    button.innerText = "{$signpost->name}";

    //create the popup container element
    var popup_container = document.createElement("div");
    popup_container.style.display = "none";
    popup_container.style.position = "fixed";
    popup_container.style.top = "0";
    popup_container.style.left = "0";
    popup_container.style.width = "100%";
    popup_container.style.height = "100%";
    popup_container.style.background = "rgba(0, 0, 0, 0.5)";

    //create the iframe element
    var iframe = document.createElement("iframe");
    iframe.src = iframe_url;
    iframe.style.width = "80%";
    iframe.style.height = "80%";
    iframe.style.position = "absolute";
    iframe.style.top = "50%";
    iframe.style.left = "50%";
    iframe.style.transform = "translate(-50%, -50%)";

    //add the button and iframe to the popup container
    popup_container.appendChild(button);
    popup_container.appendChild(iframe);

    //get the div element to render the button and popup container inside
    var target_div = document.getElementById("{$div_id}");

    //add the button and popup container to the target div
    target_div.appendChild(button);
    // target_div.appendChild(popup_container);
    document.body.appendChild(popup_container);

    //add an event listener to the button
    button.addEventListener("click", function() {
        popup_container.style.display = "block";
    });

    //add an event listener to the popup container to close it when clicked outside
    popup_container.addEventListener("click", function(event) {
        if (event.target === popup_container) {
            popup_container.style.display = "none";
        }
    });
})();
EOT;

        $response->getBody()->write($script);
        return $response->withHeader('Content-Type', 'application/javascript; charset=utf-8');

    });

});
