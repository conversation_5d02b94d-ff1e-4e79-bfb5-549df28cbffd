<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/invoice",  function ($group) {

	// lists report and allows downloading of data
	$group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();
		$query_id = 'costanalysisList';
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		$option = isset($args["option"]) ? $args["option"] : "";

		switch($args["option"]) {
			case "/download":
				$query
					->selectRaw("CONCAT(users.fname, ' ', users.lname) as apprentice_name")
				;

				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				$export_fields = [
					"ID" => "users.id",
					"Apprentice" => "apprentice_name",
					"Company" => "company_name",
					"Department" => "department_name",
					"Standard" => "standard_name",
					"Cost(£)" => "cost",
					"Completion(%)" => "percentage",
					"Time Spent(h)" => "time_spent"
				];


				$download_file_name = uniqid("Cost_Analysis.report.") . ".xlsx";

				\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				);

				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
			break;
			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'Cost Analysis', // Template name
					['user', 'id'], // where to look for user's ID in this query
					$args
				);

			break;
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));

});
