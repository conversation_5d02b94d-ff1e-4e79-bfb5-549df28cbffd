<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/usercustomreview",  function ($group) {

    $group->post('/users/list', function (Request $request, Response $response) {

        $params = $request->getParsedBody();
        if (
        	isset($params["search"]) &&
        	isset($params["search"]["custom_review_id"])
        ) {
			$custom_review_id = $params["search"]["custom_review_id"];
			unset($params["search"]["custom_review_id"]);
		}

		$query = \Models\User
			::select("users.id", "fname", "lname", "email", "company_id", "role_id")
			->where("users.status", true)
			->with('Role')
			->whereIn('users.role_id',
				\Models\Role
					::select('id')
					->where('is_learner', false)
					->get()
			)
		;


		if (!empty($custom_review_id)) {
			$query = $query
				->with(["customreviews" => function($query) use($custom_review_id) {
					$query
						->select(["custom_reviews.id", "custom_reviews.name"])
						->where("custom_reviews.id", $custom_review_id);
					;
				}])
			;
		}


		if (isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query
					->whereIn('users.id',
						\Models\UserCustomReview
							::select('user_id')
							->where('custom_review_id', $custom_review_id)
							->get()
					)
				;
			}

			if ($params["search"]["assigned"] == "0") {
				$query
					->whereNotIn('users.id',
						\Models\UserCustomReview
							::select('user_id')
							->where('custom_review_id', $custom_review_id)
							->get()
					)
				;
			}
			unset($params["search"]["assigned"]);
		}



		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getAdminCheck());

    /*
    	Retrieve list of type and mark item if assigned to custom_review_id
    */
    $group->post('/{type:designation|department|group}/list', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();
		if (isset($params["search"]) && isset($params["search"]["custom_review_id"])) {
			$custom_review_id = $params["search"]["custom_review_id"];
			unset($params["search"]["custom_review_id"]);
		}

		switch ($args['type']) {
			case 'designation':
				$query = \Models\Designation
					::where('id', '>', 0)
				;
				break;

			case 'department':
				$query = \Models\Department
					::where('id', '>', 0)
					->with('company')
				;
				break;

			case 'group':
				$query = \Models\Group
					::where('id', '>', 0)
				;
				break;
		}


		$query = $query
			->selectRaw($args['type'] . "s.*")
			->where($args['type'] . "s.status", true)
		;

		if (!empty($custom_review_id)) {
			$query = $query
				->with(["customreviews" => function($query) use($custom_review_id) {
					$query
						->select(["custom_reviews.id", "custom_reviews.name"])
						->where("custom_reviews.id", $custom_review_id);
					;
				}])
			;
		}


		if (isset($params["search"]["assigned"])) {

			if ($params["search"]["assigned"] == "1") {
				$query
					->whereHas('customreviews', function ($query) use ($custom_review_id) {
						$query
							->where("custom_reviews.id", $custom_review_id);
						;
					})
				;
			}

			if ($params["search"]["assigned"] == "0") {
				$query
					->whereDoesntHave('customreviews', function ($query) use ($custom_review_id) {
						$query
							->where("custom_reviews.id", $custom_review_id);
						;
					})
				;
			}
			unset($params["search"]["assigned"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getAdminCheck());

    // Link department with custom report, permission
    $group->put('/department/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
        $dep = \Models\Department::find($args["id"]);
        $dep->CustomReviews()->syncWithoutDetaching($data["custom_review_id"]);


		$user_ids = \Models\User
			::where("department_id", $dep->id)
			->whereIn('users.role_id',
				\Models\Role
					::select('roles.id')
					->where('is_learner', false)
					->get()
			)
			->get()
			->pluck('id')
			->toArray()
		;

        $custom_review_id = $data["custom_review_id"];

        $data = [];

		foreach($user_ids as $user_id) {
			$data[] = [
				'custom_review_id' => $custom_review_id,
				'user_id' => $user_id,
				'created_at' => \Carbon\Carbon::now(),
				'updated_at' => \Carbon\Carbon::now(),
			];
		}

		\Models\UserCustomReview
			::where("custom_review_id", $custom_review_id)
			->whereIn("user_id", $user_ids)
			->delete()
		;
		\Models\UserCustomReview::insert($data);


		return $response;
	})->add(\APP\Auth::getAdminCheck());

    // Link designation/job with custom review
    $group->put('/designation/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$entry = \Models\Designation::find($args["id"]);

		$entry->CustomReviews()->syncWithoutDetaching($data["custom_review_id"]);

		$user_ids = \Models\User
			::where("designation_id", $entry->id)
			->whereIn('users.role_id',
				\Models\Role
					::select('id')
					->where('is_learner', false)
					->get()
			)
			->get()
			->pluck('id')
			->toArray()
        ;
        $custom_review_id = $data["custom_review_id"];
		$data = [];

		foreach($user_ids as $user_id) {
			$data[] = [
				'custom_review_id' => $custom_review_id,
				'user_id' => $user_id,
				'created_at' => \Carbon\Carbon::now(),
				'updated_at' => \Carbon\Carbon::now(),
			];
		}

		\Models\UserCustomReview
			::where("custom_review_id", $custom_review_id)
			->whereIn("user_id", $user_ids)
			->delete()
		;

		\Models\UserCustomReview::insert($data);

		return $response;
	})->add(\APP\Auth::getAdminCheck());

    $group->put('/group/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$entry = \Models\Group::find($args["id"]);

		$entry->CustomReviews()->syncWithoutDetaching($data["custom_review_id"]);

		$user_ids = \Models\User
			::whereIn("users.id",
				\Models\GroupUser
					::select('user_id')
					->where('group_id', $entry->id)
					->get()
				)
			->whereIn('users.role_id',
				\Models\Role
					::select('roles.id')
					->where('is_learner', false)
					->get()
			)
			->get()
			->pluck('id')
			->toArray()
        ;
        $custom_review_id = $data["custom_review_id"];
		$data = [];

		foreach($user_ids as $user_id) {
			$data[] = [
				'custom_review_id' => $custom_review_id,
				'user_id' => $user_id,
				'created_at' => \Carbon\Carbon::now(),
				'updated_at' => \Carbon\Carbon::now(),
			];
		}

		\Models\UserCustomReview
			::where("custom_review_id", $custom_review_id)
			->whereIn("user_id", $user_ids)
			->delete()
		;

		\Models\UserCustomReview::insert($data);

		return $response;
	})->add(\APP\Auth::getAdminCheck());


    // Link custom review to user
    $group->put('/users/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();
        $user = \Models\User::find($args["id"]);

        $user->CustomReviews()->syncWithoutDetaching($data["custom_review_id"]);

        return $response;
	})->add(\APP\Auth::getAdminCheck());

    $group->put('/department/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
        $dep = \Models\Department::find($args["id"]);
        $custom_review_id = $data["custom_review_id"];

        $dep->CustomReviews()->detach($custom_review_id);

		$d_user_ids = \Models\User
			::where("department_id", $dep->id)
			->get()
			->pluck('id')
			->toArray()
		;
		print_r(json_encode($d_user_ids));
		\Models\UserCustomReview
			::where("custom_review_id", $custom_review_id)
			->whereIn("user_id", $d_user_ids)
			->delete()
		;

		return $response;
	})->add(\APP\Auth::getAdminCheck());

    $group->put('/designation/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$entry = \Models\Designation::find($args["id"]);

		$custom_review_id = $data["custom_review_id"];
        $entry->CustomReviews()->detach($custom_review_id);

		$entry_user_ids = \Models\User
			::where("designation_id", $entry->id)
			->get()
			->pluck('id')
			->toArray()
		;

		\Models\UserCustomReview
			::where("custom_review_id", $custom_review_id)
			->whereIn("user_id", $entry_user_ids)
			->delete()
		;

		return $response;
	})->add(\APP\Auth::getAdminCheck());

    $group->put('/group/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$entry = \Models\Group::find($args["id"]);

		$custom_review_id = $data["custom_review_id"];
        $entry->CustomReviews()->detach($custom_review_id);

		$entry_user_ids = \Models\User
			::whereIn("id",
				\Models\GroupUser
					::select('user_id')
					->where('group_id', $entry->id)
					->get()
				)
			->get()
			->pluck('id')
			->toArray()
        ;

		\Models\UserCustomReview
			::where("custom_review_id", $custom_review_id)
			->whereIn("user_id", $entry_user_ids)
			->delete()
		;

		return $response;
	})->add(\APP\Auth::getAdminCheck());

    $group->put('/users/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
        $user = \Models\User::find($args["id"]);

        $custom_review_id = $data["custom_review_id"];
        $user->CustomReviews()->detach($custom_review_id);

		return $response;
	})->add(\APP\Auth::getAdminCheck());

});