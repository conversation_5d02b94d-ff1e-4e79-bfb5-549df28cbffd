<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/group",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$group = \Models\Group::with(["users" => function ($query) {
			$query->select('users.id')->where('users.status', 1);
		}])->find($args["id"]);

		$response->getBody()->write(json_encode($group));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'select'));

	// Update group details
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$group = \Models\Group::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$group->name = $data["name"];
		}
		if (isset($data["add_remove_resources"])) {
			$group->add_remove_resources = $data["add_remove_resources"];
		}
		if (isset($data["is_jackdaw_team"]) && $this->get('settings')['licensing']['isJackdawCloud']) {
			$group->is_jackdaw_team = $data["is_jackdaw_team"];
		}

		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'update'));

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$group = \Models\Group::find($args["id"]);
		$group->status = 0;
		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$group = \Models\Group::find($args["id"]);
		$group->status = 1;
		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'disable'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$query = [];
		if (\APP\Auth::isAdminInterface()) {
			$query = \Models\Group
				::where("status", true)
				->get()
			;
		}

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$group = new \Models\Group;

		if (isset($data["name"])) {
			$group->name = $data["name"];
		}
		if (isset($data["add_remove_resources"])) {
			$group->add_remove_resources = $data["add_remove_resources"];
		}
		if (isset($data["is_jackdaw_team"]) && $this->get('settings')['licensing']['isJackdawCloud']) {
			$group->is_jackdaw_team = $data["is_jackdaw_team"];
		}

		$group->status = 1;

		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'insert'));

	// Assign user to given group and assign all group's learning modules to user
	$group->put('/{group_id:[0-9]+}/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$group = \Models\Group::find($args["group_id"]);
		$user = \Models\User
			::where('id', '=', $args["user_id"])
			->first()
		;

		$groupUser = new \Models\GroupUser;
		$groupUser->user_id = $args["user_id"];
		$groupUser->group_id = $args["group_id"];
		$groupUser->status = 1;
		$groupUser->save();

		if ($group->add_remove_resources) {
			\Models\GroupLearningModule::AssingToUser($args["group_id"], $user);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'insert'));

	// Remove user from group and remove group resources.
	$group->delete('/{group_id:[0-9]+}/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		$group = \Models\Group::find($args["group_id"]);
		if ($group) {
			\Models\GroupUser::where("user_id", $args["user_id"])->where("group_id", $args["group_id"])->get()->each(function($group_user) {
				$group_user->delete();
			});


			if ($group->add_remove_resources) {
				\Models\GroupLearningModule::RemoveFromUser($args["group_id"], $args["user_id"]);
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'disable'));

	$group->post('/{id:[0-9]+}/users', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\User::selectRaw("users.*, MAX(`group_users`.group_id  IS NOT NULL) AS allotted")
			->leftjoin('group_users', function($join) use ($args) {
				$join->on('group_users.user_id', '=', 'users.id')
					->where("group_users.group_id", "=", $args["id"]);
			})
            ->where('users.status', 1)
			->groupBy("users.id")
		;

		if (isset($params["search"]) && isset($params["search"]["allotted"]))
		{
			if ($params["search"]["allotted"] == "1")
			{
				$query->whereRaw("`group_users`.group_id IS NOT NULL");
			}
			elseif ($params["search"]["allotted"] == "0")
			{
				$query->whereRaw("`group_users`.group_id IS NULL");
			}
			unset($params["search"]["allotted"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);


		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'select'));

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Group
			::select("groups.*")
			->selectRaw("COUNT(group_users.id) as n_users")
			->leftJoin("group_users", function($join){
				$join
					->on("group_users.group_id", "=", "groups.id")
				;
			})
			->groupBy("groups.id")
		;


		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}

			if (isset($params["search"]["status"])) {
				$params["search"]["groups.status"] = $params["search"]["status"];
				unset($params["search"]["status"]);
			}
		}

		$query = \Models\Schedule::countAndConditions($query, $params);

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
					"ID" => "id",
					"Group Name" => "name",
					"Number of Users Assigned" => "n_users"
			];


			$download_file_name = uniqid("groups.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
						$export_fields,
						$this->get('settings')["LMSTempPath"] . $download_file_name
					);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-groups', 'select'));
});