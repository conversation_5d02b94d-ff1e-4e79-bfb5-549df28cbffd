<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/trainingimpactapprentixapprentice",  function ($group) {
	$group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$user_custom_review = \Models\UserCustomReview
			::where("custom_review_id", 4)
			->get()
			->count()
		;

		if ($user_custom_review > 0 && !\APP\Auth::isAdmin()) {
			$current_user = \APP\Auth::getUserId();
			$current_user_custom_review = \Models\UserCustomReview
			::where("custom_review_id", 4)
			->where("user_id", $current_user)
			->get()->count();
			if ($current_user_custom_review <= 0) {
				$data = [];
				$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
			}


		}
		$query_id = 'trainingimpactapprentixapprenticeList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		switch($args["option"]) {
			case "/download":
				$query
					->selectRaw("CONCAT(users.fname, ' ', users.lname) as trainee_name")
				;
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				// Loop $data, if 'standard_type' value is "Skills Monitoring", then make following fields empty: "completion_status", "time_spent", "time_behind", "percentage_time", "expected_time"
				$data = array_map(function($item) {
					if ($item['standard_type'] == 'Skills Monitoring') {
						$item['completion_status'] = '';
						$item['time_spent'] = '';
						$item['time_behind'] = '';
						$item['percentage_time'] = '';
						$item['expected_time'] = '';
					}
					return $item;
				}, $data);

				return \Models\CustomReview::returnDownloadFile(
					'trainingimpactapprentixapprentice',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);

				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
				break;

			case "/powerbi":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \Models\CustomReview::exportToPowerBi(
					'trainingimpactapprentixapprentice',
					$params["export_config"],
					$data,
					$response
				);
			break;

			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'training impact reports tpl', // Template name
					['user_id'], // where to look for user's ID in this query
					$args
				);
			break;
		}

		// group response by year/month if request is for making a chart.
		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$query->groupBy(DB::raw('
				year, month
			'));
			$p = $query->get();
		// else fire up pagination
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}


		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));


	$group->post('/qa/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$query_id = 'trainingimpactapprentixapprenticeList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . "trainingimpactapprentixapprenticeListQA";

		$query = $QueryBuilder::generate($params, $args);
		$qcs = \Models\QualityControl::where('qa','=','Rejected')->get();

        $qc_users =[];
        $query->whereRaw("".$qcs->count()." > "."0");
        $query->where(function ($query)use($qcs){
            $qc_standards =[];

		foreach($qcs as $key=>$qa){
		    $flag=true;
	        foreach($qc_standards as $standard)
            {
                if($standard['user']==$qa->user_id && $standard['standard']==$qa->standard)
                {
                    $flag=false;
                }
            }
	        if($flag)
            {
                array_push($qc_standards,['user'=>$qa->user_id,'standard'=>$qa->standard]);

                    $query->orWhere(function($query) use($qa){
                        $query->where(['apprenticeship_standards_users.user_id' => $qa->user_id])
                            ->where(['apprenticeship_standards_users.standard_id' => $qa->standard]);
                    });
            }
        }
        });
        $query = $query
            ->whereHas("Standard", function($query) {
                $query = \Models\ManagerLearningModuleCategory::checkManagerAccessToCategories($query);
            })
        ;
		switch($args["option"]) {
			case "/download":
				$query
					->selectRaw("CONCAT(users.fname, ' ', users.lname) as trainee_name")
				;
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::returnDownloadFile(
					'trainingimpactapprentixapprentice',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);

				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
				break;

			case "/powerbi":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \Models\CustomReview::exportToPowerBi(
					'trainingimpactapprentixapprentice',
					$params["export_config"],
					$data,
					$response
				);
			break;

			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'training impact reports tpl', // Template name
					['user_id'], // where to look for user's ID in this query
					$args
				);
			break;
		}

		// group response by year/month if request is for making a chart.
		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$query->groupBy(DB::raw('
				year, month
			'));
			$p = $query->get();
		// else fire up pagination
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}


		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));

	$group->post('/qa-sign-off/list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$select = [
				'users.id as user_id',
				'users.fname as fname',
				'users.lname as lname',
				'apprenticeship_standards.name as standard_name',
				'apprenticeship_standards.id as standard_id',
				'companies.name as companies_name',
				'departments.name as departments_name',
				'apprenticeship_standards_users.completion_status as completion_status',
				'apprenticeship_standards_users.time_spent as time_spent',
				'apprenticeship_standards_users.percentage as percentage',
				'apprenticeship_standards_users.percentage_time as percentage_time',
				'manager_reviews.id as review_id'
			]
		;

		$query = \Models\ApprenticeshipStandardUser
			::select($select)
			->join('manager_reviews', function ($join) {
				$join
					->on('manager_reviews.user_id', '=', 'apprenticeship_standards_users.user_id')
					//->where('apprenticeship_standards_users.standard_id', '=', 'manager_reviews.standard_id')
					->whereRaw('manager_reviews.standard_id = apprenticeship_standards_users.standard_id')
					// I still don't understand why WHERE does not work, but whereRaw works.
					->where('manager_reviews.visit_type', 'QA Report')
					->where('manager_reviews.completion_status', 'Not Started')
				;
			})
			->leftJoin('users', 'users.id', '=', 'apprenticeship_standards_users.user_id')
			->leftJoin('companies', 'companies.id', '=', 'users.company_id')
			->leftJoin('departments', 'departments.id', '=', 'users.department_id')
			->leftJoin('apprenticeship_standards', 'apprenticeship_standards.id', '=', 'apprenticeship_standards_users.standard_id')

			->where('apprenticeship_standards.status', '=', 1)
			->where('users.status', '=', 1)
		;

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));

});
