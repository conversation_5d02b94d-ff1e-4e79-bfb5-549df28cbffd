<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/learningcategory",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningcategory = \Models\LearningModuleCategory::find($args["id"]);
		$learningcategory->status = 0;
		$learningcategory->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningcategory = \Models\LearningModuleCategory::find($args["id"]);
		$learningcategory->status = 1;
		$learningcategory->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningcategory = \Models\LearningModuleCategory::find($args["id"]);

		$response->getBody()->write(json_encode($learningcategory));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$learningcategory = new \Models\LearningModuleCategory;

		$fields = [
			"name",
			"order",
			"is_mandatory"
		];

		\APP\Tools::setObjectFields($learningcategory, $fields, $data, false, true);

		$learningcategory->landing_image = \APP\Tools::uploadImage($this->get('settings')["CategoryLandingImage"], 'landing_image', $learningcategory->logo);
		$learningcategory->status = 1;
		$learningcategory->save();

		$response->getBody()->write((string)$learningcategory->id);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'insert'));


	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$learningcategory = \Models\LearningModuleCategory::find($args["id"]);

		$fields = [
			"name",
			"order",
			"is_mandatory"
		];

		\APP\Tools::setObjectFields($learningcategory, $fields, $data, false, true);

		$learningcategory->landing_image = \APP\Tools::uploadImage($this->get('settings')["CategoryLandingImage"], 'landing_image', $learningcategory->landing_image);
		$learningcategory->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'update'));

	// Delete logo of company
	$group->delete('/landing_image/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningcategory = \Models\LearningModuleCategory::find($args["id"]);
		if (is_file($this->get('settings')["CategoryLandingImage"] . $learningcategory->landing_image)) {
			unlink($this->get('settings')["CategoryLandingImage"] . $learningcategory->landing_image);
		}
		$learningcategory->landing_image = null;
		$learningcategory->save();

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'update'));

	$group->get('/all[/{option:.*}]', function (Request $request, Response $response, $args) {
		session_write_close();
		$response = \APP\Tools::cors($response);
		$data = \Models\LearningModuleCategory
			::where("status", 1);

		$skip_permissions = false;
		if (
			isset($args['option']) &&
			$args['option'] == 'no-filter'
		) {
			$skip_permissions = true;
		}

		// If manager, check if there is need to filter out categories
		if (!$skip_permissions) {
			$data = \Models\ManagerLearningModuleCategory::checkManagerAccessToCategories($data, 'id');
		}

		if (
			\APP\Tools::getConfig('showOnlyAssignedCategories') &&
			\APP\Auth::isLearner()
		) {
			$data = $data
				->where(function ($query) {
					$query
						->whereHas("Modules", function ($query) {
							$query
								->where('status', true)
								->where(function ($query) {
									$query
										->whereIn(
											'learning_modules.id',
											\Models\UserLearningModule
												::select('learning_module_id')
												->where('user_id', \APP\Auth::getUserId())
												->whereNull('deleted_at')
												->get()
										)
										->orWhere('learning_modules.self_enroll', true)
									;
								})
							;
						})
						->orWhereHas("Schedules", function ($query) {
							$query
								->where('schedules.status', true)
								->where('schedules.visible_learner', true)
								->whereNull('schedules.deleted_at')
								->where(function ($query) {
									$query
										->whereIn(
											'schedules.id',
											\Models\ScheduleLink
												::select('schedule_links.schedule_id')
												->where('schedule_links.link_id', \APP\Auth::getUserId())
												->where(function ($query) {
													$query = $query
														->where('schedule_links.type', 'users')
														->orWhere('schedule_links.type', 'users_queue');
												})
												->whereNull('schedule_links.deleted_at')
												->get()
										)
										->orWhere('schedules.enrole_any_learner', 1)
									;
								})
							;
						})

					;
				});
		}

		if (\APP\Auth::isLearner()) {
			$data = $data
				->withCount(['Modules' => function ($query) {
					// --- Define variables once for use within the closure ---
					$userId = \APP\Auth::getUserId();
					$user = \APP\Auth::getUser();
					$company = $user?->company;
					$companyId = $company?->id;
					$isEnterPriseVersion = !\APP\Tools::getConfig('sharedClients');
					$makeAllEnrollable = $company?->make_all_enrollable ?? false;
					$showEventsInLearningView = \APP\Tools::getConfig('ShowEventsineLearningView');
					$hideResourcesInLesson = \APP\Tools::getConfig('hideResourcesInLesson');

					// --- The main query for the count ---
					$query
						// A module must be active to be counted
						->where('learning_modules.status', true);
					// If configured to hide modules that are parts of a course (i.e., lessons)
					if ($hideResourcesInLesson) {
						$query->leftJoin('learning_course_modules', 'learning_modules.id', '=', 'learning_course_modules.learning_module_id')
							->whereNull('learning_course_modules.learning_module_id');
					}
					// A module is countable if it's either assigned OR available for enrollment
					$query->where(function ($query) use ($userId, $makeAllEnrollable, $showEventsInLearningView, $companyId, $isEnterPriseVersion) {

						// Condition 1: The module is directly assigned to the user.
						$query->whereIn(
							'learning_modules.id',
							\Models\UserLearningModule::select('learning_module_id')->where('user_id', $userId)
						);

						// Condition 2: OR the module is available for enrollment.
						$query->orWhere(function ($enrollableQuery) use ($userId, $makeAllEnrollable, $showEventsInLearningView, $companyId, $isEnterPriseVersion) {

							// A) A module must meet the primary enrollment and company permission rules.
							$enrollableQuery->where(function ($mainRulesQuery) use ($makeAllEnrollable, $companyId, $isEnterPriseVersion) {

								// Rule Set 1: Check against 'make_all_enrollable' and catalog settings.
								$mainRulesQuery->where(function ($baseEnrollQuery) use ($makeAllEnrollable) {
									if (!$makeAllEnrollable) {
										$baseEnrollQuery->where("learning_modules.self_enroll", true);
									} else {
										$baseEnrollQuery->where("learning_modules.self_enroll", true)
											->orWhereIn("learning_modules.name", \Models\LearningModule::$catelog);
									}
								});

								// Rule Set 2: AND it must also pass company-specific permissions.
								$mainRulesQuery->where(function ($permissionQuery) use ($companyId, $isEnterPriseVersion) {
									// Handles legacy data where self_enroll_access is not set
									$permissionQuery->where(function ($legacyQuery) use ($companyId, $isEnterPriseVersion) {
										$legacyQuery->whereNull('learning_modules.self_enroll_access');
										if (!$isEnterPriseVersion) {
											$legacyQuery->where('learning_modules.company_id', $companyId);
										}
									});

									// Case 1: Open to all companies
									$permissionQuery->orWhere('learning_modules.self_enroll_access', 0);

									// Case 2: Accessible only to selected (whitelisted) companies
									$permissionQuery->orWhere(function ($subQuery) use ($companyId) {
										$subQuery->where('learning_modules.self_enroll_access', 1)
											->whereHas('companyModuleEnrollments', function ($cq) use ($companyId) {
												$cq->where('company_module_enrollment.company_id', $companyId)->where('company_module_enrollment.type', 1);
											});
									});

									// Case 3: Accessible to all EXCEPT selected (blacklisted) companies
									$permissionQuery->orWhere(function ($subQuery) use ($companyId) {
										$subQuery->where('learning_modules.self_enroll_access', 2)
											->whereDoesntHave('companyModuleEnrollments', function ($cq) use ($companyId) {
												$cq->where('company_module_enrollment.company_id', $companyId)->where('company_module_enrollment.type', 1);
											});
									});
								});
							});

							// B) OR, if events are hidden from the learning view, a module is available if it's a course linked to an upcoming, enrollable event.
							if (!$showEventsInLearningView) {
								$enrollableQuery->orWhere(function ($eventQuery) use ($userId) {
									$eventQuery->where("learning_modules.is_course", 1)
										->whereNotIn(
											'learning_modules.id', // Ensure it's not already assigned
											\Models\UserLearningModule::select('learning_module_id')->where('user_id', $userId)
										)
										->whereHas('ScheduleLesson', function ($slQuery) use ($userId) {
											$slQuery->where('schedule_links.status', true)
												->whereHas('Schedule', function ($sQuery) use ($userId) {
													$sQuery->where('schedules.enrole_any_learner', true)
														->where('schedules.status', true)
														->where('schedules.start_date', '>', \Carbon\Carbon::now())
														->where(function ($accessQuery) use ($userId) {
															$accessQuery->whereHas('Queries', function ($rQuery) use ($userId) {
																$rQuery->whereRaw("FIND_IN_SET(?, resource_queries.user_ids)", [$userId]);
															})->orDoesntHave('Queries');
														});
												});
										});
								});
							}
						});
					});
				}])
				->withCount(['Schedules' => function ($query) {
					$query
						->where('schedules.status', true)
						->where('schedules.visible_learner', true)
						->whereNull('schedules.deleted_at')
						->where(function ($query) {
							$query
								->whereIn(
									'schedules.id',
									\Models\ScheduleLink
										::select('schedule_links.schedule_id')
										->where('schedule_links.link_id', \APP\Auth::getUserId())
										->where(function ($query) {
											$query = $query
												->where('schedule_links.type', 'users')
												->orWhere('schedule_links.type', 'users_queue');
										})
										->whereNull('schedule_links.deleted_at')
										->get()
								)
								->orWhere(function ($query) {
									$query
										->where('schedules.enrole_any_learner', 1);
									$query = \Models\Schedule::filterVisibleDays($query);
								})

							;
						})
					;
				}])
				->with(['programmes' => function ($query) use ($args) {
					$query
						->whereIn(
							'apprenticeship_standards.id',
							\Models\ApprenticeshipStandardUser
								::select('standard_id')
								->where('user_id', \APP\Auth::getUserId())
								->get()
						);
				}]);
		}

		$data = $data
			->get();

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'select'));

	$group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\LearningModuleCategory::where("id", ">", "0");
		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-categories', 'select'));
});
