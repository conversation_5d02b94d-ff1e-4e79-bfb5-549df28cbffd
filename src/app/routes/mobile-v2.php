<?php

use APP\Controllers\MobileController;
use Illuminate\Database\Capsule\Manager as DB;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Routing\RouteCollectorProxy;

$app->group('/v2/mobile', function (RouteCollectorProxy $group) {
    $group->get('/reports', [MobileController::class, 'getReports'])->add(\APP\Auth::getSessionCheck());
    $group->post('/mylearning', [MobileController::class, 'getCourses'])->add(\APP\Auth::getSessionCheck());
    $group->get('/announcement-link', [MobileController::class, 'getAnnouncementLink'])->add(\APP\Auth::getSessionCheck());
    $group->post('/announcements', [MobileController::class, 'getAnnouncements'])->add(\APP\Auth::getSessionCheck());
    $group->post('/forms', [MobileController::class, 'getForms'])->add(\APP\Auth::getSessionCheck());
    $group->get('/schedule', [MobileController::class, 'getSchedule'])->add(\APP\Auth::getSessionCheck());
    $group->get('/user-stat', [MobileController::class, 'getUserLearningStats'])->add(\APP\Auth::getSessionCheck());

    $group->get('/get-form-by-id/{form_id}', function (Request $request, Response $response, $args) {

        $form_id = $args['form_id'];

        if (!$form_id) {
            $response->getBody()->write(json_encode([
                'success' => false,
                'message' => 'Invalid parameters'
            ]));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }

        $user = \APP\Auth::getUser();

        $user_form = \Models\UserForm::select(
            'id',
            'form_id',
            'user_id',
            'user_form_status AS completion_status',
            'user_form_status',
            'id as user_form_id',
            'type_id',
            DB::raw("(select name from forms where id = form_id) as name"),
            DB::raw("DATE_FORMAT(updated_at, '" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk")
        )
            ->where('user_forms.user_form_status', '!=', 'completed')
            ->whereDoesntHave('UserFormSignoff', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('user_id', $user->id)
            ->where('user_forms.form_id', $form_id)
            ->with(['formLearningModule' => function ($query) {
                $query->select('learning_modules.id', 'learning_modules.name')
                    ->join('user_forms', 'user_forms.type_id', '=', 'learning_modules.id')
                    ->where('user_forms.type', 'learning_module');
            }]);



        $user_form = $user_form->first(); // Retrieve only the first result, since we're looking for a single form

        $response->getBody()->write(json_encode([
            'success' => true,
            'user_form' => $user_form,
            'user_id' => $user->id
        ]));
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getSessionCheck());

    $group->get('/get-assignment-details/{assignment_id}', function ($request, $response, $args) {

        $assignmentId = $args['assignment_id'];

        if (!$assignmentId) {
            $response->getBody()->write(json_encode([
                'success' => false,
                'message' => 'Invalid parameters'
            ]));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }

        $user = \APP\Auth::getUser();

        // Retrieve assigned document templates
        $documentTemplate = \Models\DocumentTemplate::query()
            ->where('status', 1)
            ->select('id', 'name', 'icon')
            ->whereIn(
                'id',
                \Models\Assignment::select('link_table_id')
                    ->where('link_table', 'document_templates')
                    ->where('source_table', 'users')
                    ->where('source_table_id', $user->id)
                    ->get()
            )
            ->where('id', $assignmentId);

        if (\APP\Auth::isUserMainRole()) {
            $documentTemplate->where(function ($query) use ($user) {
                $query->whereHas('roles', function ($query) use ($user) {
                    $query->where('roles.id', '=', $user->Role->id);
                })->orWhereDoesntHave('roles');
            });
        } else {
            $documentTemplate->where(function ($query) use ($user) {
                $query->whereHas('roles', function ($query) use ($user) {
                    $query->where('roles.id', [\APP\Auth::roleId()]);
                })->orWhereDoesntHave('roles');
            });
        }

        $documentTemplate = $documentTemplate->first();

        if ($documentTemplate) {
            $response->getBody()->write(json_encode([
                'success' => true,
                'assignment' => [
                    'id' => $documentTemplate->id,
                    'icon' => $documentTemplate->icon,
                    'name' => $documentTemplate->name,
                    'type' => 'document_template'
                ]
            ]));
            return $response->withHeader('Content-Type', 'application/json');
        }

        // Retrieve assigned form workflows
        $userWorkflowForm = \Models\UserWorkflowForm::with('form_workflow')
            ->whereHas('form_workflow')
            ->whereIn(
                'form_workflow_id',
                \Models\Assignment::select('link_table_id')
                    ->where('link_table', 'form_workflow')
                    ->where('source_table', 'users')
                    ->where('source_table_id', $user->id)
                    ->get()
            )
            ->where('user_id', $user->id)
            ->where('form_workflow_id', $assignmentId);

        if (\APP\Auth::isUserMainRole()) {
            $userWorkflowForm->whereHas('form_workflow', function ($query) use ($user) {
                $query->whereHas('assignmentRoles', function ($innerQuery) use ($user) {
                    $innerQuery->where('roles.id', '=', $user->Role->id);
                })->orWhereDoesntHave('assignmentRoles');
            });
        } else {
            $userWorkflowForm->whereHas('form_workflow', function ($query) use ($user) {
                $query->whereHas('assignmentRoles', function ($innerQuery) use ($user) {
                    $innerQuery->where('roles.id', [\APP\Auth::roleId()]);
                })->orWhereDoesntHave('assignmentRoles');
            });
        }

        $userWorkflowForm = $userWorkflowForm->first();

        if ($userWorkflowForm) {
            $response->getBody()->write(json_encode([
                'success' => true,
                'assignment' => [
                    'id' => $userWorkflowForm->form_workflow->id,
                    'user_workflow_form_id' => $userWorkflowForm->id,
                    'icon' => $userWorkflowForm->form_workflow->icon,
                    'user_id' => $user->id,
                    'name' => $userWorkflowForm->form_workflow->name . ' - ' . ($userWorkflowForm->type ?: 'direct'),
                    'type' => 'form_workflow'
                ]
            ]));

            return $response->withHeader('Content-Type', 'application/json')->withStatus(200);;
        }

        $response->getBody()->write(json_encode([
            'success' => false,
            'message' => 'Assignment not found'
        ]));

        return $response->withHeader('Content-Type', 'application/json')->withStatus(404);
    })->add(\APP\Auth::getSessionCheck());

    $group->get('/get-learning-url', [MobileController::class, 'getLearningZipURLforOffline'])->add(\APP\Auth::getSessionCheck());

    // track api - which will fetch all track details of the scorm 
    $group->get('/get-resource-details-for-scorm', [MobileController::class, 'getResourceDetailsForScorm'])->add(\APP\Auth::getSessionCheck());
    // $group->post('/refresh-seskey', [MobileController::class, 'userSessionGenerator'])->add(\APP\Auth::getSessionCheck());
    $group->post('/refresh-seskey', [MobileController::class, 'refreshSessKey'])->add(\APP\Auth::getSessionCheck());

    // API to offline to online sync 
    $group->post('/datamodel', [MobileController::class, 'dataModel'])->add(\APP\Auth::getSessionCheck());
});
