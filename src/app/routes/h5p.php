<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/h5p",  function ($group) {

    $group->get("/trainee/{learning_module_id:[0-9]+}" , function (Request $request, Response $response, $args) {

        $lm_id = $args["learning_module_id"];

        $h5pservice = new \APP\H5PService();

        $formHTML = $h5pservice->getTraineeSubmissionForm($lm_id);

		return $response->getBody()->write($formHTML);
    })->add(\APP\Auth::getSessionCheck());


    $group->get("/create" , function (Request $request, Response $response, $args) {

        $h5pservice = new \APP\H5PService();

        $formHTML = $h5pservice->getEditorSubmissionForm();

        unset($_SESSION["h5p_launch_url_used"]);

		$response->getBody()->write($formHTML);
		return $response->withHeader('Content-Type', 'text/html');
    })->add(\APP\Auth::getSessionCheck());


    $group->post("/grading" , function (Request $request, Response $response, $args) {

        $xml_data = $request->getBody();
        $xml_obj = simplexml_load_string($xml_data);

        $msg_id = $xml_obj->imsx_POXHeader->imsx_POXRequestHeaderInfo->imsx_messageIdentifier;

        $operation = "";

        if (isset($xml_obj->imsx_POXBody->readResultRequest)) $operation = "readResult";
        if (isset($xml_obj->imsx_POXBody->replaceResultRequest)) $operation = "replaceResult";
        if (isset($xml_obj->imsx_POXBody->deleteResultRequest)) $operation = "deleteResult";

        if ($operation == "deleteResult"){
            return $response->getBody()->write(\APP\H5PService::generateUnsupportedResponse($msg_id, $operation));
        }

        if ($operation == "readResult"){

            $lr_id = $xml_obj->imsx_POXBody->readResultRequest->resultRecord->sourcedGUID->sourcedId;

            try{
                $lr = \Models\LearningResult::find($lr_id);
                if ($lr){
                    $score = is_numeric($lr->score) ? doubleval($lr->score / 100) : "";
                } else {
                    $score = "";
                }
                $xml_response = \APP\H5PService::generateReadResultResponse($msg_id, "success", $score);
                $response = $response->withHeader('Content-type', 'application/xml; charset=UTF-8');
                $response->getBody()->write($xml_response);
                return $response;
            } catch(\Exception $ex) {
                $GLOBALS["LOGGER"]->addInfo("Can't process H5P learning result: $xml_data");

                return $response->getBody()->write(\APP\H5PService::generateReadResultResponse($msg_id, "failure", ""));
            }
        }

        if ($operation == "replaceResult"){
            $lr_id = $xml_obj->imsx_POXBody->replaceResultRequest->resultRecord->sourcedGUID->sourcedId;

            try {

                $result = $xml_obj->imsx_POXBody->replaceResultRequest->resultRecord->result->resultScore;
                $score = round(doubleval($result->textString) * 100.0);

                $lr = \Models\LearningResult::findOrFail($lr_id);

                $lr->score = $score;
                $lr->passing_status = ($score >= \APP\Tools::getConfig('h5pPassMark')) ? "passed" : "failed";
                if ($lr->passing_status == "passed") {
                    $lr->completion_status = "completed";
                    $lr->completed_at = \Carbon\Carbon::now();
                    $lr->sign_off_manager = 1;
                }
                $lr->learner_action = true;
                $lr->save();

                // Perform check if SMCR and resource in F&P category that is hidden to user and resource is upload, then do not send e-mail to user!
                $smcr = new \APP\Smcr();
                $send_email = $smcr->sendLearnerEmail($lr);

                // Send out "Learning Resource Signed Off by Coach" to learner
                $template = \Models\EmailTemplate
                    ::where('name', '%%learning_resource%% Signed Off by %%manager%%')
                    ->where('status', true)
                    ->first()
                ;
                if (
                    $template &&
                    $template->id &&
                    $send_email &&
                    $lr->module->track_progress
                ) {
                    $email_queue = new \Models\EmailQueue;
                    $email_queue->email_template_id = $template->id;
                    $email_queue->learning_module_id = $lr->module->id;
                    $email_queue->recipients = [intval($lr->user->id)];
                    $email_queue->from = $lr->created_by;
                    $email_queue->save();
                }

                $xml_response = \APP\H5PService::generateReplaceResultResponse($msg_id, $lr_id, "success");
                $response = $response->withHeader('Content-type', 'application/xml; charset=UTF-8');

                $response->getBody()->write($xml_response);

                return $response;

            } catch(\Exception $ex) {
                $GLOBALS["LOGGER"]->addInfo("Can't process H5P learning result: $xml_data");

                return $response->getBody()->write(\APP\H5PService::generateReplaceResultResponse($msg_id, $lr_id, "failure"));
            }
        }

        return $response;
    });

    $group->post("/created" , function (Request $request, Response $response) {

        $data = $request->getParsedBody();

        $json_data = json_decode($data["content_items"], true);

        $launch_url = $json_data["@graph"][0]["url"];
        $icon_id = $json_data["@graph"][0]["icon"]["@id"];
        $has_assignment = \APP\H5PService::hasAssignment($icon_id);
        $resource_title = $json_data["@graph"][0]["lineItem"]["label"];


        if (!isset($_SESSION["h5p_launch_url_used"]) || $_SESSION["h5p_launch_url_used"] != $launch_url){

            $new_learning_module = new \Models\LearningModule();
            $new_learning_module->type_id = \Models\LearningModuleType::where("slug", "h5p")->first()->id;
            $new_learning_module->name = $resource_title;
            $new_learning_module->is_course = 0;
            $new_learning_module->material = ["sessions" => [], "link" => $launch_url];
            $new_learning_module->require_management_signoff = !$has_assignment;
            $new_learning_module->status = 1;
            $new_learning_module->thumbnail = "h5p.jpg";
            $new_learning_module->promo_image = "h5p.jpg";
            $new_learning_module->save();

            $_SESSION["h5p_launch_url_used"] = $launch_url;

            return $response->getBody()->write("<script>window.parent.closeIframeInline();</script>");
        } else {
            return $response->getBody()->write("Learning resource already created.");
        }

    })->add(\APP\Auth::getSessionCheck());
});
