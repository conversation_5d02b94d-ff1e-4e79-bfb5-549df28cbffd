<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/fallingbehindapprentixassessorreports",  function ($group) {

	$group->get('/updatefallingbehindapprentixassessorpercentage', function (Request $request, Response $response, array $args) {
		\Models\ApprenticeshipStandardUser::updateFallBehindAssesorPercentage();
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));

	$group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$user_custom_review = \Models\UserCustomReview
		::where("custom_review_id", 6)
		->get()->count();
		if ($user_custom_review > 0 && !\APP\Auth::isAdmin()) {
			$current_user = \APP\Auth::getUserId();
			$current_user_custom_review = \Models\UserCustomReview
			::where("custom_review_id", 6)
			->where("user_id", $current_user)
			->get()->count();
			if ($current_user_custom_review <= 0) {
				$data = [];
				$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
			}


		}
		$query_id = 'fallingbehindapprentixassessorreportsList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);


		$option = isset($args["option"]) ? $args["option"] : "";


		switch($args["option"]) {
			case "/download":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::returnDownloadFile(
					'fallingbehindapprentixassessorreports',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);
			case "/powerbi":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::exportToPowerBi(
					'fallingbehindapprentixassessorreports',
					$params["export_config"],
					$data,
					$response
				);
			break;
			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					'fallingbehindapprentixassessorreportslist', // ID that will be used to generate query
					'Apprentix Coach_Assesors Falling Behinds', // Template name
					['id'], // where to look for user's ID in this query
					$args
				);
			break;
		}


		// group response by year/month if request is for making a chart.
		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$query->groupBy(DB::raw('
				year, month
			'));
			$p = $query->get();
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}


		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));

});
