<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/target-catalogue",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$entry = \Models\TargetCatalogue::find($args["id"]);

		$response
			->getBody()
			->write(json_encode($entry))
		;
		return
			$response
				->with<PERSON>eader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'select'));


	$group->post("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$entry = \Models\TargetCatalogue::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$entry->name = $data["name"];
		}
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'update'));

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\TargetCatalogue::find($args["id"]);
		$entry->status = 0;
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\TargetCatalogue::find($args["id"]);
		$entry->status = 1;
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'disable'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$query = [];
		if (\APP\Auth::isAdminInterface()) {
			$query = \Models\TargetCatalogue
				::where("status", true)
				->get()
			;
		}

		$response
			->getBody()
			->write(json_encode($query))
		;
		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$entry = new \Models\TargetCatalogue;

		if (isset($data["name"])) {
			$entry->name = $data["name"];
		}
		if (\APP\Auth::getUserId()) {
			$entry->created_by = \APP\Auth::getUserId();
		}
		$entry->status = 1;
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'insert'));

	// Assign resource to target catalogue
	$group->put('/{target_catalogue_id:[0-9]+}/resource/{learning_module_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$catalogue = \Models\TargetCatalogue::find($args["target_catalogue_id"]);
		$resource = \Models\LearningModule::find($args["learning_module_id"]);
		if (
			$catalogue &&
			$resource
		) {
			$resource_catelogue = new \Models\LearningModuleTargetCatalogue;
			$resource_catelogue->target_catalogue_id = $args["target_catalogue_id"];
			$resource_catelogue->learning_module_id = $args["learning_module_id"];
			if (\APP\Auth::getUserId()) {
				$resource_catelogue->created_by = \APP\Auth::getUserId();
			}
			$resource_catelogue->save();
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'insert'));

	// Remove resource from target catalogue
	$group->delete('/{target_catalogue_id:[0-9]+}/resource/{learning_module_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$entry = \Models\LearningModuleTargetCatalogue
			::where('target_catalogue_id', $args["target_catalogue_id"])
			->where('learning_module_id', $args["learning_module_id"])
			->first()
		;
		if ($entry) {
			if (\APP\Auth::getUserId()) {
				$entry->deleted_by = \APP\Auth::getUserId();
			}
			$entry->saveWithoutEvents();
			$entry->delete();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'disable'));


	// Smart table list
	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\TargetCatalogue
			::where('id', '>', 0);


		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\Schedule::countAndConditions($query, $params);

		if (
			isset($args["download"]) &&
			$args["download"] == "/download"
		) {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name",
			];


			$download_file_name = uniqid("target-catalogue.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response
				->getBody()
				->write(json_encode($download_file_name))
			;

			return
				$response
					->withHeader('Content-Type', 'application/json')
			;
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response
			->getBody()
			->write($p->toJson())
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-target-catalogue', 'select'));
});