<?php

use APP\Controllers\MobileVerificationController;
use Slim\Routing\RouteCollectorProxy;
use APP\Auth;

// Apply middleware once to the whole group
$app->group('/mobile-verification', function (RouteCollectorProxy $group) {
    $group->post('/request-otp', [MobileVerificationController::class, 'requestOTP']);
    $group->post('/verify-otp', [MobileVerificationController::class, 'verifyOTP']);
    $group->post('/resend-otp', [MobileVerificationController::class, 'requestOTP']);
    $group->post('/check-verification', [MobileVerificationController::class, 'checkVerificationStatus']);
    $group->get('/get-countries', [MobileVerificationController::class, 'getCountries']);
})->add(Auth::getSessionCheck());