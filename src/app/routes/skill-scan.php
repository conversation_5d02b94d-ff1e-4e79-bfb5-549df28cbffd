<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Illuminate\Database\Capsule\Manager as DB;


$app->group("/skill",  function ($group) {
	$group->post("{router:scan|-scan}/list{download:[\/a-z]*}", function (Request $request, Response $response, array $args) {

		$query_id = 'skillscanList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		if (isset($args["download"]) && $args["download"] == "/download") {

			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"User ID" => "user_id",
				"Name" => "trainee_name",
				"Skill Scan" => "learning_module_name",
				"First Score" => "first_score",
				"Last Score" => "last_score",
				"Improvenment (%)" => "improvements",
				"Next Due Date" => "next_due_date_uk",
			];


			$download_file_name = uniqid("skill-scan.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} elseif (isset($args["download"]) && $args["download"] == "/powerbi") {
			set_time_limit(0);

			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			return \Models\CustomReview::exportToPowerBi(
				'skillscan',
				$params["export_config"],
				$data,
				$response
			);
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));




	// Get specific ckill-scan
	$group->post("{router:scan|-scan}/{learning_results_id:[0-9]+}[/list/{download}]", function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"])) {
			unset($params["search"]["refresh"]);
		}

		$query = \Models\SkillScan
			::select(
				'skill_scans.id',
				'users.fname as fname',
				'users.lname as lname',
                DB::raw("CONCAT(users.fname, ' ', users.lname) AS full_name"),
                'users.username as user_name',
				'learning_modules.name as learning_module_name',
				DB::raw("DATE_FORMAT(skill_scans.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"),
				'skill_scan_answers.score as score',
				'assessment_answers.task_name as task_name',
				'assessment_questions.title as question_title',
				'assessment_questions.text as question_text',
				'assessment_answers.task_description as task_description',
				'skill_scan_answers.id as skill_scan_answer_id'
			)
			->join("users", function($join) {
				$join
					->on("skill_scans.user_id", "=", "users.id")
					->where("users.status", "=", 1)
				;
			})
			->join("learning_modules", function($join) {
				$join
					->on("skill_scans.learning_module_id", "=", "learning_modules.id")
					->where("learning_modules.status", "=", 1)
				;
			})
			->join("skill_scan_answers", function($join) {
				$join
					->on('skill_scan_answers.skill_scan_id', '=', 'skill_scans.id')
				;
			})
			->leftJoin("assessment_answers", function($join) {
				$join
					->on('assessment_answers.course_id', '=', 'skill_scans.learning_module_id')
					->on('assessment_answers.question_id', '=', 'skill_scan_answers.question')
					->on('assessment_answers.order', '=', 'skill_scan_answers.score')
				;
			})
			->leftJoin("assessment_questions", function($join) {
				$join
					->on('assessment_questions.course_id', '=', 'skill_scans.learning_module_id')
					->on('assessment_questions.question_id', '=', 'skill_scan_answers.question')
				;
			})
			->where('skill_scans.learning_results_id', $args['learning_results_id'])
		;

		if (\APP\Auth::isLearner()) {
			$query = $query
				->where('skill_scans.user_id', \APP\Auth::getUserId())
			;
		} elseif (!\APP\Auth::accessAllLearners()) {
			$query->whereIn(
				"skill_scans.user_id",
				\Models\ManagerUser
					::where('manager_id', '=', \APP\Auth::getUserId())
					->select('user_id')
					->get()
			);
		}

        if (isset($args["download"]) && $args["download"] == "download") {

            $data = \APP\SmartTable::searchPaginate($params, $query, false, false);

            $export_fields = [
                "User" => "full_name",
                "Username" => "user_name",
                "Skills Scan" => "learning_module_name",
                "Question" => "question_text",
                "Skill (TaskName)" => "task_name",
                "Skill Description" => "task_description",
                "Date" => "created_at_uk",
                "Score" => "score",
            ];

            $download_file_name = uniqid("user-skill-scan.list.") . ".xlsx";

            \APP\Tools::generateExcelDownload(
                $data,
                $export_fields,
                $this->get('settings')["LMSTempPath"] . $download_file_name
            );

            $response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
        } else {
            $p = \APP\SmartTable::searchPaginate($params, $query);

            $response->getBody()->write($p->toJson());
			return $response->withHeader('Content-Type', 'application/json');

        }

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


	// Get specific ckill-scan answer
	$group->get("{router:scan|-scan}/answer/{answer_id:[0-9]+}", function (Request $request, Response $response, array $args) {

		$query = \Models\SkillScanAnswer
			::select(
				'skill_scan_answers.id',
				'skill_scans.id as skill_scan_id',
				'learning_modules.name as learning_module_name',
				DB::raw("DATE_FORMAT(skill_scans.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"),
				'skill_scan_answers.score as score',
				'assessment_answers.task_name as task_name',
				'assessment_answers.task_description as task_description'
			)
			->where('skill_scan_answers.id', $args['answer_id'])
			->join("skill_scans", function($join) {
				$join
					->on('skill_scans.id', '=', 'skill_scan_answers.skill_scan_id')
				;
			})
			->join("learning_modules", function($join) {
				$join
					->on("skill_scans.learning_module_id", "=", "learning_modules.id")
					->where("learning_modules.status", "=", 1)
				;
			})
			->leftJoin("assessment_answers", function($join) {
				$join
					->on('assessment_answers.course_id', '=', 'skill_scans.learning_module_id')
					->on('assessment_answers.question_id', '=', 'skill_scan_answers.question')
					->on('assessment_answers.order', '=', 'skill_scan_answers.score')
				;
			})
			->with('Comments.AddedBy')
		;

		if (\APP\Auth::isLearner()) {
			$query = $query
				->where('skill_scans.user_id', \APP\Auth::getUserId())
			;
		} elseif (!\APP\Auth::accessAllLearners()) {
			$query->whereIn(
				"skill_scans.user_id",
				\Models\ManagerUser
					::where('manager_id', '=', \APP\Auth::getUserId())
					->select('user_id')
					->get()
			);
		}

		$query = $query->first();

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


	// Update score of specific skill scan
	$group->put("{router:scan|-scan}/answer/{answer_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\SkillScanAnswer
			::where('skill_scan_answers.id', $args['answer_id'])
			->join("skill_scans", function($join) {
				$join
					->on('skill_scans.id', '=', 'skill_scan_answers.skill_scan_id')
				;
			})
		;

		if (\APP\Auth::isLearner()) {
			$query = $query
				->where('skill_scans.user_id', \APP\Auth::getUserId())
			;
		} elseif (!\APP\Auth::accessAllLearners()) {
			$query->whereIn(
				"skill_scans.user_id",
				\Models\ManagerUser
					::where('manager_id', '=', \APP\Auth::getUserId())
					->select('user_id')
					->get()
			);
		}

		$query = $query->first();

		if (
			$query &&
			isset($params['score'])
		) {
			$answer = \Models\SkillScanAnswer::find($args['answer_id']);
			$answer->score = $params['score'];
			$answer->save();

			// And recalculate skill scan percentage.
			\Models\SkillScan::recalculateExisting($answer->skill_scan_id);
		}

		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'update'));


	// Get specific users skill scan history
	$group->get("-scan/history/{user_id:[0-9]+}", function (Request $request, Response $response, array $args)
	{
		if (!\Models\Role::getRoleParam('lfp_show_skill_scans')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

		if (\APP\Auth::isLearner()) {
			$args['user_id'] = \APP\Auth::getUserId();
		}

		$query1 = \Models\SkillScanHistory
			::where('user_id', $args['user_id'])
			->select(
				'skill_scan_histories.*',
				DB::raw("DATE_FORMAT(updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk"),
				DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
			)
			->with(['Module' => function ($query1) {
				$query1
					->select(
						'id',
						'name'
					)
				;
			}])
			->orderBy('created_at', 'DESC')
			->get()
		;
		$query2 = \Models\SkillScan
			::where('user_id', $args['user_id'])
			->select(
				'skill_scans.*',
				DB::raw("DATE_FORMAT(updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk"),
				DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
			)
			->with(['Module' => function ($query2) {
				$query2
					->select(
						'id',
						'name'
					)
				;
			}])
			->orderBy('created_at', 'DESC')
			->get()
		;

		$query = array_merge($query1->toArray(),$query2->toArray());

		array_multisort(
			array_map('strtotime', array_column($query, 'created_at')),
			SORT_DESC,
			$query
		);

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	//Scan History List
	$group->post("{router:scanhistory|-scanhistory}/list{download:[\/a-z]*}", function (Request $request, Response $response, array $args)
	{
		if (!\Models\Role::getRoleParam('lfp_show_skill_scans')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

		$query_id = 'skillScanHistoryList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);
		if(isset($params['search']) && isset($params['search']['user_id']))
				{
					$params['search']['users__id']=$params['search']['user_id'];
					unset($params['search']['user_id']);
			}
		$query = \APP\SmartTable::searchPaginate($params, $query, false, false);

		if (isset($args["download"]) && $args["download"] == "/download") {

			$export_fields = [
				"User ID" => "user_id",
				"Name" => "trainee_name",
				"Skill Scan" => "learning_module_name",
				"First Score" => "score_first",
				"Last Score" => "score_final",
				"Improvenment (%)" => "improvements_final",
				"Next Due Date" => "next_due_date_uk",
			];


			$download_file_name = uniqid("skill-scan-history.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$query,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else if (
			isset($args["download"]) &&
			$args["download"] == "/powerbi"
		) {
			set_time_limit(0);

			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			return \Models\CustomReview::exportToPowerBi(
				'skillscan',
				$params["export_config"],
				$data,
				$response
			);
		} else {

			// After union paginate does not work as paginate counts first part of query not whole union, too complicated, will have to run full query in LengthAwarePaginator.
			$page = isset($params["page"]) ? $params["page"] : 1;
			$nPage = isset($params["nPage"]) ? $params["nPage"] : 10;
			$results = new \Illuminate\Pagination\LengthAwarePaginator(
				array_slice(
					$query,
					($page - 1) * $nPage,
					$nPage
				),
				count($query),
				$nPage,
				$page,
				[
					"path" => "search"
				]
			);

			//$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($results->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get('{router:scan|-scan}/list/name', function (Request $request, Response $response, array $args) {

	    $skillScanHistory = DB::table('skill_scan_histories')
            ->select(
                'skill_scan_id',
                'user_id',
                'learning_results_id',
                'learning_module_id',
                'first_score',
                'last_score',
                'improvements',
                'next_due_date',
                'status',
                'created_at',
                'updated_at'
            );

        $subSkillScan = \Models\SkillScan::query()->select(
            'id',
            'user_id',
            'learning_results_id',
            'learning_module_id',
            'first_score',
            'last_score',
            'improvements',
            'next_due_date',
            'status',
            'created_at',
            'updated_at'
        )->union($skillScanHistory);

        $subSkillScanQuery = $subSkillScan->toSql();

        $results = DB::table(DB::raw("({$subSkillScanQuery}) as sub_skill_scan"))
            ->select(
            'learning_modules.id AS id',
                'learning_modules.name AS name'
            )
            ->join('users', function ($query) {
                $query->on('users.id', '=', 'sub_skill_scan.user_id')
                    ->where('users.status', '=', 1);
            })
            ->join('learning_modules', function ($query) {
                $query->on('sub_skill_scan.learning_module_id', '=', 'learning_modules.id')
                    ->where('learning_modules.status', '=', 1);
            })
            ->groupBy('name')
        ->get();

		$response->getBody()->write(json_encode($results));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


});
