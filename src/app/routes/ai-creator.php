<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/ai-creator", function($group) {
    $group->get("", function (Request $request, Response $response, array $args)
    {
        $vars = \APP\Templates::getVariables($this->get('settings'));
        return $this->get('view')->render($response, 'ai-creator/app.html', $vars);

    });
})->add(\APP\Auth::getLoginCheck($settings["LMSUri"] . "login"));
