<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;

$app->group("/role",  function ($group) {

	// Get specific role
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$role = \Models\Role
			::where('id', $args["id"])
		;
		if (\APP\Auth::isAdmin(true)) {
			$role = $role
				->with('access')
				->with('RolePermissions')
				->with('Permissions')
			;
		}

		$role = $role
			->first()
		;

		\Models\TableExtension::returnAllFields('roles', $role->id, $role);

		$response->getBody()->write(json_encode($role));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'select'));

	$group->get("/permissions" , function (Request $request, Response $response, $args) {
		session_write_close();
		$permissions = [];

		if (\APP\Auth::isAdminInterface()) {
			$permissions = \Models\Permission
				::get()
			;
		}

		$response->getBody()->write(json_encode($permissions));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// Update spcific role
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$role = \Models\Role::find($args["id"]);
		$data = $request->getParsedBody();

		$fields = [
			"name",
			"is_admin",
			"is_manager",
			"access_all_companies",
			"access_all_learners",
			"jackdaw_type",
			"is_demo",
			"is_qa",
			"is_cd",
			"is_fa",
			"is_learner",
			"sign_off_learner_status",
			"admin_interface",
			"admin_interface_manage_programmes",
			"admin_interface_programmes_action",
			"admin_interface_add_users",
			"admin_interface_add_users_assign",
			"admin_interface_edit_users",
			"admin_interface_missing_learners",
			"admin_interface_assing_learners_programme",
			"hide_set_training_tab",
			"hide_qa_tab",
			"hide_set_pending_assessment_tab",
			"hide_sign_off_tab",
			"hide_forms_sign_off_tab",
			"hide_approve_and_manage_booking",
			"disable_edit_ilr_fields",
			"disable_mfa",
			"show_all_resources",
			"description",
			"email_disable_manager_notifications",
			"exclude_manager_from_schedule",
			"exclude_from_form",
			"show_creator_menu",
			"show_assign_learning",
			"allow_assigning_manager_roles",
			"allow_change_department_self",
			'update_managers_assign_categories',
			'update_managers_assign_departments',
			'update_managers_assign_groups',
			'update_managers_assign_employees',
			'update_managers_assign_resources',
			'attach_files_to_comments',
			'learner_can_delete_files_from_comments',
			'show_skill_monitoring',
			'edit_learning_resources',
			'hide_not_assigned_learner_information_in_events',
			'allow_impersonate_learners',
			'show_send_ilr_data_to_the_hub_or_fis',
			'hide_assigning_manager',
			'hide_learning_setup',
			'show_programme_sub_tab',
			'show_resource_sub_tab',
			'show_user_sub_tab',
			'allow_access_to_user_profile',
			'hide_events_tab',
			'allow_refresh_event_resources',
			'allow_refresh_forms',
			'show_open_elms_ai',
			'docs_bot__id',
			'allow_refresh_programmes',
			'show_user_role_in_qa_list',
			'allow_send_custom_event_email',
			'powerbi_access_all_data',
			'lfp_show_edit_button',
			'lfp_show_managers_button',
			'lfp_show_packandgo_button',
			'lfp_show_add_learning',
			'lfp_show_set_work',
			'lfp_show_add_evidence',
			'lfp_show_reviews',
			'lfp_show_add_forms',
			'lfp_show_learning_programmes',
			'lfp_show_learning_resources',
			'lfp_show_learning_resources_archives',
			'lfp_show_events',
			'lfp_show_programme_status',
			'lfp_show_reports',
			'lfp_show_leaderboard',
			'lfp_show_badges',
			'lfp_show_required_competencies',
			'lfp_show_skill_scans',
			'lfp_show_qa_reports',
			'lfp_show_comment_log',
			'lfp_show_assigned_forms',
			'lfp_show_assigned_workflow',
			'lfp_show_email_history',
		];

		// If demo specified, nullify other permissions
		if (
			isset($data['is_demo']) &&
			$data['is_demo'] &&
			\APP\Tools::getConfig('isDemoAccess')
		) {
			$data['is_admin'] = false;
			$data['is_manager'] = false;
			$data['access_all_companies'] = false;
			$data['access_all_learners'] = false;
			$data['sign_off_learner_status'] = false;
			$data['admin_interface'] = false;
			$data['hide_set_training_tab'] = false;
			$data['hide_qa_tab'] = false;
			$data['hide_set_pending_assessment_tab'] = false;
			$data['hide_sign_off_tab'] = false;
			$data['hide_approve_and_manage_booking'] = false;
			$data['disable_edit_ilr_fields'] = false;
			$data['disable_mfa'] = false;
			$data['show_all_resources'] = false;
			$data['jackdaw_type'] = '';
			$data['hide_learning_setup'] = false;
			$data['hide_assigning_manager'] = false;
		}

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$role->$field = $data[$field];
			}
		}

		$role->save();

		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('roles', $role->id, $field_name, $value);
			}
		}

		// If admin and access list is given, update it.
		if (
			\APP\Auth::isAdmin()
		) {

			if (
				isset($data['access']) &&
				is_array($data['access'])
			) {
				// Remove all access for current role
				\Models\RoleAccess::where('role_id', $role->id)->delete();
				// Assign passed access
				foreach ($data['access'] as $key => $access) {
					if (isset($access['id'])) {
						$new_access = new \Models\RoleAccess;
						$new_access->role_id = $role->id;
						$new_access->access_id = $access['id'];
						$new_access->save();
					}
				}
			}

			// Check if role permissions are sent, update/remove them accordingly.
			if (isset($data['permissions_selected']) && is_array($data['permissions_selected'])) {
				$permission_ids = [];
				foreach ($data['permissions_selected'] as $key => $permission) {
					if ($permission) {
						$permission_ids[] = $key;
						$permission_exists = \Models\RolePermission
							::where('role_id', $role->id)
							->where('permission_id', $key)
							->first()
						;
						if (!$permission_exists) {
							$new_permission = new \Models\RolePermission;
							$new_permission->role_id = $role->id;
							$new_permission->permission_id = $key;
							$new_permission->created_by = \APP\Auth::getUserId();
							$new_permission->save();
						}
					}

				}
				$delete_permissions = \Models\RolePermission
					::where('role_id', $role->id)
					->whereNotIn('permission_id', $permission_ids)
					->get()
				;
				foreach ($delete_permissions as $key => $permission) {
					$permission->deleted_by = \APP\Auth::getUserId();
					$permission->save();
					$permission->delete();
				}
			}
		}

		// Reset Demo role's permissions
		if (isset($data['is_demo']) && $data['is_demo'] && \APP\Tools::getConfig('isDemoAccess')) {
			\Models\Role::updateSpecificRole('demo', $response, $role->id);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'update'));

	// Disable role
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$role = \Models\Role::find($args["id"]);
		$role->status = 0;
		$role->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'disable'));

	// Enable role
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$role = \Models\Role::find($args["id"]);
		$role->status = 1;
		$role->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'disable'));

	// Get all roles
	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$current_user = \APP\Auth::getUser();

		$roles = \Models\Role
			::where("status", true)
		;


		if (
			\APP\Auth::isAdmin(true)
		) {
			$roles = $roles
				->with('access')
			;
		} else {
			$roles = $roles
				->where('is_admin', false)
			;
		}

		$roles = $roles
			->get()
		;

		if (!\APP\Auth::checkStructureAccess('system-setup-organisation-roles', 'select')) {
			$roles = [];
		}

		$response->getBody()->write(json_encode($roles));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->get('/shadow/all', function (Request $request, Response $response) {
		session_write_close();
		$current_user = \APP\Auth::getUser();

		// Generate cache key based on user role permissions
		$isAdmin = \APP\Auth::isAdmin(true);
		$cacheKeySuffix = $isAdmin ? 'admin' : 'role_' . $current_user->role_id;
		$cacheKey = \APP\Cache\CacheHelper::key('roles', 'shadow_all', $cacheKeySuffix);

		if (function_exists('cache')) {
			$roles = cache()->remember($cacheKey, 1800, function() { // 30 minutes (roles change less frequently)
				// Re-determine permissions inside the closure to avoid cache poisoning
				$currentUser = \APP\Auth::getUser();
				$isAdminInClosure = \APP\Auth::isAdmin(true);
				$query = \Models\Role::where("status", true);
				
				if (!$isAdminInClosure) {
					$accessIds = \APP\Cache\RoleAccessCache::getAccessIds($currentUser->role_id);
					$query = $query
						->whereIn('id', $accessIds)
						->orWhereIn('id', [$currentUser->role_id]);
				}
				
				return $query->get();
			});
		} else {
			// Fallback for CLI/contexts without cache
			$query = \Models\Role::where("status", true);
			
			if (!$isAdmin) {
				$accessIds = \APP\Cache\RoleAccessCache::getAccessIds($current_user->role_id);
				$query = $query
					->whereIn('id', $accessIds)
					->orWhereIn('id', [$current_user->role_id]);
			}
			
			$roles = $query->get();
		}

		// Loop all roles and check if image exists, if not, assign default one.
		foreach ($roles as $key => $role) {
			if (
				empty($role->image) ||
				!is_file($this->get('settings')['LMSRoleImagePath'] . $role->image)
			) {
				$role->default_image = true;
				if ($role->is_learner) {
					$role->image = 'trainee.png';
				}
				if ($role->is_manager) {
					$role->image = 'coach_trainer.png';
				}
				if ($role->is_qa) {
					$role->image = 'quality_assurer.png';
				}
				if ($role->is_cd) {
					$role->image = 'curriculum_developer.png';
				}
				if ($role->is_fa) {
					$role->image = 'financial_auditor.png';
				}
				if ($role->is_admin) {
					$role->image = 'administrator.png';
				}
			}
		}

		$response->getBody()->write(json_encode($roles));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	// Add new role
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$role = new \Models\Role;

		$fields = [
			"name",
			"is_admin",
			"is_manager",
			"access_all_companies",
			"access_all_learners",
			"jackdaw_type",
			"is_demo",
			"is_qa",
			"is_cd",
			"is_fa",
			"is_learner",
			"sign_off_learner_status",
			"admin_interface",
			"admin_interface_manage_programmes",
			"admin_interface_programmes_action",
			"admin_interface_add_users",
			"admin_interface_add_users_assign",
			"admin_interface_edit_users",
			"admin_interface_missing_learners",
			"admin_interface_assing_learners_programme",
			"show_all_resources",
			"description",
			"email_disable_manager_notifications",
			"exclude_manager_from_schedule",
			"show_creator_menu",
			"show_assign_learning",
			"allow_assigning_manager_roles",
			"allow_change_department_self",
			'update_managers_assign_categories',
			'update_managers_assign_departments',
			'update_managers_assign_groups',
			'update_managers_assign_employees',
			'update_managers_assign_resources',
			'attach_files_to_comments',
			'learner_can_delete_files_from_comments',
			'show_skill_monitoring',
			'edit_learning_resources',
			'hide_not_assigned_learner_information_in_events',
			'allow_impersonate_learners',
			'show_send_ilr_data_to_the_hub_or_fis',
			'hide_assigning_manager',
			'hide_learning_setup',
			'show_programme_sub_tab',
			'show_resource_sub_tab',
			'show_user_sub_tab',
			'allow_access_to_user_profile',
			'hide_events_tab',
			'allow_refresh_event_resources',
			'allow_refresh_forms',
			'show_open_elms_ai',
			'docs_bot__id',
			'allow_refresh_programmes',
			'show_user_role_in_qa_list',
			'allow_send_custom_event_email',
			'lfp_show_edit_button',
			'lfp_show_managers_button',
			'lfp_show_packandgo_button',
			'lfp_show_add_learning',
			'lfp_show_set_work',
			'lfp_show_add_evidence',
			'lfp_show_reviews',
			'lfp_show_add_forms',
			'lfp_show_learning_programmes',
			'lfp_show_learning_resources',
			'lfp_show_learning_resources_archives',
			'lfp_show_events',
			'lfp_show_programme_status',
			'lfp_show_reports',
			'lfp_show_leaderboard',
			'lfp_show_badges',
			'lfp_show_required_competencies',
			'lfp_show_skill_scans',
			'lfp_show_qa_reports',
			'lfp_show_comment_log',
			'lfp_show_assigned_forms',
			'lfp_show_assigned_workflow',
			'lfp_show_email_history',

		];

		// If demo specified, nullify other permissions
		if (isset($data['is_demo']) && $data['is_demo'] && \APP\Tools::getConfig('isDemoAccess')) {
			$data['is_admin'] = false;
			$data['is_manager'] = false;
			$data['access_all_companies'] = false;
			$data['access_all_learners'] = false;
			$data['is_qa'] = false;
			$data['sign_off_learner_status'] = false;
			$data['admin_interface'] = false;
			$data['show_all_resources'] = false;
			$data['jackdaw_type'] = '';
			$data['hide_learning_setup'] = false;
			$daa['hide_assigning_manager'] = false;
		}

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$role->$field = $data[$field];
			}
		}

		$role->status = 1;
		$role->save();

		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('roles', $role->id, $field_name, $value);
			}
		}


		// If admin and access list is given, update it.
		if (
			\APP\Auth::isAdmin()
		) {

			if (
				isset($data['access']) &&
				is_array($data['access'])
			) {
				// Remove all access for current role
				\Models\RoleAccess::where('role_id', $role->id)->delete();
				// Assign passed access
				foreach ($data['access'] as $key => $access) {
					if (isset($access['id'])) {
						$new_access = new \Models\RoleAccess;
						$new_access->role_id = $role->id;
						$new_access->access_id = $access['id'];
						$new_access->save();
					}
				}
				$role->access = $data['access'];
			}

			// Check if role permissions are sent, update/remove them accordingly.
			if (isset($data['permissions_selected']) && is_array($data['permissions_selected'])) {
				$permission_ids = [];
				foreach ($data['permissions_selected'] as $key => $permission) {
					if ($permission) {
						$permission_ids[] = $key;
						$permission_exists = \Models\RolePermission
							::where('role_id', $role->id)
							->where('permission_id', $key)
							->first()
						;
						if (!$permission_exists) {
							$new_permission = new \Models\RolePermission;
							$new_permission->role_id = $role->id;
							$new_permission->permission_id = $key;
							$new_permission->created_by = \APP\Auth::getUserId();
							$new_permission->save();
						}
					}

				}
				$delete_permissions = \Models\RolePermission
					::where('role_id', $role->id)
					->whereNotIn('permission_id', $permission_ids)
					->get()
				;
				foreach ($delete_permissions as $key => $permission) {
					$permission->deleted_by = \APP\Auth::getUserId();
					$permission->save();
					$permission->delete();
				}
			}
		}

		// Apply default permissions for manager role
		if (isset($data['is_manager']) && $data['is_manager']) {
			\Models\Role::updateSpecificRole('manager', $response, $role->id);
		}

		// Apply default permissions for QA
		if (isset($data['is_qa']) && $data['is_qa']) {
			\Models\Role::updateSpecificRole('qa', $response, $role->id);
		}

		// Apply default permissions for CD
		if (isset($data['is_cd']) && $data['is_cd']) {
			\Models\Role::updateSpecificRole('cd', $response, $role->id);
		}

		// Apply default permissions for demo role.
		if (isset($data['is_demo']) && $data['is_demo'] && \APP\Tools::getConfig('isDemoAccess')) {
			\Models\Role::updateSpecificRole('demo', $response, $role->id);
		}
		if (($data['is_admin'] ?? false) === true) {
			\Models\Role::updateAdminRoles();
		}

		$response->getBody()->write(json_encode($role));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'insert'));

	// Get whole structure and attach roles from role_structure table.
	$group->post('/{id:[0-9]+}/structure', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$structure = \Models\Structure
			::with(['role' => function($query) use ($args) {
				$query
					->where('role_id', '=', $args["id"])
				;
			}])
			->whereHas('role', function ($query) {
				if (
					!\APP\Auth::isAdmin(true)
				) {
					$query
						->whereHas('role', function ($query) {
							$query = $query
								->where('is_admin', false)
							;
						})
					;
				}
			})
			->get()
		;

		$response->getBody()->write(json_encode(\APP\Tools::buildTree($structure->toArray())));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'select'));

	// update roles with structure
	$group->put('/{role_id:[0-9]+}/structure/{structure_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		if (!empty($params['permission']) && isset($params['permission_state'])) {
			$user = \Models\RoleStructure
				::firstOrNew(
					[
						'role_id' => $args['role_id'],
						'structure_id' => $args['structure_id'],
					]
				)
			;
			if ($params['permission'] == 'all') {
				$user['view'] = $params['permission_state'];
				$user['select'] = $params['permission_state'];
				$user['insert'] = $params['permission_state'];
				$user['update'] = $params['permission_state'];
				$user['disable'] = $params['permission_state'];
			} else {
				$user[$params['permission']] = $params['permission_state'];
			}
			$user->save();
		}

		return
			$response
				->withHeader('Content-Type', 'application/json')
				//->write(json_encode($structure))
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'update'));


	// Retrieve paginated pages with roles attached
	$group->post('/{id:[0-9]+}/pages', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\Page::selectRaw("pages.*, (MAX(`role_pages`.role_id)  IS NOT NULL) AS added")
			->whereNotIn("pages.link", $this->get('settings')['licensing']['hiddenMenuItems'])
			->leftjoin('role_pages', function($join) use ($args) {
				$join->on('role_pages.page_id', '=', 'pages.id')
					->where("role_pages.role_id", "=", $args["id"]);
			})
			->groupBy("pages.id")
		;

		if (isset($params["search"]) && isset($params["search"]["added"]))
		{
			if ($params["search"]["added"] == "1")
			{
				$query->whereRaw("`role_pages`.role_id IS NOT NULL");
			}
			elseif ($params["search"]["added"] == "0")
			{
				$query->whereRaw("`role_pages`.role_id IS NULL");
			}
			unset($params["search"]["added"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);


		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'select'));

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Role
			::select("roles.*")
			->selectRaw("COUNT(users.id) AS n_users")
			->leftJoin("users", function($join){
				$join
					->on("users.role_id", "=", "roles.id")
					->where("users.status","=", true)
				;
			})
			->groupBy("roles.id")
		;

		if (
			\APP\Auth::isAdmin(true)
		) {
			$query = $query
				->with('access')
			;
		} else {
			$query = $query
				->where('is_admin', false)
			;
		}


		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}


		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Role Name" => "name",
				"Number of Users" => "n_users",
			];


			$download_file_name = uniqid("roles.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'select'));

	// Add default trainee roles
	$group->put("/populate-role/{role_name}" , function (Request $request, Response $response, $args) {

		return \Models\Role::updateSpecificRole($args['role_name'], $response);

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'insert'));

	// Set shadow role if admin or manager
	$group->post("/shadow" , function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		// Get user directly and check roles there, as isadmin and ismanager is modified by shadow rules
		$user = \APP\Auth::getUser();
		$role = \Models\Role
			::find($params['id'])
		;
		$allowed_roles = \Models\RoleAccess
			::where('role_id', $user->role_id)
			->get()
			->pluck('access_id')
			->toArray()
		;


		// If given role is active, user is admin or if user is not admin and is not switching to admin and users role has access to this role.

		if (
			$role &&
			(
				(
					(
						$user->role->is_admin ||
						(
							in_array($role->id, $allowed_roles) &&
							!$role->is_admin
						)
					) &&
					$role->status == true
				) ||
				(
					$user->role->id == $role->id
				)
			)
		) {
			// Update given shadow role, if current role is above shadow rule
			if ($user->role->id != $role->id) {
				$user->shadow_role_id = $role->id;
			} else {
				$user->shadow_role_id = null;
			}

			$user->save();
			return $response;
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	$group->post("/add_image/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$role = \Models\Role::find($args['id']);

		$image_file = \Models\Role::uploadImage($role, $this->get('settings')['LMSRoleImagePath']);

		$response->getBody()->write($image_file);
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'insert'));


	// display role's image!
	$group->get('/image/{image}', function (Request $request, Response $response, array $args) {
		$role = \Models\Role::where('image', $args["image"])->first();

		if (is_file($this->get('settings')['LMSRoleImagePath'] . $role->image)) {
			$imageStream = new OpenStream($this->get('settings')['LMSRoleImagePath'] . $role->image, 'r');
			$response = $response
				->withBody($imageStream)
				->withHeader('Content-Type', FILEINFO_MIME_TYPE)
			;
		} else {
			$response = $response
				->withStatus(404)
			;
		}

		return $response;
	})->add(\APP\Auth::getSessionCheck());

	// Delete image from user profile
	$group->delete('/delete_image/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$role = \Models\Role::find($args["id"]);
		if (is_file($this->get('settings')['LMSRoleImagePath'] . $role->image)) {
			unlink($this->get('settings')['LMSRoleImagePath'] . $role->image);
		}
		$role->image = null;
		$role->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'disable'));

});
