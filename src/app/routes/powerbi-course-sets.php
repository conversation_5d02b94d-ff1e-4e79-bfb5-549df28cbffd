<?php

use APP\Auth;
use APP\Controllers\PowerBIController;
use APP\SmartTable;
use Models\Company;
use Models\ManagerUser;
use Models\PowerBiAccessToken;
use Models\User;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/powerbi-course-sets", function ($group) use ($app) {

    $group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $coursetSet = \Models\PowerBiCourseSet::find($args["id"]);
        $coursetSet->status = 0;
        $coursetSet->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'disable'));

    $group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $coursetSet = \Models\PowerBiCourseSet::find($args["id"]);
        $coursetSet->status = 1;
        $coursetSet->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'disable'));

    $group->post('/list', function (Request $request, Response $response) {

        $params = $request->getParsedBody();

        $query = \Models\PowerBiCourseSet::withCount(['courses', 'categories']);

        if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
            $query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
        }

        if (isset($params["search"]) && is_array($params["search"])) {
            if (isset($params["search"]["refresh"])) {
                unset($params["search"]["refresh"]);
            }
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);
        return $response->withHeader('Content-Type', 'application/json')->write($p->toJson());

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'select'));

    $group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {

        $report = \Models\PowerBiCourseSet::find($args["id"]);
        if ($report){
            $response->getBody()->write($report->toJson());
            return $response->withHeader('Content-Type', 'application/json');
        }else{
            return \APP\Tools::returnCode($request, $response, 404);
        }


    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'select'));

    $group->post("/new", function (Request $request, Response $response) {

        $data = $request->getParsedBody();

        $powerbiCourseSet = new \Models\PowerBiCourseSet();
        $powerbiCourseSet->name = $data["name"];
        $powerbiCourseSet->status = true;
        $powerbiCourseSet->save();

        return $response;

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'insert'));

    $group->put("/{id:[0-9]+}", function (Request $request, Response $response, $args) {

        $data = $request->getParsedBody();

        $courseSet = \Models\PowerBiCourseSet::where('id', $args['id'])->first();
        $courseSet->name = $data["name"];
        $courseSet->save();

        return $response;

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'insert'));

    $group->get('/all', function (Request $request, Response $response) {
        $course_sets = \Models\PowerBiCourseSet::where('status', 1)->get();

        $response->getBody()->write(json_encode($course_sets));
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'select'));

    $group->post("/{id:[0-9]+}/learning_modules", function (Request $request, Response $response, $args) {

        $dashboard = \Models\PowerBiCourseSet::find($args["id"]);
        $params = $request->getParsedBody();

        $query = \Models\LearningModule
            ::selectRaw("learning_modules.*, (`powerbi_course_set_links`.powerbi_course_set_id  IS NOT NULL) AS assigned")
            ->leftjoin('powerbi_course_set_links', function ($join) use ($args) {
                $join
                    ->on('powerbi_course_set_links.type_id', '=', 'learning_modules.id')
                    ->where('powerbi_course_set_links.type', 'learning_module')
                    ->where("powerbi_course_set_links.powerbi_course_set_id", "=", $args["id"]);
            })
            ->where('learning_modules.status', true)// ->where('learning_modules.is_course', true)
        ;

        if (isset($params["search"]) && isset($params["search"]["assigned"])) {
            if ($params["search"]["assigned"] == "1") {
                $query->whereRaw("`powerbi_course_set_links`.powerbi_course_set_id IS NOT NULL");
            } elseif ($params["search"]["assigned"] == "0") {
                $query->whereRaw("`powerbi_course_set_links`.powerbi_course_set_id IS NULL");
            }
            unset($params["search"]["assigned"]);
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);
        $response->getBody()->write($p->toJson());
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'select'));

    $group->put('/{powerbi_course_set_id:[0-9]+}/learning_modules/{course_id:[0-9]+}', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();

        $powerbi_course_set = \Models\PowerBiCourseSet::find($args["powerbi_course_set_id"]);
        $course = \Models\LearningModule::find($args["course_id"]);

        $powerbi_course_setLearningModules = new \Models\PowerbiCourseSetLinks();
        $powerbi_course_setLearningModules->powerbi_course_set_id = $args["powerbi_course_set_id"];
        $powerbi_course_setLearningModules->type = "learning_module";
        $powerbi_course_setLearningModules->type_id = $args["course_id"];
        $powerbi_course_setLearningModules->save();

        // FIXME
        // $assigned_course_ids = $powerbi_course_set->courses()->where('status', true)->get()->pluck("id")->toArray();
        // $powerbi_course_set->courses()->sync($assigned_course_ids);

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'update'));

    $group->delete('/{powerbi_course_set_id:[0-9]+}/learning_modules/{course_id:[0-9]+}', function (Request $request, Response $response, $args) {

        \Models\PowerbiCourseSetLinks
            ::where("powerbi_course_set_id", $args["powerbi_course_set_id"])
            ->where("type_id", $args["course_id"])
            ->where("type", "learning_module")
            ->delete();
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'disable'));

    $group->post("/{id:[0-9]+}/learning_module_categories", function (Request $request, Response $response, $args) {

        $dashboard = \Models\PowerBiCourseSet::find($args["id"]);
        $params = $request->getParsedBody();

        $query = \Models\LearningModuleCategory
            ::selectRaw("learning_module_categories.*, (`powerbi_course_set_links`.powerbi_course_set_id  IS NOT NULL) AS assigned")
            ->leftjoin('powerbi_course_set_links', function ($join) use ($args) {
                $join
                    ->on('powerbi_course_set_links.type_id', '=', 'learning_module_categories.id')
                    ->where('powerbi_course_set_links.type', 'learning_module_category')
                    ->where("powerbi_course_set_links.powerbi_course_set_id", "=", $args["id"]);
            })
            ->where('learning_module_categories.status', true)// ->where('learning_modules.is_course', true)
        ;

        if (isset($params["search"]) && isset($params["search"]["assigned"])) {
            if ($params["search"]["assigned"] == "1") {
                $query->whereRaw("`powerbi_course_set_links`.powerbi_course_set_id IS NOT NULL");
            } elseif ($params["search"]["assigned"] == "0") {
                $query->whereRaw("`powerbi_course_set_links`.powerbi_course_set_id IS NULL");
            }
            unset($params["search"]["assigned"]);
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);

        $response->getBody()->write($p->toJson());
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'select'));

    $group->put('/{powerbi_course_set_id:[0-9]+}/learning_module_categories/{category_id:[0-9]+}', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();

        $powerbi_course_set = \Models\PowerBiCourseSet::find($args["powerbi_course_set_id"]);

        $powerbi_course_setLearningModules = new \Models\PowerbiCourseSetLinks();
        $powerbi_course_setLearningModules->powerbi_course_set_id = $args["powerbi_course_set_id"];
        $powerbi_course_setLearningModules->type = "learning_module_category";
        $powerbi_course_setLearningModules->type_id = $args["category_id"];
        $powerbi_course_setLearningModules->save();

        // FIXME
        // $assigned_category_ids = $powerbi_course_set->categories()->where('status', true)->get()->pluck("id")->toArray();
        // $powerbi_course_set->categories()->sync($assigned_category_ids);

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'update'));

    $group->delete('/{powerbi_course_set_id:[0-9]+}/learning_module_categories/{category_id:[0-9]+}', function (Request $request, Response $response, $args) {

        \Models\PowerbiCourseSetLinks
            ::where("powerbi_course_set_id", $args["powerbi_course_set_id"])
            ->where("type_id", $args["category_id"])
            ->where("type", "learning_module_category")
            ->delete();
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-course-sets', 'disable'));

});
