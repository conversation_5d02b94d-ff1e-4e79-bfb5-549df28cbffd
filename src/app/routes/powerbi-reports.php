<?php

use APP\Auth;
use APP\Controllers\PowerBIController;
use APP\SmartTable;
use Models\Company;
use Models\ManagerUser;
use Models\PowerBiAccessToken;
use Models\User;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/powerbi-reports", function ($group) use ($app) {

    $group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $report = \Models\PowerBiReport::find($args["id"]);
        $report->status = 0;
        $report->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-reports', 'disable'));

    $group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $report = \Models\PowerBiReport::find($args["id"]);
        $report->status = 1;
        $report->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-reports', 'disable'));

    $group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {

        $report = \Models\PowerBiReport::with(['dataset','dashboard'])->find($args["id"]);
        if ($report){
            return $response->withHeader('Content-Type', 'application/json')->write($report->toJson());
        }else{
            return $response
                ->withStatus(404)
                ->withHeader('Content-Type', 'text/html')
                ->write('404 Not Found');
        }

        $response->getBody()->write($report->toJson());
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-reports', 'select'));

    $group->post('/list', function (Request $request, Response $response) {

        $params = $request->getParsedBody();

        $query = \Models\PowerBiReport::
        // join('powerbi_datasets', 'powerbi_reports.dataset_id', 'powerbi_datasets.id')->
        with(['dataset','dashboard']);

        if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
            $query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
        }

        if (isset($params["search"]) && is_array($params["search"])) {
            if (isset($params["search"]["refresh"])) {
                unset($params["search"]["refresh"]);
            }
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);
        $response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-reports', 'select'));

    $group->post("/update-report/{id:[0-9]+}", PowerBIController::class . ':cloneOrUpdateReport')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-reports', 'insert'));

    $group->delete('/{report_id:[0-9]+}', PowerBIController::class . ':deleteReport')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-reports', 'select'));

});
