<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/smcr-f-p-category",  function ($group) {

	// Get individual entry
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$category = \Models\SmcrFPCategory::find($args["id"]);

		$response->getBody()->write(json_encode($category));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// Update entry
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$category = \Models\SmcrFPCategory::find($args["id"]);
		$params = $request->getParsedBody();

		$fields = [
			"name", "description", "status_learner",
		];
		\APP\Tools::setObjectFields($category, $fields, $params, true);
		$category->save();

		// Rerun custom review update
		\DB\CustomReviews::update();
		\DB\CustomReviews::updateFilters($this->get('settings'));

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-f-p-categories', 'update'));


	// Disable entry
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$category = \Models\SmcrFPCategory::find($args["id"]);
		$category->status = false;
		$category->save();

		// Rerun custom review update
		\DB\CustomReviews::update();
		\DB\CustomReviews::updateFilters($this->get('settings'));

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-f-p-categories', 'disable'));

	// Enable entry
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$category = \Models\SmcrFPCategory::find($args["id"]);
		$category->status = true;
		$category->save();

		// Rerun custom review update
		\DB\CustomReviews::update();
		\DB\CustomReviews::updateFilters($this->get('settings'));

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-f-p-categories', 'disable'));


	// Get list of all entries
	$group->get('/all', function (Request $request, Response $response) {
		$query = \Models\SmcrFPCategory
			::where("status", true)
		;

		/*
			if (\APP\Auth::isLearner()) {
				$query = $query
					->where('status_learner', true)
				;
			}
		*/

		$query = $query->get();

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


/*
	// Add new entry
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$category = new \Models\SmcrFPCategory;
		$category->name = $data["name"];
		if (isset($data["description"])) {
			$category->description = $data["description"];
		}
		$category->status = true;
		$category->save();


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-f-p-categories', 'insert'));
*/

   $group->post('/list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\SmcrFPCategory::where("id", ">", "0");

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-f-p-categories', 'select'));
});