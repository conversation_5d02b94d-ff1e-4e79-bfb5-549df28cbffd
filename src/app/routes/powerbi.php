<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use APP\Controllers\PowerBiController;

use Illuminate\Database\Capsule\Manager as DB;

$app->group("/powerbi",  function ($group) use($app) {

    //|| $powerbi->hasTokenInsession()
    $group->get('/hastoken', function (Request $request, Response $response) {
        $powerbi = new \APP\PowerBi(false);

        try{
            $powerbi->getTokenFromSession();

            return $response;

        } catch(\Exception $e){

            return $response->withStatus(404);
        }
    })->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));

	$group->get('', function (Request $request, Response $response) use ($app) {

        $powerbi = new \APP\PowerBi(false);

        $params = $request->getQueryParams();

        if (isset($params["code"])){

            $powerbi->getOAuthTokenFromAccessCode($params["code"]);
            $powerbi->storeTokenInSession();

            ?>
            <script>
                window.name = "close";
                setTimeout(function(){ window.close(); }, 1000);
            </script>
            Loging to PowerBi successful!
            <?php
        } elseif(empty($params)) {
            setcookie("LMSSite", $app->getContainer("settings")["settings"]["LMSUrl"], time()+3600, "/");
            return $response->withStatus(302)->withHeader('Location', $powerbi->getInstallLink());
        } else {
            throw new \Exception($params);
        }

		return $response;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));


    $group->post("/exportall", function (Request $request, Response $response) {

        set_time_limit(0);

        $params = $request->getParsedBody();
        $dataset = $params["export_config"];

        \APP\PowerBi::exportAllReports($dataset, $response);

        return $response;
    })->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));

	$group->get('/datasets{report_name:[\/a-z]*}', function (Request $request, Response $response, $args) {

        $powerbi = new \APP\PowerBi();
        $powerbi->getTokenFromSession();

        $dataset_names = [];

        $report_name = isset($args["report_name"]) ? str_replace("/", "", $args["report_name"]) : False;

        foreach($powerbi->getDatasets() as $dataset)
        {
            $dataset_names[] = $dataset["name"];
        }


		$response->getBody()->write(json_encode($dataset_names));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));

    $group->get('/get-last-refreshed-time',PowerBiController::class.':getLastRefreshedTime')->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));
});
