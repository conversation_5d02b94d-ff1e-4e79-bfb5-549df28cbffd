<?php

use APP\Teams;
use APP\Tools;
use APP\Form;
use Carbon\Carbon;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Models\Configuration;
use Models\Schedule;
use Models\Venue;

$app->group("/venue",  function ($group) {
	$group->get('/rooms',function(Request $request,Response $response){
		if(!Tools::getConfig('OutlookRoomAccessToken')){
		$teams = new Teams();
		return $response->withStatus(302)->withHeader('Location', $teams->get_room_authorize_link($this->get('settings')));
		} else {
			Configuration::where('key','OutlookRoomAccessToken')->update(['value'=>'']);
			Configuration::where('key','OutlookRoomRefreshToken')->update(['value'=>'']);
			$response->getBody()->write(json_encode(['status'=>0]));
			return $response->withHeader('Content-Type', 'application/json');
		}
	});
	$group->get('/check-token',function(Request $request,Response $response){
		$response->getBody()->write(json_encode(['status'=>Tools::getConfig('OutlookRoomAccessToken')?true:false]));
		return $response->withHeader('Content-Type', 'application/json');
	});
	$group->get('/get-room',function(Request $request,Response $response){
		try {
			$teams = new Teams();
			$teams->token = Venue::getToken();
			if ($teams) {
				$data = $teams->makeAPIGetRequest("https://graph.microsoft.com/v1.0/places/microsoft.graph.room");
				$data = json_decode($data);
				$response->getBody()->write(json_encode($data));
				return $response->withHeader('Content-Type', 'application/json');

			} else {
				$response->getBody()->write('[]');
				return $response->withHeader('Content-Type', 'application/json');
			}
		} catch(Exception $e) {
			$response->getBody()->write(json_encode(['message'=>"Outlook Room not configured. Please contact the administrator to configure or disable Outlook Room."]));
			return $response
				->withHeader('Content-Type', 'application/json')
				->withStatus(404)
			;
		}

	});

	$group->post('/room-availability',function(Request $request,Response $response){
		$params = $request->getParsedBody();
        $currentTimeZone =  Tools::getConfig('defaultTimezone');
		$teams = new Teams();
		$data = [
			'schedules'=>$params['schedules'],
			'startTime'=>[
				'dateTime'=>$params['startTime'],
				'timeZone'=> $currentTimeZone
			],
			'endTime'=>[
				'dateTime'=>$params['endTime'],
				'timeZone'=>$currentTimeZone
			],
			'availabilityViewInterval'=>$params['availabilityViewInterval']
        ];
	$startTime = Carbon::parse($params['startTime'])->format('Y-m-d\TH:i:sP');
	$endTime = Carbon::parse($params['endTime'])->format('Y-m-d\TH:i:sP');

		$teams->token = Venue::getAdminToken();
		$defaultTimeZone = Tools::getConfig('defaultTimezone');
		$header = ['Prefer: outlook.timezone="'.$defaultTimeZone.'"'];
		$header = ['Prefer: IdType="ImmutableId"'];
    $data = $teams->makeAPIGetRequest("https://graph.microsoft.com/v1.0/users/".$params['schedules'][0]."/calendarView?startDateTime=$startTime&endDateTime=$endTime",$header);
		$data = json_decode($data,true);
		$status = false;
		if (isset($data['value'])) {
			$data = $data['value'];
			if (count($data)==0) {
				$status =true;
			} elseif(count($data)==1 && isset($params['event'])){
				$outlook_id = $data[0]['iCalUId'];
				$schedule = Schedule::where('outlook_event_response','LIKE',"%".$outlook_id."%")->where('id',$params['event'])->first();
				if($schedule){
					$status = true;
				}
			}
		}

		$response->getBody()->write(json_encode(['status'=>$status,'out'=>$data]));
		return $response->withHeader('Content-Type', 'application/json');
	});

	$group->put('/{status:disable|enable}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\Venue::find($args["id"]);
		$entry->status = 0;
		if ($args['status'] == 'enable') {
			$entry->status = 1;
		}
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-venues', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$venue = \Models\Venue
			::where('id', $args["id"]);

		$venue = $venue->first();

		$response->getBody()->write(json_encode($venue));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-venues', 'select'));

	/* Add new venue */
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$uploadedFiles = $request->getUploadedFiles();

		$venue = new \Models\Venue();
		$venue->name = data_get($data, 'name');
		$venue->address = data_get($data, 'address');
		$venue->postcode = data_get($data, 'postcode');
		$venue->contact_number = data_get($data, 'contact_number');
		$venue->contact_name = data_get($data, 'contact_name');
		$venue->rating = data_get($data, 'rating');
		$venue->capacity = data_get($data, 'capacity');
		$venue->instructions = data_get($data, 'instructions');
		$venue->status = 1;

		if (isset($uploadedFiles['image'])) {
			$image = $uploadedFiles['image'];
			$imageEntryPath = $this->get('settings')['LMSVenuePath'];

			$imageUploadStatus = $venue->uploadVenueImage($image, $imageEntryPath);

			if (!(bool)$imageUploadStatus['status']) {
				$response->getBody()->write(\Illuminate\Support\Arr::first($imageUploadStatus['errors']));
				return $response->withStatus(422);
			}
			$venue->image = $imageUploadStatus['newFileName'];
		}

		$venue->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-venues', 'insert'));

	/* Update venue */
	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

				$venue = \Models\Venue::query()->find($args["id"]);
				$venue->name = data_get($data, 'name');
				$venue->address = data_get($data, 'address');
				$venue->postcode = data_get($data, 'postcode');
				$venue->contact_number = data_get($data, 'contact_number');
				$venue->contact_name = data_get($data, 'contact_name');
				$venue->rating = data_get($data, 'rating');
				$venue->capacity = data_get($data, 'capacity');
				$venue->instructions = data_get($data, 'instructions');
				$venue->status = 1;
				if (isset($data['custom_field'])) {
						Form::saveCustomForm($data['custom_field'], 'venue', $venue->id);
				}

				if(!empty($_FILES['image'])) {
						$imageUploadStatus = $venue->uploadVenueImage($_FILES['image']['name'], $this->get('settings')['LMSVenuePath']);
						if(!(bool)$imageUploadStatus['status']) {
								return $response->withStatus(422)->write(\Illuminate\Support\Arr::first($imageUploadStatus['errors']));
						}
						$venue->image = $imageUploadStatus['imageFileName'];
				}

				$venue->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-venues', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$data = \Models\Venue
			::where("status", true)
			->get();

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck()); // Everyone logged in gets access to enabled venues

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Venue::where("id", ">", "0");

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach ($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Venue Title" => "name",
			];


			$download_file_name = uniqid("venues.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-venues', 'select'));

	// Import Venues
	$group->post('/import', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		if (isset($_FILES['importFile'])) {

			//upload file
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSTempPath"]);
			$import_file = new \Upload\File('importFile', $storage);
			$import_file_name = $import_file->getNameWithExtension();
			$import_file_id = uniqid();
			$import_file->setName($import_file_id);
			$notify_roles = [];
			try {
				$import_file->upload();
			} catch (\Exception $e) {
				$errors = $import_file->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}

			// Log import files!
			\Models\LogExportImport::insertRecord(file_get_contents($this->get('settings')["LMSTempPath"] . $import_file->getNameWithExtension()), '.' . $import_file->getExtension(), $import_file_name, false, 'imports');

			$n_records = \APP\Import::venues($this->get('settings')["LMSTempPath"] . $import_file->getNameWithExtension());

			$response->getBody()->write(json_encode([
				'updated' => $n_records['n_record_updated'],
				'inserted' => $n_records['n_record_inserted'],
				'disabled' => $n_records['n_record_disabled'],
				'rejected' => $n_records['n_record_rejected'],
				'deleted' => $n_records['n_record_deleted'],
				'message' => $n_records['message'],
				'log' => $n_records['log']
			]));
			return $response->withHeader('Content-Type', 'application/json');
		};
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-venues'], 'select'));

		// Delete image
		$group->delete('/image/{id:[0-9]+}', function (Request $request, Response $response, $args) {
				$venue = \Models\Venue::query()->find($args["id"]);
				if (is_file($this->get('settings')["LMSVenuePath"] . $venue->image)) {
						unlink($this->get('settings')["LMSVenuePath"] . $venue->image);
				}
				$venue->image = null;
				$venue->save();

				return $response;

		})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-venues', 'update'));

		$group->get('/{img_name}', function (Request $request, Response $response, $args) {
				$imgPath = $this->get('settings')['LMSVenuePath'] . $args['img_name'];

				if(is_file($imgPath)) {
						$fileStream = new OpenStream($imgPath, 'r');

						$finfo = finfo_open(FILEINFO_MIME_TYPE);

						return $response
								->withHeader('Content-Length', filesize($imgPath))
								->withHeader('Content-Type', finfo_file($finfo, $imgPath))
								->withBody($fileStream);
				}

				return $response->withStatus(404);

	});

});
