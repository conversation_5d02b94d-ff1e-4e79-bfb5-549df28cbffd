<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Endroid\QrCode\QrCode;

$app->group("/myprofile",  function ($group) {

	$group->get('/', function (Request $request, Response $response, $args) {

		$user = \Models\User
			::with('Role')
			->find(\APP\Auth::getUserId())
		;

		$user->password = "";

		// Baby steps in table extension, add any fields inside table_ext
		\Models\TableExtension::returnAllFields('users', $user->id, $user);

		$user->accessible_ui = $user->accessible_ui == '1' ? true : false;

		$response->getBody()->write(json_encode($user));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-my-profile', 'trainee-edit-profile'], 'select'));


	$group->get('/mfaQR', function (Request $request, Response $response) {
		$user = \APP\Auth::getUser();

		$googleAuthQRUr = null;

		if ($user->enabled_google_2FA && $user->google_2FA_secret) {

			$username = $user->username; // Assuming this is the user's username
			$issuer = urlencode($this->get('settings')["LMSName"] . $this->get('settings')["LMSUri"]); // The issuer, urlencoded to handle spaces and special characters
			$secret = $user->google_2FA_secret; // The user's 2FA secret key

			// The otpauth string that represents the 2FA details
			$otpAuthString = "otpauth://totp/{$username}@{$issuer}?secret={$secret}&issuer={$issuer}";

			// Create the QR code instance
			$qrCode = new QrCode($otpAuthString);
			$qrCode->setSize(200);

			// Set other options as needed
			$qrCode->setMargin(0);

			// Write the QR code data to a string
			$qrCodeData = $qrCode->writeString();

			// Base64 encode the data
			$googleAuthQRUrl = base64_encode($qrCodeData);

		}

		return $response
			->withHeader('Content-Type', 'application/json')
			->write(json_encode(["qr_url" => $googleAuthQRUrl]));

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-my-profile', 'trainee-edit-profile'], 'update'));


	$group->put('/', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::find(\APP\Auth::getUserId());

		$fields = [
			"fname", "lname", "email", "phone", "country_id",
			"company_id", "department_id", "location_id", "city_id", "school",
			"emergency_name", "emergency_relationship", "emergency_contact_numbers",
			"visa_length", "visa_number", "visa_date", "DateOfBirth", "Postcode", "accessible_ui"
		];

		foreach($fields as $field) {
			/*If UniqueEmailPerUser = False then make this field uneditable for a trainee (otherwise any user could change this and impersonate another user.)*/
			if ($field == 'email') {
				if (
					\APP\Auth::isLearner() &&
					!\APP\Tools::getConfig('uniqueEmailPerUser')
				) {
					continue;
				}
				// Disable email change if email exists
				if ($user->email) {
					continue;
				}

				// Disable email change is any other user has this email
				if (\Models\User::where('email', $data['email'])->count() > 0) {
					continue;
				}

				if (!\APP\Auth::permission('my_profile__edit_email')) {
					continue;
				}
			}

			if ($field == 'fname' && !\APP\Auth::permission('my_profile__edit_fname')) {
				continue;
			}

			if ($field == 'lname' && !\APP\Auth::permission('my_profile__edit_lname')) {
				continue;
			}

			if ($field == 'phone' && !\APP\Auth::permission('my_profile__edit_phone')) {
				continue;
			}

			if ($field == 'country_id' && !\APP\Auth::permission('my_profile__edit_country')) {
				continue;
			}

			if ($field == 'city_id' && !\APP\Auth::permission('my_profile__edit_city')) {
				continue;
			}

			if ($field == 'company_id' && !\APP\Auth::permission('my_profile__edit_company')) {
				continue;
			}

			if ($field == 'department_id' && !\APP\Auth::permission('my_profile__edit_department')) {
				continue;
			}

			if ($field == 'location_id' && !\APP\Auth::permission('my_profile__edit_location')) {
				continue;
			}

			if ($field == 'school' && !\APP\Auth::permission('my_profile__edit_school')) {
				continue;
			}

			if ($field == 'accessible_ui' && !\APP\Auth::permission('my_profile__edit_accessibility')) {
				continue;
			}

			// if user has no access to all companies, company field can not be updated.
			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::accessAllCompanies() &&
				$field == 'company_id'
			) {
				continue;
			}

			// Check role permissions if user role allows changing of department
			if (
				$field == 'department_id' &&
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::allowChangeDepartmentSelf()
			) {
				continue;
			}

			if (isset($data[$field])) {
				$user->$field = $data[$field];
			}
		}

		$user->save();

		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {
			$allowed_fields = [
				'Salutation', 'Gender__c', 'Health_Consent__c', 'Employment_Status__c', 'Contact_Source__c', 'Contact_Relationship__c', 'Year_of_Onset__c', 'SMILE_Relationship__c'
			];
			foreach ($data["extended"] as $field_name => $value) {
				if (in_array($field_name, $allowed_fields)) {
					\Models\TableExtension::updateField('users', $user->id, $field_name, $value);
				}
			}
		}

		if (
			$this->get('settings')["licensing"]['version'] == 'nras' &&
			$user->email
		) {
			$api_response = \APP\Api::salesforceUpdateRemoteUser($user);
			// $api_response will be returned only when exception is cought, give user some usable information what is going on.
			if ($api_response) {
				return
					$response
						->withStatus(400)
						->withHeader('Content-Type', 'text/html')
						->write($api_response)
				;
			}
		}


		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-my-profile', 'trainee-edit-profile'], 'update'));

	/**
	 * Route to access user other linked accounts
	 */
	$group->post('/linked-accounts', function (Request $request, Response $response, $args) {

		$user = \Models\User
			::with('role')
			->with('AccountType')
			->validuser()
			->find(\APP\Auth::getUserId())
		;

		$userEmails = \Models\User
			::select('username','id', 'account_type_id', 'fname', 'lname')
			->where('email', $user->email)
			->validuser()
			->with('AccountType')
			->get()
		;

		$data = ['userEmails' => $userEmails,'user' => $user];

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-my-profile', 'trainee-edit-profile'], 'select')); //TODO:: Check permission for accessing all accounts

});