<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

// checks if instance has mobile API enabled
$app->group("/checkmobileapi",  function ($group) {
	$group->get('', function (Request $request, Response $response, array $args) {
		$allowApi = \APP\Tools::getConfig('allowApi');
		if (!$allowApi) {
			$response = $response
					->withStatus(403)
					->write('403 Forbidden')
			;
		} else {
			$response = \APP\Tools::cors($response);
			$response = $response
					->write('1')
			;
		}



		return
			$response
				->withHeader('Content-Type', 'text/html')
		;
	});
});

$app->group("/mobile",  function ($group) {
	// Function to log in, returns getSessionId for use by mobile client.
	// identical code exists in app.php(321), not sure if anyone uses it
	$group->post('/login', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$response = \APP\Tools::cors($response);
		$response = \APP\Mobile::logIn($data["username"], $data["password"], $response);

		return
			$response
				->withHeader('Content-Type', 'text/html')
		;
	});

	// generates URL for learner to download SCORM archive
	$group->get('/download-scorm-url/{resource_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$allowApi = \APP\Tools::getConfig('allowApi');

		if (!$allowApi) {
			$response = $response
				->withStatus(403)
				->write('403 Forbidden')
			;
		} else {
			$response = \APP\Tools::cors($response);
			$user = \APP\Auth::getUser();
			$resource = \Models\LearningModule
				::where('type_id', 1)
				->where('id', $args['resource_id'])
				->where('status', true)
				->first()
			;
			if ($resource) {
				// Check if resource is assigned to user
				$assignment = \Models\UserLearningModule
					::where('user_id', $user->id)
					->where('learning_module_id', $resource->id)
					->first()
				;
				$rootPath = realpath($this->get('settings')["LMSScormDataPath"] . $resource->id . "/moddata/scorm/1");
			}
			if (
				$resource &&
				$assignment &&
				is_dir($rootPath)
			) {
				$zip = new ZipArchive();
				$filename = $this->get('settings')["LMSPrivateScormDownloadPath"] . \APP\Tools::safeName($resource->name, "_") . ".zip";

				if ($zip->open($filename, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
					exit("cannot create <$filename>\n");
				}

				// https://stackoverflow.com/questions/4914750/how-to-zip-a-whole-folder-using-php
				// Create recursive directory iterator
				$files = new RecursiveIteratorIterator(
					new RecursiveDirectoryIterator($rootPath),
					RecursiveIteratorIterator::LEAVES_ONLY
				);

				foreach ($files as $name => $file) {
				// Skip directories (they would be added automatically)
				if (!$file->isDir()) {
						// Get real and relative path for current file
						$filePath = $file->getRealPath();
						$relativePath = substr($filePath, strlen($rootPath) + 1);
						// Add current file to archive
						$zip->addFile($filePath, $relativePath);
					}
				}

				$zip->close();

				$download_resp = new \stdClass();
				$download_resp->url = $this->get('settings')["LMSUrl"] . 'mobile/download-scorm/' . \APP\Tools::safeName($resource->name, "_") . ".zip";

				$response->getBody()->write(json_encode($download_resp));
		return $response->withHeader('Content-Type', 'application/json');
			} else {
				$response = $response
					->withStatus(404)
					->write('404 Resource does not exist or is not e-learning, or has no SCORM data!')
				;
			}
		}
		return
			$response
				->withHeader('Content-Type', 'text/html')
		;
	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));

	// Downloads SCORM file and deletes it.
	$group->get("/download-scorm/{file}", function (Request $request, Response $response, array $args) {

		$allowApi = \APP\Tools::getConfig('allowApi');

		if (!$allowApi) {
			return $response
				->withStatus(403)
				->write('403 Forbidden')
			;
		}
		$response = \APP\Tools::cors($response);

		if (!is_file($this->get('settings')["LMSPrivateScormDownloadPath"] . $args["file"])) {
			return $response
				->withStatus(404)
				->write('404 Not Found')
			;
		}

		$file_content = file_get_contents($this->get('settings')["LMSPrivateScormDownloadPath"] . $args["file"]);
		unlink($this->get('settings')["LMSPrivateScormDownloadPath"] . $args["file"]);
		return $response
			->withHeader('Content-Type', 'application/zip')
			->withHeader('Content-Disposition','attachment;filename="' . $args["file"] . '"')
			->withHeader('Cache-Control','max-age=0')
			->withHeader('Cache-Control','max-age=1')
			->withHeader('Expires','Mon, 26 Jul 1997 05:00:00 GMT')
			->withHeader('Last-Modified',gmdate('D, d M Y H:i:s').' GMT')
			->withHeader('Cache-Control','cache, must-revalidate')
			->withHeader('Pragma','public')
			->write($file_content)
		;

	});

	# Logs out user and deletes session
	$group->get('/logout', function (Request $request, Response $response, array $args) {
		$allowApi = \APP\Tools::getConfig('allowApi');
		if (!$allowApi) {
			$response = $response
				->withStatus(403)
				->write('403 Forbidden')
			;
		} else {
			$response = \APP\Tools::cors($response);
			if (!isset($request->getHeaders()["HTTP_AUTHENTICATION"])) {
				//no authorization header
				$response = $response
					->withStatus(403)
					->write('401 No Header!')
				;
			} else {
				$session = \Models\Session
					::where('hash', $request->getHeaders()['HTTP_AUTHENTICATION'][0])
					->where('api', true)
					->first()
				;
				if ($session) {
					$_SESSION['LMSUserId'] = $session->user_id;
					$_SESSION['LMSSessionHash'] = $session->hash;
					\APP\Auth::logout();
				} else {
					$response = $response
						->withStatus(404)
						->write('404 Session not found!')
					;
				}
			}
		}

		return
			$response
				->withHeader('Content-Type', 'text/html')
		;
	});

	$group->post('/register', function (Request $request, Response $response, array $args) {
		$allowApi = \APP\Tools::getConfig('allowApi');
		if (!$allowApi) {
			$response = $response
				->withStatus(403)
				->write('403 Forbidden')
			;
		} else {
			$response = \APP\Tools::cors($response);
			$data = $request->getParsedBody();

			try {
				foreach([
					"password" => "password",
					"fname" => "first name",
					"lname" => "last name",
					"username" => "username",
					"email" => "email",
				] as $field_id => $field_name){
					if (empty($data[$field_id])) {
						throw new Exception("No {$field_name} specified");
					}
				}

				\APP\Autoregister
					::registerUser(
						$data["username"],
						$data["email"],
						$data["password"],
						$data["fname"],
						$data["lname"],
						"mobile"
					)
				;

			} catch(Exception $ex) {
				$response = $response
					->withStatus(500)
					->write("Unable to register user.")
					->write($ex->getMessage())
				;
			}

		}
		return
			$response
				->withHeader('Content-Type', 'text/html')
		;
	});

});