<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;


$app->group("/learners-interface",  function ($group) {

	// Retrieve background for learner's interface
	$group->get("/background[/{type}]", function (Request $request, Response $response, array $args) {
			$filename = $this->get('settings')['LMSLearnersInterfaceImagesPath'] . 'learners-background.jpg';

			$type = 'dark';
			if (isset($args['type'])) {
				$type = $args['type'];
			}

			if (\APP\Auth::isLearner()) {
				$color_scheme = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'color_scheme');
				if ($color_scheme) {
					$type = $color_scheme;
				}
			}

			if (
				$type &&
				is_file($this->get('settings')['LMSLearnersInterfaceImagesPath'] . 'learners-background_' . $type . '.jpg')
			) {
				$filename = $this->get('settings')['LMSLearnersInterfaceImagesPath'] . 'learners-background_' . $type . '.jpg';
			}
			// See if learner logged in from branded company landing page, check if there is background image meant for learner interface
			if (
				!empty($_SESSION["branded_company"]) &&
				\APP\Auth::isLearner()
			) {
				$company = \Models\Company::find($_SESSION["branded_company"]);
				if (
					$company &&
					$company->learner_bg
				) {
					if (is_file($this->get('settings')['CompanyLearnerBgPath'] . $company->learner_bg)) {
						$filename = $this->get('settings')['CompanyLearnerBgPath'] . $company->learner_bg;
					}
				}
			}

			// Check if default learner background exists.
			if (!is_file($filename)) {
				$filename = $this->get('settings')['LMSLearnersInterfaceImagesPathDefault'] . $this->get('settings')['licensing']['version'] . '_dark.jpg';
				if (
					$type &&
					is_file($this->get('settings')['LMSLearnersInterfaceImagesPathDefault'] . $this->get('settings')['licensing']['version'] . '_' . $type . '.jpg')
				) {
					$filename = $this->get('settings')['LMSLearnersInterfaceImagesPathDefault'] . $this->get('settings')['licensing']['version'] . '_' . $type . '.jpg';
				}
			}

            if (\APP\Auth::isLearner()) {

				// If user has company, check if that contains background image
				if(\APP\Auth::getUserCompanyId()) {
					$company = \Models\Company::find(\APP\Auth::getUserCompanyId());
					if (
						$company &&
						$company->learner_bg
					) {
						if (is_file($this->get('settings')['CompanyLearnerBgPath'] . $company->learner_bg)) {
							$filename = $this->get('settings')['CompanyLearnerBgPath'] . $company->learner_bg;
						}
					}


					$background_image = \Models\TableExtension::getValue('users', \APP\Auth::getUserId(), 'background_image');
					if ($background_image) {
						$filename = $this->get('settings')['LMSUsersImagesPath'] . $background_image;
					}
				}

            }

			if (is_file($filename)) {

				$imageInfo = getimagesize($filename);
				if ($imageInfo === false) {
					$response->getBody()->write('Invalid image file');
					return \APP\Tools::returnCode($request, $response, 500);
				}

				$mimeType = $imageInfo['mime'];
				$imageContent = file_get_contents($filename);

				$response->getBody()->write($imageContent);
				return $response->withHeader('Content-Type', $mimeType);
			} else  {
				return \APP\Tools::returnCode($request, $response, 404);
			}

		return
			$response
		;
	})->add(\APP\Auth::getSessionCheck());

	$group->delete("/background/{type:dark|light}", function (Request $request, Response $response, array $args) {
			$filename = $this->get('settings')['LMSLearnersInterfaceImagesPath'] . 'learners-background_' . $args['type'] . '.jpg';
			if (is_file($filename)) {
				unlink($filename);
			} else {
				return \APP\Tools::returnCode($request, $response, 404);
			}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-branding', 'disable'));

	// Update settings/background for learner's interface
	$group->post("", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (isset($_FILES['background_dark'])) {
			$background_file = $this->get('settings')['LMSLearnersInterfaceImagesPath'] . 'learners-background_dark.jpg';
			$storage = new \Upload\Storage\FileSystem($this->get('settings')['LMSLearnersInterfaceImagesPath'], true);
			$background = new \Upload\File('background_dark', $storage);
			$fileSizeValidation = new \Upload\Validation\Size(500000); // 500KB
			$background->setName('learners-background');
			$backgroundFileName = $background->getNameWithExtension();
			$fileTypeValidation = new \Upload\Validation\Mimetype(['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp']);
			$fileTypeValidation->setMessage("Invalid file type. You must upload a image file.");
			$background->addValidations([
				$fileTypeValidation,
				$fileSizeValidation
			]);

			try {
				$background->upload();
				// If uploaded image is not ".jpg", convert it to that format
				if ($backgroundFileName !== 'learners-background_dark.jpg') {
					$new_file = $this->get('settings')['LMSLearnersInterfaceImagesPath'] . $backgroundFileName;
					$convert = \APP\Tools::convertImageToJpg($new_file, $background_file);
					if (!$convert) {
						$response->getBody()->write('Uploaded image format is not JPG and cannot be converted to a JPG file!');
						return $response
							->withStatus(500)
							->withHeader('Content-Type', 'text/html');
					} else {
						if (is_file($new_file)) {
							unlink($new_file);
						}
					}
				}
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $background->getErrors();
				$response->getBody()->write(implode("\n", $errors));
				return $response
					->withStatus(500)
					->withHeader('Content-Type', 'text/html');

			}
		}


		if (isset($_FILES['background_light'])) {
			$background_file = $this->get('settings')['LMSLearnersInterfaceImagesPath'] . 'learners-background_light.jpg';
			$storage = new \Upload\Storage\FileSystem($this->get('settings')['LMSLearnersInterfaceImagesPath'], true);
			$background = new \Upload\File('background_light', $storage);
			$fileSizeValidation = new \Upload\Validation\Size(500000); // 500KB
			$background->setName('learners-background');
			$backgroundFileName = $background->getNameWithExtension();
			$fileTypeValidation = new \Upload\Validation\Mimetype(['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp']);
			$fileTypeValidation->setMessage("Invalid file type. You must upload a image file.");
			$background->addValidations([
				$fileTypeValidation,
				$fileSizeValidation
			]);

			try {
				$background->upload();
				// If uploaded image is not ".jpg", convert it to that format
				if ($backgroundFileName !== 'learners-background_light.jpg') {
					$new_file = $this->get('settings')['LMSLearnersInterfaceImagesPath'] . $backgroundFileName;
					$convert = \APP\Tools::convertImageToJpg($new_file, $background_file);
					if (!$convert) {
						return
							$response->withStatus(500)
								->withHeader('Content-Type', 'text/html')
								->write('Uploaded image format is not JPG and can not be converted to JPG file!')
						;
					} else {
						if (is_file($new_file)) {
							unlink($new_file);
						}
					}
				}
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $background->getErrors();
				return
					$response->withStatus(500)
						->withHeader('Content-Type', 'text/html')
						->write(implode("\n", $errors))
				;
			}
		}


		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-branding', 'insert'));

});
