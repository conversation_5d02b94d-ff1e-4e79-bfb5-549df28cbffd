<?php

use APP\Auth;
use Carbon\Carbon;
use Models\ManagerUser;
use Models\User;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\LearningResult;
use Models\Role;
use Models\UserFormSignoff;

$app->group("/mylearning",  function ($group) {

	$group->get('/moodle/update/{learning_result_id:[0-9]+}/{moodle_course_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$user = \APP\Auth::getUser();
		$learning_result_id = trim($args["learning_result_id"]);
		$moodle_course_id = trim($args["moodle_course_id"]);
		$moodle_user_id = 2;

		if ($moodle_status = file_get_contents("https://openelms.e-learningwmb.co.uk/moodle/webservice/rest/server.php?wstoken=b6385a0e81c0415769acd67c3468b4c8&wsfunction=core_completion_get_course_completion_status&moodlewsrestformat=json&courseid=" . $moodle_course_id ."&userid=".$moodle_user_id))
		{
			$moodle_status = json_decode($moodle_status);

			$lr = \Models\LearningResult::find($learning_result_id);

			$dt_completed = false;
			foreach($moodle_status->completionstatus->completions as $completion)
			{
				if ($completion->complete)
				{
					$dt_completed = $completion->timecompleted;
				}
			}
			if ($dt_completed)
			{
				$lr->completion_status =  "completed";
				$lr->completed_at = \Carbon\Carbon::createFromTimestamp($dt_completed);
			} else {
				$lr->completion_status = "in progress";
				$lr->completed_at = \Carbon\Carbon::now();

			}
			$lr->save();
		}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get('/certificate/{learning_result_id:[0-9]+}[/{schedule_id:[0-9]+}]', function (Request $request, Response $response, array $args) {

		$vars = \APP\Templates::getVariables($this->get('settings'));
		$user = \APP\Auth::getUser();

		$learning_result = \Models\LearningResult
			::find($args["learning_result_id"])
		;
		$LearningName = $learning_result->module->name;

		$userCertification = $learning_result->userCertification;

		// If event is specified, retrieve its link to current user!
		$event_link = false;
		if (isset($args["schedule_id"])) {
			$event_link = \Models\ScheduleLink
				::where('type', 'users')
				->where('schedule_id', $args["schedule_id"])
				->where('link_id', $user->id)
				->with('schedule')
				->first()
			;

			if ($event_link) {
				$LearningName = $event_link->schedule->name;
			}

			$userCertification = $event_link->userCertification;
		}

		if (
			(
				$learning_result->completion_status != "completed" &&
				!$event_link
			) ||
			(
				$event_link &&
				$event_link->completion_status != "Completed" &&
				$event_link->completion_status != "%%event_completion_state_completed%%" &&
				$event_link->completion_status != \APP\Templates::translate('%%event_completion_state_completed%%')
			)
		) {
			return
				$response
					->withStatus(403)
					->getBody()
					->write("access denied");
			;
		}
		if (!empty($learning_result->module->accreditation_main_logo)) {
			$vars["Logo"] = $this->get('settings')["LMSUrl"] . '/images/accreditation/' . $learning_result->module->accreditation_main_logo;
		} elseif (empty($learning_result->user->company->logo) || $this->get('settings')["CertificateAlwaysUseDefaultLogo"]) {
			$vars["Logo"] = $this->get('settings')["DefaultLogo"];
		} else {
			$vars["Logo"] = $this->get('settings')["CompanyLogosPath"] . $learning_result->user->company->logo;
		}

		// Custom Accreditation fields
		if (!empty($learning_result->module->accreditation_logo)) {
			$vars["AccreditationLogo"] = $this->get('settings')["LMSUrl"] . '/images/accreditation/' . $learning_result->module->accreditation_logo;
		}

		$vars["AccreditationDescription"] = $learning_result->module->accreditation_description;
		if (
			$learning_result->module->accreditation_alternative_learning_name &&
			$learning_result->module->accreditation_alternative_learning_name > ''
		) {
			$LearningName = $learning_result->module->accreditation_alternative_learning_name;
		}

		$vars["LearningName"] = $LearningName;

		$vars["Score"] = $learning_result->score;

		$vars["CompletionDate"] = \Carbon\Carbon::parse($learning_result->completed_at)->format(\APP\Tools::getConfig('defaultDateFormat'));

		// Event com,pletion dater overrrides learning result completion date
		if (
			$event_link &&
			$event_link->completed_at
		) {
			$vars["CompletionDate"] = \Carbon\Carbon::parse($event_link->completed_at)->format(\APP\Tools::getConfig('defaultDateFormat'));
		}

		$vars["LMSTitle"] = \APP\Tools::getConfig('LMSTitle') ? \APP\Tools::getConfig('LMSTitle') : $this->get('settings')["LMSTitle"];
		$vars["DefaultCertificateMessageTop"] = \APP\Tools::getConfig('CertificateMessageTop');
		$vars["DefaultCertificateMessageBottom1"] = \APP\Tools::getConfig('CertificateMessageBottom1');
		$vars["DefaultCertificateMessageBottom2"] = \APP\Tools::getConfig('CertificateMessageBottom2');

		$vars["UserFname"]  = $learning_result->user->fname;
		$vars["UserLname"]  = $learning_result->user->lname;
		$vars["UserDepartmentName"]  = $learning_result->user->department ? $learning_result->user->department->name : '';

		if ($learning_result->user->company && $learning_result->user->company->name) {
			$vars["UserCompanyName"]  = $learning_result->user->company->name;
		}

		// getting certificate template from DB
		if(empty($userCertification)) {
			// create an entry to the table
			if($event_link) {
				\Models\ScheduleLink::issueCertificate($event_link);
				$event_link = $event_link->refresh();
				$userCertification = $event_link->userCertification;
			} else {
				if(empty($learning_result->userCertification)) {
					LearningResult::issueCertificate($learning_result);
					$learning_result = $learning_result->refresh();
					$userCertification = $learning_result->userCertification;
				}
			}
		}

		$certTemplate = $userCertification->Certificate->template;

		$vars['certificate__html'] = \APP\Tools::replaceVars($certTemplate, $vars);

		if(!empty($vars['UserCompanyName'])) {
			$replace = "<span class='print_big' id='user-company-name'>".$vars['UserCompanyName']."</span>";
			$vars['certificate__html'] = str_replace('<span class="print_big" id="user-company-name"></span>', $replace, $vars['certificate__html']);
		}

		if (!empty($vars['Score'])) {
			$replace = "<span class='print_medium'><BR><BR><span>". $vars['certificate__has_achieved'] ."&nbsp;</span><span class='print_big'>". $vars['Score'] .' '. $vars['certificate__percent'] ."</span></span>";
			$vars['certificate__html'] = str_replace('<span class="print_medium" id="user-grade-block"></span>', $replace, $vars['certificate__html']);
		}

		if (!empty($vars['AccreditationLogo'])) {
			$replace = "<img src=".$vars['AccreditationLogo']." style='max-width: 100%; max-height: 300px;'>";
			$vars['certificate__html'] = str_replace('<span id="accreditation-logo"></span>', $replace, $vars['certificate__html']);
		}

		return $this->get('view')->render($response, 'html/certificate_new.html', $vars);

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get('/launchvideo/{id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$module = \Models\LearningModule::findOrFail($args["id"]);

		$module->prerequisites = \APP\Learning::augmentPrerequisitesLearningResult(
				$user->id,
				$module->prerequisites
			);

		$error = false;

		if ($module->do_prerequisite)
		{
			foreach($module->prerequisites as $prerequisite)
			{
				if (
						!$prerequisite->learning_result
						||
						$prerequisite->learning_result->completion_status != "completed"
						)
				{
					$error = true;
					break;
				}
			}
		}

		$required_modules = \APP\Learning::getCourseRequiredModules($user->id, $module);
		foreach($required_modules as $required_module)
		{
			if (
					!$required_module->learning_result
					||
					$required_module->learning_result->completion_status != "completed"
					)
			{
				$error = true;
				break;
			}
		}

		if ($error)
		{
			$data = ["error" => true];
		}
		else
		{
			$module->user = $user;


			if (
				$module->type->slug == 'youtube' &&
				isset($module->material->link)
			) {
				$module->video_id = \APP\Tools::getVideoId($module->material->link, "youtube");
				$module->video_type = "youtube";
			}

			if ($module->type->slug == 'vimeo')
			{
				$module->video_id = \APP\Tools::getVideoId($module->material->link, "vimeo");
				$module->video_type = "vimeo";
			}

			$data = $module;
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->post('/scheduleapproval{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$params['nPage'] = 10;

		$QueryBuilder = "\\APP\\QueryBuilder\\scheduleapprovalList";
		$query = $QueryBuilder::generate($params, $args);

		switch($args["option"]) {
			case "/download":
				$query
					->selectRaw("CONCAT(users.fname, ' ', users.lname) as trainee_name")
					->selectRaw("IF(approved = 0, 'Not Approved', 'Approved') as approval_status")
					->selectRaw("CASE
									WHEN is_paid IS NULL THEN 'N/A'
									WHEN is_paid = '0' THEN 'Not Paid'
									WHEN is_paid = '1' AND (user_payment_transactions.payment_total = 0 AND user_payment_transactions.item_cost > 0) THEN 'N/A'
									WHEN is_paid = '1' THEN 'Paid'
									ELSE 'Unknown'
									END as payment_status")
					->selectRaw("CASE
									WHEN schedule_links.type = 'users_queue' THEN 'Waiting List'
									WHEN schedule_links.type = 'users' THEN 'Booking'
									END as type_status")
					->selectRaw("DATE_FORMAT(start_date, '" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') as formatted_start_date")
				;

				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				$export_fields = [
					"Name" => "trainee_name",
					"Managers" => "user.managers",
					"Event" => "name",
					"Type" => "type_status",
					"Venue" => "venue_name",
					"Date/Time" => "formatted_start_date",
					"Status" => "completion_status",
					"Approval Status" => "approval_status",
					"Payment Status" => "payment_status"
				];

				$download_file_name = uniqid("Events_and_Manage_Booking.report.") . ".xlsx";
				\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				);

				$response
					->getBody()
					->write(json_encode($download_file_name))
				;

				return
					$response
						->withHeader('Content-Type', 'application/json')
					;
				break;
		}

        if(isset($params['search']) && isset($params['search']['action'])) {
            unset($params['search']['action']);
        }
		$data = \APP\SmartTable::searchPaginate($params, $query, false);

		foreach ($data  as $key => $value) {
			if ($value->type == 'users_queue') {
				$value->type = 'Waiting List';
			}
			if ($value->type == 'users') {
				$value->type = 'Booking';
			}
		}



		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->put('/scheduleapproval', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$insert["type"] = $data['type'];
		$schedulelink = \Models\ScheduleLink
			::where('id', $data['id'])
			->first()
		;

		$getModuleDetails = \Models\ScheduleLink
			::where('schedule_id', $schedulelink["schedule_id"])
			->where('type', "lesson")
			->first()
		;

		/** For email Template */
		$event = \Models\Schedule::find($schedulelink["schedule_id"]);
		$count_of_assigned_users = \Models\ScheduleLink
			::where('type','users')
			->where('schedule_id', $schedulelink["schedule_id"])
			->count()
		;

		$learner_details = \Models\User::find($schedulelink->link_id);
		/** For email Template */

		$schedulelink->approved = 1;
		$schedulelink->is_paid = $data["is_paid"];
		$schedulelink->cron_task = true;

		//Approved - It changes user_queue to users
		//Doubt what about user limit
		$updated_type = false; // Another workaround
		if (
			(
				$event->cost == null ||
				(
					$event->cost != NULL &&
		  $data["is_paid"] !== NULL &&
		  $data["is_paid"] !== 0
				)
			) &&
			(
				!$event->enrole_any_learner ||
				(
					$event->enrole_any_learner &&
					$event->maxclass == 0
				) ||
				(
					$event->enrole_any_learner &&
					$count_of_assigned_users < $event->maxclass
				)
			)
		) {
			$schedulelink->type = 'users';
			$updated_type = true;
		}
		$schedulelink->save();

		// Set even to be processed and send out outlook invites.
		$event->cron_task = true;
		$event->save();

		// Make sure event is linked to user!
		\Models\Schedule::processEvents(null, [$schedulelink->schedule_id], [$schedulelink->link_id]);
		$event->cron_task = true;
		$event->update_outlook = "user";
		$event->save();

		if ($event->cost == null ||  $schedulelink->is_paid=='1') {

			// This whole logic here ignores previously set type, two different scripts fighting for setting type.
			$schedule = \Models\Schedule
				::where('id', $schedulelink["schedule_id"])
				->with(['Schedules' => function ($query) {
					$query = $query
						->with(['Users' => function ($query) {
							$query = $query
								->select(
									'users.id'
								)
								->where('users.status', true)
							;
						}])
					;
				}])
				->with(['users' => function ($query) {
					$query = $query
						->select(
							'users.id'
						)
						->where('users.status', true)
					;
				}])
				->first()
			;

			if (isset($schedule->users)) {
				if ($schedule->maxclass > 0) {
					if (
						$schedule->maxclass <= count($schedule->users) ||
						$schedule->cost != NULL
					) {
						if (!$updated_type) {
							$insert["type"] = 'users_queue';
						}
					}
				}
			}

			if (
				$event->cost == null ||
				\APP\Auth::isAdminInterface()
			) {
				$ids = \Models\Schedule::getChild($schedulelink["schedule_id"]);
				$schedules = \Models\Schedule
					::whereIn("id", $ids)
					->with(['users' => function ($query) {
						$query = $query
							->select(
								'users.id'
							)
							->where('users.status', true)
						;
					}])
					->get()
				;
				$waitingList = 0;

				foreach ($schedules as $key => $linked_schedule) {
					if ($linked_schedule->id != $schedule->id) {
						$insert["link_id"] = $schedulelink->link_id;
						$insert["schedule_id"] = $linked_schedule->id;

						$linked_schedule_user_link = \Models\ScheduleLink
							::where('link_id', $schedulelink->link_id)
							->where('schedule_id', $linked_schedule->id)
							->whereIn('type', ['users', 'users_queue'])
							->first()
						;

						if (isset($linked_schedule->users)) {
							if ($linked_schedule->maxclass > 0) {
								if ($linked_schedule->maxclass <= count($linked_schedule->users)) {
									$insert["type"] = 'users_queue';
									$waitingList = 1;
								}
							}
						}
						if ($linked_schedule->cost > 0) {
							$insert["is_paid"] = '0';
						}

						$insert["approval"] = 0;
						if (
							(
								$linked_schedule_user_link &&
								$linked_schedule_user_link->approved != 1
							) ||
							!$linked_schedule_user_link
						) {
							$insert["approval"] = $linked_schedule->approval;
			}
			if($schedulelink->learner_requirement){
			  $insert['learner_requirement'] = $schedulelink->learner_requirement;
			}

						\Models\ScheduleLink::addNewLink($insert);
			$linked_schedule->cron_task = true;
			$linked_schedule->update_outlook = "user";
						$linked_schedule->save();
					}
				}
			}
		}

		//manager_payment_status -> is used for identify manager select payment directly or not for learners

		if($event->approval=="1" && $schedulelink->type == 'users')
		{
			// Send e-mail to Manager that status changes to approval
			$template = \Models\EmailTemplate
			::where('name', 'Event Approval')
			->where('status', true)
			->first();
			if ($template && $template->id) {
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$learner_details->id];
				$email_queue->from = \APP\Auth::getUserId();
				$email_queue->custom_variables = json_encode([
					'USER_FNAME' => $learner_details->fname,
					'EVENT_NAME'=>$event->name,
					'EVENT_DESCRIPTION'=>$event->description,
					'EVENT_LOCATION'=>$event->location,
					'EVENT_DATE' => \Carbon\Carbon::parse($event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat') . " H:i"),
					'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
					'EVENT_APPROVAL_LINK'=>'app/learner/resources/'.$getModuleDetails->link_id.'-'.$event->id,
					'REGARDS'=>$GLOBALS["CONFIG"]->Regards,
				]);
				$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($event,$email_queue->custom_variables);
				$email_queue->save();
			}
		}

		if($event->approval=="1" && $schedulelink["type"] == 'users_queue')
		{
			// Send e-mail to Manager that status changes to approval
			$template = \Models\EmailTemplate
			::where('name', 'Event Waiting List')
			->where('status', true)
			->first();
			if ($template && $template->id) {
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$learner_details->id];
				$email_queue->from = \APP\Auth::getUserId();
				$email_queue->custom_variables = json_encode([
					'USER_FNAME' => $learner_details->fname,
					'EVENT_NAME'=>$event->name,
					'EVENT_LOCATION'=>$event->location,
					'EVENT_DATE'=> \Carbon\Carbon::parse($event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat') . " H:i"),
					'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
					'EVENT_APPROVAL_LINK'=>'app/learner/resources/'.$getModuleDetails->link_id.'-'.$event->id,
					'REGARDS'=>$GLOBALS["CONFIG"]->Regards,
				]);
				$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($event,$email_queue->custom_variables);
				$email_queue->save();
			}
		}

		$response->getBody()->write(json_encode($schedulelink));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get('/launchscorm/{id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$data = \APP\Learning::launchScorm($args['id']);

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get("/waitingforapproval", function (Request $request, Response $response) {
		$waiting_for_approval = 0;
		if (\APP\Auth::isManager())
		{
			$waiting_for_approval = \Models\LearningResult
				::whereIn("user_id", function($query) {
					$query
						->select("user_id")
						->from("manager_users")
						->where("manager_id", "=", \APP\Auth::getUserId())
						->whereNull('manager_users.deleted_at')
					;
				})
				->where(function($query){
					$query
						->whereIn("learning_results.learning_module_id", function($query) {
							$query
								->select("learning_module_id")
								->from("learning_sessions")
								->whereRaw("learning_sessions.user_id = learning_results.user_id")
								->where("learning_sessions.completed", "=", 0)
								->where("learning_sessions.approved", "=", 0)
						;
						})
						->orWhere("learning_results.approved", "=", 0)
					;
				})
				->count()
			;
		}

		$response->getBody()->write(json_encode($waiting_for_approval));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));

	$group->get("/elearnings{module_id:[\/0-9]*}", function (Request $request, Response $response, array $args) {
		$query = \Models\LearningResult
			::with("Module")
			->where("user_id", "=", \APP\Auth::getUserId())
			->where("refreshed", "=", 0)
			->join("learning_modules", function($join) {
				$join
					->on("learning_results.learning_module_id", "=", "learning_modules.id")
					->where("type_id", "=", 1)
				;
			})
		;
		if (isset($args["module_id"]) && !empty($args["module_id"]))
		{
			$module_id = str_replace('/', "", $args["module_id"]);
			$query->where("learning_module_id", "=", $module_id);
		}

		$modules = [];
		foreach($query->get() as $module)
		{
			$modules[] = $module;
		}

		$response->getBody()->write(json_encode($modules));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


	$group->post("/elearnings/{action}/{module_id}", function (Request $request, Response $response, array $args) {

		$data = $request->getParsedBody();

		$learning_result = \Models\LearningResult
			::where("learning_module_id", '=', $args["module_id"])
			->where("user_id", "=", \APP\Auth::getUserId())
			->where("refreshed", "=", 0)
			->whereIn("learning_module_id", function($query){
				$query
					->select("id")
					->from("learning_modules")
					->where("type_id", "=", 1)
				;
			})
			->firstOrFail()
		;

		if ($args["action"] == "complete")
		{
			if (!isset($data["score"])) {
				if (!empty($learning_result->module->material->min_passing_percentage)) {
					$data["score"] = $learning_result->module->material->min_passing_percentage;
				} else {
					$data["score"] = 100;
				}
			}
			$new_status = "completed";
		}
		else
		{
			$data["score"] = null;
			$new_status = "in progress";
		}

		if (isset($data["completed_at"])) {
			// if completed_at is passed, but is empty, nullify completed date, in case status is changed from complete to something else.
			if ($data["completed_at"] != null) {
				$data["completed_at"] = \Carbon\Carbon::parse($data["completed_at"]);
			} else {
				$data["completed_at"] = null;
			}
		} else {
			$data["completed_at"] = \Carbon\Carbon::now();
		}

		$course = \APP\Course::get($learning_result->learning_module_id);
		$course->updateUserScormRecord(
			\APP\Auth::getUserId(),
			$new_status,
			$learning_result->completion_status,
			$data["score"],
			$data["completed_at"],
			False
		);
		$course->updateUserResult(\APP\Auth::getUserId(), $this->get('settings')['licensing']['isApprentix']);

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	// Get count of each individual actions used in manage learning page, to be displayed on each action button.
	$group->get("/action-count", function (Request $request, Response $response,$args) {


		$isMeetings = \APP\Tools::getConfig('isMeetings');
		$isManageBookings = \APP\Tools::getConfig('isManageBookings');

		session_write_close();

		$retrieveAllData = false;
		if(\APP\Auth::isAdmin()) {
			$retrieveAllData = true;
		}

		// Get approval count
		$query = \Models\LearningResult
			::select('learning_results.*')
			->where("refreshed", false)
			->whereExists(function ($query) {
				$query->select(DB::raw(1))
					->from('user_learning_modules')
					->whereRaw('user_learning_modules.user_id = learning_results.user_id')
					->whereRaw('user_learning_modules.learning_module_id = learning_results.learning_module_id')
					->whereRaw('user_learning_modules.deleted_at is null')
				;
			})
			->join("users", function($join) {
				$join
					->on("learning_results.user_id", "=", "users.id")
					->where("users.status", "=", 1)
				;
				if (
					!\APP\Auth::accessAllCompanies() &&
					!\APP\Auth::accessAllLearners()
				) {
					$join->where("users.company_id", "=", \APP\Auth::getUser()->company_id);
				}
			})
			->join("learning_modules", function($join) {
				$join
					->on("learning_results.learning_module_id", "=", "learning_modules.id")
					->where("learning_modules.status", "=", 1)
				;
				if (\APP\Auth::isAdminInterface()) {
					$join = $join
						->where('learning_modules.track_progress', true)
					;
				}
			})
		;

		if(!$retrieveAllData) {
			$query = $query
				->whereHas("Module", function($query) {
					$query = \Models\ManagerLearningModuleCategory::checkManagerAccessToCategories($query);
				})
			;
		}

		if (!\APP\Auth::accessAllLearners()) {
			if (
				\APP\Auth::isManager() ||
				\APP\Auth::isAdmin()
			) {
				$query->where(function($query) {
					$query
						->whereIn(
							"learning_results.user_id",
							\Models\ManagerUser
								::where('manager_id', \APP\Auth::getUserId())
								->select('user_id')
								->get()
						)
						->orWhereIn(
							"learning_results.learning_module_id",
							\Models\ManagerLearningModule
								::select('manager_learning_modules.learning_module_id')
								->where('manager_learning_modules.manager_id', \APP\Auth::getUserId())
								->get()
						)
						->orWhere("learning_results.processing_manager_id", '=', \APP\Auth::getUserId())
					;
				});
			}
		}


		$approve = clone $query;
		$approve = $approve
			->where("learning_results.approved", "=", false)
		;

		if ($isMeetings) {
			$meetings = clone $query;
			$meetings = $meetings
				->join("learning_module_evidence_meetings", function($join) {
					$join
						->on("learning_module_evidence_meetings.learning_modules_id", "=", "learning_results.learning_module_id")
						->on("learning_module_evidence_meetings.user_id", "=", "learning_results.user_id")
					;
				})
				->whereNotNull("learning_module_evidence_meetings.manager_accepted")
				->where('learning_module_evidence_meetings.manager_accepted', '=', 0)
			;
		}



		if ($isManageBookings) {
			$bookings = clone $query;
			$bookings
				->whereIn("learning_modules.type_id", [4, 6])
				->leftjoin("learning_sessions", function($join) {
					$join
						->on("learning_sessions.learning_module_id", "=", "learning_results.learning_module_id")
						->on("learning_sessions.user_id", "=", "learning_results.user_id")
						->where("learning_sessions.completed", "=", 0)
						->where("learning_sessions.approved", "=", 0)
					;
				})
			;
			if (\APP\Auth::isManager()) 	{
				if(!$retrieveAllData) {
					$bookings
						->where(function ($query) {
							$query
								->where("learning_results.user_id", "<>", \APP\Auth::getUserId())
								->orWhere(function($query) {
									$query
										->where("learning_results.user_id", "=", \APP\Auth::getUserId())
										->whereNotNull("learning_sessions.id")
									;
								})
							;
						})
						->where('learning_results.due_at', '>=', \Carbon\Carbon::now())
						->where('learning_results.completion_status', 'not attempted')
					;
				} else {
					$bookings->where('learning_results.due_at', '>=', \Carbon\Carbon::now())
						->where('learning_results.completion_status', 'not attempted');
				}
			} else {
				$bookings
					->where("learning_results.user_id", "=", \APP\Auth::getUserId())
					->whereNotNull("learning_sessions.id")
				;
			}
		}

		$assesments = clone $query;
		$assesments
			->join("assessment_data", function($join) {
				$join
					->on("assessment_data.user_id", "=", "learning_results.user_id")
					->on("assessment_data.course_id", "=", "learning_results.learning_module_id")
					->where("assessment_data.status", "<>", 4)
					->whereNotNull("assessment_data.submitted_at")
				;
			})
			->where('learning_modules.is_skillscan', false)
		;


		$signOffs = clone $query;
		if (
			\APP\Auth::isManager() ||
			\APP\Auth::isAdmin()
		) {
			$signOffs
				->where(function ($query) {
					$query
						->where(function($query) {
							$query
								->where('sign_off_trainee', '=', 1)
								->where('learning_results.sign_off_manager', '=', 0)
							;
						})
					;
					// if SMCR, list all uploads from hidden categories by default
					if ($GLOBALS["CONFIG"]->licensing['isSMCR']) {
						$query = $query
							->orWhere(function($query) {
								$query
									->whereIn("learning_results.learning_module_id",
										\Models\LearningModule
											::select('id')
											->whereNotNull('f_p_category_id')
											->whereIn('f_p_category_id',
												\Models\SmcrFPCategory
													::select('id')
													->where('status_learner', false)
													->get()
											)
											->where('type_id', 7)
											->get()
									)
									->where('learning_results.sign_off_manager', false)
								;
							})
						;
					}

				});
			;
		}

		if (
			!\APP\Auth::isAdmin() &&
			\APP\Auth::isManager()
		) {

			$assigned_users = \Models\ManagerUser
				::where('manager_id', \APP\Auth::getUserId())
				->select('user_id')
				->get()
			;


			$signOffs
				->where(function($query) use ($assigned_users) {
					$query
						->where(function($querylr) use ($assigned_users) {
							$querylr
								->where(function($query)use($assigned_users) {
									$query
										->whereIn("learning_results.user_id", $assigned_users)
										->orWhereIn("learning_results.learning_module_id",
											\Models\ManagerLearningModule
												::select('manager_learning_modules.learning_module_id')
												->where('manager_learning_modules.manager_id', \APP\Auth::getUserId())
												->get()
										)
									;
								})
								->where("learning_results.processing_manager_id","=",NULL)
							;
						})
						->orWhere(function($query) {
							$query
								->where("learning_results.processing_manager_id","!=",NULL)
								->whereRaw("learning_results.processing_manager_id=".\APP\Auth::getUserId())
							;
						})
					;
				})
			;
		}

		$schedules = \Models\ScheduleLink
			::with('user')
			->with('schedule')
			->where('schedule_links.approved', 0)
			->where('schedule_links.status', true)
			->where(function($query) {
				$query
					->where('schedule_links.type', 'users')
					->orWhere('schedule_links.type', 'users_queue')
				;
			})
			->whereHas("Schedule", function($query) {
				$query
					->where('status', true)
					->where('start_date', '>', \Carbon\Carbon::now()->startOfDay())
				;
			})
			->whereHas("User", function($query) {
				$query
					->where('status', 1)
				;
			})
		;

		if (!\APP\Auth::accessAllLearners()) {
			$schedules = $schedules
				->whereIn('schedule_links.link_id',
					\Models\ManagerUser
						::select('user_id')
						->where('manager_id', \APP\Auth::getUserId())
						->get()
				)
			;
		}
		$schedules = $schedules
			->get()
		;



		$actionObject = new \stdClass;
		if (
			!\APP\Auth::isCd()
		) {
			$actionObject->apprentices = 1;
			$actionObject->learning = 2;


			if ($isMeetings) {
				$actionObject->meetings = count($meetings->get());
			}

			if ($isManageBookings) {
				$actionObject->bookings = count($bookings->get());
				$actionObject->approve = count($approve->get());
				$actionObject->approveManageBookings = $actionObject->approve + count($schedules);
			}

			$actionObject->assesments = count($assesments->get());

			$actionObject->signOffs = count($signOffs->get());


		}


		if (
			$this->get('settings')['licensing']['isApprentix']
		) {
			if (\APP\Auth::isManager()) {
				$reviews = \Models\ManagerReview
					::select(
						DB::raw("null as learning_results_id"),
						'users.id as users_id',
						DB::raw("'QA Report' as learning_name"),
						DB::raw("null as learning_modules_id"),
						'manager_reviews.id as review_id',
						DB::raw("'Report' as qa_type"),
						DB::raw("null as due_at_uk"),
						'manager_reviews.completion_status as completion_status'
					)
					->where('manager_reviews.visit_type', 'QA Report')
					->where('manager_reviews.completion_status', 'Not Started')
					->where('manager_reviews.status', true)
					->join("users", function($join) {
						$join
							->on("manager_reviews.user_id", "=", "users.id")
							->where("users.status", true)
						;
						if (!\APP\Auth::accessAllCompanies() && !\APP\Auth::accessAllLearners()) {
							$join->where("users.company_id", "=", \APP\Auth::getUser()->company_id);
						}
					})
				;
				if (!\APP\Auth::accessAllLearners()) {
					$reviews->whereIn(
						"manager_reviews.user_id",
						\Models\ManagerUser::where('manager_id', '=', \APP\Auth::getUserId())
							->select('user_id')
							->get()
						)
					;
				}


				$results = \Models\LearningResult::select(
						'learning_results.id as learning_results_id',
						'users.id as users_id',
						'learning_modules.name as learning_name',
						'learning_modules.id as learning_modules_id',
						DB::raw("null as review_id"),
						DB::raw("'Learning' as qa_type"),
						DB::raw("DATE_FORMAT(learning_results.due_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS due_at_uk"),
						'learning_results.completion_status as completion_status'
					)
					->where('learning_results.qa', 'Rejected')
					->join("learning_modules", function($join) {
						$join
							->on("learning_results.learning_module_id", "=", "learning_modules.id")
							->where("learning_modules.status", true)
						;
					})
					->where('learning_results.refreshed', 0)
					->whereExists(function ($query) {
						$query->select(DB::raw(1))
							->from('user_learning_modules')
							->whereRaw('user_learning_modules.user_id = learning_results.user_id')
							->whereRaw('user_learning_modules.learning_module_id = learning_results.learning_module_id')
							->whereRaw('user_learning_modules.deleted_at is null')
						;
					})
					->join("users", function($join) {
						$join
							->on("learning_results.user_id", "=", "users.id")
							->where("users.status", true)
						;
						if (!\APP\Auth::accessAllCompanies() && !\APP\Auth::accessAllLearners()) {
							$join->where("users.company_id", "=", \APP\Auth::getUser()->company_id);
						}
					})
				;



				if (!\APP\Auth::accessAllLearners()) {
					$results->whereIn(
						"learning_results.user_id",
						\Models\ManagerUser
							::where('manager_id', '=', \APP\Auth::getUserId())
							->select('user_id')
							->get()
						)
					;
				}

				$results = $results
					->union($reviews)
				;

				$actionObject->qa = count($results->get()) + \Models\QualityControlReportView::where('quality_controls_report_view.qa','=','Rejected')
						->whereIn('quality_controls_report_view.user_id',
						\Models\ManagerUser
							::select('user_id')
							->where('manager_id', \APP\Auth::getUserId())
							->get()
					)->count();

			}
		}




		// Get count of smcr_functions_responsibilities that are not linked
		if ($this->get('settings')['licensing']['isSMCR']) {
			$smcr_query = \Models\SmcrFunctionResponsibility::where('status', true)
				->whereDoesntHave('staff')
			;

			$smcr_query_smf = clone $smcr_query;
			$smcr_query_smf
				->where('type', 'function')
				->where('smcr_staff_type_id', 3)
			;

			$smcr_query_smr = clone $smcr_query;
			$smcr_query_smr
				->where('type', 'responsibility')
				->where('smcr_staff_type_id', 3)
			;

			$smcr_query_csf = clone $smcr_query;
			$smcr_query_csf
				->where('type', 'function')
				->where('smcr_staff_type_id', 2)
			;

			$actionObject->smcr = [
				'smf' => $smcr_query_smf->count(),
				'smr' => $smcr_query_smr->count(),
				'csf' => $smcr_query_csf->count(),
			];
		}

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isCd()
		) {
			$unassignedStandardUsers = \Models\User::whereHas('role', function ($query) {
					$query
						->where('is_admin', false)
						->where('is_manager', false)
						->where('is_demo', false)
						->where('access_all_companies', false)
						->where('access_all_learners', false)
						->where('status', true)
					;
				})
				->where('status', true)
				->whereDoesntHave('Standards')
				->count()
			;
			$actionObject->unassignedStandardUsers = $unassignedStandardUsers;

			$unassignedUsers = \Models\User::whereHas('role', function ($query) {
					$query
						->where('is_admin', false)
						->where('is_manager', false)
						->where('is_demo', false)
						->where('access_all_companies', false)
						->where('access_all_learners', false)
						->where('status', true)
					;
				})
				->where('status', true)
				->whereDoesntHave('managers')
				->count()
			;
			$actionObject->unassignedUsers = $unassignedUsers;
		}
		$user = Auth::getUser();
		$role_id = $user->shadow_role_id==null?$user->role_id:$user->shadow_role_id;
		$isAccessAllLearners = Role::find($role_id)->access_all_learners;
		$matchedUserFormIds = [];
		if($isAccessAllLearners){
			$userFormIds = DB::table('forms')
						->select('user_forms.id', 'forms.has_sign_off_order')
						->join('form_signoff_roles', 'form_signoff_roles.form_id', '=', 'forms.id')
						->join('user_forms', 'user_forms.form_id', '=', 'forms.id')
						->where('form_signoff_roles.role_id', $role_id)
						->get();
			$userFromIdsWithNoOrder = $userFormIds->filter(function($item){ return $item->has_sign_off_order == 0; });
			$userFormIdsWithOrder 	= $userFormIds->filter(function($item){ return $item->has_sign_off_order == 1; });
			if(count($userFormIdsWithOrder) > 0) {
				$userFormIdsWithOrder_aa = DB::table('user_forms')
						->select('user_forms.id as user_form_id', 'form_signoff_roles.role_id', 'form_signoff_roles.order')
						->join('form_signoff_roles', 'form_signoff_roles.form_id', '=', 'user_forms.form_id')
						->whereIn('user_forms.id', $userFormIdsWithOrder->pluck('id'))
						->get();
				if (count($userFormIdsWithOrder_aa) > 0) {
					foreach ($userFormIdsWithOrder_aa as $key => $value) {
						if (!isset($newFormat[$value->user_form_id])) {
							$newFormat[$value->user_form_id] = [
								"user_form_id" => $value->user_form_id,
								"roles" => []
							];
						}

						$newFormat[$value->user_form_id]['roles'][] = [
							"role_id" => $value->role_id,
							"order" => $value->order
						];
					}
				}
				if (count($newFormat) > 0) {
					foreach ($newFormat as $key => $userForm) {
						$userFormId = $userForm['user_form_id'];
						$roles = $userForm['roles'];
						foreach ($roles as $key => $role) {
							if ($role_id == $role['role_id']) {
								$currentOrder = $role['order'];
								// If the current order is 0, directly add the user_form_id
								if ($currentOrder == 0) {
									$matchedUserFormIds[] = $userFormId;
									break;
								}
									else {
									$previousRole = collect($roles)->firstWhere('order', $currentOrder - 1);
									$previousRoleId = $previousRole['role_id'];
									if (UserFormSignoff::where(['user_form_id' => $userFormId, 'signoff_role' => $previousRoleId])->exists()) {
										$matchedUserFormIds[] = $userFormId;
										break;
									}
								}
							}
						}
					}
				}
			}
			$matchedUserFormIds = array_merge($userFromIdsWithNoOrder->pluck('id')->toArray(), $matchedUserFormIds);
		}

		$userForms = \Models\UserForm
			::with('FormValue')
			->with(['Form'=>function($query){
				$query->with('FormSignOffRoles.role');
			}])
			->leftjoin('forms','forms.id','user_forms.form_id')
			->leftjoin('users','users.id','user_forms.user_id')
			->with('User')
		;

		if (
			!\APP\Auth::isAdmin()
		) {
			$userForms = $userForms
				->where(function($query) use ($user, $role_id, $isAccessAllLearners, $matchedUserFormIds) {
					// Logic applied to all non admin roles.
					$query = $query
						->where(function($query) use ($user, $role_id) {
							$query = $query
								->where(function($query) use ($user) {
									$query->where(function ($subquery) {
										$subquery->whereExists(function ($existsQuery) {
											$existsQuery->select(DB::raw(1))
												->from('schedule_links')
												->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
												->where('user_forms.type','schedule')
												->where('link_id', \APP\Auth::getUserId())
												->whereNull('deleted_by')
												->where('schedule_links.type', 'managers')
												->whereNull('schedule_links.deleted_at');
										})
											->where(function ($innerSubquery) {
												$innerSubquery->whereExists(function ($nestedSubquery) {
													$nestedSubquery->from('schedules')
														->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
														->whereColumn('user_forms.type_id', '=', 'schedules.id')
														->where('user_forms.type', 'schedule')
														->where('forms.restricted_form', 1);
												});
											});
									})
										->orWhere(function ($subquery) {
											$subquery->whereNotExists(function ($notExistsQuery) {
												$notExistsQuery->select(DB::raw(1))
													->from('schedule_links')
													->where('user_forms.type','schedule')
													->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
													->where('schedule_links.type', 'managers');
											})
												->whereIn('user_forms.user_id', function ($whereInQuery) {
													$whereInQuery->select('user_id')
														->from('manager_users')
														->where('manager_id', \APP\Auth::getUserId())
														->whereNull('manager_users.deleted_at');
												});
										})
										->orWhere(function ($innerSubquery) {
											$innerSubquery->whereExists(function ($nestedSubquery) {
												$nestedSubquery->from('schedules')
													->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
													->whereColumn('user_forms.type_id', '=', 'schedules.id')
													->where('user_forms.type', 'schedule')
													->where('forms.restricted_form', 0);
											})
												->whereIn('user_forms.user_id', function ($query) {
													$query->select('user_id')
														->from('manager_users')
														->where('manager_id', \APP\Auth::getUserId())
														->whereNull('manager_users.deleted_at');
												});
										})
									;
								})
								// This will show only sign off form if current role is in "Management Roles To Sign-off Form"
								//FOR ISSUE 3260: This is commented because of it needs estio but in wrap ,not shows with out sign off forms

								// ->whereHas("Form.FormSignOffRoles", function ($query) use ($user) {
								// 	$query = $query
								// 		->where('role_id', \APP\Auth::roleId())
								// 	;
								// })
								//END OF FIX
							;

							//need to identify manager approval or not
							$query = $query
								->where(function($query) use ($user,$role_id) {
									$query
										->where(function($query) use ($user,$role_id) {
											$query
												->WhereDoesntHave("UserFormSignOff", function ($query) use ($user,$role_id) {
													$query = $query
														->where('signoff_role', '=', $role_id)
														->where('user_id', '=', $user->id)
													;
												})
												->where("user_forms.user_form_status", "Awaiting Sign-off")
											;
										})
										->orWhere("user_forms.user_form_status", "!=", "Awaiting Sign-off")
									;
								})
							;
						})
					;
					// If manager, then also show forms assigned to him
					if (\APP\Auth::isManager()) {
						$query = $query
							->orWhere('user_forms.user_id', \APP\Auth::getUserId())
						;
					}
					if ($isAccessAllLearners) {
						$query = $query->orWhereIn('user_forms.id',$matchedUserFormIds);
					}
				})
			;
		}

		$userForms = $userForms
			->where("user_forms.user_form_status", "Awaiting Sign-off")
			->count()
		;
		$actionObject->signOffForms = $userForms;


		$response->getBody()->write(json_encode($actionObject));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));

	// List all users and show calculated learning results
	$group->post('{router:employee/list|/employee-list}{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'mylearningemployeeList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);


		if (isset($args["download"]) && $args["download"] == "/download") {

			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = ["Name" => "learner_name"];

			if (!$GLOBALS["CONFIG"]->licensing['isSMCR']) {
				$export_fields["%%company%%"] = "company.name";
			}

			if (!$GLOBALS["CONFIG"]->licensing['isSMCR']) {
				$export_fields["%%department%%"] = "department.name";
			}

			if ($GLOBALS["CONFIG"]->licensing['isSMCR']) {
				$export_fields["SMCR Role"] = "smcr_staff_type_name";
			}
			$export_fields["Total Learning resources"] = "total_resources";
			if (!$GLOBALS["CONFIG"]->licensing['isSMCR']) {
				$export_fields["Not Started"] = "not_started_resources";
				$export_fields["In Progress"] = "in_progress_resources";
			}
			$export_fields["Completed"] = "completed_resources";
			if ($GLOBALS["CONFIG"]->licensing['isApprentix']) {
				$export_fields["Days since contact"] = "last_contact_date";
			}
			if (!$GLOBALS["CONFIG"]->licensing['isSMCR']) {
				$export_fields["Total time spent"] = "time_spent";
				$export_fields["% Completed"] = "completed";
			}
			if ($GLOBALS["CONFIG"]->licensing['isSMCR']) {
				$export_fields["% Completed"] = "completed_smcr";
				$export_fields["Certified"] = "certified";
				$export_fields["Self-Attested"] = "self_attested";
				$export_fields["Next Due"] = "next_completion_date";

				// Add all SmcrFPCategory in list here
				$fp_categories = \Models\SmcrFPCategory
					::where('status', true)
					->get()
				;
				foreach ($fp_categories as $key => $fp_category) {
					$export_fields[$fp_category->name] = $fp_category->key;
				}

			}


			$download_file_name = uniqid("view-by-employees.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));

	// List simple learning resources for requested user
	$group->get("/list/{user_id:[0-9]+}{option:[\/a-z]*}", function (Request $request, Response $response, array $args) {
		try {
			if (!\Models\Role::getRoleParam('lfp_show_learning_resources')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

			$response = \APP\Tools::cors($response);

			$careerPathIDs = explode(',', data_get($request->getQueryParams(), 'career_path_ids', ''));
			$jobIds = explode(',', data_get($request->getQueryParams(), 'job_ids', ''));
			$args['career_path_ids'] = empty(array_filter($careerPathIDs, function ($val) { return $val != ''; })) ? [] : $careerPathIDs;
			$args['job_ids'] = empty(array_filter($jobIds, function ($val) { return $val != ''; })) ? [] : $jobIds;
			$args['curr_page'] = data_get($request->getQueryParams(), 'page');

			// If learner, he can see only his assignments
			if (\APP\Auth::isLearner()) {
				$args['user_id'] = \APP\Auth::getUserId();
			}

			$args['ShowNotApplicableLearning'] = true;

			$results = LearningResult::getUserLearningListQuery($args);

			$results = $results
				->get()
			;

			$resultcopy = $results->toArray();

			$translator = \APP\Templates::getTranslator();
			$lesson_str = $translator->replaceVersionLabels('%%lesson%%');

			foreach ($resultcopy as $key => $result) {

				// If lesson, add fake type, for filtering purposes
				if (
					isset($result['module']['is_course']) &&
					$result['module']['is_course'] == 1
				) {
					$resultcopy[$key]['module']['type'] = [
						'id' => 0,
						'name' => $lesson_str,
						'slug' => 'resource_lesson'
					];
					$resultcopy[$key]['module']['type_id'] = 0;
				}

				if (
					$result['duration_hours'] > 0 ||
					$result['duration_minutes'] > 0
				) {
					$resultcopy[$key]['time'] = ($result['duration_hours']*60)+$result['duration_minutes'] .' minutes';
					$resultcopy[$key]['time_raw'] = ($result['duration_hours']*60)+$result['duration_minutes'];
				} else if ($result['module']['duration_hours'] > 0 || $result['module']['duration_minutes'] > 0) {
					$resultcopy[$key]['time'] = ($result['module']['duration_hours']*60)+$result['module']['duration_minutes'] .' minutes';
					$resultcopy[$key]['time_raw'] = ($result['module']['duration_hours']*60)+$result['module']['duration_minutes'];
				} else if ($result['duration_scorm'] > 0) {
					$resultcopy[$key]['time'] = $result['duration_scorm'] .' minutes';
					$resultcopy[$key]['time_raw'] = $result['duration_scorm'];
				} else {
					$resultcopy[$key]['time'] = 0;
					$resultcopy[$key]['time_raw'] = 0;
				}

				$resultcopy[$key]['standard_list'] = \Models\LearningModule::Standards($result['module']['id'], $result['user_id']);
			}

			$results =json_decode(json_encode($resultcopy),false);

			switch($args["option"]) {
				case "/download":

					$export_fields = [
						"Learning" => "module.name",
						\APP\Licensing::getTranslation('programme') => "standard_list",
						"Type" => "module.type.name",
						"Category" => "module.category.name",
						"Version" => "module.version",
						"Time" => "time",
						"Date Due" => "expected_completion_date_uk",
						"Refresh At"=>"refreshed_at",
						"Status" => "completion_status"
					];


					$download_file_name = uniqid("User_Lessons_and_Learning_Resources.report.") . ".xlsx";

					\APP\Tools::generateExcelDownload(
						$results,
						$export_fields,
						$this->get('settings')["LMSTempPath"] . $download_file_name
					);

					$response->getBody()->write(json_encode($download_file_name));
					return $response->withHeader('Content-Type', 'application/json');
				break;

				case "/print":
					$response->getBody()->write(json_encode($results));
					return $response->withHeader('Content-Type', 'application/json');
				break;
			}


			$response->getBody()->write(gzencode(json_encode($results)));
			return $response
				->withHeader('Content-Encoding', 'gzip')
				->withHeader('Content-Type', 'application/json')
			;

			$response->getBody()->write(json_encode($results));
			return $response->withHeader('Content-Type', 'application/json');
		} catch(\Exception $e) {
			print_r($e->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->post('/list/download-skills-report', function (Request $request, Response $response, array $args) {
		try{

			$user = Auth::getUser();
			$params = $request->getParsedBody();

			if(isset($params["search"]["additionalSearchParams"])){
				$additional_search_params = $params["search"]["additionalSearchParams"];
				unset($params["search"]["additionalSearchParams"]);
			}

			if(isset($params["search"]["refresh"])){
				unset($params["search"]["refresh"]);
			}


			$query = User::with(['Department'])
				->leftJoin('departments', 'departments.id', 'users.department_id')
				->select('users.id','users.fname','users.lname','departments.name as department_name')
				->validuser()
				->with(['LearningResults' => function ($query) use ($additional_search_params)  {
					$query->whereHas('Module',function ($query) use ($additional_search_params)  {
						$query->where('is_skill','=','1');
						if(isset($additional_search_params["skill_subjects"]) && $additional_search_params["skill_subjects"]) {
							$query = $query->whereHas('ApprenticeshipIssuesLearningModules',function ($query) use ($additional_search_params)  {
								$query->whereHas('Issue',function ($query) use ($additional_search_params)  {
									$query->whereHas('IssueCategory',function ($query) use ($additional_search_params)  {
										$query->whereHas('Standard',function ($query) use ($additional_search_params)  {
											$query->where('apprenticeship_standards.id','=',$additional_search_params["skill_subjects"]);
										});
									});
								});
							});
						}
					})
					//->whereRaw('due_at < NOW()')
					//->whereRaw('due_at < NOW() AND completion_status != "completed"')
					->where('refreshed', 0)
					;

					if(isset($additional_search_params["period_from"])){
						$query->where('due_at','>=',$additional_search_params["period_from"]);
					}

					if(isset($additional_search_params["period_to"])){
						$query->where('due_at','<=',$additional_search_params["period_to"]);
					}

				},'LearningResults.Module'])
				->whereExists(function ($query) {
					$query
						->select(DB::raw(1))
						->from('learning_results')
						->whereRaw('learning_results.user_id = users.id')
						->where('refreshed', 0)
						->whereNull('learning_results.deleted_at')
						->join('learning_modules', 'learning_results.learning_module_id', '=', 'learning_modules.id')
						->join('user_learning_modules', function($join) {
							$join
								->on('user_learning_modules.user_id', '=', 'learning_results.user_id')
								->on('user_learning_modules.learning_module_id', '=', 'learning_results.learning_module_id')
								->whereNull('user_learning_modules.deleted_at')
							;
						})
						->where('learning_modules.is_skill', '1')
					;
				})
			;

			if (!\APP\Auth::accessAllLearners()) {
				$assignedUser = ManagerUser::where('manager_id',$user->id)->pluck('user_id');
				$query = $query->whereIn('users.id',$assignedUser);
			}

			if (!\APP\Auth::accessAllCompanies()) {
				$query = $query->where('users.company_id',$user->company_id);

			}

			$query->withCount(['LearningResults' => function ($query) use ($params, $additional_search_params) {
				$query->whereHas('Module', function ($query) use ($additional_search_params){
					$query
						->where('is_skill', '=', '1')
						->where('status', 1)
					;
					if (isset($additional_search_params["category_id"]) && $additional_search_params["category_id"]) {
						$query->where('category_id','=',$additional_search_params["category_id"]);
					}
					if (isset($additional_search_params["skill_subjects"]) && $additional_search_params["skill_subjects"]) {
						$query = $query->whereHas('ApprenticeshipIssuesLearningModules',function ($query) use ($additional_search_params)  {
							$query->whereHas('Issue',function ($query) use ($additional_search_params)  {
								$query->whereHas('IssueCategory',function ($query) use ($additional_search_params)  {
									$query->whereHas('Standard',function ($query) use ($additional_search_params)  {
										$query->where('apprenticeship_standards.id','=',$additional_search_params["skill_subjects"]);
									});
								});
							});
						});
					}
				})
				->join('user_learning_modules', function($join) {
					$join
						->on('user_learning_modules.user_id', '=', 'learning_results.user_id')
						->on('user_learning_modules.learning_module_id', '=', 'learning_results.learning_module_id')
						->whereNull('user_learning_modules.deleted_at')
					;
				})
				->where('refreshed', 0)
				->whereRaw('DATEDIFF(due_at,CURDATE()) <= 0')
				;

				if (isset($additional_search_params["period_from"])) {
					$query = $query->where('due_at', '>=', $additional_search_params["period_from"]);
				}
				if (isset($additional_search_params["period_to"])) {
					$query = $query->where('due_at', '<=', $additional_search_params["period_to"]);
				}

				//$query->whereRaw('DATEDIFF(due_at,NOW())');
				//$query->whereRaw('DATEDIFF(due_at,CURDATE()) <= 0 AND completion_status != "completed"');
				//$query = $query;

				//$query->where('user_id', 18);
			}]);

			$query->withCount(['LearningResults as skill_count' => function ($query) use ($params, $additional_search_params) {
				$query->whereHas('Module', function ($query) use ($additional_search_params){
					$query
						->where('is_skill', '=', '1')
						->where('status', 1)
					;
					if (isset($additional_search_params["category_id"]) && $additional_search_params["category_id"]) {
						$query->where('category_id','=',$additional_search_params["category_id"]);
					}
					if (isset($additional_search_params["skill_subjects"]) && $additional_search_params["skill_subjects"]) {
						$query = $query->whereHas('ApprenticeshipIssuesLearningModules',function ($query) use ($additional_search_params)  {
							$query->whereHas('Issue',function ($query) use ($additional_search_params)  {
								$query->whereHas('IssueCategory',function ($query) use ($additional_search_params)  {
									$query->whereHas('Standard',function ($query) use ($additional_search_params)  {
										$query->where('apprenticeship_standards.id','=',$additional_search_params["skill_subjects"]);
									});
								});
							});
						});
					}
				})
				->join('user_learning_modules', function($join) {
					$join
						->on('user_learning_modules.user_id', '=', 'learning_results.user_id')
						->on('user_learning_modules.learning_module_id', '=', 'learning_results.learning_module_id')
						->whereNull('user_learning_modules.deleted_at')
					;
				})
				->where('refreshed', 0)
				;

				if (isset($additional_search_params["period_from"])) {
					$query = $query->where('due_at', '>=', $additional_search_params["period_from"]);
				}
				if (isset($additional_search_params["period_to"])) {
					$query = $query->where('due_at', '<=', $additional_search_params["period_to"]);
				}

				//$query->whereRaw('DATEDIFF(due_at,NOW())');
				//$query->whereRaw('DATEDIFF(due_at,CURDATE()) <= 0 AND completion_status != "completed"');
				//$query = $query;

				//$query->where('user_id', 18);
			}]);

			if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])){
				if($params["sort"]["predicate"] == "score_first") {
					$query->orderBy('learning_results_count', $params["sort"]["reverse"] ? "DESC" : "ASC");
				} elseif (preg_match('/skill_*/',$params['sort']['predicate'])){
					$skill_id = str_replace("skill_", "", $params['sort']['predicate']);
					$order = $params["sort"]["reverse"] ? "DESC" : "ASC";

					$query->leftJoin('learning_results', function ($join) use ($additional_search_params,$skill_id) {
						$join->on('learning_results.user_id', '=', 'users.id')
							->where('learning_results.learning_module_id',$skill_id);

						if (isset($additional_search_params["period_from"])) {
							$join->where('due_at', '>=', $additional_search_params["period_from"]);
						}
						if (isset($additional_search_params["period_to"])) {
							$join->where('due_at', '<=', $additional_search_params["period_to"]);
						}
					});
					$query->orderBy("learning_results.due_at", $order);
				}
				unset($params["sort"]["predicate"]);
			}

			if (isset($params["search"]) && isset($params["search"]["department"])) {
				$query->whereHas('Department', function ($query) use ($params) {
					$query->where('name', 'LIKE', '%'. $params["search"]["department"] . '%');
				});
				unset($params["search"]["department"]);
			}

			if(isset($additional_search_params)){
				// Department
				if (isset($additional_search_params["department_id"]) && $additional_search_params["department_id"]) {
					$query = $query->where('department_id', '=', $additional_search_params["department_id"]);
				}

				if (isset($additional_search_params["location_id"]) && $additional_search_params["location_id"]) {
					$query = $query->where('location_id', '=', $additional_search_params["location_id"]);
				}

				if (isset($additional_search_params["category_id"]) && $additional_search_params["category_id"]) {
					$query->whereHas('LearningResults', function ($query) use ($additional_search_params) {
						$query->whereHas('Module',function ($query) use ($additional_search_params)  {
							$query->where('category_id','=',$additional_search_params["category_id"]);
						});
					});
				}
			}

			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"Name" => "fname,lname",
				"Department" => "department_name",
				"No of Lapsed Skills" => "learning_results_count",
				"No of Skills" => "skill_count",
			];

			// dynamic columns
			$skills = \Models\LearningModule::where('is_skill',1);
			if(isset($additional_search_params["skill_subjects"]) && $additional_search_params["skill_subjects"]) {
				$skills =$skills->whereHas('ApprenticeshipIssuesLearningModules',function ($query) use ($additional_search_params)  {
					$query->whereHas('Issue',function ($query) use ($additional_search_params)  {
						$query->whereHas('IssueCategory',function ($query) use ($additional_search_params)  {
							$query->whereHas('Standard',function ($query) use ($additional_search_params)  {
								$query->where('apprenticeship_standards.id','=',$additional_search_params["skill_subjects"]);
							});
						});
					});
				});
			}
			$skills=$skills->get();

			foreach ($skills as $skill){
				$export_fields[$skill->name] = 'learning_results';
			}

			$download_file_name = uniqid("Lessons_and_Learning_Resources_report_") . ".xlsx";

			//print_r(json_encode($data));
			//die();

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');

		}catch(\Exception $e){
			print_r($e->getMessage());
		}

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	// List all learning resources!
	$group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {
	try{
		// If user is distributor, assign all installed e-learning resources to him if there is some that are not assigned, might slow down on first request(if installation have hundreds), but should not be a problem on subsequent requests/
		$response = \APP\Tools::cors($response);
		if (\APP\Auth::isDistributor(true)) { // No joking around, do not run this on shadow distributor
			$distributor = \APP\Auth::getUser();
			$non_distributor_modules = \Models\LearningModule
				::where('status', true)
				->where('type_id', 1)
				// Only those that are not assigned to distributor
				->whereNotIn('id',
					\Models\UserLearningModule
						::select('id')
						->where('user_id', $distributor->id)
						->get()
				)
				->pluck('id')
				->toArray()
			;
			$distributor->modules()->syncWithoutDetaching($non_distributor_modules);
			\APP\Learning::syncUserResults($distributor->id);
		}

		$totaldiscount = 0;

		$totaldiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
		$totaldiscount = ($totaldiscount > 100) ? 100 : $totaldiscount;


		$query_id = 'mylearningList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$generated = $QueryBuilder::generate($params, $args, true);
		$query = $generated['query'];

		// filter by manager name
		if(isset($params['search']['manager_name'])) {
			$query->whereHas('user.Managers', function ($query) use ($params) {
				$query
					->whereRaw("CONCAT(fname,  ' ', lname ) LIKE '%" . $params['search']['manager_name'] . "%'")
				;
			});
			unset($params['search']['manager_name']);
		}

			switch($args["option"]) {
				case "/download":
					$query
						->selectRaw("CONCAT(users.fname, ' ', users.lname) as trainee_name")
						->selectRaw("
							IF (learning_modules.is_course = 1, 'Lesson', 'Learning Resource') as module_resource_lesson
						")
						//						CASE WHEN module.is_course = 1 THEN 'Lesson'
							//ELSE 'Learning Resource' END AS module_resource_lesson

					;
					$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

					$export_fields = [
						"Name" => "trainee_name",
						"Managers" => "user.managers",
						"Learning" => "module.name",
						"Learning Resource/Lesson" => "module_resource_lesson",
						"Type" => "module.type.name",
						"Date Due" => "due_at",
						"Status" => "completion_status"
					];


					$download_file_name = uniqid("Lessons_and_Learning_Resources.report.") . ".xlsx";

					\APP\Tools::generateExcelDownload(
						$data,
						$export_fields,
						$this->get('settings')["LMSTempPath"] . $download_file_name
					);
					$response->getBody()->write(json_encode($download_file_name));
					return $response->withHeader('Content-Type', 'application/json');
				break;

				case "/email":
					$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
					return \APP\Email::getRecipients (
						$response, // response object used to return modified response
						$data, // results from query
						$params, // filtered/modified parameters
						$request->getParsedBody(), // original parameters
						$this->get('settings')["email"], // email settings from config file
						$query_id, // ID that will be used to generate query
						'Lessons and Learning Resources report', // Template name
						['user', 'id'], // where to look for user's ID in this query
						$args
					);
				break;
			}

			// Do not paginate for learner, not now.
			if (\APP\Auth::isLearner()) {
				$data = \APP\SmartTable::searchPaginate($params, $query, false, true);
			} else {
				$data = \APP\SmartTable::searchPaginate($params, $query, false, true, true);
			}

			$recommendations = [];
			if (\APP\Tools::getConfig("showRecommendations")){
				$n_recommendations = \APP\Tools::getConfig("RecommendationsNumber") ?? 3;
				$recommendations_query = \Models\UserLearningRecommendations
					::where("user_id", \APP\Auth::getUserId())
					->where("is_dismissed", "=", 0)
					->orderByRaw("RAND()")
					->limit($n_recommendations)
				;
				foreach($recommendations_query->get() as $recom){
					$recommendations[] = $recom->learning_module_id;
				}
			}
			// If learner, attach full generated URL to thumbnail and highlight.
			// Also, sort resources!, give lesson a 1000, 2000, 3000, etc order and resources in lessons 1001, 1002, 1003, etc.
			// Had to loop twice, first time to set lesson order, second time to use lesson order to set resource order.
			if (
				\APP\Auth::isLearner() ||
				\APP\Auth::isDemoUser()
			) {
				$include_standard_modules = $generated['include_standard_modules'];
				$sorted_lessons = [];
				$lesson_order = 1000;
				foreach($data as $result) {

					//if resource is in $include_standard_modules, set standards_cnt to 1 to learning result
					$result->standards_cnt = 0;
					if (
						is_array($include_standard_modules) &&
						in_array($result->learning_module_id, $include_standard_modules, true)
					) {
						$result->standards_cnt = 1;
					}

					$result->module->setAppends(['safe_thumbnail', 'highlight','isLocked']);
					$result->totaldiscount = $totaldiscount;
					if ($result->module->is_course) {
						$result->module->sort_order = $lesson_order;
						$sorted_lessons[$result->module->id] = $lesson_order;
						$lesson_order = $lesson_order + 1000;
					}
				}

				// add order to resources belonging in lessons
				foreach($data as $result) {
					if ($result->module->is_course == 0) {
						// Give default sort order, that will put resources at the end.
						$result->module->sort_order = $lesson_order;
						$lesson_order++;

						// Find if resource is in lesson, get first lesson ID and add sort order from $sorted_lessons
						foreach ($result->module->Courses as $key => $course) {
							if (
								$course->status &&
								isset($sorted_lessons[$course->id])
							) {
								$sorted_lessons[$course->id]++;
								$result->module->sort_order = $sorted_lessons[$course->id];
								$lesson_order--;
							}
						}
					}
				}

				foreach($data as $result) {
					$result->is_recommended = in_array($result->learning_module_id, $recommendations) && ($result->completion_status == "not attempted");
				}

				// Check is resource has query builder, if user is not part of query, mark it as hidden and hide it at the front end.
				if (\APP\Tools::getConfig('HideNotApplicableLearning')) {
					foreach($data as $result) {
						$resource_query = \Models\ResourceQuery
							::where('action', 'add')
							->where('type_id', $result->learning_module_id)
							->whereIn('type', ['resources', 'lessons'])
							->where('user_ids', '>', '')
							->first()
						;
						if (
							$resource_query &&
							!in_array($result->user_id, array_map('intval', explode(',', $resource_query->user_ids)))
						) {
							$result->HideNotApplicableLearning = true;
						}
					}
				}
			}

			$response->getBody()->write($data->toJson());
			return $response->withHeader('Content-Type', 'application/json');

		}catch(\Exception $e){
			print_r($e->getMessage());
		}

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	/*Learner Resource Sorting Order*/
	$group->get('/resource-sorting-order', function (Request $request, Response $response, array $args) {

		$data = \APP\Auth::getUser()->sort_resource_order;

		$response->getBody()->write($data);
		return $response;

	})->add(\APP\Auth::getSessionCheck());

	/* Learner Resource Sorting Order Save */
	$group->post('/resource-sorting-order', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$data = \Models\User::find(\APP\Auth::getUserId());
		$data->sort_resource_order = $params['sort_resource_order'];
		$data->save();

		return
			$response
		;
	})->add(\APP\Auth::getSessionCheck());

	/* Get Users Learning list paginated results */
	$group->post('/list/user/{user_id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$results = LearningResult::getUserLearningListQuery($args);

		unset($params['search']['refresh']);

		// Show lessons filter
		if (
			isset($params['search']['learning_modules__type_id']) &&
			$params['search']['learning_modules__type_id'] == 0
		) {
			$results = $results
				->where('learning_modules.is_course', 1)
			;
			unset($params['search']['learning_modules__type_id']);
		}
	if(isset($params['search']) && isset($params['search']['expected_completion_date'])){
	  $params['search']['expected_completion_date'] = json_decode($params['search']['expected_completion_date'],true);
	  $period_from = date('Y-m-d',strtotime($params['search']['expected_completion_date']['period_from']));
	  $period_to = date('Y-m-d',strtotime($params['search']['expected_completion_date']['period_to']));
			$results = $results->whereBetween(DB::raw('
	CASE
		WHEN learning_results.completion_date_custom IS NOT NULL THEN learning_results.completion_date_custom
		ELSE
			CASE
				WHEN learning_results.grace_at IS NOT NULL THEN learning_results.grace_at
				ELSE learning_results.due_at
			END
		END'),[$period_from,$period_to]);
		  unset($params['search']['expected_completion_date']);
	}
	if(isset($params['sort'])){
	  if(isset($params['sort']['predicate']) && $params['sort']['predicate'] == 'completion_date_custom'){
		$orderBY = $params['sort']['reverse'] ? "DESC" : "ASC";
		$results= $results->orderBY(DB::raw('
		CASE
			WHEN learning_results.completion_date_custom IS NOT NULL THEN learning_results.completion_date_custom
		ELSE
			CASE
				WHEN learning_results.grace_at IS NOT NULL THEN learning_results.grace_at
				ELSE learning_results.due_at
			END
		END'),$orderBY);
	  }
	}

		// If no completion state is selected, show not attempted and in progress only
		if (empty($params['search']['completion_status'])) {
			$results = $results
				->where('learning_results.completion_status', '!=', 'completed')
			;
		}

		$data = \APP\SmartTable::searchPaginate($params, $results);

		$json = $data->toJson();

		$response->getBody()->write($json);
		return $response->withHeader('Content-Type', 'application/json');
		})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

});
