<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/smcr-committee",  function ($group) {

	// Get spcific SMCR committee
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {

		$committee = \Models\SmcrCommittee
			::where('id', $args["id"])
			->with(['roles' => function($query) use($args) {
				$query
					->where('status', true)
					->with(['personnel' => function($query) use($args) {
						$query
							->where('status', true)
							->with(['user' => function($query) use($args) {
								$query
									->select('id', 'fname', 'lname')
									->where('status', true)
								;
							}])
							->select(
								'smcr_committee_role_personnel.*',
								DB::raw("DATE_FORMAT(smcr_committee_role_personnel.updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk"),
								DB::raw("DATE_FORMAT(smcr_committee_role_personnel.assigned,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS assigned_uk")
							)
						;
					}])
				;
			}])
			->first()
		;

		$response->getBody()->write(json_encode($committee));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'select'));

	// Update existing SMCR committee
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$committee = \Models\SmcrCommittee::find($args["id"]);
		$data = $request->getParsedBody();

		$committee->name = $data["name"];
		if (isset($data["description"])) {
			$committee->description = $data["description"];
		}
		$committee->live = isset($data["live"]) ? $data["live"] : false;
		$committee->save();

		\Models\SmcrCommittee::updateCounts($committee->id);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'update'));

	// Disable SMCR committee
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$committee = \Models\SmcrCommittee::find($args["id"]);
		$committee->status = 0;
		$committee->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'disable'));

	// Enable SMCR committee
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$committee = \Models\SmcrCommittee::find($args["id"]);
		$committee->status = 1;
		$committee->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'disable'));

	// Get all list of enabled SMCR functions
	$group->get('/all', function (Request $request, Response $response) {
		$user = \APP\Auth::getUser();
		$query = [];
		if (
			$user->staff_type_id == 3 ||
			\APP\Auth::checkStructureAccess(['system-setup-organisation-committees'], 'select')
		) {
			$query = \Models\SmcrCommittee
				::where("status", true)
				->with(['Roles' => function($query) {
					$query
						->where('status', true)
					;
				}])
				->get()
			;
		}

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// Add new SMCR committee
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$committee = new \Models\SmcrCommittee;

		$committee->name = $data["name"];
		if (isset($data["description"])) {
			$committee->description = $data["description"];
		}
		$committee->live = isset($data["live"]) ? $data["live"] : false;
		$committee->status = true;
		$committee->save();

		if (isset($data["roles"]) && $data["roles"]) {
			foreach ($data["roles"] as $key => $role) {
				$committee_role = new \Models\SmcrCommitteeRole;
				$committee_role->name = $role["name"];
				$committee_role->smcr_committee_id = $committee->id;
				$committee_role->status = true;
				$committee_role->save();
			}
		}

		\Models\SmcrCommittee::updateCounts($committee->id);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'insert'));

   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\SmcrCommittee
			::with(['roles' => function($query) use($args) {
				$query
					->where('status', true)
				;
			}])
			->withCount(['roles as chairman' => function ($query) {
				$query
					->where(function($query) {
						$query
							->where('name', 'Chairperson')
							->orWhere('name', 'Chairman')
						;
					})
					->where('status', true)
				;
			}])
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name",
				"Description" => "description",
			];


			$download_file_name = uniqid("smcr-committees.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committees', 'select'));
});