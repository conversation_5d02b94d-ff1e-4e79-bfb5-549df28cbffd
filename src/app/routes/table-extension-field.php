<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/table-extension-field",  function ($group) {

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\TableExtensionField::find($args["id"]);

		$response->getBody()->write(json_encode($entry));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->get('/all', function (Request $request, Response $response, $args) {
		session_write_close();
		$data = \Models\TableExtensionField
			::where("status", true)
			->where('versions', 'like', '%"' . $this->get('settings')["licensing"]['version'] . '"%')
			->where(function ($query) {
				$query
					->where('show_learner', true)
				;
				if (\APP\Auth::isAdminInterface()) {
					$query = $query
						->orWhere('show_administration', true)
					;
				}
			})
		;

		$data = $data
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

});