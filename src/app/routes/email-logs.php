<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/email-logs",  function ($group) {

   $group->post('/list', function (Request $request, Response $response) {
      try{
		$params = $request->getParsedBody();
		$query = \Models\EmailHistory
			::where("email_history.id", ">", 0)
			->select(
				"email_history.*",
				DB::raw("CONCAT(users.fname, ' ', users.lname) as user_name_to")
			)
			->leftjoin("users", function($join) {
				$join
					->on("users.id", "=", "email_history.user_id")
				;
			})
			->where("email_history.email_to", "!=", "")
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["search"]["id"])) {
			$id = $params["search"]["id"];
			$query = $query->where("email_history.id", "LIKE", "%{$id}%");
			unset($params["search"]["id"]);
		}


		if (isset($params["search"]["email_to"])) {
			$to = $params["search"]["email_to"];
			$query = $query
				->where(function ($query) use ($to) {
					$query = $query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $to . "%'")
						->orWhere("email_history.email_to", "LIKE", "%{$to}%")
					;
				})
			;
			unset($params["search"]["email_to"]);
		}

		if (isset($params["search"]["email_from"])) {
			$from = $params["search"]["email_from"];
			$query = $query
				->where(function ($query) use ($from) {
					$query = $query
						->where("email_history.name_from", "LIKE", "%{$from}%")
						->orWhere("email_history.email_from", "LIKE", "%{$from}%")
					;
				})
			;
			unset($params["search"]["email_from"]);
		}

		$body = false;
		if (isset($params["search"]["body"])) {
			$body = $params["search"]["body"];
			$query = $query
				->where("email_history.body_text_only", "LIKE", "%{$body}%")
				// ->whereRaw("MATCH(body) AGAINST(? IN NATURAL LANGUAGE MODE)", [$body])
				// ->whereRaw("MATCH(body_text_only) AGAINST(? IN NATURAL LANGUAGE MODE)", [$body])
				//->whereRaw("MATCH(body_text_only) AGAINST(? IN BOOLEAN MODE)", ["+{$body}"])
				// ->whereRaw("MATCH(body_text_only) AGAINST(? IN NATURAL LANGUAGE MODE)", [$body])
			;
			unset($params["search"]["body"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		if ($body) {
			foreach ($p as $key => $value) {
				$p[$key]->body = str_ireplace($body, "<span class=\"label label-warning\">$body</span>", $p[$key]->body);
			}
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		} catch(\Exception $e) {
			print_r($e->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-logs', 'select'));

	//send email
	$group->post('/send-email/{id}', function (Request $request, Response $response, array $args) {
		try {
			$email = \Models\EmailHistory
				::where("id",$args['id'])
				->first()
			;
			$email->resend = "1";
			$email->save();

			$response->getBody()->write(json_encode([
				"status" => "success",
				"message" => "Email has been sent successfully."
			]));
			return $response->withHeader('Content-Type', 'application/json');
		} catch(\Exception $e) {
			$logger = \APP\LoggerHelper::getLogger();
			$logger->error("Error sending email", [
				'Email History ID' => $email->id,
				'error' => $e->getMessage(),
			]);
		}
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-logs', 'select'));
});