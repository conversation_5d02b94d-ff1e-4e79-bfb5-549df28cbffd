<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/turnitin",  function ($group) {

    $group->get("/trainee/{learning_module_id:[0-9]+}" , function (Request $request, Response $response, $args) {

        $lm_id = $args["learning_module_id"];

        $turnitin = new \APP\Turnitin();

        $turnitin->getInstructorSubmissionForm($lm_id, \APP\Auth::getUserId(), true);

        $formHTML = $turnitin->getStudentSubmissionForm($lm_id);

		return $response->getBody()->write($formHTML);
    })->add(\APP\Auth::getSessionCheck());


    $group->get("/instructor/{learning_module_id:[0-9]+}/{trainee_user_id:[0-9]+}" , function (Request $request, Response $response, $args) {

        $lm_id = $args["learning_module_id"];
        $trainee_user_id = $args["trainee_user_id"];

        $turnitin = new \APP\Turnitin();

        $formHTML = $turnitin->getInstructorSubmissionForm($lm_id, $trainee_user_id);

		return $response->getBody()->write($formHTML);
    })->add(\APP\Auth::getSessionCheck());

    $group->post("/grading" , function (Request $request, Response $response, $args) {
        $xml_data = $request->getBody();
        $xml_obj = simplexml_load_string($xml_data);

        try {
            $lr_id = $xml_obj->imsx_POXBody->replaceResultRequest->resultRecord->sourcedGUID->sourcedId;

            $result = $xml_obj->imsx_POXBody->replaceResultRequest->resultRecord->result->resultScore;
            $score = round(doubleval($result->textString) * 100.0);

            $lr = \Models\LearningResult::findOrFail($lr_id);
            $lr->score = $score;
            $lr->passing_status = ($score >= 55) ? "passed" : "failed";
            if ($lr->passing_status == "passed") {
                $lr->completion_status = "completed";
                $lr->completed_at = \Carbon\Carbon::now();
                $lr->sign_off_manager = 1;
            }
            $lr->learner_action = true;
            $lr->save();

            // Perform check if SMCR and resource in F&P category that is hidden to user and resource is upload, then do not send e-mail to user!
            $smcr = new \APP\Smcr();
            $send_email = $smcr->sendLearnerEmail($lr);

            // Send out "Learning Resource Signed Off by Coach" to learner
            $template = \Models\EmailTemplate
                ::where('name', '%%learning_resource%% Signed Off by %%manager%%')
                ->where('status', true)
                ->first()
            ;
            if (
                $template &&
                $template->id &&
                $send_email &&
                $lr->module->track_progress
            ) {
                $email_queue = new \Models\EmailQueue;
                $email_queue->email_template_id = $template->id;
                $email_queue->learning_module_id = $lr->module->id;
                $email_queue->recipients = [intval($lr->user->id)];
                $email_queue->from = $lr->created_by;
                $email_queue->save();
            }

        } catch(\Exception $ex) {
            $GLOBALS["LOGGER"]->addInfo("Can't process Turninit learning result: $xml_data");
        }

    });

    $group->post("/submtted" , function (Request $request, Response $response, $args) {
        $json_data = $request->getBody();
        $json_obj = json_decode($json_data);

        try {
            $lr_id = $json_obj->lis_result_sourcedid;

            $lr = \Models\LearningResult::findOrFail($lr_id);
            $lr->sign_off_trainee = 1;
            $lr->save();

        } catch(\Exception $ex) {
            $GLOBALS["LOGGER"]->addInfo("Can't process Turninit ext_resource_tool_placement_url: $json_data");
        }

    });
});
