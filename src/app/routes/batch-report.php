<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/batch-report",  function ($group) {


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$report = \Models\BatchReport::find($args["id"]);
		$report->status = false;
		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$report = \Models\BatchReport::find($args["id"]);
		$report->status = true;
		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'disable'));

	// Get individual batch report
	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$report = \Models\BatchReport
			::where('id', $args["id"])
			->with('managers')
			->with(['Files' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->first()
		;

		$response->getBody()->write(json_encode($report));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));

	// Update batch report
	$group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();


		$report = \Models\BatchReport::find($args["id"]);
		$report->title = $data['title'];
		if (isset($data['copy_manager'])) {
			$report->copy_manager = $data['copy_manager'];
		}
		$report->description = $data['description'];
		if (
			isset($data['frequency_pattern']) &&
			!$data['frequency_pattern']
		) {
			$report->run_date = null;
		} else {
			if (
				$report->frequency_pattern !== json_encode($data['frequency_pattern'])
			) {
				$report->run_date = \APP\Tools::nextDateFromPattern($data['frequency_pattern'], \Carbon\Carbon::now());
			}
		}
		if(isset($data['manager']))
		{
			\Models\BatchReportManager::where('batch_report_id',$report->id)->delete();
			foreach($data['manager'] as $value)
			{
				$batchReportManager=new \Models\BatchReportManager;
				$batchReportManager->user_id=$value;
				$batchReportManager->batch_report_id=$report->id;
				$batchReportManager->save();
			}
		}
		$report->frequency_pattern = json_encode($data['frequency_pattern']);
		$report->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'update'));


	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$report = new \Models\BatchReport;
		$report->title = $data["title"];
		$report->description = isset($data["description"]) ? $data["description"] : null;
		$report->slug = isset($data["slug"]) ? $data["slug"] : null;
		$report->custom_review_id = isset($data["custom_review_id"]) && $data["custom_review_id"] ? $data["custom_review_id"] : null;
		$report->table_state = isset($data["table_state"]) ? $data["table_state"] : null;
		$report->table_state_original = isset($data["table_state_original"]) ? $data["table_state_original"] : null;
		$report->args = isset($data["args"]) && $data["args"] ? json_encode($data["args"]) : null;
		$report->display_list = isset($data["display_list"]) ? $data["display_list"] : null;
		$report->filter_list = isset($data["filter_list"]) ? $data["filter_list"] : null;
		$report->frequency_pattern = isset($data["frequency_pattern"]) ? $data["frequency_pattern"] : null;
		$report->copy_manager = isset($data["copy_manager"]) ? $data["copy_manager"] : false;
		$report->run_date = null;

		// If frequency pattern is given, find out next run date for this batch report.

		$report->type = isset($data["type"]) ? $data["type"] : null;

		if (
			isset($data["frequency_pattern"]) &&
			$data["frequency_pattern"] &&
			$data["frequency_pattern"] !== 'false'
		) {
			$frequency_pattern = json_decode($data["frequency_pattern"], TRUE);
			$report->run_date = \APP\Tools::nextDateFromPattern($frequency_pattern, \Carbon\Carbon::now());
			if (
				$report->type &&
				$report->type == 'email'
			) {
				$report->run_date = \APP\Tools::nextDateFromPattern($frequency_pattern);
			}
		}

		$report->get_users_parameters = isset($data["get_users_parameters"]) ? $data["get_users_parameters"] : null;
		$report->get_users_modified_parameters = isset($data["get_users_modified_parameters"]) ? $data["get_users_modified_parameters"] : null;
		$report->get_users_arguments = isset($data["get_users_arguments"]) ? $data["get_users_arguments"] : null;
		$report->get_users_url = isset($data["get_users_url"]) ? $data["get_users_url"] : null;
		$report->get_users_method = isset($data["get_users_method"]) ? $data["get_users_method"] : null;
		$report->user_id_key = isset($data["user_id_key"]) ? $data["user_id_key"] : null;

		if (
			(
				$report->type == 'email' &&
				!$report->run_date
			) ||
			(
				$report->type != 'email'
			)
		) {
			$report->times_run = 1;
		}

		$report->user_id = \APP\Auth::getUserId();

		$report->save();

		if (
			(
				$report->type == 'email' &&
				!$report->run_date
			) ||
			(
				$report->type != 'email'
			)
		) {
			// Run the report and add to data table
			$report_data = \Models\BatchReportData::generateEntry($report);
			\Models\BatchReportManager::generateEntry($report,$data['manager']);
			// if e-mail and there is no next run date, send e-mails now
			if (
				$report->type == 'email' &&
				!$report->run_date
			) {
				\APP\Email::sendBatchReport (
					$report->title,
					$report->description,
					$report_data->data,
					$report->copy_manager,
					$report->id,
					$report_data
				);
			}
			$emptydata = json_encode([]);
			if (
				$report->type == 'report' &&
				!$report->run_date
			) {
				\APP\Email::sendBatchReport (
					$report->title,
					$report->description,
					$emptydata,
					$report->copy_manager,
					$report->id,
					$report_data
				);
			}
		}



		$response->getBody()->write(json_encode($report->id));
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('review', 'insert'));


	$group->post('/list', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\BatchReport::select(
				'id',
				'title',
				'type',
				'description',
				'slug',
				'custom_review_id',
				'frequency_pattern',
				'run_date',
				'times_run',
				'status',
				'updated_at',
				'created_at',
				'table_state',
				'table_state_original',
				DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"),
				DB::raw("DATE_FORMAT(run_date,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS run_date_uk")
			)
			->orderBy('created_at', 'DESC')
			->withCount(['DataEntries' => function($query) {
				$query
					->where('batch_report_data.status', true)
					->where('batch_report_data.unread', true)
				;
			}])
		;

		if (array_key_exists('slug', $params)) {
			$query = $query
				->where('slug', $params["slug"])
			;
		}

		if (
			array_key_exists('custom_review_id', $params) &&
			$params['custom_review_id']
		) {
			$query = $query
				->where('custom_review_id', $params["custom_review_id"])
			;
		}

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');



  })->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));
});
