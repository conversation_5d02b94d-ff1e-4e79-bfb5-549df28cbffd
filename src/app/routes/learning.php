<?php

use APP\Auth;
use APP\Controllers\ChatBotController;
use APP\Controllers\ImportEvidenceController;
use APP\Form;
use APP\Templates;
use APP\Tools;
use APP\Course;
use Carbon\Carbon;
use DB\LicenseFeatures;
use Illuminate\Database\Capsule\Manager as DB;
use Models\ApprenticeshipIssuesLearningModules;
use Models\ApprenticeshipStandard;
use Models\LearningModule;
use Models\LearningModuleCredlyBadge;
use Models\LearningModuleLastAccess;
use Models\LearningResult;
use Models\LearningModuleType;
use Models\LinkedLearningModule;
use Models\LinkedLearningTemplate;
use Models\SignoffLog;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;
use Slim\Psr7\Stream;
use Middleware\RateLimitMiddleware;

$app->group("/learning", function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learning = \Models\LearningModule::find($args["id"]);
		$learning->status = 0;
		$learning->save();

		/** Softdelete Learning Modules */
		$user_form_list = \Models\UserFormTemplateWorkflowRelations::where(function ($query) use ($args) {
				$query->where("type_id", $args["id"])
					->where("type", "lesson");
			})
			->orWhere(function ($query) use ($args) {
				$query->where("reference_type_id", $args["id"])
					->where("reference_type", "lesson");
			})
			->get();
		if ($user_form_list) {
			foreach ($user_form_list as $user_form_list_val) {
				\Models\UserForm::deleteUserForms($user_form_list_val->user_form_id);
			}
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learning = \Models\LearningModule::find($args["id"]);
		$learning->status = 1;
		$learning->save();

		/** Softdelete Learning Modules */
		$user_form_list = \Models\UserFormTemplateWorkflowRelations::withTrashed()
			->where(function ($query) use ($args) {
				$query->where("type_id", $args["id"])
					->where("type", "lesson");
			})
			->orWhere(function ($query) use ($args) {
				$query->where("reference_type_id", $args["id"])
					->where("reference_type", "lesson");
			})
			->get();
		if ($user_form_list) {
			foreach ($user_form_list as $user_form_list_val) {
				\Models\UserForm::deleteUserForms($user_form_list_val->user_form_id, false);
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'disable'));

	// Disable/Enable multiple resources
	$group->put('/{state}-multiple', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (
			isset($data['ids']) &&
			is_array($data['ids'])
		) {
			$query = \Models\LearningModule
				::whereIn('id', $data['ids']);
			if ($args['state'] == 'disable') {
				$query = $query
					->update(['status' => false]);
			}
			if ($args['state'] == 'enable') {
				$query = $query
					->update(['status' => true]);
			}
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'disable'));


	// Move/change category for given resources
	$group->put('/move-category', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (
			isset($data['ids']) &&
			is_array($data['ids']) &&
			isset($data['category_id'])
		) {
			$query = \Models\LearningModule
				::whereIn('id', $data['ids'])
				->update(['category_id' => $data['category_id']]);
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	// Get lesson
	$group->get('/course/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learning = \Models\LearningModule
			::with(["modules" => function ($query) {
				$query
					->select("learning_modules.id", "name")
					->orderBy('learning_course_modules.id', 'asc');
			}])
			->with(["formworkflow", "companies", "editAccessCompanies", "competencies"])
			->with(['Files' => function ($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function ($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role');
					}]);
			}]);

		if (\APP\Tools::getConfig('showExtraResourceFields_TDG')) {
			$learning
				->with(['TargetCatalogues' => function ($query) {
					$query
						->wherePivot('deleted_at', null)
						->where('target_catalogues.status', true);
				}])
				->with(['LearningModuleDeliveryProviderType' => function ($query) {
					$query
						->where('learning_module_delivery_provider_types.status', true);
				}])
				->with(['LearningModuleGroupDepartmentCode' => function ($query) {
					$query
						->where('learning_module_group_department_codes.status', true);
				}]);
		}

		$learning = $learning
			->find($args["id"])
		;
        if ($learning->self_enroll) {
            $learning->learner_access = 2;
        } else if ($learning->open_in_events_only) {
            $learning->learner_access = 1;
        } else {
			$learning->learner_access = 3;
		}

		$response->getBody()->write(json_encode($learning));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));

	// get learning resource
	$group->get('/module/{id:[0-9]+}[/{schedule_id:[0-9]+}]', function (Request $request, Response $response, $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());

		// If User is learner, check if resource is assigned to user or enrollable, if neither are, deny access!
		if (
			\APP\Auth::isLearningInterface()
		) {
			$assigned = \Models\UserLearningModule
				::where('learning_module_id', $args["id"])
				->where('user_id', \APP\Auth::getUserId())
                ->first();

			$enrollable = \Models\LearningModule
            	::where('id', $args["id"])
			;
            if (
				Auth::getUser()->company &&
				Auth::getUser()->company->make_all_enrollable
			) {
				$enrollable
					->where(function($query){
						$query
							->where('self_enroll', true)
							->orWhere(function($query){
								$query->whereIn('learning_modules.id', LinkedLearningModule::getAvailableLearning());
							})
							->orWhereIn('name', LearningModule::$catelog)
						;
					})
                ;
            } else {
                $enrollable
					->where('self_enroll', true)
					->orWhere(function($query){
						$query->whereIn('learning_modules.id', LinkedLearningModule::getAvailableLearning());
					})
				;
            }
            $enrollable = $enrollable->first();

			$enrollable_event = false;
			if (isset($args['schedule_id'])) {
				$enrollable_event = \Models\Schedule
					::where('id', $args['schedule_id'])
					->where('schedules.status', true)
					->where('visible_learner', true)
                    ->where(function($query){
                        $query->where('enrole_any_learner', true)
                            ->orWhereIn('schedules.id',	\Models\ScheduleLink
							::select('schedule_id')
							->whereIn('type', ['users_queue'])
							->where('schedule_links.status', 1)
							->where('link_id', \APP\Auth::getUserId())
							->whereNull('deleted_at')
							->get());
                    })
					->where('parent_id', null)
					->whereNotIn(
						'schedules.id',
						\Models\ScheduleLink
							::select('schedule_id')
							->whereIn('type', ['users'])
							->where('schedule_links.status', 1)
							->where('link_id', \APP\Auth::getUserId())
							->whereNull('deleted_at')
							->get()
					)
					->whereIn(
						'schedules.id',
						\Models\ScheduleLink
							::select('schedule_id')
							->where('type', 'lesson')
							->where('link_id', $args["id"])
							->get()
					)
					->first();
			}

			if (
				//!$assigned &&
				!$enrollable &&
				!$enrollable_event
			) {
				return \APP\Tools::returnCode($request, $response, 403);
			}

		}
        $user = \APP\Auth::getUser();
        $makeAllEnrollable = false;
        $catelogList = '';
        if (
			\APP\Tools::getConfig('sharedClients') &&
			$user->company
		) {
            $makeAllEnrollable = $user->company->make_all_enrollable?1:0;
            $catelog = array_map(function($name) {
                    return "'" . addslashes($name) . "'";
            }, LearningModule::$catelog);
            $catelogList = implode(",", $catelog);
        }
		// Handle empty catalog list
		if (empty($catelogList)) {
			$catelogList = "''"; // Empty string in SQL
		}
		$learning = \Models\LearningModule
			::where('learning_modules.id', $args["id"])
			->with(["type" => function ($query) {
				$query
					->with("LearningModuleTypeParameter");
			}])
			->with("CredlyBadges")
			->with("competencies")
            ->with("companies")
			->with("editAccessCompanies")
			->with(["AssessmentCategories" => function ($query) {
				$query
					->with("questions");
			}])
			->with("AssessmentQuestions")
            ->with("LinkedSkills")
            ->with('linked')

			->with(["prerequisites" => function ($query) {
				$query->select("learning_modules.id", "name");
			}])
			->with(["LearningModuleEvidences" => function ($query) use ($user, $args) {
				$query
					->where(function ($query) use ($user, $args) {
						$query
							->whereRaw('user_id = ? ', [$user->id]) //see your own added evidence
							->orWhereRaw('manager = ?', [1]) // manager added this, so everyone must see this.
						;
					});
			}])
			->with(['LearningResults' => function ($query) use ($args) {
				$query->where('user_id', '=', \APP\Auth::getUserId())
					->where('learning_module_id', '=', $args["id"])
					->with('rejectRequestItems');
			}])

			->select(
				'*',
				DB::raw('DATE_FORMAT(learning_modules.created_at, "' . \APP\Tools::defaultDateFormatMYSQL() . ' %H:%i") as version_created_at'),
				DB::raw('DATE_FORMAT(learning_modules.updated_at, "' . \APP\Tools::defaultDateFormatMYSQL() . ' %H:%i") as version_updated_at'),
				DB::raw("
					CASE
						WHEN '{$makeAllEnrollable}' = '1' OR learning_modules.name IN ({$catelogList})
					THEN true
						ELSE learning_modules.self_enroll
					END AS self_enroll
				")
			)
		;

		if (\APP\Tools::getConfig('showExtraResourceFields_TDG')) {
			$learning
				->with(['TargetCatalogues' => function ($query) {
					$query
						->wherePivot('deleted_at', null)
						->where('target_catalogues.status', true);
				}])
				->with(['LearningModuleDeliveryProviderType' => function ($query) {
					$query
						->where('learning_module_delivery_provider_types.status', true);
				}])
				->with(['LearningModuleGroupDepartmentCode' => function ($query) {
					$query
						->where('learning_module_group_department_codes.status', true);
				}]);
		}

		if (
			isset($enrollable_event) &&
			$enrollable_event
		) {
			$learning = $learning
				->with(['ScheduleLessonLinks' => function ($query) use ($enrollable_event) {
					$query
						->select([
							'id',
							'schedule_id',
							'link_id'
						])
						->where('schedule_id', $enrollable_event->id)
						->with(['Schedule' => function ($query) {
							$query
								->select([
									'id',
									'name',
									'cost',
									'description',
									'category_id',
                                    'start_date',
                                    'end_date',
									'duration',
									'enrole_any_learner',
									DB::raw("CONCAT(DATE_FORMAT(start_date,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i'), '-', DATE_FORMAT(DATE_ADD(start_date, INTERVAL duration MINUTE),'%H:%i')) AS event_date_range")
								])
								->with('VenueDeatils')
								->with(['Waiting' => function ($query) {
									$query
											->select(
											'users.id',
											'fname',
											'lname',
											'role_id',
											'schedule_links.approved',
											'schedule_links.is_paid'
										)
										->where('users.status', true)
										->where('users.id', \APP\Auth::getUserId());
								}])
								->with(['WaitingForApproval' => function ($query) {
									$query
										->select(
											'users.id',
											'fname',
											'lname',
											'role_id',
											'schedule_links.approved'
										)
										->where('users.status', true)
										->where('users.id', \APP\Auth::getUserId());
								}]);
						}]);
				}]);
		}

		$learning = $learning
			->first();

		$totaldiscount = 0;
		$totaldiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
		$totaldiscount = ($totaldiscount > 100) ? 100 : $totaldiscount;
		// $learning->total_discount = $totaldiscount;
		if ($totaldiscount && $learning->cost) {
			$learning->discounted_cost = $learning->cost - round(($learning->cost * $totaldiscount) / 100, 2);
		} else {
			$learning->discounted_cost = $learning->cost;
		}
		foreach ($learning->ScheduleLessonLinks as $schedule_link) {
			$totaldiscount = 0;
			$totaldiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
			$totaldiscount = ($totaldiscount > 100) ? 100 : $totaldiscount;
			$scheduleCost = $schedule_link->Schedule->cost;
			if ($totaldiscount && $scheduleCost) {
				$schedule_link->Schedule->discounted_cost = $scheduleCost - round(($scheduleCost * $totaldiscount) / 100, 2);
			} else {
				$schedule_link->Schedule->discounted_cost = $scheduleCost;
			}
		}

		$learning->setAppends(['safe_thumbnail', 'safe_promo', 'highlight']);

		$response->getBody()->write(json_encode($learning));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));

	// Approve learning result
	$group->put('/{module_id:[0-9]+}/{user_id:[0-9]+}/{id:[0-9]+}/approve', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$learning_result = \Models\LearningResult
			::where("id", '=', $args["id"])
			->where("learning_module_id", "=", $args["module_id"])
			->firstOrFail();
		$learning_result->approved = true;
		$learning_result->is_paid =  empty($learning_result->Module->cost) ? null :$data["is_paid"];
		$learning_result->save();

		$learner_details = \Models\User::find($args["user_id"]);

		$learning_module = \Models\LearningModule
			::where("id", "=", $args["module_id"])
			->firstOrFail()
		;


		if ($learning_module->approval) {
			// Send e-mail to Manager that status changes to approval
			$template = \Models\EmailTemplate::getTemplate('Resource Approval');
			if ($template) {
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$learner_details->id];
				$email_queue->from = \APP\Auth::getUserId();
				$email_queue->custom_variables = json_encode([
					'USER_FNAME' => $learner_details->fname,
					'RESOURCE_NAME' => $learning_module->name,
					'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
					'RESOURCE_APPROVAL_LINK' => 'app/learner/resources/' . $args["module_id"],
					'REGARDS' => $GLOBALS["CONFIG"]->Regards,
				]);
				$email_queue->save();
			}
		}

		// If Resource is lesson, look up all resources and assign to learner as well!
		if ($learning_module->is_course) {
			$module_ids = \APP\Learning::getAllModuleIds([$learning_module->id]);
			$module_ids = \Models\LearningModule::returnValidResourcesIDs($module_ids);
			if (count($module_ids) > 0) {
				\Models\UserLearningModule::linkResources($learning_result->user_id, $module_ids, 'lesson ' . $learning_module->id . ' approved by manager, assigning linked resources.');
			}
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	// Reject Learning Result enrol request
	$group->put('/{module_id:[0-9]+}/{user_id:[0-9]+}/{id:[0-9]+}/enrol/reject', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$learning_result = \Models\LearningResult
			::where("id", '=', $args["id"])
			->where("learning_module_id", "=", $args["module_id"])
			//->where("user_id", "=", $args["user_id"])
			->firstOrFail();

		$learning_result->is_rejected = true;
		$learning_result->save();

		$learner_details = \Models\User::query()->find($args["user_id"]);

		$learning_module = \Models\LearningModule
			::where("id", "=", $args["module_id"])
			->firstOrFail();

		// add rejection reason by manager
		if (isset($data['rejection_reason_id'])) {
			$rejectionReason = new \Models\RejectRequestItems();
			$rejectionReason->user_id = \APP\Auth::getUserId();
			$rejectionReason->item()->associate($learning_result);
			$rejectionReason->rejection_reason_id = $data['rejection_reason_id'];
			$rejectionReason->rejection_reason = $data['rejetion_reason_comment'];
			$rejectionReason->save();
		}

		\Models\UserLearningModule::unlinkResources($args["user_id"], [$learning_result->learning_module_id], 'rejected enrol request');


		if ($learning_module->approval == "1") {
			// Send e-mail to Learner that request has rejected
			$template = \Models\EmailTemplate::query()
				->where('name', 'Resource Not Approved')
				->where('status', true)
				->first();

			if ($template && $template->id) {
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$learning_result->user_id];
				$email_queue->from = \APP\Auth::getUserId();
				$email_queue->custom_variables = json_encode([
					'USER_FNAME' => $learner_details->fname,
					'USER_LNAME' => $learner_details->lname,
					'RESOURCE_NAME' => $learning_module->name,
					'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
					'REJECT_REASON' => $rejectionReason->rejection_reason,
					'REGARDS' => $GLOBALS["CONFIG"]->Regards,
				]);
				$email_queue->save();
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->post('/addqa/{module_id:[0-9]+}/{user_id:[0-9]+}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$learning_result = \Models\LearningResult
			::where("id", '=', $args["id"])
			->with('User')
			->first()
		;

		if (!$learning_result) {
			return \APP\Tools::returnCode($request, $response, 404);
		}


		$learning_result->qa = $data["qa"];
		$learning_result->judgement_reason = $data['judgement_reason'];
		$learning_result->qa_date = \Carbon\Carbon::now();
		$learning_result->qa_created_by = \APP\Auth::getUserId();
		if ($learning_result->completion_status == 'completed') {
			$learning_result->completion_status = 'in progress';
		}
		$learning_result->save();

		if (
			$learning_result->qa != null &&
			$learning_result->judgement_reason != null
		) {
			$qc = new \Models\QualityControl;
			$qc->type = 'learning_results';
			$qc->type_id = $learning_result->id;
			$qc->user_id = $args["user_id"];
			$qc->qa_user_id = \APP\Auth::getUserId();
			$qc->qa = $learning_result->qa;
			$qc->qa_favorite = 0;
			$qc->judgement_reason = $learning_result->judgement_reason;
			$qc->created_at = $learning_result->qa_date ? $learning_result->qa_date : $learning_result->created_at;
			$qc->save();
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	// Update learning result
	// TODO: need a rework of this, in eventual future! Too much if/elses and cascading logic
	$group->put('/{module_id:[0-9]+}/{user_id:[0-9]+}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$learning_result = \Models\LearningResult
			::where("id", $args["id"])
			->where("learning_module_id", $args["module_id"])
			->where("user_id", $args["user_id"])
			->with('User')
			->first();

		if (!$learning_result) {
			return \APP\Tools::returnCode($request, $response, 404);
		}


		if (
			$learning_result->module->expiration_date &&
			$learning_result->module->expiration_date < \Carbon\Carbon::now()
		) {
			return \APP\Tools::returnCode($request, $response, 500, 'The learning resource has expired.  Contact your administrator to get this resource extended in order to change its status.');
		}

		if (
			$this->get('settings')['licensing']['isApprentix'] &&
			isset($data['qa_favorite']) &&
			\APP\Auth::isManager()
		) {
			$favorite = \Models\Favorite
				::where('user_id', \APP\Auth::getUserId())
				->where('relation_id', $learning_result->id)
				->where('type', 'learning-qa')
				->first();
			if ($data['qa_favorite']) {
				if (count($favorite) == 0) {
					$favorite = new \Models\Favorite;
					$favorite->user_id = \APP\Auth::getUserId();
					$favorite->relation_id = $learning_result->id;
					$favorite->type = 'learning-qa';
					$favorite->save();
				}
			} else {
				if ($favorite) {
					$favorite->delete();
				}
			}
		}

		if (!empty($data['comment'])) {
			$query = new \Models\LearningResultsComment;
			$query->comment_by_user_id = \APP\Auth::getUserId();
			$query->created_for_user_id = $args["user_id"];
			$query->learning_module_id = $args["module_id"];
      $query->learning_results_id = $args["id"];
      $query->comment_at = isset($data['sign_off_manager_at']) && !empty($data['sign_off_manager_at'])? \Carbon\Carbon::parse($data['sign_off_manager_at']):\Carbon\Carbon::now();
			$query->visible_learner = true;
			$query->comment = $data["comment"];
			$comment = $query->save();
            $comment = $query;
		}

		$learning_result->module->prerequisites = \APP\Learning::augmentPrerequisitesLearningResult(
			$args["user_id"],
			$learning_result->module->prerequisites
		);

		$hassAccessToOfflineTaskStatus =
			$args["user_id"] == \APP\Auth::getUserId() &&
			in_array($learning_result->module->type_id, [5, 6]) &&
			\APP\Auth::checkStructureAccess(['misc-permissions-change-offline-task-status'], 'update');

		if (isset($data["completed_at"])) {
			// if completed_at is passed, but is empty, nullify completed date, in case status is changed from complete to something else.
			if ($data["completed_at"] != null) {
				$data["completed_at"] = \Carbon\Carbon::parse($data["completed_at"]);
			} else {
				$data["completed_at"] = null;
			}
		} else {
			$data["completed_at"] = \Carbon\Carbon::now();
		}
		// If grade is passed, update it!
		if (
			isset($data["grade"]) &&
			$data["grade"] &&
			\APP\Auth::isManagerOf($args["user_id"]) ||
			\APP\Auth::isAdmin() ||
			\APP\Auth::accessAllLearners()
		) {
			$learning_result->grade = $data["grade"];
		}


		// Toggle off_the_job_training
		if (
			$learning_result->user_id == \APP\Auth::getUserId() ||
			\APP\Auth::isManagerOf($learning_result->user_id) ||
			\APP\Auth::isAdmin() ||
			\APP\Auth::accessAllLearners()
		) {
			$off_the_job_training = false;
			if (
				isset($data['off_the_job_training']) &&
				$data['off_the_job_training']
			) {
				$off_the_job_training = true;
			}
			$learning_result->off_the_job_training = $off_the_job_training;
		}

		// If learning result is updated not by learner and status is changed, set it as action for learner

		if (
			!(
				$data["completion_status"] == 'completed' &&
				$learning_result->completion_status == 'completed'
			) &&
			!\APP\Auth::isLearner()
		) {
			$learning_result->learner_action = true;
			$learning_result->learner_action_date = \Carbon\Carbon::now();
		}


		if (
			(
				$args["user_id"] == \APP\Auth::getUserId() &&
				\APP\Auth::isManager()
			) ||
			\APP\Auth::isManagerOf($args["user_id"]) ||
			\APP\Auth::isProcessingManagerOf($learning_result->id) ||
			\APP\Auth::isAdmin() ||
			\APP\Auth::accessAllLearners() ||
			$hassAccessToOfflineTaskStatus
		) {
			if (
				isset($data["module"]) &&
				$data["module"]["type_id"] == 1
			) {
				if (!isset($data["score"])) {
					if (!empty($learning_result->module->material->min_passing_percentage)) {
						$data["score"] = $learning_result->module->material->min_passing_percentage;
					} else {
						$data["score"] = 100;
					}
				}

				$course = \APP\Course::get($args["module_id"]);
				$course->updateUserScormRecord(
					$args["user_id"],
					$data["completion_status"],
					$learning_result->completion_status,
					$data["score"],
					$data["completed_at"],

					// Change completed_at date if you are admin and agree to change completed at date.
					(\APP\Auth::isAdmin() || \APP\Auth::accessAllLearners()) && isset($data['updateComplete']) && $data['updateComplete'] && $data["completed_at"] != null
				);
				$course->updateUserResult($args["user_id"], $this->get('settings')['licensing']['isApprentix']);
			} else {
				if (isset($data["completion_status"])) {
					$learning_result->completion_status = $data["completion_status"];
				}
				if ($learning_result->completion_status == "completed") {
					$learning_result->completed_at = $data["completed_at"];
				}

				// Any other resource can have score also
				if (isset($data["score"])) {
					$learning_result->score = $data["score"];
				}

				if (
					\APP\Auth::isManager() ||
					\APP\Auth::isAdmin() ||
					\APP\Auth::accessAllLearners()
				) {
					$learning = LearningResult::where('id', $learning_result->id)->first();
						$learningStandard = ApprenticeshipIssuesLearningModules::with('Issue.IssueCategory.Standard')->where('learning_modules_id',$learning->learning_module_id)->first();
					$repeatation_period = null;
					if (
						$learningStandard &&
						$learningStandard->Issue &&
						$learningStandard->Issue->IssueCategory &&
						$learningStandard->Issue->IssueCategory->Standard
					) {
						$standard =  $learningStandard->Issue->IssueCategory->Standard;
						$repeatation_period = $standard->repetition_period; // If the programme/skill repatation period exist then use that repetition period
							if(!$repeatation_period) // If its empty
						{
						$repeatation_period = $standard->default_skill_repetition_period; //Use default repetitation period
						}
					}
					if (!$repeatation_period) {
							$repeatation_period =$learning->Module->repetition_period;
					}
					$due_date = $learning->due_at;

					// Update date when manager signed off
						$learning_result->sign_off_manager_at = isset($data['sign_off_manager_at']) && !empty($data['sign_off_manager_at'])? \Carbon\Carbon::parse($data['sign_off_manager_at']):\Carbon\Carbon::now();
					if ($repeatation_period) {
						$due_date = Carbon::parse($learning_result->sign_off_manager_at)->addMonths($repeatation_period);
					}
					$learning_result->due_at = $due_date;

					if (
						(
							!$learning_result->sign_off_trainee ||
							!$learning_result->sign_off_manager
						) && (
							isset($data["sign_off_trainee"]) &&
							isset($data["sign_off_manager"])
						)
					) {
						$learning_result->sign_off_manager_by = \APP\Auth::getUserId();
					}

					// for the task scor-3425 - update signoff date also for signed off skills
					if (isset($data["sign_off_manager"])) {
						$learning_result->sign_off_manager_at = $data['sign_off_manager_at'];
					}

					if (isset($data["sign_off_trainee"])) {
						$learning_result->sign_off_trainee = $data["sign_off_trainee"];
					}
					if (isset($data["sign_off_manager"])) {
						$learning_result->sign_off_manager = $data["sign_off_manager"];
					}

					// If siggned off by manager/admin, send email to trainee


					if (
						$learning_result->sign_off_trainee == 1 &&
						$learning_result->sign_off_manager == 1 &&
						isset($data["sign_off_refused"]) &&
						$data["sign_off_refused"] == false
					) {
						// Perform check if SMCR and resource in F&P category that is hidden to user and resource is upload, then do not send e-mail to user!
						$send_email = \APP\Smcr::sendLearnerEmail($learning_result);

						// Send out "Learning Resource Signed Off by Coach" to learner
						$template = \Models\EmailTemplate
							::where('name', '%%learning_resource%% Signed Off by %%manager%%')
							->where('status', true)
							->first();
						if (
							$template &&
							$template->id &&
							$send_email &&
							$learning_result->module->track_progress
						) {
							$email_queue = new \Models\EmailQueue;
							$email_queue->email_template_id = $template->id;
							$email_queue->learning_module_id = $args["module_id"];
							$email_queue->recipients = [intval($args["user_id"])];
							$email_queue->from = \APP\Auth::getUserId();
							$email_queue->save();
						}

						//Send e-mail to to QA if there are link between QA and this learning result
						$qa_learning_resource = \Models\QaLearningResult
							::where('learning_result_id', $learning_result->id)
							->first();
						if ($qa_learning_resource) {
							$qa_template = \Models\EmailTemplate
								::where('name', 'QA task completed and re-submitted for Quality Approval')
								->where('status', true)
								->first();

							$coach = \APP\Auth::getUser();
							$qa_user = \Models\User::find($qa_learning_resource->qa_id);

							if (
								$qa_template &&
								$qa_user &&
								$qa_user->status &&
								$learning_result->module->track_progress
							) {
								$email_queue = new \Models\EmailQueue;
								$email_queue->email_template_id = $qa_template->id;
								$email_queue->learning_module_id = $args["module_id"];
								$email_queue->recipients = [$qa_learning_resource->qa_id];
								$email_queue->from = $coach->id;
								$email_queue->custom_variables = json_encode([
									'COACH_NAME' => $coach->fname . ' ' . $coach->lname,
									'QA_LEARNER_NAME' => $learning_result->user->fname . ' ' . $learning_result->user->lname,
								]);
								$email_queue->save();
							}
						}
					}
				}


				if (
					isset($data["sign_off_refused"]) &&
					$data["sign_off_refused"]
				) {
					$learning_result->manager_refused_time = \Carbon\Carbon::now();
					$learning_result->manager_refused_by = \APP\Auth::getUserId();
					$learning_result->manager_refused_comment = null;
					$learning_result->completed_at = null;
					$learning_result->completion_status = $learning_result->module->is_skill != 1 ? 'in progress' : 'not attempted';
					$learning_result->sign_off_manager_by = null;
					$learning_result->sign_off_manager_at = null;
					$learning_result->completed_by = null;
					$learning_result->completed_version = null;
					$learningStandard = ApprenticeshipIssuesLearningModules::with('Issue.IssueCategory.Standard')->where('learning_modules_id', $learning_result->learning_module_id)->first();
					$repeatation_period = null;
					if (
						$learningStandard &&
						$learningStandard->Issue &&
						$learningStandard->Issue->IssueCategory &&
						$learningStandard->Issue->IssueCategory->Standard
					) {
						$standard =  $learningStandard->Issue->IssueCategory->Standard;
						$repeatation_period = $standard->repetition_period; // If the programme/skill repatation period exist then use that repetition period
						if (!$repeatation_period) // If its empty
						{
							$repeatation_period = $standard->default_skill_repetition_period; //Use default repetitation period
						}
					}
					if (!$repeatation_period) {
							$repeatation_period = $learning_result->Module->repetition_period;
					}
					if ($repeatation_period) {
						$learning_result->due_at = Carbon::parse($learning_result->due_at)->subMonths($repeatation_period);
					}

					$send_email = \APP\Smcr::sendLearnerEmail($learning_result);

					// Send e-mail to learner that resource is refused.
					$template = \Models\EmailTemplate::where('name', 'Learning Resource Needs Attention')
						->where('status', true)
						->first();
					if (
						$template &&
						$template->id &&
						$send_email &&
						$learning_result->module->track_progress
					) {
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->learning_module_id = $args["module_id"];
						$email_queue->recipients = [intval($args["user_id"])];
						$email_queue->from = \APP\Auth::getUserId();
						$email_queue->save();
					}
				}

				// Update all user's timing if standards are enabled
				if (
					$this->get('settings')['licensing']['isApprentix'] &&
					$learning_result->isDirty('completion_status')
				) {
					$learning_result->save();
					$standard_ids = \Models\LearningModule
						::Standards($args['module_id'], $args['user_id'], false, false)
						->pluck('id')
						->toArray()
					;
					if (count($standard_ids) > 0) {
						\Models\ApprenticeshipStandardUser::updateUserProgress($args["user_id"], $standard_ids);
					}
				}
			}

			// Adding back this one, if completed_at is forced, do it to learning result too
			if (
				\APP\Auth::isAdmin() &&
				isset($data['updateComplete']) &&
				$data['updateComplete'] &&
				$data["completed_at"] != null
			) {
				$learning_result->completed_at = $data["completed_at"];
				$learning_result->completion_status = $data["completion_status"];
				$learning_result->score = $data["score"];
			}

			// If QA update just qa status
			if (
				\APP\Auth::isQa() &&
				isset($data["qa"])
			) {
				$learning_result->qa = $data["qa"];
				// if rejected, set status to "In progress"
				if ($learning_result->qa == 'Rejected') {
					$learning_result->completed_at = null;
					$learning_result->completion_status = 'in progress';
					$learning_result->sign_off_trainee = false;
					$learning_result->sign_off_manager = false;


					// Send e-mail to learners managers that this was rejected!
					// "Work rejected by QA"
					$template = \Models\EmailTemplate
						::where('name', 'Actions required following Quality Review')
						->where('status', true)
						->first();
					if (
						$template &&
						$template->id
					) {

						// Get all managers that needs notification
						$manager_ids = [];
						foreach ($learning_result->user->managers as $key => $manager) {
							if (
								!$manager->role->email_disable_manager_notifications &&
								$manager->status
							) {
								$manager_ids[] = $manager->id;
							}
						}

						if (
							count($manager_ids) > 0 &&
							$learning_result->module->track_progress
						) {
							$email_queue = new \Models\EmailQueue;
							$email_queue->email_template_id = $template->id;
							$email_queue->recipients = $manager_ids;
							$email_queue->from = \APP\Auth::getUserId();
							$email_queue->custom_variables = json_encode([
								'REJECTED_WORK' => $learning_result->module->name,
								'REJECTED_LEARNER' => $learning_result->user->fname . ' ' . $learning_result->user->lname,
								'REJECTED_WORK_ID' => $learning_result->module->id,
							]);
							$email_queue->save();
						}
					}
				}
				if (
					$learning_result->qa != $data['qa'] ||
					$learning_result->judgement_reason != $data['judgement_reason']
				) {
					if (
						$learning_result->qa != null &&
						$learning_result->judgement_reason != null
					) {

						$learning_result->qa_date = \Carbon\Carbon::now();
						$learning_result->qa_created_by = \APP\Auth::getUserId();
					}
				}

				// Add this user to qa-learning_results table
				\Models\QaLearningResult::firstOrCreate(
					[
						'qa_id' => \APP\Auth::getUserId(),
						'learning_result_id' => $learning_result->id
					]
				);

				$learning_result->judgement_reason = $data['judgement_reason'];
				if ($learning_result->judgement_reason) {
					if (!$learning_result->qa_date) {
						$learning_result->qa_date = \Carbon\Carbon::now();
					}
					if (
						!$learning_result->qa_created_by &&
						\APP\Auth::getUserId()
					) {
						$learning_result->qa_created_by = \APP\Auth::getUserId();
					}
				}
			}




			$learning_result->processing_manager_id = isset($data['processing_manager_id']) ? $data['processing_manager_id'] : null;
			$learning_result->save();
		}

		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('learning_results', $learning_result->id, $field_name, $value);
			}
		}

		if (Tools::getConfig('SignOffAutomatedSkillsMonitoring')) {
		$standard_ids = LearningModule::Standards($args['module_id'],$args['user_id'],false,false)->pluck('id');
			$standards = ApprenticeshipStandard
		::whereIn('id',$standard_ids)
		->whereDoesntHave('Issues.Modules.LearningResults',function($query) use($args) {
				$query
						->where('user_id', $args['user_id'])->where('completion_status', '!=', 'completed');
				})
		->whereDoesntHave('SkillSignoffLogs',function($query) use($args) {
				$query
						->where('user_id', $args['user_id']);
				})
				->get();

		foreach($standards as $standard){
		SignoffLog::create(['type'=>'Skills Monitoring','type_id'=>$standard->id,'user_id'=>$args['user_id'],'comment'=>'','sign_off_user_id'=>Auth::getUserId(),'sign_off_role_id'=>Auth::roleId(),'status'=>'completed','sign_off_date'=>\Carbon\Carbon::now()]);
			}
		}
        //if isset $comment then add comment id in response
        if (isset($comment) && !empty($comment)){
            $response->getBody()->write(json_encode(['comment_id' => $comment->id]));
        }

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	// Update/add custom completion date for specific resource assigned to user
	// Used for Apprentix where resource is assigned to issue
	$group->put('/update-due', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();


		if (
			\APP\Auth::isManagerOf($data["user_id"]) ||
			\APP\Auth::isAdmin() ||
			\APP\Auth::accessAllLearners()
		) {

			$learning_result = \Models\LearningResult
				::where("id", $data["id"])
				->where("user_id", $data["user_id"])
				->where("refreshed", false)
				->with(["module" => function ($query) {
					$query
						->with(["modules" => function ($query) {
							$query
								->where('status', true);
						}]);
				}])
				->first();

			if (!$learning_result) {
				return \APP\Tools::returnCode($request, $response, 404);
			}

			$learning_result->completion_date_custom = \Carbon\Carbon::parse($data["due_at"]);

			if (isset($data['standard_start_at'])) {
				$learning_result->completion_date_custom_days = \Carbon\Carbon::parse($data['standard_start_at'])->diffInDays($learning_result->completion_date_custom, false);
			}

			$learning_result->save();

			// If resource is course, then update completion date to all relevant learning results
			if ($learning_result->module->is_course == 1) {
				foreach ($learning_result->module->modules as $key => $module) {

					$learning_result = \Models\LearningResult
						::where("user_id", $data["user_id"])
						->where('learning_module_id', $module->id)
						->where("refreshed", false)
						->first();
					if ($learning_result) {
						$learning_result->completion_date_custom = \Carbon\Carbon::parse($data["due_at"]);

						if (isset($data['standard_start_at'])) {
							$learning_result->completion_date_custom_days = \Carbon\Carbon::parse($data['standard_start_at'])->diffInDays($learning_result->completion_date_custom, false);
						}

						$learning_result->save();
					}
				}
			}

			exit(0);
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	// remove custom completion date
	// Used in Apprentix
	$group->put('/remove-due', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (
			\APP\Auth::isManagerOf($data["user_id"])
			|| \APP\Auth::isAdmin()
			|| \APP\Auth::accessAllLearners()
		) {

			$learning_result = \Models\LearningResult
				::where("id", '=', $data["id"])
				->where("user_id", "=", $data["user_id"])
				->first();
			if ($learning_result) {
				$learning_result->completion_date_custom = null;
				$learning_result->save();
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->delete("/{module_id:[0-9]+}/{user_id:[0-9]+}/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		if (
			\APP\Auth::isManager() ||
			\APP\Auth::isAdmin() ||
			(
				\APP\Auth::accessAllLearners() &&
				\APP\Auth::isAdminInterface()
			)
		) {

			$learning_result = \Models\LearningResult::where('id', $args['id'])->first();
			\Models\UserLearningModule::unlinkResources($args["user_id"], [$learning_result->learning_module_id], 'resource deleted manually');
			$learningEvidence = \Models\LearningModuleEvidence::where(['manager' => 0, 'learning_modules_id' => $learning_result->learning_module_id, 'user_id' => $args['user_id']])->get();
			foreach ($learningEvidence as $evidence) {
				$path=$this->get('settings')["LMSEvidencePath"].$evidence->hash.".".$evidence->extension;
				if (is_file($path)) {
					unlink($path);
				}
				$evidence->delete();
			}
			\Models\LearningModule
				::where('created_in_learner_interface', 1)
				->where('id', $learning_result->learning_module_id)
				->delete()
			;
			$learning_result->delete();

			$response->getBody()->write(json_encode(["status" =>true]));
			return $response->withHeader('Content-Type', 'application/json');
		}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'disable'));

	// Get learning result
	$group->get('/{module_id:[0-9]+}/{user_id:[0-9]+}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$hassAccessToOfflineTaskStatus = false;

		if (
			$args["user_id"] == \APP\Auth::getUserId() ||
			\APP\Auth::isManager() ||
			\APP\Auth::isAdmin() ||
			(
				\APP\Auth::accessAllLearners() &&
				\APP\Auth::isAdminInterface()
			)
		) {
			$learning_result = \Models\LearningResult
				::where("id", $args["id"])
				->with('survey')
				->with(["module" => function ($query) use ($args) {
					$query
						//->with('formworkflow.form_workflow_templates.templateForms')
						->with(["userForms" => function ($query) {
							$query->with('module')->with("Form")
								->select("user_forms.id AS user_form_id", "user_forms.*", "forms.name AS name")
								->join("forms", "forms.id", "=", "user_forms.form_id")
								->where('forms.system_form', '0')
								->where('forms.status', '1')
								->where('user_id', \APP\Auth::getUserId());
						}])
						->with(['userProgrammeForms' => function ($query) {
							$query
								->with('module')
								->with("Form")
								->select("user_forms.id AS user_form_id", "user_forms.*", "forms.name AS name")
								->join("forms", "forms.id", "=", "user_forms.form_id")
								->where('forms.system_form', '0')
								->where('forms.status', '1')
								->where('user_forms.user_id', \APP\Auth::getUserId());
						}])
						->with(["type" => function ($query) {
							$query
								->withoutGlobalScope('type_filter')
								->with("LearningModuleTypeParameter");
						}])
						->with("competencies", "category", "FPCategory", "provider", "EvidenceType", "EventType")
                        ->with("linked")
						->with("files") // for use mostly by lessons
						->with(['modules' => function ($query) use ($args) {
							$query = \APP\Learning::simpleLearningResult($query, $args);
							$query = $query
								->orderBy('learning_course_modules.id', 'asc');
						}])
						// Attach ID's of lessons this resource is part of and user is assigned to.
						->with(['Courses' => function ($query) use ($args) {
							$query = $query
								->select('learning_modules.id')
								->where('status', 1)
								->whereIn(
									'learning_modules.id',
									\Models\UserLearningModule
										::select('learning_module_id')
										->where('user_learning_modules.user_id', $args["user_id"])
										->whereNull('user_learning_modules.deleted_at')
										->get()
								);
						}])
						->with(['ScheduleLessonLinks' => function ($query) use ($args) {
							$query
								->select([
									'id',
									'schedule_id',
									'link_id'
								])
								->with(['Schedule' => function ($query) use ($args) {
									$query
										->select([
											'id',
											'name',
											'description',
											'category_id',
											'start_date',
											'duration',
											'end_date',
											'all_day_event',
											'enrole_any_learner',
											'approval',
											'cost',
											'type',
										])
										->with(['ResourceLinks' => function ($query) use ($args) {
											$query
												->with(['resource' => function ($query) use ($args) {
													$query = \APP\Learning::simpleLearningResult($query, $args);
												}])
												// check if resource is self enrollable or is assigned to user!
												->whereHas('resource', function ($query) use ($args) {
													$query = $query
														->where('status', true)
														->where('visible_learner', true)
														->where(function ($query) use ($args) {
															$query = $query
																->where('self_enroll', true)
																->orWhere(function ($query) use ($args) {
																	$query = $query
																		->whereHas('Users', function ($query) use ($args) {
																			$query = $query
																				->where('users.id', $args["user_id"]);
																		});
																});
														});
												});
										}])
										->with(['UserLink' => function ($query) use ($args) {
											$query
												->where('link_id', $args["user_id"]);
										}])
										->with(['Files' => function ($query) {
											$query
												->where('status', true)
												->with(['AddedBy' => function ($query) {
													$query
														->select(
															'id',
															'fname',
															'lname',
															'role_id'
														)
														->with(['role' => function ($query) {
															$query
																->select(
																	'id',
																	'name'
																);
														}]);
												}]);
										}])
										->with(['Comments' => function ($query) {
											$query
												->where('status', true)
												->where('visible_learner', true)
												->with(['AddedBy' => function ($query) {
													$query
														->select(
															'id',
															'fname',
															'lname',
															'role_id',
															'image'
														)
														->with('role')
														->with(['role' => function ($query) {
															$query
																->select(
																	'id',
																	'name'
																);
														}]);
												}]);
										}])
										/*Forum Added by managers*/
										->with(['forum' => function ($query) {
											$query
												->where('visible_learner', true)
												->select('id', 'schedule_id', 'added_by', 'visible_learner', 'created_at', 'updated_at')
												->with(['AddedBy' => function ($query) {
													$query
														->select(
															'id',
															'fname',
															'lname',
															'role_id',
															'image'
														)
														->with('role');
												}])
												->with(['topics' => function ($query) {
													$query
														->select('id', 'forum_id', 'added_by', 'created_at', 'updated_at', 'name', 'content')
														->with(['AddedBy' => function ($query) {
															$query
																->select(
																	'id',
																	'fname',
																	'lname',
																	'role_id',
																	'image'
																)
																->with('role');
														}])
														->first();
												}]);
										}])/*Forum ends here*/
										->with('VenueDeatils');
								}])
								->whereIn(
									'schedule_id',
									\Models\ScheduleLink
										::select('schedule_id')
										->where('type', 'users')
										->where('link_id', $args["user_id"])
										->where('status', true)
										->get()
								)
								->where('status', true);
						}])
						->with(['prerequisites' => function ($query) use ($args) {
							$query = \APP\Learning::simpleLearningResult($query, $args);
						}])
						->with(['LearningModuleEvidences' => function ($query) use ($args) {
							$query
								->where('status', true)
								->where(function ($query) use ($args) {
									$query = $query
										->where('user_id', $args["user_id"])
										->orWhere('manager', 1);
								})
								->orderBy('created_at', 'desc')
								->with(['user' => function ($query) {
									$query
										->select('id', 'fname', 'lname');
								}])
								->with(['AddedBy' => function ($query) {
									$query
										->select('id', 'fname', 'lname');
								}]);
						}])
						->with(['meetings' => function ($query) use ($args) {
							$query
								->where('user_id', $args["user_id"])
								->where('status', 1)
								->with(['createdby' => function ($query) {
									$query
										->select('id', 'fname', 'lname');
								}])
								->with(['approvedby' => function ($query) {
									$query
										->select('id', 'fname', 'lname');
								}]);
						}])
						->select(
							'learning_modules.*',
							DB::raw('
								CASE
									WHEN learning_modules.expiration_date < NOW()
									THEN 1
									ELSE 0
								END
								as expired
							')
						);
					if (
						\APP\Tools::getConfig('enableFeedback') &&
						\APP\Tools::getConfig('enableFeedbackList')
					) {
						$query
							->with(["feedback" => function ($query) {
								$query
									->where('status', true)
									->where('rating', '>', 0)
									->orderBy('created_at', 'desc')
									->whereHas('module', function ($query) {
										$query
											->where('learning_modules.type_id', '!=', 7);
									});
								if (!\APP\Auth::isLearner()) {
									$query
										->with(['user' => function ($query) {
											$query
												->select('id', 'fname', 'lname');
										}]);
								}
							}]);
					}
					if (!\APP\Tools::getConfig('showUnassignedDisabledLearning')) {
						$query
							->where('status', true)
						;
					}
				}])
				->with(["user" => function ($query) {
					$query->select('id', 'fname', 'lname', 'e_signature');
				}])
				->with(["createdby" => function ($query) {
					$query->select('id', 'fname', 'lname');
				}])
				->with(['QualityControl' => function ($query) {
					$query
						->select('*', DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"))
						->with(["qaUser" => function ($query) {
							$query->select('id', 'fname', 'lname');
						}])->orderBy('quality_controls.created_at','desc')
						->get()
					;
				}])
				->with(["refusedby" => function ($query) {
					$query->select('id', 'fname', 'lname');
				}])
				->with(["SignOffManagerBy" => function ($query) {
					$query->select('id', 'fname', 'lname', 'e_signature');
				}])
				->with(["comments" => function ($query) use ($args) {
					$query
						->where('created_for_user_id', $args["user_id"])
						->where('status', true)
						->with(["createdby" => function ($query) {
							$query
								->select("id", "fname", "lname", "role_id")
								->with('role');
						}])
						->with(["files" => function ($query) {
						}])
                        ->with(["certificateFiles" => function ($query) {
						}]);
					if (\APP\Auth::isLearner()) {
						$query = $query
							->where('visible_learner', true);
					}
				}])
				->with(["QaCreatedBy" => function ($query) {
					$query->select('id', 'fname', 'lname');
				}])
				->with('rejectRequestItems')
				->where("learning_module_id", "=", $args["module_id"])
				->where("user_id", "=", $args["user_id"])
				//->where("refreshed", "=", 0)
				->select('*', DB::raw("DATE_FORMAT(learning_results.qa_date,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS qa_created_at_uk"),
					DB::raw('(SELECT COUNT(*) FROM learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1) AS refreshed_count'),
					DB::raw('(SELECT lr.created_at FROM learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 order by lr.id DESC limit 1) AS previous_created_at'),
					DB::raw('(SELECT lr.sign_off_manager_at FROM learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 order by lr.id DESC limit 1) AS previous_completed_at')
				)
				// ->select(DB::raw('(SELECT COUNT(*) FROM learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1) AS refreshed_count'))
				->first();
			if (
				$learning_result &&
				(
					\APP\Auth::isManagerOf($args["user_id"]) ||
					\APP\Auth::accessAllLearners()
				)
			) {
				$learning_result->managed_by = true;
			}

			/*
			if (isset($learning_result->module->prerequisites)) {
				$learning_result->module->prerequisites = \APP\Learning::augmentPrerequisitesLearningResult(
					$args["user_id"],
					$learning_result->module->prerequisites
				);
			}
			*/

			// In case of "Learning Lesson" where "modules" are populated, also want to know learning_result
			// TODO: assign "RequiringCourseName" property to modules, I don't know how, yet!
			/*
			if (isset($learning_result->module->modules)) {
				$learning_result->module->modules = \APP\Learning::augmentPrerequisitesLearningResult(
					$args["user_id"],
					$learning_result->module->modules
				);
			}
			*/

			if (isset($learning_result->module)) {
				$totaldiscount = 0;
				$totaldiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
				$totaldiscount = ($totaldiscount > 100) ? 100 : $totaldiscount;
				$moduleCost = $learning_result->module->cost;

				if (isset($learning_result->module->type->slug) && $learning_result->module->type->slug == 'e_learning')
				{
					if (
						(isset($learning_result->module->material->course_complete_status)
						&& (int)$learning_result->module->material->course_complete_status == 1)
						&&
						(isset($learning_result->module->material->max_quiz_fails)
						&& (int)$learning_result->module->material->max_quiz_fails > 0)
					)
					{
						$attemptCount = \Models\Scorm\Track::
							where("scormid", $learning_result->module->id)
							->where("userid", $args["user_id"])
							->max("attempt")
						;
						$learning_result->attempt = $attemptCount - $learning_result->attempts_at_refresh;
						$learning_result->attempts_left = (int)$learning_result->module->material->max_quiz_fails - $learning_result->attempt;
					}
					else $learning_result->attempts_left = 99;
				}

				if ($totaldiscount && $moduleCost) {
					$learning_result->module->discounted_cost = $moduleCost - round(($moduleCost * $totaldiscount) / 100, 2);
				} else {
					$learning_result->module->discounted_cost =  $moduleCost;
				}
				$learning_result->module->required_modules = \APP\Learning::getCourseRequiredModules(
					$args["user_id"],
					$learning_result->module,
					true // course status must be enabled to get list.
				);
				foreach ($learning_result->module->required_modules as $result) {
					$result->setAppends(['safe_thumbnail']);
					// Also, check if this module can be launched, expensive
					$result = \APP\Learning::canResourceBeLaunched($result, $args);
				}

				foreach ($learning_result->module->prerequisites as $prerequisite) {
					$prerequisite->setAppends(['safe_thumbnail']);
					// Also, check if this module can be launched, expensive
					$prerequisite = \APP\Learning::canResourceBeLaunched($prerequisite, $args);
				}

				if (isset($learning_result->module->ScheduleLessonLinks)) {
					foreach ($learning_result->module->ScheduleLessonLinks as $key => $schedule_lesson_link) {
						$totaldiscount = 0;
						$totaldiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
						$totaldiscount = ($totaldiscount > 100) ? 100 : $totaldiscount;
						$scheduleCost = $schedule_lesson_link->Schedule->cost;
						if ($totaldiscount && $scheduleCost) {
							$schedule_lesson_link->Schedule->discounted_cost = $scheduleCost - round(($scheduleCost * $totaldiscount) / 100, 2);
						} else {
							$schedule_lesson_link->Schedule->discounted_cost = $scheduleCost;
						}
						foreach ($schedule_lesson_link->Schedule->ResourceLinks as $key => $resource_link) {
							if ($resource_link->resource) {
								$resource_link->resource->setAppends(['safe_thumbnail', 'safe_promo']);
								$resource_link->resource = \APP\Learning::canResourceBeLaunched($resource_link->resource, $args);
							}
						}
					}
				}
			}

			if (isset($learning_result->module->modules)) {
				foreach ($learning_result->module->modules as $module) {
					$module->setAppends(['safe_thumbnail']);

					$module->required_modules = \APP\Learning::getCourseRequiredModules(
						$args["user_id"],
						$module,
						true // course status must be enabled to get list.
					);

					if (
						isset($module->type->slug) &&
						$module->type->slug == 'e_learning' &&
						$module->LearningResult
					) {
						if (
							(isset($module->material->course_complete_status)
							&& (int)$module->material->course_complete_status == 1)
							&&
							(isset($module->material->max_quiz_fails)
							&& (int)$module->material->max_quiz_fails > 0)
						) {
							$attemptCount = \Models\Scorm\Track::
								where("scormid", $module->id)
								->where("userid", $args["user_id"])
								->max("attempt")
							;
							$module->LearningResult->attempt = $attemptCount - $module->LearningResult->attempts_at_refresh;
							$module->LearningResult->attempts_left = (int)$module->material->max_quiz_fails - $module->LearningResult->attempt;
						}else {
							$module->LearningResult->attempts_left = 99;
						}
					}

					// Also, check if this module can be launched, expensive
					$module = \APP\Learning::canResourceBeLaunched($module, $args);
				}
			}


			$hassAccessToOfflineTaskStatus = false;
			if (
				$learning_result &&
				isset($learning_result->module) &&
				isset($learning_result->module->type_id)
			) {
				$hassAccessToOfflineTaskStatus =
					$args["user_id"] == \APP\Auth::getUserId() &&
					in_array($learning_result->module->type_id, [5, 6]) &&
					\APP\Auth::checkStructureAccess(['misc-permissions-change-offline-task-status'], 'update');
			}

			if (
				$this->get('settings')['licensing']['isApprentix'] &&
				\APP\Auth::isManager() &&
				$learning_result
			) {
				$favorite = \Models\Favorite
					::where('user_id', \APP\Auth::getUserId())
					->where('relation_id', $learning_result->id)
					->where('type', 'learning-qa')
					->count();
				$learning_result->qa_favorite = false;
				if ($favorite > 0) {
					$learning_result->qa_favorite = true;
				}
			}

			// Append safe thumbnail!
			if (isset($learning_result->module)) {
				$learning_result->module->setAppends(['safe_thumbnail', 'safe_promo']);
			}
		} else {
			$learning_result = null;
		}

		if ($learning_result) {
			\Models\TableExtension::returnAllFields('learning_results', $learning_result->id, $learning_result);
		}

		$is_recommended = 0;
		if (\APP\Tools::getConfig("showRecommendations")) {
			$recommendation = \Models\UserLearningRecommendations
				::where("user_id", \APP\Auth::getUserId())
				->where("learning_module_id", "=", $learning_result->learning_module_id)
				->first();
			if ($recommendation) {
				$is_recommended = $recommendation->is_dismissed ? 0 : 1;
			}
		}

		/*KSB's in details section*/
		$current_user_id = \APP\Auth::getUserId();
		$userEvidences = \Models\ApprenticeshipIssues
			::with(["IssueCategory" => function ($query) {
				$query->with(["Standard" => function ($query) {
				}]);
			}])
			->where(function ($query) use ($current_user_id, $args) {
				$query
					->whereHas('attachedevidences', function ($query) use ($current_user_id, $args) {
						$query
							->where('user_id', $current_user_id)
							->where('learning_modules_id', $args["module_id"])
							->whereHas('module', function ($query) {
								$query
									->where('status', true);
							});
					});
			})
			->where('status', true)
			->get();

			if ($learning_result && is_object($learning_result)) {
				$learning_result->user_evidence = $userEvidences;
			}

		/*KSB's in details section*/

		$response
			->getBody()
			->write(json_encode([
				"learning_result" => $learning_result,
				"is_recommended" => $is_recommended,
				"hassAccessToOfflineTaskStatus" => $hassAccessToOfflineTaskStatus,
				"accessAllLearners" => \APP\Auth::accessAllLearners()
			]))
		;
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results', 'lessons-and-learning-resources'], 'select'));

	$group->put('/dismiss-recommendation/{module_id:[0-9]+}', function (Request $request, Response $response, $args) {

		$recm = \Models\UserLearningRecommendations
			::where("user_id", \APP\Auth::getUserId())
			->where("learning_module_id", "=", $args["module_id"])
			->first();

		if ($recm) {
			$recm->is_dismissed = 1;
			$recm->save();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results', 'lessons-and-learning-resources'], 'select'));

	// Add scorm file to be used for learning resource
	$group->post('/module/uploadscormfile', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		if (!(isset($data['token']) && strlen(Tools::getConfig('openelmsAiLinkToken')) > 0 &&
			$data['token'] == base64_encode(crypt((string)floor(time() * 0.01), Tools::getConfig('openelmsAiLinkToken'))))
			&& !(Auth::checkSession() && Auth::checkStructureAccess('library-learning-resources-and-lessons', 'update')))
		{
			return Tools::returnCode($request, $response, 403);
		}

		if ($request->getUploadedFiles()['zipfile']) {
			$uploadedFile = $request->getUploadedFiles()['zipfile'];
			$scormZipFileName = preg_replace('/[^a-zA-Z0-9]/', '_', $data["name"]) . '.zip';

			if ($uploadedFile->getError() !== UPLOAD_ERR_OK) {
				// Get detailed upload error message
				$uploadError = $uploadedFile->getError();
				$errorMessages = [
					UPLOAD_ERR_INI_SIZE => 'File size exceeds upload_max_filesize directive (' . ini_get('upload_max_filesize') . ')',
					UPLOAD_ERR_FORM_SIZE => 'File size exceeds form MAX_FILE_SIZE directive',
					UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
					UPLOAD_ERR_NO_FILE => 'No file was uploaded',
					UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
					UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
					UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
				];

				$errorMessage = $errorMessages[$uploadError] ?? 'Unknown upload error (code: ' . $uploadError . ')';
				$fileName = $uploadedFile->getClientFilename() ?: 'unknown';
				$phpLimits = ' | PHP Limits: upload_max_filesize=' . ini_get('upload_max_filesize') . ', post_max_size=' . ini_get('post_max_size');

				return Tools::returnCode($request, $response, 500, 'File upload error: ' . $errorMessage . ' | File: ' . $fileName . $phpLimits);
			}

			$adapter = new LocalFilesystemAdapter($this->get('settings')["LMSTempPath"]);
			$filesystem = new Filesystem($adapter);
			$tempFilePath = $this->get('settings')["LMSTempPath"] . DIRECTORY_SEPARATOR . $scormZipFileName;

			$stream = $uploadedFile->getStream()->detach();
			$filesystem->writeStream($scormZipFileName, $stream);
			fclose($stream);

			if (!isset($data['module_id'])) {
				$response->getBody()->write(json_encode(['fileName' => $scormZipFileName]));
			} else {
				$zip = new ZipArchive();
				if ($zip->open($tempFilePath) === TRUE) {
					if ($zip->locateName('imsmanifest.xml') === false) {
						$zip->close();
						$filesystem->delete($scormZipFileName);
						return Tools::returnCode($request, $response, 500, "Invalid SCORM package: imsmanifest.xml not found in ZIP file. SCORM packages must contain this file in the root directory.");
					}

					if (!isset($data['version'])) {
						return Tools::returnCode($request, $response, 500, 'Versioning failed!');
					}

					$scorm_version_folder = $this->get('settings')["LMSScormDataPath"] . $data['module_id'] . '/' . $data['version'];
					if (!is_dir($scorm_version_folder)) {
						mkdir($scorm_version_folder, 0775, true);
					} else {
						Course::recursiveRemoveDirectory($scorm_version_folder);
					}

					$zip->extractTo($scorm_version_folder);
					$zip->close();
				} else {
					return Tools::returnCode($request, $response, 500, 'Missing SCORM zip file');
				}
				$response->getBody()->write(json_encode(['fileName' => $scormZipFileName]));
			}
		} else {
			return Tools::returnCode($request, $response, 500, 'Missing SCORM zip file');
		}

		return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
	});

	//-----------IMPORTANT------------>>This functions is similar to above code optimisation needs clubing both the route actions
	// Add scorm file to be used for learning resource for resource version

	$group->post('/module/version/uploadscormfile', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		// Check if file was uploaded and handle upload errors
		if (!isset($_FILES['zipfile'])) {
			return \APP\Tools::returnCode($request, $response, 500, "No SCORM zip file uploaded - zipfile parameter is required");
		}

		$uploadError = $_FILES['zipfile']['error'];
		if ($uploadError !== UPLOAD_ERR_OK) {
			// Get detailed upload error message
			$errorMessages = [
				UPLOAD_ERR_INI_SIZE => 'File size exceeds upload_max_filesize directive (' . ini_get('upload_max_filesize') . ')',
				UPLOAD_ERR_FORM_SIZE => 'File size exceeds form MAX_FILE_SIZE directive',
				UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
				UPLOAD_ERR_NO_FILE => 'No file was uploaded',
				UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
				UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
				UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
			];

			$errorMessage = $errorMessages[$uploadError] ?? 'Unknown upload error (code: ' . $uploadError . ')';
			$fileName = $_FILES['zipfile']['name'] ?? 'unknown';
			$phpLimits = ' | PHP Limits: upload_max_filesize=' . ini_get('upload_max_filesize') . ', post_max_size=' . ini_get('post_max_size');

			return \APP\Tools::returnCode($request, $response, 500, 'File upload error: ' . $errorMessage . ' | File: ' . $fileName . $phpLimits);
		}

		if (true) { // File uploaded successfully, proceed with processing
			$uploadedFile = $_FILES['zipfile'];
			$tempFilePath = $uploadedFile['tmp_name'];
			$originalFileName = $uploadedFile['name'];
			$fileSize = $uploadedFile['size'];
			$mimeType = mime_content_type($tempFilePath); // Get MIME type

			$tempStoragePath = $this->get('settings')["LMSTempPath"];
			$scormDataPath = $this->get('settings')["LMSScormDataPath"];

			// Flysystem Adapter for temporary storage
			$tempAdapter = new LocalFilesystemAdapter($tempStoragePath);
			$tempFilesystem = new Filesystem($tempAdapter);

			// File Name Handling
			$scormZipFileNameBase = preg_replace('/[^a-zA-Z0-9]/', '_', $data["name"]);
			$fileExtension = pathinfo($originalFileName, PATHINFO_EXTENSION);
			$scormZipFileName = $scormZipFileNameBase . '.' . $fileExtension;
			$tempFileDestination = $scormZipFileName; // Path within the temp filesystem

			// Validation
			$allowedMimeTypes = ['application/zip', 'application/octet-stream', 'application/x-zip-compressed', 'multipart/x-zip']; // Add common zip mimetypes
			if (!in_array($mimeType, $allowedMimeTypes)) {
				return \APP\Tools::returnCode($request, $response, 500, "Invalid file type. You must upload a zip archive.");
			}
			if ($fileSize > 1024 * 1024 * 1024) { // 1GB in bytes
				return \APP\Tools::returnCode($request, $response, 500, "File size exceeds the limit of 1GB.");
			}

			try {
				// Upload to temporary storage using Flysystem
				$stream = fopen($tempFilePath, 'r+');
				$tempFilesystem->writeStream($tempFileDestination, $stream);
				fclose($stream);

				$zipfile = $tempStoragePath . $tempFileDestination; // Full path to the temp zip file

				$zip = new \ZipArchive();
				if ($zip->open($zipfile) === TRUE) {
					if ($zip->locateName('imsmanifest.xml') === false) {
						$error = "Invalid SCORM zip file. Can't find imsmanifest.xml";
						$tempFilesystem->delete($tempFileDestination); // Delete temp file on error
						return \APP\Tools::returnCode($request, $response, 500, $error);
					} else {
						//---------Copy File to version directory and remove temp file-------------
						$scorm_version_folder = $scormDataPath . $data['module_id'] . '/' . $data['version'];

						// Flysystem Adapter for SCORM data storage
						$scormDataAdapter = new LocalFilesystemAdapter($scormDataPath); // Root path for SCORM data
						$scormDataFilesystem = new Filesystem($scormDataAdapter);

						if (!$scormDataFilesystem->has($data['module_id'] . '/' . $data['version'])) { // Check if directory exists using Flysystem
							$scormDataFilesystem->createDirectory($data['module_id'] . '/' . $data['version']);
						} else {
							$contents = $scormDataFilesystem->listContents($data['module_id'] . '/' . $data['version'], false);
							foreach ($contents as $file) {
								$scormDataFilesystem->delete($file->path()); // Delete files using Flysystem
							}
						}
						$zip->extractTo($scorm_version_folder); // Extract using ZipArchive (still needed)
						$tempFilesystem->delete($tempFileDestination); // Delete temp file after successful extraction

						$response->getBody()->write(json_encode(['zip_file' => $scormZipFileName, 'upload_file' => true]));
					}
				} else {
					$tempFilesystem->delete($tempFileDestination); // Delete temp file if zip open fails
					return \APP\Tools::returnCode($request, $response, 500, 'Missing SCORM zip file');
				}

			} catch (\League\Flysystem\FilesystemException $e) { // Catch Flysystem exceptions
				return \APP\Tools::returnCode($request, $response, 500, 'File upload error: ' . $e->getMessage());
			} catch (\Exception $e) { // Catch other exceptions (like ZipArchive errors if any)
				return \APP\Tools::returnCode($request, $response, 500, 'An error occurred: ' . $e->getMessage());
			}
		} else if (
			$data['cloned_version_zip_file'] ||
			file_exists($this->get('settings')["LMSScormDataPath"] . $data['module_id'] . "/moddata/scorm/1/imsmanifest.xml")
		) {
			$active_version_folder = $this->get('settings')["LMSScormDataPath"] . $data['module_id'] . '/' . $data['active_version'];
			$scormDataPath = $this->get('settings')["LMSScormDataPath"];

			// Flysystem Adapter for SCORM data storage
			$scormDataAdapter = new LocalFilesystemAdapter($scormDataPath);
			$scormDataFilesystem = new Filesystem($scormDataAdapter);


			// Real time updation ie When active data is updated and cloned to another vervsion
			if (
				file_exists($this->get('settings')["LMSScormDataPath"] . $data['module_id'] . "/moddata/scorm/1/imsmanifest.xml") &&
				$data['cloned_version'] == $data['active_version']
			) {
				if ($scormDataFilesystem->has($data['module_id'] . '/' . $data['active_version'] . '/' . "imsmanifest.xml")) { // Use Flysystem's has()
					\APP\Course::recursiveRemoveDirectory($active_version_folder); // Keep recursiveRemoveDirectory if it's reliable
					$scormDataFilesystem->createDirectory($data['module_id'] . '/' . $data['active_version']); // Use Flysystem createDirectory

					// Assuming \APP\Tools::recurseCopy still works with file paths...
					\APP\Tools::recurseCopy($this->get('settings')["LMSScormDataPath"] . $data['module_id'] . "/moddata/scorm/1/", $active_version_folder);
				}
			}
			if (!$data['cloned_version_zip_file']) {
				$data['cloned_version_zip_file'] = preg_replace('/[^a-zA-Z0-9]/', '_', $data["name"]);
			}
			$scorm_version_cloned_folder = $scormDataPath . $data['module_id'] . '/' . $data['cloned_version'];
			$scorm_version_folder = $scormDataPath . $data['module_id'] . '/' . $data['version'];


			if (!$scormDataFilesystem->has($data['module_id'] . '/' . $data['cloned_version'])) { // Use Flysystem has()
				$scormDataFilesystem->createDirectory($data['module_id'] . '/' . $data['cloned_version']);
			}

			if (!$scormDataFilesystem->has($data['module_id'] . '/' . $data['version'])) { // Use Flysystem has()
				$scormDataFilesystem->createDirectory($data['module_id'] . '/' . $data['version']);
			}


			if (
				$scormDataFilesystem->has($data['module_id'] . '/' . $data['cloned_version'] . '/' . 'imsmanifest.xml') && // Use Flysystem has()
				$data['cloned_version'] != $data['version']
			) {
				\APP\Course::recursiveRemoveDirectory($scorm_version_folder); // Keep recursiveRemoveDirectory if reliable
				\APP\Tools::recurseCopy($scorm_version_cloned_folder . '/', $scorm_version_folder . '/'); // Keep recurseCopy if reliable
				$response->getBody()->write(json_encode(['zip_file' => $data['cloned_version_zip_file'], 'upload_file' => false]));
			} else {
				if (
					file_exists($this->get('settings')["LMSScormDataPath"] . $data['module_id'] . "/moddata/scorm/1/imsmanifest.xml")
				) {
					\APP\Course::recursiveRemoveDirectory($scorm_version_cloned_folder); // Keep recursiveRemoveDirectory
					$scormDataFilesystem->createDirectory($data['module_id'] . '/' . $data['cloned_version']); // Use Flysystem createDirectory
					\APP\Tools::recurseCopy($this->get('settings')["LMSScormDataPath"] . $data['module_id'] . "/moddata/scorm/1/", $scorm_version_cloned_folder . '/'); // Keep recurseCopy
					\APP\Course::recursiveRemoveDirectory($scorm_version_folder); // Keep recursiveRemoveDirectory
					$scormDataFilesystem->createDirectory($data['module_id'] . '/' . $data['version']); // Use Flysystem createDirectory
					\APP\Tools::recurseCopy($scorm_version_cloned_folder . '/', $scorm_version_folder . '/'); // Keep recurseCopy
					$response->getBody()->write(json_encode(['zip_file' => $data['cloned_version_zip_file'], 'upload_file' => false]));
				} else {
					\APP\Course::recursiveRemoveDirectory($scorm_version_folder); // Keep recursiveRemoveDirectory
					\APP\Course::recursiveRemoveDirectory($scorm_version_cloned_folder); // Keep recursiveRemoveDirectory
					return \APP\Tools::returnCode($request, $response, 500, 'Version ' . $data['cloned_version'] . ' has no scorm data, please try uploading new zip file.');
				}
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 500, 'Missing SCORM zip file or clone\'s master version has no scorm data');
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->post('/module/uploadimage', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		if (isset($data["id"])) {
			$learningModule = \Models\LearningModule::find($data["id"]);
			if ($learningModule) {
				try {
					$image_response = \Models\LearningModule::uploadImages($response, $data, $_FILES, $this->get('settings'), $learningModule);
					$response->getBody()->write(json_encode($image_response));
					return $response->withHeader('Content-Type', 'application/json');
				} catch (\Throwable $th) {
					$response->getBody()->write(json_encode(['error' => 'The image you have uploaded is too large']));
					return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
				}
			}
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));


	// just to delete promo or thumbnail image from module
	$group->put('/deleteimage/module/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$learning = \Models\LearningModule::find($args["id"]);
		\Models\LearningModule::deleteImage($learning, $data['field'], $this->get('settings'));

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));


	// Universal version of deleting images
	$group->delete('/course/{field: .+}/{course_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$learning = \Models\LearningModule::find($args["course_id"]);
		\Models\LearningModule::deleteImage($learning, $args['field'], $this->get('settings'));
		\Models\LearningCourseModule::updateTracking($learning);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));


	// Will check all uploaded modules in scormdata directory, check for "xx/moddata/scorm/1/xml/information.xml" file and set flag("jackdaw") against resource, used in learning list to show/hide "jackdaw" button.
	$group->get('/module/compatibilitycheck', function (Request $request, Response $response, $args) {
		$report = [];
		//if (file_exists($this->get('settings')["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml")) {

		$directory = $this->get('settings')["LMSScormDataPath"];
		$scanned_directory = array_diff(scandir($directory), array('..', '.'));

		foreach ($scanned_directory as $module_id) {

			// loop scromdata directory subdirectories
			if (is_dir($this->get('settings')["LMSScormDataPath"] . $module_id)) {

				// if any of directories are numbers bigger than 0, must be modules
				if (intval($module_id) > 0) {
					// look into DB for existing module
					$learning = \Models\LearningModule::find(intval($module_id));
					if ($learning) {
						// check for XML file, if exists, update learning_modules table entry with jackdaw = 1, else jackadaw = 0
						if (file_exists($this->get('settings')["LMSScormDataPath"] . $module_id . "/moddata/scorm/1/xml/information.xml")) {
							$report[] = [
								'id' => intval($module_id),
								'message' => 'Found module with ID: ' . $module_id . ' (' . $learning->name . '), compatible with Jackdaw, updating database.',
								'status' => 'found'
							];
							$learning->jackdaw = 1;
						} else {
							$report[] = [
								'id' => intval($module_id),
								'message' => 'Found module with ID: ' . $module_id . ' (' . $learning->name . '), not compatible with Jackdaw, updating database.',
								'status' => 'not found'
							];
							$learning->jackdaw = 0;
						}
						$learning->save();
					} else {
						// Directory is found, but no entry in database
						$report[] = [
							'id' => intval($module_id),
							'message' => 'Found module with ID: ' . $module_id . ', module is not found in database.',
							'status' => 'not found in db'
						];
					}
				}
			}
		}

		$response->getBody()->write(json_encode($report));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	//Adds latest e-learning updates to e-learning supplied by Open eLMS.
	// This updates the course folders from Open eLMS with all files minus the quizxml.xml imsmanifest.xml, intro.swf, /docs, /images, /swf, /video and /xml to <strong>src/public/api/data/Sample course</strong> and <strong>src/public/scormdata</strong>
	$group->post('/module/updateengine', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (isset($_FILES['enginefile'])) {
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSTempPath"], true);
			$engineZipFile = new \Upload\File('enginefile', $storage);
			$engineZipFile->setName('Publish');

			$engineZipFileName = $engineZipFile->getNameWithExtension();
			$fileTypeValidation = new \Upload\Validation\Mimetype(['application/zip', 'application/octet-stream']);
			$fileTypeValidation->setMessage("Invalid file type. You must upload a zip archive.");
			$engineZipFile->addValidations([
				$fileTypeValidation,
				new \Upload\Validation\Size('1024M')
			]);
			try {
				$engineZipFile->upload();
				// Great success, now unzip and copy over all installed resources and sample
				$zip = new ZipArchive;
				if ($zip->open($this->get('settings')["LMSTempPath"] . 'Publish.zip') === TRUE) {
					$zip->extractTo($this->get('settings')["LMSTempPath"] . 'engineUpdate');
					$zip->close();
					$engineDir = $this->get('settings')["LMSTempPath"] . 'engineUpdate/';

					// delete zip file
					unlink($this->get('settings')["LMSTempPath"] . 'Publish.zip');

					// delete files/filders that should not be overwritten from extracted archive, if they exist
					// quizxml.xml imsmanifest.xml, intro.swf, /docs, /images, /swf, /video and /xml
					$delete_files = ['quizxml.xml', 'quizXML.xml', 'imsmanifest.xml', 'intro.swf'];
					$delete_directories = ['docs', 'images', 'swf', 'video', 'xml'];

					foreach ($delete_files as $key => $delete_file) {
						if (is_file($engineDir . $delete_file)) {
							unlink($engineDir . $delete_file);
						}
					}

					foreach ($delete_directories as $key => $delete_directory) {
						if (is_dir($engineDir . $delete_directory)) {
							\APP\Tools::delDirTree($engineDir . $delete_directory);
						}
					}

					//replace sample course and scormdata resources with extracrted archive
					\APP\Tools::recurseCopy($engineDir, $this->get('settings')["LMSPublicPath"] . 'api/data/Sample course');

					$learning_resources = array_diff(
						scandir($this->get('settings')["LMSPublicPath"] . 'scormdata'),
						array('..', '.')
					);
					foreach ($learning_resources as $key => $learning_resources) {
						if (file_exists($this->get('settings')["LMSPublicPath"] . 'scormdata/' . $learning_resources . "/moddata/scorm/1/xml/information.xml")) {
							\APP\Tools::recurseCopy($engineDir, $this->get('settings')["LMSPublicPath"] . 'scormdata/' . $learning_resources . "/moddata/scorm/1");
						}
					}

					// delete extracted archive
					if (is_dir($engineDir)) {
						\APP\Tools::delDirTree($engineDir);
					}

					$response = $response->getBody()->write($engineZipFileName);
				} else {
					return \APP\Tools::returnCode($request, $response, 500, 'Can\'t open ZIP archieve!');
				}
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $engineZipFile->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 500, 'Missing SCORM zip file');
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	//Update room files to all e-learning types
	$group->post('/module/updateallengine', function (Request $request, Response $response, $args) {
		$resources_actioned = \Models\LearningModule::upgradeElearningRoomFiles($this->get('settings'));
		$response->getBody()->write(json_encode($resources_actioned));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->get('/refresh/{module_id:[0-9]+}/{user_id:[0-9]+}/{result_id:[0-9]+}', function (Request $request, Response $response, $args) {
		if (
			\APP\Auth::isManagerOf($args['user_id'])
			|| \APP\Auth::accessAllLearners()
			|| \APP\Auth::isAdmin()
        ) {
            $learningModule = LearningModule::find($args['module_id']);
            if ($learningModule && $learningModule->is_course == 1) {
                \APP\Refresh::refreshLessons(true, array($args['user_id']), array($args['module_id']), false, false);
            } else {
                \APP\Refresh::refreshResults(true, array($args['user_id']), array($args['module_id']), false, false);
            }
			$newLearningResult = \Models\LearningResult
				::where('learning_module_id', $args['module_id'])
				->where('user_id', $args['user_id'])
				->where('refreshed', 0)
				->first()
			;
			$user = \APP\Auth::getUser();
			\Models\LearningModuleRefreshAudit::create([
				"learning_module_id" => $args['module_id'],
				"learning_result_id" => $args['result_id'],
				"user_id" => $args['user_id'],
				"refreshed_by" => $user->id
			]);

			$response->getBody()->write("".$newLearningResult->id);
			return $response->withHeader('Content-Type', 'text/html');
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	// Application will loop all e-learning courses supplied by Open eLMS and copy over client branding. Note branded files need to be first copied to the installation's /src/public/api/data/Sample course folder.
	// Files to copy are intro.swf and /images/thumbs/1.jpg
	$group->post('/updatebranding', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		// Upload intro.swf if provided
		if (isset($_FILES['branding-intro'])) {
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSPublicPath"] . 'api/data/Sample course/', true);
			$bandingIntro = new \Upload\File('branding-intro', $storage);
			$bandingIntro->setName('intro');

			$bandingIntroValidation = new \Upload\Validation\Mimetype(['application/x-shockwave-flash', 'application/octet-stream']);
			$bandingIntroValidation->setMessage("Invalid file type: '" . $bandingIntro->getMimetype() . "'!");
			$bandingIntro->addValidations([
				$bandingIntroValidation,
				new \Upload\Validation\Size('14M')
			]);
			try {
				$bandingIntro->upload();
				$response->getBody()->write(json_encode(['status' => true]));
				return $response->withHeader('Content-Type', 'application/json');
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $bandingIntro->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}
		}

		// Upload site logo if provided
		if (isset($_FILES['branding-logo'])) {
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSPublicPath"] . 'images/licensing/', true);
			$bandingLogo = new \Upload\File('branding-logo', $storage);

			$bandingLogo->setName($this->get('settings')["licensing"]['version'] . 'logo');

			$bandingLogoValidation = new \Upload\Validation\Mimetype(['image/png']);
			$bandingLogoValidation->setMessage("Invalid file type. You must upload a png file.");
			$bandingLogo->addValidations([
				$bandingLogoValidation,
				new \Upload\Validation\Size('2M')
			]);
			try {
				$bandingLogo->upload();
				\APP\Tools::updateConfig('randomString', uniqid());
				$response->getBody()->write(json_encode(['status' => true]));
				return $response->withHeader('Content-Type', 'application/json');
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $bandingLogo->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}
		}

		// Upload site log-in background if provided
		if (isset($_FILES['branding-bg'])) {
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSPublicPath"] . 'images/licensing/', true);
			$bandingBg = new \Upload\File('branding-bg', $storage);
			$bandingBg->setName($this->get('settings')["licensing"]['version'] . 'bg');

			$bandingBgValidation = new \Upload\Validation\Mimetype(['image/jpeg', 'image/jpg']);
			$bandingBgValidation->setMessage("Invalid file type. You must upload a jpg file.");
			$bandingBg->addValidations([
				$bandingBgValidation,
				new \Upload\Validation\Size('2M')
			]);
			try {
				$bandingBg->upload();
				// Make sure file ends with .jpg
				rename($this->get('settings')["LMSPublicPath"] . 'images/licensing/' . $bandingBg->getNameWithExtension(), $this->get('settings')["LMSPublicPath"] . 'images/licensing/' . $bandingBg->getName() . '.jpg');
				$response->getBody()->write(json_encode(['status' => true]));
				return $response->withHeader('Content-Type', 'application/json');
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $bandingBg->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}
		}

		if (isset($data['updateType']) && $data['updateType'] == 'branding') {
			// pull all directories in scormdata
			$scanned_directory = array_diff(
				scandir($this->get('settings')["LMSPublicPath"] . 'scormdata'),
				array('..', '.')
			);
			foreach ($scanned_directory as $key => $resource) {
				// if intro.swf exists then it is correct resource to be used for branding
				if (is_file($this->get('settings')["LMSPublicPath"] . 'scormdata/' . $resource . '/moddata/scorm/1/intro.swf')) {
					copy($this->get('settings')["LMSPublicPath"] . 'api/data/Sample course/intro.swf', $this->get('settings')["LMSPublicPath"] . 'scormdata/' . $resource . '/moddata/scorm/1/intro.swf');
				}
				if (is_file($this->get('settings')["LMSPublicPath"] . 'scormdata/' . $resource . '/moddata/scorm/1/images/thumbs/1.jpg')) {
					copy($this->get('settings')["LMSPublicPath"] . 'api/data/Sample course/images/thumbs/1.jpg', $this->get('settings')["LMSPublicPath"] . 'scormdata/' . $resource . '/moddata/scorm/1/images/thumbs/1.jpg');
				}
			}
		}
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	// for loading animations
	$group->get('/branding/list_animations', function (Request $request, Response $response, $args) {
		$dir = $this->get('settings')["LMSPublicPath"].'images/loading/';
		$anim_files = array_filter(scandir($dir), function ($file) use ($dir) {
			return !is_dir($dir . '/' . $file)
				&& substr($file, 0, 1) != "."
				&& in_array(strtolower(pathInfo($file)['extension']), array('svg'));
		});

		$response
			->getBody()
			->write(json_encode(array_values($anim_files)))
		;

		return
			$response
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->post('/branding/set_animation', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		\APP\Tools::updateConfig('loadingAnimation', $data['name']);

		$response
			->getBody()
			->write('ok')
		;
		return
			$response
			->withHeader('Content-Type', 'text/html')
		;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->post('/branding/remove_animation', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		unlink($this->get('settings')["LMSPublicPath"].'images/loading/'.$data['name']);
		$response
			->getBody()
			->write('ok')
		;
		return
			$response
			->withHeader('Content-Type', 'text/html')
		;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->post('/branding/upload_animation', function (Request $request, Response $response, $args) {
		$dir = $this->get('settings')["LMSPublicPath"].'images/loading/';

		if (isset($_FILES['loading-animation'])) {
			$storage = new \Upload\Storage\FileSystem($dir, true);

			$loadingAnimation = new \Upload\File('loading-animation', $storage);
			$loadingAnimation->addValidations([
				// new \Upload\Validation\Mimetype(['image/svg+xml']),	// throws an error sees it as text/html
				new \Upload\Validation\Extension('svg'),
				new \Upload\Validation\Size('128K')
			]);

			try {
				$loadingAnimation->upload();
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $loadingAnimation->getErrors();
				return \APP\Tools::returnCode($request, $response, 415, implode("\n", $errors));
			}

			$response
				->getBody()
				->write($_FILES['loading-animation']['name'])
			;

			return
				$response
				->withHeader('Content-Type', 'text/html')
			;
		}
		return \APP\Tools::returnCode($request, $response, 400, 'no-file-to-upload');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	// Updates all installed module images from stock, if stock exists.
	$group->get('/module/updateimages', function (Request $request, Response $response, $args) {
		$learning = \Models\LearningModule::
		select('id', 'name', 'thumbnail', 'promo_image', 'type_id')
			->get()
		;

		$report = [];
		$types = [
			'e-learning.jpg',
			'youtube.jpg',
			'webpage.jpg',
			'classroom.jpg',
			'bookcddvd.jpg',
			'on-the-job.jpg',
			'upload.jpg',
			'blog-entry.jpg',
			'reflective-log.jpg',
			'zoom.jpg',
		];
		$directory = $this->get('settings')["AvailableModulesLocation"];
		if (is_dir($directory)) {

			//scan module stock directory
			$scanned_directory = array_diff(scandir($directory), array('..', '.'));

			foreach ($learning as $key => $value) {

				// If installed learning_module name matches directory name in stock
				if (in_array($value->name, $scanned_directory)) {

					// delete promo image, if exists
					if (is_file($this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/promo.jpg')) {
						unlink($this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/promo.jpg');
					}

					// delete thumbnail image if exists
					if (is_file($this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/thumb.jpg')) {
						unlink($this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/thumb.jpg');
					}

					// update promo/thumb image, if source promo does not exists, copy default
					if (is_dir($this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images')) {
						if (!is_file($this->get('settings')["AvailableModulesLocation"] . $value->name . "/images/promo.jpg") || !copy($this->get('settings')["AvailableModulesLocation"] . $value->name . "/images/promo.jpg", $this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/promo.jpg')) {
							copy($this->get('settings')["LMSDefaultTypePath"] . $types[$value->type_id - 1], $this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/promo.jpg');
						}

						if (!is_file($this->get('settings')["AvailableModulesLocation"] . $value->name . "/images/thumb.jpg") || !copy($this->get('settings')["AvailableModulesLocation"] . $value->name . "/images/thumb.jpg", $this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/thumb.jpg')) {
							copy($this->get('settings')["LMSDefaultTypePath"] . $types[$value->type_id - 1], $this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/thumb.jpg');
						}

						$report[] = [
							'id' => $value->id,
							'name' => $value->name
						];
					}

					////echo $value->id . ' : ' . (is_dir($this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images') ? 'true' : 'false') . ' - ' . $this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images' . "\n";
				} else {
					// module exists, but image does not, copy default type to module.
					////echo $value->id . ' $value->type_id > 0 : ' . ($value->type_id > 0 ? 'true' : 'false') . "\n";
					if (
						$value->type_id > 0 &&
						isset($types[$value->type_id - 1]) &&
						is_file($this->get('settings')["LMSDefaultTypePath"] . $types[$value->type_id - 1]) &&
						is_dir($this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images')
					) {
						copy($this->get('settings')["LMSDefaultTypePath"] . $types[$value->type_id - 1], $this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/thumb.jpg');
						copy($this->get('settings')["LMSDefaultTypePath"] . $types[$value->type_id - 1], $this->get('settings')["LMSScormDataPath"] . $value->id . '/moddata/scorm/1/images/promo.jpg');
						// send back updated modules
						$report[] = [
							'id' => $value->id,
							'name' => $value->name
						];
					}
				}
			}
		}


		$response->getBody()->write(json_encode($report));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	// updates specified learning resource!!
	$group->put('/module/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$learning = \Models\LearningModule
			::where('id', $args["id"])
			->with('type')
			->first();
		$originalAddToAI = $learning->add_to_ai_chat;

		$data["refresh_period"] = isset($data["refresh_period"]) ? intval($data["refresh_period"]) : 0;
		$data["refresh_repeat"] = isset($data["refresh_repeat"]) ? intval($data["refresh_repeat"]) : null;
		$data["due_after_period"] = isset($data["due_after_period"]) ? intval($data["due_after_period"]) : 0;

		if (!isset($data['level'])) {
			$data['level'] = 'NULL';
		}
		if (isset($data['custom_field'])) {
						Form::saveCustomForm($data['custom_field'],'learning_resource',$learning->id);
		}

		$fields = [
			"code", "name", "category_id", "keywords", "self_enroll", "approval", "require_management_signoff",
			"company_id", "refresh", "refresh_period", "refresh_repeat", "refresh_custom_email",
			"refresh_custom_email_subject", "refresh_custom_email_body", "due_after_period", "type_id",
			"is_course", "language", "cost", "duration_hours", "duration_minutes", "duration_change",
			"provider_id", "level", "do_prerequisite", "is_skillscan", "track_progress", "print_certificate",
			"material", "description", "accreditation_description", "evidence_type_id", "f_p_category_id",
			"responsible_user", "expiration_date", "player_width", "player_height", "event_type_id",
			"visible_learner", "scorm_popup", "badge", "accreditation_alternative_learning_name", "disable_upon_completion",
			'repetition_period', 'reset_learning', "delivery_provider_type_id", "group_department_code_id",
			"after_completion_do_not_reset_completion_state", "refresh_only_if_learning_meets_query",
			"further_customise_this_query", "is_maximo_qualification", 'self_enroll_access',"payment_buys","access_duration",
			"retake_fee", "refresh_date", "refresh_period_type", "scorm_full_screen", "mandatory_certificate_upload",
			"copy_refresher_emails_to_line_managers",'add_to_ai_chat'

		];

		// if "min_passing_percentage" is provided, update the value in the courses respective information.xml file (<screens><screen><Quizes><settings><PassMark>)
		if (
			isset($data['material']) &&
			$data['material'] &&
			$learning->type &&
			$learning->type->slug == 'e_learning'
		) {
			if (file_exists($this->get('settings')["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml")) {
				$xml = simplexml_load_file($this->get('settings')["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml");
				foreach ($xml as $key => $screen) {
					if (
						isset($screen->Quizes)
						&& isset($screen->Quizes->settings)
					) {
						if (isset($data['material']['min_passing_percentage'])) {
							$screen->Quizes->settings->PassMark = ($data['material']['min_passing_percentage'] - 1);
						}
						if (isset($data['material']['max_quiz_time_allowed'])) {
							$screen->Quizes->settings->max_time_allowed = $data['material']['max_quiz_time_allowed'];
						}
						$xml->asXml($this->get('settings')["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml");
					}
				}
			}

			$min_passing_percentage = 0;

			if (isset($learning->material->min_passing_percentage)) {
				$min_passing_percentage = $learning->material->min_passing_percentage;
			}

			if (isset($data['material']['min_passing_percentage'])) {
				$min_passing_percentage = $data['material']['min_passing_percentage'];
			}

			\Models\Scorm\Data::updateOrCreate([
				'scoid' => $learning->id,
				'name' => 'masteryscore',
				'value' => $min_passing_percentage,
			]);
		}

		if (isset($data['material']['scorm_standard']))
		{
			$scorm_version = 'SCORM_1.2';

			switch ($data['material']['scorm_standard'])
			{
				case 1:
					$scorm_version = 'SCORM_1.1';
					break;
				case 3:
					$scorm_version = 'SCORM_1.3';
					break;
			}

			$scorm_scorm = \Models\Scorm\Scorm::where('course', $data['id'])->first();

			if ($scorm_scorm)
			{
				$scorm_scorm->version = $scorm_version;
				$scorm_scorm->save();
			}
		}

		\APP\Tools::setObjectFields($learning, $fields, $data);
		if (isset($data['credly_badge'])) {
			$inserted_data = [];
			foreach ($data['credly_badge'] as $credly_badge) {
				LearningModuleCredlyBadge::updateOrCreate(['learning_module_id' => $learning->id, 'badge_template_id' => $credly_badge['id']], ['learning_module_id' => $learning->id, 'badge_template_id' => $credly_badge['id']]);
				$inserted_data[] = $credly_badge['id'];
			}
			LearningModuleCredlyBadge::where(['learning_module_id' => $learning->id])->whereNotIn('badge_template_id', $inserted_data)->delete();
		}
		if (empty($data['f_p_category_id'])) {
			$learning->f_p_category_id = null;
		}
		if (empty($data['responsible_user'])) {
			$learning->responsible_user = null;
		}
		if (empty($data['expiration_date'])) {
			$learning->expiration_date = null;
		}
		if (empty($data['player_width'])) {
			$learning->player_width = null;
		}
		if (empty($data['player_height'])) {
			$learning->player_height = null;
		}

		if (empty($data['delivery_provider_type_id'])) {
			$learning->delivery_provider_type_id = null;
		}

		if (empty($data['group_department_code_id'])) {
			$learning->group_department_code_id = null;
		}

		if (empty($data['refresh_date'])) {
			$learning->refresh_date = null;
		}

		// Store original version before updating
		$originalVersion = $learning->version;

		//version Check and update
		// version update, only for resources, maybe check for type?
		//  Not sure why this was commented out, maybe something was conflicting?
		if (
			!empty($data['id']) &&
			$learning->is_course == 0 &&
			$learning->is_skill == 0
		) {
			if (!empty($data['version'])) {
				$versionUpdate = \Models\LearningModuleVersion::versionSync("update", $data, $learning);
				$learning->version = $data['version'];
			}
		}
		$isEnterprise = !\APP\Tools::getConfig('sharedClients');
		$learning->self_enroll_access = (isset($data['self_enroll']) && isset($data['self_enroll_access'])) ?  ($data['self_enroll_access'] == 0 && !$isEnterprise ? null : $data['self_enroll_access'])  : null;
		$learning->editing_access = isset($data['editing_access']) ?  ($data['editing_access'] == 0 && !$isEnterprise ? null : $data['editing_access'])  : null;

		$learning->save();

		// When version is updated, update started_version for all unstarted learning results
		// This prevents them from being marked for refresh when they haven't even started
		if (
			!empty($data['id']) &&
			$learning->is_course == 0 &&
			$learning->is_skill == 0 &&
			!empty($data['version']) &&
			$data['version'] != $originalVersion
		) {
			// Use raw database update to avoid triggering Eloquent events
			\Illuminate\Database\Capsule\Manager::table('learning_results')
				->where('learning_module_id', $learning->id)
				->where('completion_status', \Models\LearningResult::COMPLETION_STATUS_NOT_ATTEMPTED)
				->where('refreshed', 0)
				->whereNull('deleted_at')
				->update(['started_version' => $data['version']])
			;
		}


		if (
			isset($data['self_enroll']) &&
			$data['self_enroll']
		) {
			if ($data['self_enroll_access'] == 1 || $data['self_enroll_access'] == 2) {
				if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 1)->get())) {
					\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 1)->delete();
                }
                if(!isset($data['companies']) || count($data['companies'])==0 && !$isEnterprise)
                {
                    $data['companies'] = [Auth::getUser()->company];
                }
				foreach ($data['companies'] as $company) {
					$CompanyModuleEnrollment = new \Models\CompanyModuleEnrollment();
					$CompanyModuleEnrollment->learning_module_id = $learning->id;
					$CompanyModuleEnrollment->company_id = $company['id'];
					$CompanyModuleEnrollment->type = 1;
					$CompanyModuleEnrollment->save();
				}

			}
			else {
				if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 1)->get())) {
					\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 1)->delete();
				}
			}
		}
		else {
			if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->get())) {
				\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 1)->delete();
			}
		}

		if ($data['editing_access'] == 1 || $data['editing_access'] == 2) {
			if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 2)->get())) {
				\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 2)->delete();
            }
            if(!isset($data['edit_access_companies']) || count($data['edit_access_companies'])==0 && !$isEnterprise)
            {
                    $data['edit_access_companies'] = [Auth::getUser()->company];
            }
			foreach ($data['edit_access_companies'] as $company) {
				$CompanyModuleEnrollment = new \Models\CompanyModuleEnrollment();
				$CompanyModuleEnrollment->learning_module_id = $learning->id;
				$CompanyModuleEnrollment->company_id = $company['id'];
				$CompanyModuleEnrollment->type = 2;
				$CompanyModuleEnrollment->save();
			}
		}
		else {
			if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 2)->get())) {
				\Models\CompanyModuleEnrollment::where('learning_module_id', $learning->id)->where('type', 2)->delete();
			}
		}

		$competencies = [];
		if (isset($data["competencies"])) {
			foreach ($data["competencies"] as $c) {
				$competencies[$c["id"]] = [
					"points" => $c["points"],
				];
			}
		}
		$learning->competencies()->sync($competencies);


		$linked_skills = [];
		if (isset($data["linked_skills"])) {
			foreach ($data["linked_skills"] as $c) {
				$linked_skills[$c["id"]] = [
					"link_skill_id" => $c["id"],
				];
			}
			$learning->LinkedSkills()->sync($linked_skills);
		}

		$prerequisites = isset($data["prerequisites"]) ? \APP\Tools::getObjectIds($data["prerequisites"]) : [];
        $learning->prerequisites()->sync($prerequisites);
        if(isset($data['linked'])){
            $linked = isset($data["linked"]) ? $data["linked"] : [];
            $available_data = [];
            foreach($linked as $link){
                $check = LinkedLearningModule::where('learning_module_id',$learning->id)->where('linked_learning_module_id',$link['id'])->first();
                if(!$check){
                    $available_data[] = LinkedLearningModule::create([
                        'learning_module_id'=>$learning->id,
                        'linked_learning_module_id'=>$link['id'],
                        'assign'=>$link['assign'],
                        'days'=>isset($link['days'])? $link['days']:NULL
                    ]);
                }else{
                    $available_data[] = $check;
                }
            }
            LinkedLearningModule::where('learning_module_id',$learning->id)->whereNotIn('linked_learning_module_id',array_column($linked,'id'))->delete();
        }

		// Resource zip file is provided
		if (
			$learning->type_id == 1 &&
			isset($data["material"]["zip_file"]) &&
			(
				!empty($data["update_scorm_file"]) ||
				$data["active_version"] != $data["version"]
			)
		) {
			$course = \APP\Course::get($learning);
			if (
				$data["active_version"] != $data["version"] &&
				isset($data["material"]["zip_file"])
			) {
				$scorm_file =  $this->get('settings')["LMSTempPath"] . $data["material"]["zip_file"];
			}
			if ($data["update_scorm_file"]) {
				$scorm_file =  $this->get('settings')["LMSTempPath"] . $data["material"]["zip_file"];
			} else {
				// Why? But I guess there is reason.
				$scorm_file = $this->get('settings')["LMSTempPath"] . "dummy1233212.zip";
			}

			try {
				$course->deleteScormSetup();
				$course->setupScorm(
					$scorm_file,
					$this->get('settings')["LMSScormDataPath"],
					$data
				);

				// Great success with setting up new resource file.

				// If "Fix SCORM track data" is checked, replace scoid with new one, check passmark and update status.
				if (
					isset($data['updateScormTrackWithScoid']) &&
					$data['updateScormTrackWithScoid']
				) {
					// Replace scoid
					$max_scoid = \Models\Scorm\Sco::where("scorm", "=", $learning->id)->max("id");
					$scoesTrack = \Models\Scorm\Track::where('scormid', '=', $learning->id)->get();

					foreach ($scoesTrack as $track) {
						$existingTrack = \Models\Scorm\Track
							::where('scormid', '=', $track->scormid)
							->where('userid', '=', $track->userid)
							->where('element', '=', $track->element)
							->where('attempt', '=', $track->attempt)
							->first()
						;

						if ($existingTrack) {
							// Check if the scoid is different, and if so, update.
							if ($existingTrack->scoid !== $max_scoid) {
								\Models\Scorm\Track::where('id', '=', $existingTrack->id)->update(['scoid' => $max_scoid]);
							}
						}
					}


					//-----------------------
					// Look into learning results, if passing percentage is bigger than set in course, set to completed, add scorm track records to preserve completin state.
					$incomplete_learning_results = \Models\LearningResult
						::where('learning_module_id', $learning->id)
						->where('completion_status', '!=', 'completed')
						->get()
					;

					foreach ($incomplete_learning_results as $key => $incomplete_learning_result) {
						if ($incomplete_learning_result->score >= $data['material']['min_passing_percentage']) {

							$update_learning_result = \Models\LearningResult::find($incomplete_learning_result->id);
							$update_learning_result->completion_status = 'completed';
							$update_learning_result->completed_at = $update_learning_result->updated_at;
							$update_learning_result->save();

							// Update \Models\Scorm\Track "cmi.core.lesson_status" as "passed"
							$success_status = ($data['material']['scorm_standard'] < 3 ? 'cmi.core.lesson_status' : 'cmi.success_status');

							$updateScormTrackUser = \Models\Scorm\Track::where('scormid', '=', $learning->id)
								->where('userid', '=', $incomplete_learning_result->user_id)
								->where('element', '=', $success_status)
								->update(
									[
										'value' => 'passed'
									]
								)
							;
						}
					}
				}


				// Check if this module can be edited by Jackdaw, if yes, set flag in DB and that will be used in listings pages to show/hide jackdaw button
				if (file_exists($this->get('settings')["LMSScormDataPath"] . $learning->id . "/moddata/scorm/1/xml/information.xml")) {
					$learning->jackdaw = 1;
				} else {
					$learning->jackdaw = 0;
				}
				$learning->save();
			} catch (\APP\ScormException $e) {
				return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
			}
		}

		$asm = new \APP\Assessment();

		//check if the module is an e-learning one and whether it has assessments
		if ($learning->type_id == 1 && $asm->hasAssessment($learning->id)) {
			//check if assessment categories data are submited. If not, clear all categories
			$asm_cats = isset($data["assessment_categories"]) ? $data["assessment_categories"] : [];
			$asm->updateAssessmentCategories($learning->id, $asm_cats);
		}

		// Refresh period has changed and person updating resource opted in to update all due_at dates in learning_results that are completed and contain due_at date.
		if (
			isset($data["update_due_at"]) &&
			$data["update_due_at"]
		) {
			// get configuration for how to calculate due_at
			$refreshCompletedAt = \Models\Configuration
				::where('key', 'refreshCompletedAt')
				->first();
			if ($refreshCompletedAt->value == '1') {
				$refresh_base = 'completed_at';
			} else {
				$refresh_base = 'created_at';
			}

			// get all afflicted learning results
			$learning_results = \Models\LearningResult
				::where('learning_module_id', $learning->id)
				->where('completion_status', 'completed')
				->where('refreshed', 0)
				->get();

			// Will break wait so that other actions can be done.
			session_write_close();

			// loop learning results and update them accordingly
			foreach ($learning_results as $key => $learning_result) {
				$refresh_base_date = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $learning_result->$refresh_base);

				// Determine the interval to add based on the refresh_period_type
				switch ($learning->refresh_period_type) {
					case 'day':
						$learning_result->due_at = $refresh_base_date->copy()->addDays(
							$learning->refresh_period + $learning->due_after_period
						);
						break;
					case 'month':
						$learning_result->due_at = $refresh_base_date->copy()->addMonths(
							$learning->refresh_period
						)->addDays($learning->due_after_period);
						break;
					case 'year':
						$learning_result->due_at = $refresh_base_date->copy()->addYears(
							$learning->refresh_period
						)->addDays($learning->due_after_period);
						break;
					default:
						throw new \Exception("Invalid refresh period type: {$learning->refresh_period_type}");
				}

				// Save without triggering events
				$learning_result->saveWithoutEvents();
			}


			// Also update due at for active learning results, that are not assigned to standard for user!
			$learning_results_progress = \Models\LearningResult
				::where('learning_module_id', $learning->id)
				->where('completion_status', '!=', 'completed')
				->where('refreshed', 0);

			if ($this->get('settings')['licensing']['isApprentix']) {
				$learning_results_progress = $learning_results_progress
					->whereNotIn(
						'learning_results.user_id',
						\Models\ApprenticeshipStandardUser
							::select('apprenticeship_standards_users.user_id')
							->whereIn(
								'standard_id',
								\Models\ApprenticeshipStandard
									::select('id')
									->where('status', true)
									->whereIn(
										'id',
										\Models\ApprenticeshipIssueCategories
											::select('standard_id')
											->where('status', true)
											->whereIn(
												'id',
												\Models\ApprenticeshipIssues
													::select('issue_category_id')
													->where('status', true)
													->where(function ($query) use ($learning) {
														$query
															->whereIn(
																'id',
																\Models\ApprenticeshipIssuesLearningModules
																	::select('apprenticeship_issues_id')
																	->where('learning_modules_id', $learning->id)
																	->get()
															)
															->orWhereIn(
																'id',
																\Models\ApprenticeshipIssuesUserLearningModules
																	::select('apprenticeship_issues_id')
																	->where('learning_modules_id', $learning->id)
																	->get()
															)
															->orWhereIn(
																'id',
																\Models\ApprenticeshipIssuesEvidence
																	::select('apprenticeship_issues_id')
																	->where('learning_modules_id', $learning->id)
																	->get()
															);
													})
													->get()
											)
											->get()
									)
									->get()
							)
							->get()
					);
			}

			$learning_results_progress = $learning_results_progress
				->get();

			// loop learning results and update them accordingly
			foreach ($learning_results_progress as $key => $learning_result) {
				$created_at = \Carbon\Carbon::parse($learning_result->created_at);
				$learning_result->due_at = $created_at->copy()->addDays($learning->due_after_period);
				$learning_result->saveWithoutEvents();
			}
		}
		// calling the AI
		$currentAddToAI = isset($data['add_to_ai_chat']) ? $data['add_to_ai_chat'] : false;
		if ($originalAddToAI != $currentAddToAI) {
			if (!$originalAddToAI && $currentAddToAI) {
				// CASE 1: Was false, now true → Add to AI
				$chatController = $this->get(ChatBotController::class);
				$requestBody = $request->withParsedBody(['module_id' => $learning->id]);
				$chatController->addDataToGoogleCloud($requestBody, $response, []);
			}

			if ($originalAddToAI && !$currentAddToAI) {
				// CASE 2: Was true, now false → Remove from AI
				$chatController = $this->get(ChatBotController::class);
				$requestBody = $request->withParsedBody(['module_id' => $learning->id]);
				$chatController->deleteModuleFromGoogleCloud($requestBody, $response, []);
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->get('/assessment_questions/{id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$result = [];

		$asm_questions = \Models\Assessment\Question
			::where("course_id", "=", $args["id"])
			->get();

		foreach ($asm_questions as $asm_question) {
			$result[] = $asm_question;
		}

		$response->getBody()->write(json_encode($result));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));

	// Adds new learning resource
	$group
		->post('/module/new', function (Request $request, Response $response) {
			$data = $request->getParsedBody();
			if(\APP\Tools::getConfig('sharedClients')){
				$plan = LicenseFeatures::getPlanDetails();
				if($plan['external_elearning_courses_limit_reached'] && \APP\Tools::getConfig('sharedClients')){
				return \APP\Tools::returnCode($request, $response, 403, 'Licence Limit reached');
				}
			}

			$learning = \Models\LearningModule::insertImportModule($data, $this->get('settings'), $response);
			if (isset($learning["error"])) {
				return \APP\Tools::returnCode($request, $response, 500, $learning["error"]);
			} else {
				if ($learning->add_to_ai_chat) {
					$chatController = $this->get(ChatBotController::class);
					$requestBody = $request->withParsedBody([
						'module_id' => $learning->id
					]);
					$chatResponse = $chatController->addDataToGoogleCloud($requestBody, $response, []);
				}
				$response->getBody()->write((string)$learning["id"]);
				return $response;
			}
			//\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert')
		})
		->add(new RateLimitMiddleware())
		->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'))
	;


	$group->get('/module/download/{learning_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$learning_id = $args["learning_id"];
		try {
			$learning = \Models\LearningModule::where("id", "=", $learning_id)->firstOrFail();

			if ($learning->type->id == 1) {
				$scorm_files_path = $this->get('settings')["LMSScormDataPath"] . "{$learning_id}/moddata/scorm/1";
				$scorm_zip_path = $this->get('settings')["LMSTempPath"] . "{$learning_id}.scorm.zip";

				str_replace("//", "/", $scorm_files_path);
				str_replace("//", "/", $scorm_zip_path);

				if (!file_exists($scorm_files_path)) {
					throw new Exception("No files for this course.");
				}

				if (!file_exists($scorm_zip_path)) {
					$zip = new \ZipArchive();

					$result_code = $zip->open($scorm_zip_path, \ZipArchive::CREATE);
					if (true !== $result_code) {
						throw new Exception("Can't open zip file.");
					}

					\APP\Tools::zipRecursive($scorm_files_path, $zip);

					if (!$zip->status == ZIPARCHIVE::ER_OK) {
						throw new Exception("Can't open zip file.");
					}

					$zip->close();
				}

				if (!file_exists($scorm_zip_path)) {
					throw new Exception("Can't create SCORM ZIP file.");
				}

				$fh = fopen($scorm_zip_path, 'rb');
				$stream = new Stream($fh);

				return $response
					->withHeader('Content-Type', 'application/zip')
					->withHeader('Pragma', 'public')
					->withHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
					->withHeader('Content-Disposition', 'attachment; filename="' . basename($scorm_zip_path) . '"')
					->withBody($stream);
			} else throw new Exception("This learning resource has no SCORM contents.");
		} catch (Exception $ex) {
			$response->getBody()->write($ex->getMessage());
			$response = $response->withStatus(404);
			return $response;
		}
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));

	// Add evidence as comment!
	$group->post('/module/{module_id:[0-9]+}/comment', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$evidence = new \Models\LearningModuleEvidence;
		$evidence->learning_modules_id = $args['module_id'];
		$evidence->user_id = \APP\Auth::getUserId();
		$evidence->manager = \APP\Auth::isAdminInterface();
		$evidence->hash = bin2hex(random_bytes(16));
		$evidence->evidence = $data['comment_url'];
		$evidence->version = $data['version'];
		$evidence->evidence_type = 'comment';
		$evidence->status = 1;
		$evidence->save();


		$response->getBody()->write(json_encode($evidence));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));


	$group->post('/youtube-playlist/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		if (
			isset($data['category']) &&
			isset($data['videos']) &&
			is_array($data['videos'])
		) {
			$type = \Models\LearningModuleType::where('slug', 'youtube')->first();

			foreach ($data['videos'] as $key => $video) {

				$learning = \Models\LearningModule
					::where('type_id', $type->id)
					->where('material', 'LIKE', '%' . $video['videoID'] . '%')
					->first();
				if (!$learning) {
					$learning = new \Models\LearningModule;
					$learning->type_id = $type->id;
					$learning->material = ["sessions" => [], "link" => 'https://www.youtube.com/watch?v=' . $video['videoID']];
				}

				$learning->name = $video['title'];
				$learning->category_id = $data['category'];
				$learning->description = $video['description'];
				$learning->created_by = \APP\Auth::getUserId();
				$learning->status = true;
				if (isset($data['playListName'])) {
					$provider = \Models\LearningProvider::firstOrCreate(
						['company' => $data['playListName']],
						['status' => true]
					);
					$learning->provider_id = $provider->id;
				}

				// Retrieve images!
				if (
					isset($video['thumbnails'])
				) {
					if (isset($video['thumbnails']['medium']['url'])) {
						$thumb_parts = pathinfo($video['thumbnails']['medium']['url']);
						if (
							$thumb_parts['extension'] == 'jpg' ||
							$thumb_parts['extension'] == 'png' ||
							$thumb_parts['extension'] == 'jpeg'
						) {
							$thumb_path = $this->get('settings')["LMSThumbPath"];
							$thumbnail = $video['videoID'] . '_thumb.' . $thumb_parts['extension'];
							file_put_contents($thumb_path . $thumbnail, fopen($video['thumbnails']['medium']['url'], 'r'));
							$learning->thumbnail = $thumbnail;
						}
					}
					if (isset($video['thumbnails']['maxres']['url'])) {
						$promo_parts = pathinfo($video['thumbnails']['maxres']['url']);
						if (
							$promo_parts['extension'] == 'jpg' ||
							$promo_parts['extension'] == 'png' ||
							$promo_parts['extension'] == 'jpeg'
						) {
							$promo_path = $this->get('settings')["LMSPromoPath"];
							$promo = $video['videoID'] . '_promo.' . $promo_parts['extension'];
							file_put_contents($promo_path . $promo, fopen($video['thumbnails']['maxres']['url'], 'r'));
							$learning->promo_image = $promo;
						}
					}
				}

				$learning->save();
			}
		}
		return $response;
	})->add(new RateLimitMiddleware())->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));

	// Adds new lesson
	$group->post('/course/new', function (Request $request, Response $response) {

		$data = $request->getParsedBody();

		$lesson = new \Models\LearningModule;
		$lesson->self_enroll = filter_var($lesson->self_enroll, FILTER_VALIDATE_BOOLEAN);
		$lesson->print_lesson = filter_var($lesson->print_lesson, FILTER_VALIDATE_BOOLEAN);
		$lesson->approval = filter_var($lesson->approval, FILTER_VALIDATE_BOOLEAN);
		$lesson->order_modules = filter_var($lesson->order_modules, FILTER_VALIDATE_BOOLEAN);
		$lesson->open_in_events_only = filter_var($lesson->open_in_events_only, FILTER_VALIDATE_BOOLEAN);
		$lesson->complete_if_linked_events_completed = filter_var($lesson->complete_if_linked_events_completed, FILTER_VALIDATE_BOOLEAN);
		$lesson->refresh = filter_var($lesson->refresh, FILTER_VALIDATE_BOOLEAN);
		$lesson->refresh_all_attached_learning_resources = filter_var($lesson->refresh_all_attached_learning_resources, FILTER_VALIDATE_BOOLEAN);
		$lesson->is_maximo_qualification = filter_var($lesson->is_maximo_qualification, FILTER_VALIDATE_BOOLEAN);
        $lesson->remove_paid_status = isset($data['remove_paid_status'])?1:0;
		$fields = [
			"code", "name", "category_id", "f_p_category_id", "keywords", "approval", "print_lesson",
			"company_id", "description", "order_modules", "accreditation_description", "due_after_period", "cost",
			"accreditation_alternative_learning_name", "refresh", "refresh_period", "refresh_repeat", "refresh_custom_email",
			"refresh_custom_email_subject", "refresh_custom_email_body", "refresh_all_attached_learning_resources",
			"copy_refresher_emails_to_line_managers", "delivery_provider_type_id", "group_department_code_id",
			"after_completion_do_not_reset_completion_state", "refresh_only_if_learning_meets_query", "further_customise_this_query", "retake_fee",
			"is_maximo_qualification", "refresh_date", "remove_paid_status", "complete_if_linked_events_completed",
			'self_enroll_access', 'editing_access', "access_duration", "refresh_date", "refresh_period_type", "badge",
			"reset_failed_quiz",
		];

		\APP\Tools::setObjectFields($lesson, $fields, $data);

		$lesson->id = \APP\Course::getNewCourseId(
			$lesson->name,
			$this->get('settings')["FixedCourseIds"],
			$this->get('settings')["CourseIdStart"]
		);

		$isEnterprise = !\APP\Tools::getConfig('sharedClients');
		$lesson->material = false;
		$lesson->is_course = 1;
		$lesson->status = 1;
		$lesson->created_by_event = 0;
		$lesson->created_by = \APP\Auth::getUserId();

		if (!empty($data['learner_access'])) {
			switch ($data['learner_access']) {
				case 1:
					$lesson->open_in_events_only = 1;
					$lesson->self_enroll = 0;
					break;
				case 2:
					$lesson->self_enroll = 1;
					$lesson->open_in_events_only = 0;
					break;
				case 3:
					$lesson->self_enroll = 0;
					$lesson->open_in_events_only = 0;
					break;
			}
		}
		$lesson->self_enroll_access = (isset($data['self_enroll']) && isset($data['self_enroll_access'])) ?  ($data['self_enroll_access'] == 0 && !$isEnterprise ? null : $data['self_enroll_access'])  : null;
		$lesson->editing_access = isset($data['editing_access']) ?  ($data['editing_access'] == 0 && !$isEnterprise ? null : $data['editing_access'])  : null;
		$lesson->save();

		if (
			isset($data['self_enroll']) &&
			$data['self_enroll']
		) {
			if (isset($data['self_enroll_access'])) {
				if ($data['self_enroll_access'] == 1 || $data['self_enroll_access'] == 2) {

					if (\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->exists()) {
						\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->delete();
					}
					if(!isset($data['companies']) || count($data['companies'])==0 && !$isEnterprise)
					{
						$data['companies'] = [Auth::getUser()->company];
					}
					foreach ($data['companies'] as $company) {
						$CompanyModuleEnrollment = new \Models\CompanyModuleEnrollment();
						$CompanyModuleEnrollment->learning_module_id = $lesson->id;
						$CompanyModuleEnrollment->company_id = $company['id'];
						$CompanyModuleEnrollment->type = 1;
						$CompanyModuleEnrollment->save();
					}
				}
			}
		}
		if (isset($data['editing_access'])) {
			if ($data['editing_access'] == 1 || $data['editing_access'] == 2) {
				if (\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 2)->exists()) {
					\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 2)->delete();
				}
				if(!isset($data['edit_access_companies']) || count($data['edit_access_companies'])==0 && !$isEnterprise)
				{
					$data['edit_access_companies'] = [Auth::getUser()->company];
				}
				foreach ($data['edit_access_companies'] as $company) {
					$CompanyModuleEnrollment = new \Models\CompanyModuleEnrollment();
					$CompanyModuleEnrollment->learning_module_id = $lesson->id;
					$CompanyModuleEnrollment->company_id = $company['id'];
					$CompanyModuleEnrollment->type = 2;
					$CompanyModuleEnrollment->save();
				}
			}
		}

		$competencies = [];
		if (isset($data["competencies"])) {
			foreach ($data["competencies"] as $c) {
				$competencies[$c["id"]] = [
					"points" => $c["points"],
				];
			}
		}

		$lesson->Competencies()->sync($competencies);



		if(isset($data['custom_field']))
		{
			Form::saveCustomForm($data['custom_field'],'lesson',$lesson->id);
		}
		if (isset($data["target_catalogues"])) {
			$target_catalogues = explode(",", $data["target_catalogues"]);
			\Models\LearningModuleTargetCatalogue::syncEntries($lesson->id, $target_catalogues);
		}

		$course_modules = isset($data["modules"]) ? \APP\Tools::getObjectIds(json_decode($data["modules"], true)) : [];
		$lesson->modules()->sync($course_modules);
		$image_response = \Models\LearningModule::uploadImages($response, $data, $_FILES, $this->get('settings'), $lesson);


		/**
		 * Insert Pivot Table Data -Learning Module WorkFlow
		 */

		$formworkflow = isset($data["formworkflow"]) ? \APP\Tools::getObjectIds(json_decode($data["formworkflow"], true)) : [];
		// need to remove all entries from learning_course_modules then sync them
		\Models\LearningModuleWorkflow::where('learning_module_id', $lesson->id)->get()->each->delete();
		$lesson->formworkflow()->sync($formworkflow);

		/*Update Lesson progress*/
		\Models\LearningCourseModule::updateTracking($lesson);
		$response->getBody()->write(json_encode($lesson->id));
		return $response;

	})->add(new RateLimitMiddleware())->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));

	// Update lesson
	$group->post('/course/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
        if(!isset($data['is_maximo_qualification'])){
            $data['is_maximo_qualification'] = false;
        }
		$data = $request->getParsedBody();

		// get all existing 	modules and detach them from users.
		$lesson = \Models\LearningModule
			::with(["modules" => function ($query) {
				$query->select("learning_modules.id", "name");
			}])
			->find($args["id"])
		;


		$module_ids_remove = [];
		foreach ($lesson->modules as $key => $value) {
			$module_ids_remove[] = $value->id;
		}
		// get all related modules to ones needed to be removed
		if (isset($data['custom_field'])) {
			Form::saveCustomForm($data['custom_field'],'lesson',$lesson->id);
		}

		$fields = [
			"code", "name", "category_id", "keywords", "self_enroll", "approval", "cost",
			"description", "order_modules", "accreditation_description", "due_after_period", "print_lesson",
			"accreditation_alternative_learning_name", "refresh", "refresh_period", "refresh_repeat", "refresh_custom_email",
			"refresh_custom_email_subject", "refresh_custom_email_body", "refresh_all_attached_learning_resources",
			"copy_refresher_emails_to_line_managers", "delivery_provider_type_id", "group_department_code_id",
			"refresh_only_if_learning_meets_query", "further_customise_this_query", "is_maximo_qualification",
			"complete_if_linked_events_completed", "remove_paid_status", "refresh_date", 'self_enroll_access',
			'editing_access', "access_duration", "retake_fee", "after_completion_do_not_reset_completion_state",
			'refresh_period_type', 'badge', "reset_failed_quiz",
		];

		if (!empty($data['learner_access'])) {
			switch ($data['learner_access']) {
				case 1:
					$lesson->open_in_events_only = 1;
					$lesson->self_enroll = 0;
					break;
				case 2:
					$lesson->self_enroll = 1;
					$lesson->open_in_events_only = 0;
					break;
				case 3:
					$lesson->self_enroll = 0;
					$lesson->open_in_events_only = 0;
					break;
			}
		}

        $data["due_after_period"] = isset($data["due_after_period"]) ? intval($data["due_after_period"]) : 0;
		\APP\Tools::setObjectFields($lesson, $fields, $data, true);
		$isEnterprise = !\APP\Tools::getConfig('sharedClients');
		$lesson->self_enroll_access = (isset($data['self_enroll']) && isset($data['self_enroll_access'])) ?  ($data['self_enroll_access'] == 0 && !$isEnterprise ? null : $data['self_enroll_access'])  : null;
		$lesson->editing_access = isset($data['editing_access']) ?  ($data['editing_access'] == 0 && !$isEnterprise ? null : $data['editing_access'])  : null;
		if ($data['f_p_category_id'] != "null") {
			$lesson->f_p_category_id = $data['f_p_category_id'];
		}

		if ($data['company_id'] != "null") {
			$lesson->company_id = $data['company_id'];
		}

		$lesson->save();

		if (
			isset($data['self_enroll']) &&
			$data['self_enroll']
			) {
				if ($data['self_enroll_access'] == 1 || $data['self_enroll_access'] == 2) {
					if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->get())) {
						\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->delete();
					}
					if(!isset($data['companies']) || count($data['companies'])==0 && !$isEnterprise)
					{
						$data['companies'] = [Auth::getUser()->company];
					}
					foreach ($data['companies'] as $company) {
						$CompanyModuleEnrollment = new \Models\CompanyModuleEnrollment();
						$CompanyModuleEnrollment->learning_module_id = $lesson->id;
						$CompanyModuleEnrollment->company_id = $company['id'];
						$CompanyModuleEnrollment->type = 1;
						$CompanyModuleEnrollment->save();
					}
				}
				else {
					if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->get())) {
						\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->delete();
					}
				}
			}
			else {
				if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->get())) {
					\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 1)->delete();
				}
			}

			if ($data['editing_access'] == 1 || $data['editing_access'] == 2) {
				if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 2)->get())) {
					\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 2)->delete();
				}
				if(!isset($data['edit_access_companies']) || count($data['edit_access_companies'])==0 && !$isEnterprise)
				{
					$data['edit_access_companies'] = [Auth::getUser()->company];
				}
				if (isset($data['edit_access_companies'])) {
					foreach ($data['edit_access_companies'] as $company) {
						$CompanyModuleEnrollment = new \Models\CompanyModuleEnrollment();
						$CompanyModuleEnrollment->learning_module_id = $lesson->id;
						$CompanyModuleEnrollment->company_id = $company['id'];
						$CompanyModuleEnrollment->type = 2;
						$CompanyModuleEnrollment->save();
					}
				}

			}
			else {
				if (!empty(\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 2)->get())) {
					\Models\CompanyModuleEnrollment::where('learning_module_id', $lesson->id)->where('type', 2)->delete();
				}
			}
			if (isset($data["target_catalogues"])) {
				$target_catalogues = explode(",", $data["target_catalogues"]);
				\Models\LearningModuleTargetCatalogue::syncEntries($lesson->id, $target_catalogues);
			}

			$lesson_changes = $lesson->getChanges();
		if (isset($data["target_catalogues"])) {
			$target_catalogues = explode(",", $data["target_catalogues"]);
			\Models\LearningModuleTargetCatalogue::syncEntries($lesson->id, $target_catalogues);
		}
		$competencies = [];
		if (isset($data["competencies"])) {
			foreach ($data["competencies"] as $c) {
				$competencies[$c["id"]] = [
					"points" => $c["points"],
				];
			}
		}
		$lesson->competencies()->sync($competencies);

			// IF due_after_period has changed, update lesson due at times!
			if (isset($lesson_changes['due_after_period'])) {
				$lesson_results = \Models\LearningResult
				::where('learning_module_id', $lesson->id)
				->where('refreshed', 0)
				->get();

			// loop learning results and update them accordingly
			foreach ($lesson_results as $key => $lesson_result) {
				$created_at = \Carbon\Carbon::parse($lesson_result->created_at);
				$lesson_result->due_at = $created_at->copy()->addDays($lesson->due_after_period);
				$lesson_result->saveWithoutEvents();
			}
		}


		$course_modules = isset($data["modules"]) ? \APP\Tools::getObjectIds(json_decode($data["modules"], true)) : [];

		// need to remove all entries from learning_course_modules then sync them
		\Models\LearningCourseModule::where('learning_course_id', $args["id"])->get()->each->delete();
		$lesson->modules()->sync($course_modules);

		/**
		 * Insert Pivot Table Data -Learning Module WorkFlow
		 */

		$formworkflow = isset($data["formworkflow"]) ? \APP\Tools::getObjectIds(json_decode($data["formworkflow"], true)) : [];
		// need to remove all entries from learning_course_modules then sync them
		\Models\LearningModuleWorkflow::where('learning_module_id', $args["id"])->get()->each->delete();
		$lesson->formworkflow()->sync($formworkflow);


		$users_assigned = \Models\UserLearningModule
			::where('learning_module_id', $args["id"])
			->get();
		// Possibly wrong way of getting active assigned users to this course, need to research more.

		$removed_modules = array_diff($module_ids_remove, $course_modules);
		$added_modules = array_diff($course_modules, $module_ids_remove);

		if (
			!empty($removed_modules) ||
			!empty($added_modules)
		) {
			foreach ($users_assigned as $key => $user_assigned) {
				$result = \Models\LearningResult
					::where('learning_module_id', $lesson->id)
					->where('user_id', $user_assigned->user_id)
					->where('refreshed', false)
					->first()
				;


				$user = \Models\User::find($user_assigned->user_id); // get user(that is assigned to course) object.

				// detach modules assigned to course (before this course was saved) from user, clean up
				if (!empty($removed_modules)) {
					\Models\UserLearningModule::unlinkResources($user->id, $removed_modules, 'lesson updated, remove removed resources');
				}

				// Detach/attach modules from user using modules submitted in this request
				if (
					!empty($added_modules) &&
					!(
						$lesson->after_completion_do_not_reset_completion_state &&
						$result->completion_status == 'completed'
					)
				) {
					\Models\UserLearningModule::linkResources($user->id, $added_modules, 'lesson updated, assign added resources');
				}


				// do the syncing for user
				\APP\Learning::syncUserResults($user->id);
			}
		}

		$image_response = \Models\LearningModule::uploadImages($response, $data, $_FILES, $this->get('settings'), $lesson);
		\Models\LearningCourseModule::updateTracking($lesson);
		$response->getBody()->write(json_encode($lesson->id));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(new RateLimitMiddleware())->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	// Gets a list of all modules(unpaginated), except lessons and evidence created for sub-criteria.
	$group->get('/module/all', function (Request $request, Response $response) {
		session_write_close();
		$query = [];
		if (
			\APP\Auth::isAdminInterface() ||
			\APP\Auth::isDistributor()
		) {
			$query = \Models\LearningModule
				::select(
					"id",
					"name",
					"type_id",
					"category_id",
					"created_by",
					"f_p_category_id",
					"is_course",
					"is_skill"
				)
				->where("status", true)
				->where("is_course", false)
				->where('guideline', false)
				->with(["Type" => function ($query) {
					$query->select("id", "name", "slug");
				}])
				->with(["Category" => function ($query) {
					$query->select("id", "name");
				}])
				->with(["CreatedBy" => function ($query) {
					$query->select("id", "fname", "lname");
				}]);

			if ($this->get('settings')['licensing']['isSMCR']) {
				$query = $query
					->with('FPCategory');
			}

			$query = \Models\LearningModule::filterEvidenceforRoles($query);
			$query = cache()->remember('learning_module_all', 600, function () use ($query) {
				return $query->get()->toArray();
			});
		}

		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));

	$group->get('/course/all', function (Request $request, Response $response) {
		session_write_close();
		$query = [];
		if (\APP\Auth::isAdminInterface()) {
			$query = \Models\LearningModule
				::where("status", true)
				->select(
					'id',
					'name',
					'description',
					'category_id',
					'refresh',
					'refresh_period',
					'refresh_date',
					'refresh_period_type',
					'created_by_event',
					'open_in_events_only',
					'complete_if_linked_events_completed'
				)
				->where("is_course", 1)
			;
			if (!\APP\Auth::accessAllCompanies()) {
				$query->where(function($q) {
					$q->where('editing_access', 0)
					  ->orWhere(function($subQuery) {
						  $subQuery->where('editing_access', 1)
							->whereHas('editAccessCompanies', function ($subSubQuery) {
								$subSubQuery->where('company_id', \APP\Auth::getUserCompanyId());
							});
					  })
					  ->orWhere(function($subQuery) {
						  $subQuery->where('editing_access', 2)
							->whereDoesntHave('editAccessCompanies', function ($subSubQuery) {
								$subSubQuery->where('company_id', \APP\Auth::getUserCompanyId());
							});
					  });
				});
				$query = $query->get();
			} else {
				$query = cache()->remember('course_all', 600, function () use ($query) {
					return $query->get()->toArray();
				});
			}
		}
		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));

	// to show in events
	$group->get('/course/all-for-events', function (Request $request, Response $response) {
		$query = [];
		if (\APP\Auth::isAdminInterface()) {
			$query = \Models\LearningModule
				::where("status", true)
				->where('open_in_events_only', true)
				->select(
					'id',
					'name',
					'description',
					'category_id',
					'refresh',
					'refresh_period',
					'refresh_date',
					'refresh_period_type',
					'created_by_event',
					'open_in_events_only'
				)
				->where("is_course", 1)
			;

			if (!\APP\Auth::accessAllCompanies()) {
				$query = $query
					->where('company_id', \APP\Auth::getUserCompanyId());
					$query = $query->get();
			} else {
				$query = cache()->remember('event_course_all', 600, function () use ($query) {
					return $query->get()->toArray();
				});
			}

		}

		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;

	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));

	$group->get('/types/all', function (Request $request, Response $response) {
		session_write_close();
		$query = \Models\LearningModuleType
			::with(["LearningModuleTypeParameter" => function ($query) {
				$query
					->orderBy('position', 'asc')
					->where("status", 1);
			}])
			->where("status", 1)
			->whereNotIn("id", $this->get('settings')['licensing']['hiddenResourceTypes'])
		;

		$query = $query
			->get();

		foreach ($query as $key => $item) {
			foreach ($item->LearningModuleTypeParameter as $parameter_key => $parameter) {
				$parameter->ngm = 'learning.material.' . $parameter->parameterslug;
			}
		}

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));

	// Validates??!!!
	$group->group("/validate", function ($group) {
		$group->get('/code/{code: .+}', function (Request $request, Response $response, $args) {

			$code = $args["code"];
			$params = $request->getQueryParams();

			if (isset($params["exclude_id"])) {
				if (
					\Models\LearningModule::where("code", "=", $code)
					->where("id", "<>", $params["exclude_id"])->first()
				) {
					return \APP\Tools::returnCode($request, $response, 409, 'Already exists');
				}
			}

			$response->getBody()->write("ok");
			return $response;

		});
	})->add(\APP\Auth::getSessionCheck());

	// list all users related to "learning_modules" table by field "created_by"
	$group->get('/createdbyusers/list', function (Request $request, Response $response) {
		session_write_close();
		$query = [];

		if (\APP\Auth::checkStructureAccess(['library-learning-resources-and-lessons'], 'select')) {
			$query = \Models\User
				::join("learning_modules", function ($join) {
					$join
						->on("learning_modules.created_by", "=", "users.id");
				})
				->select("users.id", "users.fname", "users.lname")
				->groupBy("users.id")
				->get();
		}
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// list all resources that are active, without relations
	// TODO: filter out evidence that is created by/for learners not assigned to you
	$group->get('/list{type:[\/a-z\-]*}{user_id:[\/0-9]*}', function (Request $request, Response $response, $args) {
		$queryParams = $request->getQueryParams();
		$query = \Models\LearningModule
			::where('status', true)
			->where('guideline', false)
			->where(function ($query) use ($queryParams) {
				$query = $query
					->where(function ($query) use ($queryParams) {
						$query = $query
							->where('learning_modules.is_course', 1)
							->where('open_in_events_only', false);
						if (isset($queryParams['diable_event_lesson'])) {
							$query->where('created_by_event', 0);
						}
					})
					->orWhere('learning_modules.is_course', 0);
			});

		if (isset($queryParams['user_id'])) {
			$query->with(['UserLearningModules' => function ($query) use ($queryParams) {
				$query->where('user_id', $queryParams['user_id']);
			}]);
		}
		if (
			isset($args['type']) &&
			(
				$args['type'] == '/full' ||
				$args['type'] == '/full/'
			)
		) {
			$query = $query
				->select(
					'id',
					'name',
					'category_id',
					'f_p_category_id',
					'type_id',
					'is_course',
					'company_id'
				)
				->with('category')
				->with('FPCategory')
				->with('type')
				->with("competencies")
				->with("company");
		} else {
			$query = $query
				->select('id', 'name');
		}

		// If user_id is passed,return flag with resource if user is assigned said resource
		if (
			isset($args['user_id']) &&
			$args['user_id']
		) {
			$query = $query
				->addSelect(
					DB::raw(
						"
							(
								case when (
									select count(*) from user_learning_modules where user_id = " . $args['user_id'] . " and learning_module_id = learning_modules.id and deleted_at is null
								) > 0
									then true
									else false
								end
							) as assigned
						"
					)
				);
		}

		// You don't need to see evidence resource added by learners not assigned to you
		$query = \Models\LearningModule::filterEvidenceforRoles($query);
		$query = $query
			->get();

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));

	// learning page, paginated
	// filter out evidence that is created by/for learners not assigned to you
	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response,array $args) {
		$params = $request->getParsedBody();
		$user = \APP\Auth::getUser();
		$isEnterPriseVersion = !\APP\Tools::getConfig('sharedClients');

		if (isset($params["search"]["additionalSearchParams"])) {
			$additional_search_params = $params["search"]["additionalSearchParams"];
			unset($params["search"]["additionalSearchParams"]);
		}

		$query = \Models\LearningModule
			::select([
				"learning_modules.*",
				DB::raw("DATE_FORMAT(learning_modules.created_at, '" . \APP\Tools::defaultDateFormatMYSQL() . "') AS formatted_created_at")
			])
			->with("category")
			->with('FPCategory')
			->with("type")
			->with("competencies")
			->with("company")
			->with("versions")
			->with(['TargetCatalogues' => function ($query) {
				$query
					->wherePivot('deleted_at', null)
					->where('target_catalogues.status', true);
			}])
			->with('LearningModuleGroupDepartmentCode')
			->with(["createdby" => function ($query) {
				$query->select("id", "fname", "lname");
			}])
			->where('guideline', false)
		;
		if (Tools::getConfig('showExtraResourceFields_TDG')) {
			$query
				->leftJoin('learning_module_group_department_codes', 'learning_module_group_department_codes.id', 'learning_modules.group_department_code_id');
		}
		if (isset($params['search'])) {
			if (isset($params['search']['target_catalog_id'])) {
				$query->whereHas('TargetCatalogues', function ($query) use ($params) {
					$query->where('learning_module_target_catalogues.deleted_at', null);
					$query->where('target_catalogues.status', true)->where('target_catalogues.id', $params['search']['target_catalog_id']);
				});
				unset($params['search']['target_catalog_id']);
			}
			if (isset($params['search']['status'])) {
				$params['search']['learning_modules__status'] = $params['search']['status'];
				unset($params['search']['status']);
			}
			if (isset($params['search']['learning_modules__created_by'])) {
				$params['search']['learning_modules__created_by'] = intval($params['search']['learning_modules__created_by']);
			}
		}
		if (
			isset($params["search"]["is_skill"])
			&& $params["search"]["is_skill"]
		) {
			$query = $query->where("learning_modules.is_skill", $params["search"]["is_skill"]);
		}

		if (
			isset($params["search"]["is_course"]) &&
			$params["search"]["is_course"]
		) {
			// Hide lessons linked to events, V3
			$query = $query
				->where('learning_modules.created_by_event', 0);
		}



		// If manager, check if there is need to filter out categories
		$query = \Models\ManagerLearningModuleCategory::checkManagerAccessToCategories($query);



		// If you are manager/admin, show evidence created by you for your trainees or by you for re-use, or by other managers for reuse
		// If you are trainee, show evidence created for you or managers/admin

		if (
			!\APP\Auth::isAdmin() ||
			(
				\APP\Auth::isAdmin() &&
				!\APP\Auth::showAllResources()
			)
		) {
			$query = \Models\LearningModule::filterEvidenceforRoles($query);

			// Also hide any zoom/teams resources created in events
			$query = $query
				->where(function ($query) {
					$query
						->where(function ($query) {
							$query
								->whereDoesntHave('Type', function ($query) {
									$query
										->where('slug', 'microsoft_teams')
										->orWhere('slug', 'zoom_meeting');
								});
						})
						->orWhere(function ($query) {
							$query
								->whereHas('Type', function ($query) {
									$query
										->where('slug', 'microsoft_teams')
										->orWhere('slug', 'zoom_meeting');
								})
								->whereNotExists(function ($query) {
									$query->select(DB::raw(1))
										->from('schedule_links')
										->where('schedule_links.type', 'resources')
										->where('schedule_links.status', 1)
										// ->whereNull('schedule_links.deleted_at')
										->whereRaw('schedule_links.link_id = learning_modules.id');
								});
						});
				});
		}

		//  show only permitted learning resources if user type is not admin

		if (!\APP\Auth::isAdmin()) {
			$companyId = \APP\Auth::getUser()->company_id;
            $query = $query->where(function($query) use ($companyId, $isEnterPriseVersion) {

				// used to get the existing  data
				$query->where(function($subQuery) use ($companyId, $isEnterPriseVersion) {
					if ($isEnterPriseVersion) {
						$subQuery->whereNull('learning_modules.editing_access');
					} else {
						$subQuery->whereNull('learning_modules.editing_access')
								 ->where('learning_modules.company_id', $companyId);
					}
				});
				// end of used to get the existing  data

				// Case 1: Open to all companies
				$query->orwhere('learning_modules.editing_access', 0);

				// // Case 2: Accessible only to selected companies
				$query->orWhere(function($subQuery) use ($companyId) {
					$subQuery->where('learning_modules.editing_access', 1)
							->whereHas('companyModuleEnrollments', function($q) use ($companyId) {
								$q->where('company_module_enrollment.company_id', $companyId)->where('company_module_enrollment.type',2);
					});
				});

				// // Case 3: Accessible to all except selected companies
				$query->orWhere(function($subQuery) use ($companyId) {
					$subQuery->where('learning_modules.editing_access', 2)
							->whereDoesntHave('companyModuleEnrollments', function($q) use ($companyId) {
								$q->where('company_module_enrollment.company_id', $companyId)->where('company_module_enrollment.type',2);
							});
				});
			});
		}

		if (isset($params["search"])) {
			if (isset($params["search"]["competency_id"])) {
				$query->join("learning_module_competencies", function ($join) use ($params) {
					$join->on("learning_module_competencies.learning_module_id", "=", "learning_modules.id")
						->where("learning_module_competencies.competency_id", "=", $params["search"]["competency_id"]);
				});

				unset($params["search"]["competency_id"]);
			}
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}

			if (isset($params["search"]["company_id"])) {
				$query
					->where("company_id", "=", $params["search"]["company_id"]) //->orWhereNull("company_id")
				;
				unset($params["search"]["company_id"]);
			}

			//If lesson_id is passed, look if resource is assigned to that lesson and return indicator that so
			if (isset($params["search"]["lesson_id"])) {
				$lesson_id = $params["search"]["lesson_id"];
				unset($params["search"]["lesson_id"]);
				$query = $query
					->withCount(['Course' => function ($query) use ($lesson_id) {
						$query
							->where('learning_course_id', $lesson_id);
					}]);

				if (isset($params["search"]["added"])) {
					$added = $params["search"]["added"];
					unset($params["search"]["added"]);
					$query = $query
						->whereHas('Course', function ($query) use ($added, $lesson_id) {
							if ($added == 1) {
								$query
									->where('learning_course_id', $lesson_id);
							} else {
								$query
									->where('learning_course_id', '!=', $lesson_id);
							}
						});
				}
			}

			// If schedule ID is passed, return count of schedules resource is assigned to (target should be 1)
			$query = \Models\Schedule::countAndConditions($query, $params);
		}

		// If user is not admin and this is client shared site, then list only resources in his company.
		if (
			!\APP\Auth::isAdmin() &&
			\APP\Tools::getConfig('sharedClients')
		) {
			// If user does not have company, do not list any resource at all
			if ($user->company_id) {
				$query = $query
                    ->where(function($query)use($user){
                        $query->where('company_id', $user->company_id)
                        ->orWhereIn('learning_modules.name',LearningModule::$catelog);
                    })
				;
			} else {
				$query = $query
					->where('learning_modules.id', '<', 1)
				;
			}
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
			$export_fields = [
				"ID" => "id",
				"Name" => "name",
				"Category" => "category.name",
				"Type" => "type.name",
				"Company" => "company.name",
				"createdby" => "createdby",
				"Average time spent" => 'total_duration',
				"Number of times completed" => "users_count",
				"Created Date" => "formatted_created_at"
			];
			if (Tools::getConfig('showExtraResourceFields_TDG')) {
				$export_fields = [
					"ID" => "id",
					"Name" => "name",
					"Category" => "category.name",
					"Type" => "type.name",
					Templates::translate("%%target_catalogue%%") => "targetcatalogues",
					Templates::translate("%%group_department_code%%") => "LearningModuleGroupDepartmentCode.name",
					"Company" => "company.name",
					"createdby" => "createdby",
					"Average time spent" => 'total_duration',
					"Number of times completed" => "users_count",
					"Created Date" => "formatted_created_at"
				];
			}
			$download_file_name = uniqid("resource.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} elseif (isset($args["download"]) && $args["download"] == "/print") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
			$response->getBody()->write(json_encode($data));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		foreach ($p as $learning) {
			$learning->setAppends(['safe_thumbnail']);
		}

		// Will loop all responses and if isJackdawCloud is enabled, will return parameter with learning that will show jackdaw button
		if ($this->get('settings')['licensing']['isJackdawCloud']) {
			foreach ($p as $key => $learning) {
				if (
					$user->role->jackdaw_type &&
					$learning->type_id == 1 &&
					$learning->jackdaw == 1
				) {
					$learning->jackdaw = 0;

					// if jackdaw_type is CMS, allow to edit all e-learning resources
					if (
						$user->role->jackdaw_type == 'CMS' ||
						$user->role->jackdaw_type == 'Unlimited'
					) {
						$learning->jackdaw = 1;

						// Else check if team, then show jackdaw button from everyone in team.
					} elseif ($user->role->jackdaw_type == 'Team') {
						foreach ($user->groups as $key => $group) {
							if ($learning->created_by_group == $group->id && $group->is_jackdaw_team) {
								$learning->jackdaw = 1;
							}
						}
						// Last one, check if learning was created by user.
					} elseif (
						$learning->created_by == $user->id &&
						$learning->jackdaw_resource == true // means it was created using jackdaw editor
					) {
						$learning->jackdaw = 1;
					}
				} else {
					$learning->jackdaw = 0;
				}
			}
		}

		$json = $p->toJson();

		$response
			->getBody()
			->write($json)
		;
		return
			$response
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));

	$group->post('/lessons/list{download:[\/a-z]*}', function (Request $request, Response $response,array $args) {
		$params = $request->getParsedBody();
		$user = \APP\Auth::getUser();

		if (isset($params["search"]["additionalSearchParams"])) {
			$additional_search_params = $params["search"]["additionalSearchParams"];
			unset($params["search"]["additionalSearchParams"]);
		}


		$query = \Models\LearningModule
			::select("learning_modules.*", DB::raw("DATE_FORMAT(learning_modules.created_at, '" . \APP\Tools::defaultDateFormatMYSQL() . "') AS formatted_created_at"))
			->with("category")
			->with('FPCategory')
			->with("type")
			->with("competencies")
            ->with("linked")
			->with("company")
			->with("versions")
			->with('LearningModuleGroupDepartmentCode')
			->with('TargetCatalogues')

			->with(["createdby" => function ($query) {
				$query->select("id", "fname", "lname");
			}])
			->where('is_course', true)
			->where('guideline', false);
		if (Tools::getConfig('showExtraResourceFields_TDG')) {
			$query->leftJoin('learning_module_group_department_codes', 'learning_module_group_department_codes.id', 'learning_modules.group_department_code_id');
		}
		if (isset($params['search'])) {
			if (isset($params['search']['target_catalog_id'])) {
				$query->whereHas('TargetCatalogues', function ($query) use ($params) {
					$query->where('learning_module_target_catalogues.deleted_at', null);
					$query->where('target_catalogues.status', true)->where('target_catalogues.id', $params['search']['target_catalog_id']);
				});
				unset($params['search']['target_catalog_id']);
			}
			if (isset($params['search']['status'])) {
				$params['search']['learning_modules__status'] = $params['search']['status'];
				unset($params['search']['status']);
			}
		}
		if (
			isset($params["search"]["is_skill"])
			&& $params["search"]["is_skill"]
		) {
			$query = $query->where("is_skill", $params["search"]["is_skill"]);
		}

		if (
			isset($params["search"]["is_course"]) &&
			$params["search"]["is_course"]
		) {
			// Hide lessons linked to events, V3
			$query = $query->where('created_by_event', 0);
		}

		// If manager, check if there is need to filter out categories
		$query = \Models\ManagerLearningModuleCategory::checkManagerAccessToCategories($query);



		// If you are manager/admin, show evidence created by you for your trainees or by you for re-use, or by other managers for reuse
		// If you are trainee, show evidence created for you or managers/admin

		if (
			!\APP\Auth::isAdmin() ||
			(
				\APP\Auth::isAdmin() &&
				!\APP\Auth::showAllResources()
			)
		) {
			$query = \Models\LearningModule::filterEvidenceforRoles($query);

			// Also hide any zoom/teams resources created in events
			$query
				->whereNotIn(
					'id',
					\Models\LearningModule
						::select('id')
						->whereIn(
							'type_id',
							\Models\LearningModuleType
								::select('id')
								->where('slug', 'microsoft_teams')
								->orWhere('slug', 'zoom_meeting')
								->get()
						)
						->whereIn(
							'id',
							\Models\ScheduleLink
								::select('link_id')
								->where('type', 'resources')
								->get()
						)
						->get()
				);
		}

		if (isset($params["search"])) {
			if (isset($params["search"]["competency_id"])) {
				$query->join("learning_module_competencies", function ($join) use ($params) {
					$join->on("learning_module_competencies.learning_module_id", "=", "learning_modules.id")
						->where("learning_module_competencies.competency_id", "=", $params["search"]["competency_id"]);
				});

				unset($params["search"]["competency_id"]);
			}
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}

			if (isset($params["search"]["company_id"])) {
				$query->where("company_id", "=", $params["search"]["company_id"]) //->orWhereNull("company_id")
				;
				unset($params["search"]["company_id"]);
			}

			//If lesson_id is passed, look if resource is assigned to that lesson and return indicator that so
			if (isset($params["search"]["lesson_id"])) {
				$lesson_id = $params["search"]["lesson_id"];
				unset($params["search"]["lesson_id"]);
				$query = $query
					->withCount(['Course' => function ($query) use ($lesson_id) {
						$query
							->where('learning_course_id', $lesson_id);
					}]);

				if (isset($params["search"]["added"])) {
					$added = $params["search"]["added"];
					unset($params["search"]["added"]);
					$query = $query
						->whereHas('Course', function ($query) use ($added, $lesson_id) {
							if ($added == 1) {
								$query
									->where('learning_course_id', $lesson_id);
							} else {
								$query
									->where('learning_course_id', '!=', $lesson_id);
							}
						});
				}
			}

			// If schedule ID is passed, return count of schedules resource is assigned to (target should be 1)
			$query = \Models\Schedule::countAndConditions($query, $params);
		}
		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
			$export_fields = [
				"ID" => "id",
				"Name" => "name",
				"Category" => "category.name",
				"Company" => "company.name",
				"Created By" => "createdby",
				"Created Date" => "formatted_created_at",
			];
			if (Tools::getConfig('showExtraResourceFields_TDG')) {
				$export_fields = [
					"id" => "id",
					"name" => "name",
					"category" => "category.name",
					templates::translate("%%target_catalogue%%") => "targetcatalogues",
					templates::translate("%%group_department_code%%") => "learningmodulegroupdepartmentcode.name",
					"company" => "company.name",
					"Created By" => "createdby",
					"Created Date" => "formatted_created_at",
				];
			}

			$download_file_name = uniqid("lessons.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} elseif (isset($args["download"]) && $args["download"] == "/print") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
			$response->getBody()->write(json_encode($data));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		foreach ($p as $learning) {
			$learning->setAppends(['safe_thumbnail']);
		}
		$json = $p->toJson();

		$response->getBody()->write($json);
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));


	// Update all installed learning resources if found in installation folder, preserve quizxml and information.xml
	$group->put('/available/update/all', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

        $installedResources = \Models\AvailableModule::list(['all'=>true,'type'=>'scorm']);
		$updated_resources = [];
		$failed_resources = [];
		foreach ($installedResources as $key => $installedResource) {

			// ignoring errors for update all
            try {
				$source_location = $this->get('settings')["AvailableModulesLocation"] . "/" . $installedResource->name;
				$source_location = $source_location = \Models\AvailableModule::download($available_module->id,$this->get('settings')['LMSTempPath'].$available_module->original_filename,$this->get('settings')['LMSTempPath']);
                if (is_dir($source_location) && $available_module->title) {
                    $learning_module = \Models\LearningModule
                        ::where("name", $available_module->title)
                        ->where('type_id', 1)
                        ->first();
					$course = \APP\Course::get($learning_module->id);
					$course->copyAvailableModule(
						$source_location,
						$this->get('settings')["LMSScormDataPath"],
						true,
						isset($params['force']) && $params['force'] ? false : true // true will preserve quizxml and information.xml, force - false will overwrite everything.
					);

					// Update room files
					\APP\Jackdaw::updateRoomFiles($this->get('settings')["LMSPublicPath"], $installedResource->id);
                    $updated_resources[] = $learning_module->name;
				}
            } catch (Exception $e) {
				$failed_resources[] = $learning_module>name;
			}
        }

		$response->getBody()->write(
					json_encode(
						[
							"updated_resources" => $updated_resources,
							"failed_resources" => $failed_resources
						]
					)
				);
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->put('/available/update/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		// First, find the local learning module by its ID
		$learning_module = \Models\LearningModule::find($args["id"]);

		if (!$learning_module) {
			return \APP\Tools::returnCode($request, $response, 404, "Learning module not found");
		}

		// Use library_item_id if available, otherwise fallback to the learning module's own ID
		$library_item_id = $learning_module->library_item_id ?: $learning_module->id;

		// Now fetch the library resource using the library_item_id or fallback ID
		$available_module = \Models\AvailableModule::getItem($library_item_id);

		if ($available_module) {
			try {
				// We already have the learning module, no need to search again
				if ($learning_module->type_id != 1) {
					throw new Exception("Not e-learning resource!");
				}

				// Download the latest version from the library
				$source_location = \Models\AvailableModule::download(
					$library_item_id,
					$this->get('settings')['LMSTempPath'] . $available_module->original_filename,
					$this->get('settings')['LMSTempPath']
				);

				if ($source_location && is_dir($source_location)) {
					$course = \APP\Course::get($learning_module);
					$course->copyAvailableModule(
						$source_location,
						$this->get('settings')["LMSScormDataPath"],
						true
					);
					// Update room files
					\APP\Jackdaw::updateRoomFiles($this->get('settings')["LMSPublicPath"], $learning_module->id);
				} else {
					return \APP\Tools::returnCode($request, $response, 404, 'Failed to download source learning resource from library!');
				}
			} catch (\APP\ScormException $e) {
				return \APP\Tools::returnCode($request, $response, 500, "SCORM error during update: " . $e->getMessage());
			} catch (Exception $e) {
				return \APP\Tools::returnCode($request, $response, 500, 'An error occured while updating the course: ' . $e->getMessage());
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 404, "Library resource not found (ID: {$library_item_id})");
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->get('/available/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$available_module_name = "";
		$available_module = \Models\AvailableModule::getItem($args["id"]);
		if ($available_module) {

			$available_module->description = "This course has been imported from Open eLMS Catalogue";
			if (isset($this->get('settings')["DefaultCourseDescriptions"][$available_module->title])) {
				$available_module->description = $this->get('settings')["DefaultCourseDescriptions"][$available_module->title];
				}


			if (isset($this->get('settings')["DefaultCourseKeywords"][$available_module->title])) {
				$available_module->keywords = $this->get('settings')["DefaultCourseKeywords"][$available_module->title];
			}
			$available_module_array = $available_module->toArray();

			$available_module_array['material']['scorm_standard'] = "2"; // SCORM 1.2
			$available_module_array['material']['min_passing_percentage'] = "70";
			$available_module_array['material']['course_complete_status'] = "0"; // Passing Non Mandatory

			$response->getBody()->write(json_encode($available_module));
			return $response->withHeader('Content-Type', 'application/json');

		} else {
			return $response;
		}
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));

	$group->post('/install-available', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

        $available_module = \Models\AvailableModule::getItem($data["resource_id"]);
		if ($available_module) {
			$module_data['description'] = "This course has been imported from Open eLMS Catalogue";

			if (isset($this->get('settings')["DefaultCourseDescriptions"][$available_module->title])) {
				$module_data['description'] = $this->get('settings')["DefaultCourseDescriptions"][$available_module->title];
			}
			if($available_module->description){
				$module_data['description'] = $available_module->description;
			}
			$module_data['cost'] = $available_module->price;
			$module_data['keywords'] = $available_module->keywords;


			$module_data['material']['scorm_standard'] = "2"; // SCORM 1.2
			$module_data['material']['min_passing_percentage'] = "70";
			$module_data['material']['course_complete_status'] = "0"; // Passing Non Mandatory
			$module_data['availableModuleId'] = $data["resource_id"];
			$module_data['library_item_id'] = $data["resource_id"];
            $module_data['name'] = $available_module->title;
			$module_data['type_id'] = 1; // e-learning
			$learning = \Models\LearningModule::insertImportModule($module_data, $this->get('settings'), $response);
			if (isset($learning["error"])) {
				return \APP\Tools::returnCode($request, $response, 500, $learning["error"]);
			} else {
				$response->getBody()->write((string)$learning["id"]);
				return $response;
			}
		} else {
			return $response;
		}

	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));

	// List all available learning resources for installation/update
	$group->post('/available/list', function (Request $request, Response $response) {

		$params = $request->getParsedBody();
		$params['type']='scorm';
		$data = \Models\AvailableModule::list($params);

		// Check installation status against local LearningModule table
		if (isset($data['data']) && is_array($data['data'])) {
			foreach ($data['data'] as &$item) {
				// Look for existing module by name (title from library becomes name in LMS)
				$existingModule = \Models\LearningModule::where('name', $item['name'])->first();

				if ($existingModule) {
					// Module is installed
					$item['installed'] = true;
					$item['enabled'] = $existingModule->status == 1;
					$item['disabled'] = $existingModule->status == 0;
					$item['local_module_id'] = $existingModule->id;
				} else {
					// Module is not installed
					$item['installed'] = false;
					$item['enabled'] = false;
					$item['disabled'] = false;
					$item['local_module_id'] = null;
				}
			}
		}

		if($data['next_page_url']){
		  $parsed_url = parse_url($data['next_page_url']);
		  parse_str($parsed_url['query'], $query_params);
		  $data['next_page_url'] = "/?".http_build_query($query_params);
		}
		if($data['prev_page_url']){
		  $parsed_url = parse_url($data['prev_page_url']);
		  parse_str($parsed_url['query'], $query_params);
		  $data['prev_page_url'] = "/?".http_build_query($query_params);
		}
		if($data['first_page_url']){
		  $parsed_url = parse_url($data['first_page_url']);
		  parse_str($parsed_url['query'], $query_params);
		  $data['first_page_url'] = "/?".http_build_query($query_params);
		}
		$data['path'] = "/";

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));

	// Add comment to learning result
	$group->post('/addlearningresultscomment', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		if (!isset($data["user_id"])) {
			$data["user_id"] = \APP\Auth::getUserId();
		}

		// Manager is submitting comment.
		//\APP\Auth::isManager()
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($data["user_id"]) ||
			$data["user_id"] == \APP\Auth::getUserId() ||
			\APP\Auth::accessAllLearners() ||
			\APP\Auth::isProcessingManagerOf($data["learning_results_id"])
		) {

			$learningResult = \Models\LearningResult
				::find($data["learning_results_id"])
			;

			$query = new \Models\LearningResultsComment;
			$query->comment_by_user_id = \APP\Auth::getUserId();
			$query->created_for_user_id = $data["user_id"];
			$query->learning_module_id = $data["learning_module_id"];
			$query->learning_results_id = $data["learning_results_id"];
			if (!\APP\Auth::isLearner()) {
				$query->visible_learner = isset($data["visible_learner"]) ? $data["visible_learner"] : false;
			}
			$query->comment = $data["comment"];
			if (\APP\Auth::isQa()) {
				$query->qa = true;
			}
			$query->save();

			// If manager refused with this comment update learning results table's manager_refused fields.
			if (
				isset($data['manager_refused']) &&
				$data['manager_refused']
			) {

				$learningResult->manager_refused_comment = $data["comment"];
				$learningResult->manager_refused_time = \Carbon\Carbon::now();
				$learningResult->manager_refused_by = \APP\Auth::getUserId();
			}

			// If learner, update last active date in user's table
			if (\APP\Auth::isLearner()) {
				$user = \APP\Auth::getUser();
				$user->last_contact_date = \Carbon\Carbon::now();
				$user->save();


				// Send e-mail to learner's managers
				// Send out "Learning Resource Comment for Manager" to learner
				$template = \Models\EmailTemplate
					::where('name', 'Learning Resource Comment for Manager')
					->where('status', true)
					->first();

				$manager_ids = [];
				foreach ($user->managers as $key => $manager) {
					if (
						!$manager->role->email_disable_manager_notifications &&
						$manager->status
					) {
						$manager_ids[] = $manager->id;
					}
				}

				if (
					$template &&
					$template->id &&
					count($manager_ids) > 0 &&
					$learningResult->module->track_progress
				) {
					$email_queue = new \Models\EmailQueue;
					$email_queue->email_template_id = $template->id;
					$email_queue->learning_module_id = $data["learning_module_id"];
					$email_queue->recipients = $manager_ids;
					$email_queue->comment = $data["comment"];
					$email_queue->from = \APP\Auth::getUserId();
					$email_queue->custom_variables = json_encode([
						'COMMENT_ID' => $query->id,
						'FROM' => 'learner',
					]);
					$email_queue->save();
				}
				$learningResult->learner_action = false;
				$learningResult->save();
			} else {

				if (isset($data["grade_score"])) {
					$learningResult->score = $data["grade_score"];
				}

				if (isset($data["grade_level"])) {
					$learningResult->grade = $data["grade_level"];
				}

				$learningResult->learner_action = true;
				$learningResult->learner_action_date = \Carbon\Carbon::now();
				$learningResult->save();
			}

			// Update last contact date against users standard pivot if this resource is assigned to any standard.
			\Models\ApprenticeshipStandardUser::lastUpdate($data["learning_module_id"], $data["user_id"]);

			// Send e-mail to learner and update last contact's date for learner
			if (
				\APP\Auth::isManager() ||
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners()
			) {
				$user_update_date = \Models\User::find($data["user_id"]);
				$user_update_date->last_contact_date = \Carbon\Carbon::now();
				$user_update_date->save();

				// Update managers activity date also
				$manager_update_date = \Models\User::find(\APP\Auth::getUserId());
				$manager_update_date->last_contact_date = \Carbon\Carbon::now();
				$manager_update_date->save();

				// Do not send if you are QA
				if (
					!\APP\Auth::isQa() &&
					$query->visible_learner
				) {

					$send_email = \APP\Smcr::sendLearnerEmail($learningResult);

					$template = \Models\EmailTemplate::getTemplate('%%learning_resource%% Comment for %%user%%');
					if (
						$template &&
						$learningResult->module &&
						$learningResult->module->track_progress
					) {
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->learning_module_id = $data["learning_module_id"];
						$email_queue->recipients = [intval($data["user_id"])];
						$email_queue->comment = $data["comment"];
						$email_queue->from = \APP\Auth::getUserId();
						$email_queue->custom_variables = json_encode([
							'COMMENT_ID' => $query->id,
							'FROM' => 'manager',
						]);
						$email_queue->save();
					}
				}
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$response->getBody()->write(json_encode($query->id));
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results', 'misc-permissions-learning-results-comments'], 'insert'));

	$group->put("/comment-visibility/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (
			\APP\Auth::isDemoUser() ||
			\APP\Auth::isLearner()
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$comment = \Models\LearningResultsComment::find($args['id']);
		$comment->visible_learner = !$comment->visible_learner;
		$comment->save();
		return $response;

	})->add(\APP\Auth::getSessionCheck());

	//Comment list for learner interface.
	$group->get('/results/{learning_results_id:[0-9]+}/commentslist', function (Request $request, Response $response, $args) {
		$query = \Models\LearningResultsComment
			::where('learning_results_id', $args['learning_results_id'])
			->where('created_for_user_id', \APP\Auth::getUserId())
			->where('status', true)
			->where('visible_learner', true)
			->with(["createdby" => function ($query) {
				$query->select("id", "fname", "lname");
			}])
			->with(["files" => function ($query) {
			}])
			->where('qa', false) // Hide QA comments for learner
			->get();

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	// Update duration for learning results by trainee
	$group->put('/update-duration/{learning_results_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (
			isset($data['user_id']) &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($data["user_id"]) ||
				\APP\Auth::accessAllLearners()
			)
		) {
			$user = \Models\User::findOrFail($data['user_id']);
		} else {
			$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		}

		$learning_result = \Models\LearningResult
			::where("id", '=', $args["learning_results_id"])
			->where("user_id", "=", $user->id)
			->with('module')
			->firstOrFail();

		if (\APP\Auth::isLearner()) {
			$learning_result->learner_action = false;
		}

		if (
			(
				isset($data['duration_hours']) ||
				isset($data['duration_minutes'])
			) &&
			(
				$learning_result->module->duration_change ||
				\APP\Auth::isAdminInterface()
			)
		) {
			if (isset($data['duration_hours'])) {
				$learning_result->duration_hours = $data['duration_hours'];
			}
			if (isset($data['duration_minutes'])) {
				$learning_result->duration_minutes = $data['duration_minutes'];
			}
			$learning_result->save();
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'update'));


	// If configuration option "allowLearnerRefreshLearning" is true, allow for Learner to refresh learning_result.
	$group->get('/record-new-learning/{module_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		if (\APP\Tools::getConfig('allowLearnerRefreshLearning')) {
			\APP\Refresh::refreshResults(true, [\APP\Auth::getUserId()], [$args['module_id']]);
		} else {
			return
				$response
				->withStatus(500);
		}
		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('trainee-learning-results', 'update'));

	$group->get('/re-attempt-learning/{module_id:[0-9]+}', function (Request $request, Response $response, array $args)
	{
		$user_id = \APP\Auth::getUserId();
		$module_id = $args['module_id'];

		$learningResult = \Models\LearningResult
			::where('learning_module_id', $module_id)
			->where('user_id', $user_id)
			->where('refreshed', 0)
			->first()
		;

		$learningModule = \Models\LearningModule
			::where('id', $module_id)
			->first()
		;

		$attemptCount = \Models\Scorm\Track::
			where("scormid", $module_id)
			->where("userid", $user_id)
			->max("attempt")
		;

		$attemptCount = $attemptCount - $learningResult->attempts_at_refresh;

		$maxFails = (isset($learningModule->material) && isset($learningModule->material->max_quiz_fails))
			? $learningModule->material->max_quiz_fails
			: null;

		$noLimit = empty($maxFails) || $maxFails === 'null';

		if (
			$learningResult->passing_status == 'failed' &&
			(
				$noLimit ||
				(
					is_numeric($maxFails) &&
					(int)$maxFails > 0 &&
					(int)$maxFails > $attemptCount
				)
			)
            ) {
           //Check if the learning module have retake_fee then need to redirect for purchase page
                if($learningModule->is_course && $learningModule->retake_fee && $learningModule->retake_fee > 0 && Tools::getConfig('PaymentsEngine')){
                    $response->getBody()->write(json_encode([
                            'retake_fee' => $learningModule->retake_fee
                        ]));
                    return $response->withHeader('Content-Type', 'application/json');
            }else{
                \APP\Refresh::refreshResults(true, [\APP\Auth::getUserId()], [$args['module_id']], false, false, false, false, true, true);
                }
		}

		return
			$response;

	})->add(\APP\Auth::getStructureAccessCheck('trainee-learning-results', 'update'));

	// Favorite learning_result, for filtering
	$group->get('/favorite/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$result = \Models\LearningResult
			::where('id', $args['id'])
			->with('userlearningmodules')
			->first();

		if (count($result->userlearningmodules) > 0) {
			$result->favorite = !$result->favorite;
			$result->save();
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('trainee-learning-results', 'update'));

	// List all actions needed for current logged in user.
	$group->get('/actions', function (Request $request, Response $response, array $args) {
		$user = Auth::getUser();

		$query = \Models\LearningResult
			::where('learning_results.user_id', \APP\Auth::getUserId())
			->select(
				'learning_results.id',
				'learning_results.user_id',
				'learning_results.learning_module_id',
				'learning_results.learner_action_date',
				'learning_results.completion_status',
				'learning_results.refreshed',
				'learning_results.learner_action',
				DB::raw("DATE_FORMAT(learning_results.learner_action_date,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS learner_action_date_uk")
			)
			->where('learning_results.refreshed', false)
			->where('learning_results.learner_action', true)
			->join("learning_modules", function ($join) {
				$join
					->on("learning_modules.id", "=", "learning_results.learning_module_id")
					->where('learning_modules.status', true)
					->whereIn('type_id', array_merge([3, 4, 5, 6, 7, 8, 9], LearningModuleType::where('custom', true)->pluck('id')->toArray()))
				;
			})
			->join("user_learning_modules", function ($join) {
				$join
					->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
					->where('user_learning_modules.user_id', \APP\Auth::getUserId())
					->whereNull('user_learning_modules.deleted_at');
			})
			->with(["module" => function ($query) {
				$query
					->select(
						'id',
						'name',
						'type_id'
					)
					->with("type");
			}])
			->withCount(["Comments" => function ($query) {
				$query
					->where("status", true);
			}]);

		$standard_user = \Models\ApprenticeshipStandardUser
			::where('user_id', \APP\Auth::getUserId())
			->first();;

		// Needs an rewrite, re-imagine!
		if (
			$standard_user &&
			$standard_user->start_at &&
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isManager()
		) {
			// Get all modules that are assigned to standard, considering slider functionality too.
			$include_standard_modules = \Models\LearningModule::getIncludeStandardModules($standard_user);
			$non_standard_modules = \Models\LearningModule::getNonStandardModules($standard_user, true);
			$query
				->where(function ($query) use ($include_standard_modules, $non_standard_modules) {

					// show all resources assigned to standard and are visible due to date settings
					if (count($include_standard_modules) > 0) {
						$query
							->whereIn('learning_results.learning_module_id', $include_standard_modules);
					}

					// show all resources that are directly assigned to user outside of standards
					if (count($non_standard_modules) > 0) {
						$query
							->orWhereIn('learning_results.learning_module_id', $non_standard_modules);
					}
				});
		}

		$query = $query
			->get();

		// Here will also check if enableUserFieldAlertSystem is enabled and user is missing any fields, if so, notify em!
		$userFieldAlertSystemInterval = \APP\Tools::getConfig('userFieldAlertSystemInterval');
		if ($userFieldAlertSystemInterval) {
			$userFieldAlertSystemMonitoredFields = \APP\Tools::getConfig('userFieldAlertSystemMonitoredFields');
			$userFieldAlertSystemMonitoredFields = explode(',', $userFieldAlertSystemMonitoredFields);
			if (count($userFieldAlertSystemMonitoredFields) > 0) {

				// get generic fields from picklist!
				$user_field_alert_list = \Models\Picklist::where('type', 'user_field_alert_list')->get();
				$field_names = [];
				foreach ($user_field_alert_list as $key => $user_field_alert_list_item) {
					$field_names[$user_field_alert_list_item->slug] = $user_field_alert_list_item->value;
				}

				// Get extended fields!
				$user_field_alert_list_extended = \Models\TableExtensionField
					::where("status", true)
					->where('versions', 'like', '%"' . $this->get('settings')["licensing"]['version'] . '"%')
					->where('show_learner', true)
					->get();
				$field_names_extended = [];
				foreach ($user_field_alert_list_extended as $key => $user_field_alert_list_item_extended) {
					$field_names_extended[$user_field_alert_list_item_extended->field_key] = $user_field_alert_list_item_extended->field_name;
				}


				$user = \APP\Auth::getUser();
				\Models\TableExtension::returnAllFields('users', $user->id, $user);

				$field_status = new \stdClass();
				$field_status->completion_status = [];


				foreach ($userFieldAlertSystemMonitoredFields as $key => $field) {
					if (
						// Complicated
						strpos($field, 'extended_') === false &&
						array_key_exists($field, $user->toArray()) &&
						(
							$user->{$field} == '' ||
							$user->{$field} == null
						) &&
						isset($field_names[$field])
					) {
						$field_status->completion_status[] = $field_names[$field];
					}

					$extended_field = str_replace("extended_", "", $field);
					if (
						strpos($field, 'extended_') !== false &&
						$user->extended &&
						isset($field_names_extended[$extended_field]) &&
						(
							(
								property_exists($user->extended, $extended_field) &&
								(
									$user->extended->{$extended_field} == '' ||
									$user->extended->{$extended_field} == null
								)
							) ||
							!property_exists($user->extended, $extended_field)
						)

					) {
						$field_status->completion_status[] = $field_names_extended[$extended_field];
					}
				}

				$field_status->module = new \stdClass();
				$field_status->module->name = $GLOBALS["CONFIG"]->licensing['labels']['missing_fields_in_your_profile'];
				$field_status->module->type = new \stdClass();
				$field_status->module->type->name = 'user profile';
				if (count($field_status->completion_status) > 0) {
					$query[] = $field_status;
				}
			}
		}

		$user_forms=\Models\UserForm::select(
			'id',
			'form_id',
			'user_id',
			'user_form_status AS completion_status',
			'user_form_status',
			'id as user_form_id',
			'type_id',
			DB::raw("(select name from forms where id = form_id) as name"),
			DB::raw("DATE_FORMAT(updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk")

	)
	 ->where('user_forms.user_form_status','!=','completed')
		->whereDoesntHave('UserFormSignoff',function($query)use($user){
		$query->where('user_id',$user->id);
		})
	->where('user_id', $user->id)
	->with('Module','Form:id,learning_module_feedback_form')
	->with(['formLearningModule' => function($query){
		$query->select('learning_modules.id', 'learning_modules.name')
          ->join('user_forms', 'user_forms.type_id', '=', 'learning_modules.id')
          ->where('user_forms.type', 'learning_module');
	}]);


		if (\APP\Auth::isLearner()) {
			$user_forms = $user_forms->whereHas("Module", function ($query) {
				$query->where('forms.system_form', "0");
			});
		}


		$user_forms = $user_forms->get();
		if (count($user_forms) > 0) {
			foreach ($user_forms as  $forms) {
				$forms->completion_status = $forms->user_form_status;
				// if($forms->completion_status==1)
				// {
				// $forms->completion_status="completed";
				// }else
				// {
				// $forms->completion_status="not attempted";
				// }
				$forms['module']['type'] = ['id' => 9];
				$query[] = $forms;
			}
		}
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// Remove completed action by learner
	$group->delete('/actions/{id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$query = \Models\LearningResult
			::where('id', $args['id'])
			->where('user_id', \APP\Auth::getUserId())
			->where('completion_status', 'completed')
			->where('learner_action', true)
			->first();

		// Could be that there is no action
		if ($query) {
			$query->learner_action = false;
			$query->save();
		}

		return
			$response;
	})->add(\APP\Auth::getSessionCheck());

	// Toggle off_the_job_training
	$group->put('/off_the_job_training/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$result = \Models\LearningResult::find($args["id"]);

		if (
			$result->user_id == \APP\Auth::getUserId() ||
			\APP\Auth::isManagerOf($result->user_id) ||
			\APP\Auth::isAdmin() ||
			\APP\Auth::accessAllLearners()
		) {
			$result->off_the_job_training = isset($data['off_the_job_training']) ? $data['off_the_job_training'] : false;
			$result->save();
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return $response;
	})->add(\APP\Auth::getSessionCheck());

	// Update completion_status of the learning result to 'in progress' if present completion_status is 'not completed' and learning_module_type is 'webpage'.
	// Security Note: using this user can set ANY learning result to ANY status
	$group->put('/learning-result/update-completion-status/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$learning_result = \Models\LearningResult::find($args["id"]);

		if (
			$learning_result &&
			$learning_result->user_id == \APP\Auth::getUserId()
		) {
			$learning_result->completion_status = $data['completion_status'];
			$learning_result->save();
		} else {
			return \APP\Tools::returnCode($request, $response, '404');
		}
		return $response;
	})->add(\APP\Auth::getSessionCheck());


	$group->get('/learning-result/completion-status/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learning_result = \Models\LearningResult::find($args["id"]);
		if (
			$learning_result &&
			$learning_result->user_id == \APP\Auth::getUserId()
		) {
			$response->getBody()->write(json_encode(['completion_status' => $learning_result->completion_status]));
        		return $response->withHeader('Content-Type', 'application/json');
		} else {
			return \APP\Tools::returnCode($request, $response, '404');
		}
	})->add(\APP\Auth::getSessionCheck());


	/* Import Legacy Course Data */
	$group->post('/import-course', function (Request $request, Response $response) {
		$params = $request->getParsedBody();

		if (isset($_FILES['importFile'])) {
			 $uploadDir = $this->get('settings')["LMSTempPath"];
			 $adapter = new LocalFilesystemAdapter($uploadDir);
			 $filesystem = new Filesystem($adapter);
			 $uploadedFile = $_FILES['importFile'];

			 $import_file_id = uniqid();
			 $import_file_name = $import_file_id . '.' . pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
			 $filePath = $uploadDir . DIRECTORY_SEPARATOR . $import_file_name;

			 try {
				  // Upload the file
				  $stream = fopen($uploadedFile['tmp_name'], 'r+');
				  $filesystem->writeStream($import_file_name, $stream);

				  if (is_resource($stream)) {
						fclose($stream);
				  }

				  // Log import files!
				  \Models\LogExportImport::insertRecord(file_get_contents($filePath), '.' . pathinfo($import_file_name, PATHINFO_EXTENSION), $import_file_name, false, 'imports');
				  $n_records = \APP\Import::learningResults($filePath);

				  // Clean up temporary file after processing
				  $filesystem->delete($import_file_name);

				  // Return the response
				  $response->getBody()->write(
						json_encode(
							 [
								  'updated' => $n_records['n_record_updated'],
								  'inserted' => $n_records['n_record_inserted'],
								  'disabled' => $n_records['n_record_disabled'],
								  'rejected' => $n_records['n_record_rejected'],
								  'deleted' => $n_records['n_record_deleted'],
								  'message' => $n_records['message'],
							 ]
						)
				  );
				  return $response->withHeader('Content-Type', 'application/json');
			 } catch (\Exception $e) {
				  // Clean up temporary file in case of exception
				  if ($filesystem->fileExists($import_file_name)) {
						$filesystem->delete($import_file_name);
				  }
				  return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
			 }
		} else {
			 return \APP\Tools::returnCode($request, $response, 400, 'No import file provided.');
		}
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'insert'));


	/*Import Legacy Course Data code Ends Here*/

	$group->post('/import-evidence', ImportEvidenceController::class . ':import')->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'insert'));

	/*VERSION ADDING*/
	$group->post('/add_version', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		if (!\Models\LearningModule::find($data['id'])) {
			return $response;
		}

		$data["LMSScormVersionPath"]=$this->get('settings')["LMSScormVersionPath"];
		$data["LMSScormDataPath"]=$this->get('settings')["LMSScormDataPath"];
		$version = \Models\LearningModuleVersion::versionSync("add", $data);
		$response->getBody()->write((string)$version);
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));

	$group->get('/get_versions/{module_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$versions = \Models\LearningModuleVersion::where('learning_module_id', '=', $args['module_id'])
			->orderBy('learning_module_versions.version', 'asc')
			->with(['CreatedUser' => function ($query) {
				$query
					->select('fname', 'lname', 'id', DB::raw("CONCAT(users.fname, ' ', users.lname) as version_created_user"));
			}])
			->with(['UpdatedUser' => function ($query) {
				$query
					->select('fname', 'lname', 'id', DB::raw("CONCAT(users.fname, ' ', users.lname) as version_updated_user"));
			}])
			->select('*', DB::raw('DATE_FORMAT(created_at, "' . \APP\Tools::defaultDateFormatMYSQL() . ' %H:%i") as version_created_at'), DB::raw('DATE_FORMAT(updated_at, "' . \APP\Tools::defaultDateFormatMYSQL() . ' %H:%i") as version_updated_at'))
			->get();

		// Tried "with" but seems not working with condition. need optimisation
		foreach ($versions as $key => $version) {
			$evidence = \Models\LearningModuleEvidence
				::where(
					[
						['learning_modules_id', '=', $version->learning_module_id],
						['version', '=', $version->version]
					]
				)
				->where(function ($query) {
					$query
						->where('status', true)
						->where('manager', 1)
						->orderBy('created_at', 'desc')
						->with(['user' => function ($query) {
							$query
								->select('id', 'fname', 'lname');
						}])
						->with(['AddedBy' => function ($query) {
							$query
								->select('id', 'fname', 'lname');
						}]);
				})
				->get();
			$versions[$key]['learning_module_evidences'] = $evidence;
		}

		$response->getBody()->write(json_encode($versions));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));


	// Remove completed action by learner
	$group->post('/remove_version', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$learning = \Models\LearningModule::find($data['id']);

		if ($learning->type_id == 1) {

			$scorm_version_folder = $this->get('settings')["LMSScormDataPath"].$data['id'].'/'.$data['version'];
			\APP\Course::recursiveRemoveDirectory($scorm_version_folder);
		}
		$query = \Models\LearningModuleVersion
			::where('learning_module_id', "=", $data['id'])
			->where('version', "=", $data['version'])
			->first();

		// Could be that there is no action
		if ($query) {
			$query->delete();
		}

		return
			$response;
	})->add(\APP\Auth::getSessionCheck());

	$group->put('/module-duplicate/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		/*Replicate Learning Resource*/


		$learning = \Models\LearningModule::find($args["id"]);

		$learning_clone = $learning->replicate()->fill(
			[
				'code' => null,
				'name' => "copy of " . $learning->name,
			]
		);
		$learning_clone->save();

		/*Replicate Learning Resource Competencies*/
		$learning_compentencies = $learning->competencies;
		if (count($learning_compentencies) > 0) {
			foreach ($learning_compentencies as $compentency) {
				$learning_module_compentency =  \Models\LearningModuleCompetency::find($compentency->id);
				if ($learning_module_compentency) {
					$learning_module_compentency_clone = $learning_module_compentency->replicate()->fill(
						[
							'learning_module_id' => $learning_clone->id,
						]
					);
					$learning_module_compentency_clone->save();
				}
			}
		}

		/*Replicate Learning Resource Prere*/
		$learning_prerequisites = $learning->prerequisites;
		if (count($learning_prerequisites) > 0) {
			foreach ($learning_prerequisites as $prerequisite) {
				$learning_module_prerequisite =  \Models\LearningModulePrerequisite::find($prerequisite->id);
				if ($learning_module_prerequisite) {
					$learning_module_prerequisite_clone = $learning_module_prerequisite->replicate()->fill(
						[
							'learning_module_id' => $learning_clone->id,
						]
					);
					$learning_module_prerequisite_clone->save();
				}
			}
		}

		/*Replicate Learning Resource Versions*/
		$learning_versions = $learning->versions;
		if (count($learning_versions) > 0) {
			foreach ($learning_versions as $version) {
				$learning_module_version =  \Models\LearningModuleVersion::find($version->id);
				$learning_module_version_clone = $learning_module_version->replicate()->fill(
					[
						'learning_module_id' => $learning_clone->id,
					]
				);
				$learning_module_version_clone->save();
			}
		}
		/*Replicate Learning Resource Versions SCORM folder and files for type e-learning only*/
		if ($learning->type->slug == 'e_learning') {
			if (file_exists($this->get('settings')["LMSPublicPath"] . 'scormdata/' . $learning->id . "/moddata/scorm/1/xml/information.xml")) {
				\APP\Tools::recurseCopy($this->get('settings')["LMSPublicPath"] . 'scormdata/' . $learning->id , $this->get('settings')["LMSPublicPath"] . 'scormdata/' . $learning_clone->id );
				$course = \APP\Course::get($learning_clone);
				$course->createScormData($this->get('settings')["LMSScormDataPath"]);
			}
		}
		/*Replicate Learning Resource Versions SCORM folder and files*/

		/*Replicate Learning Resource Evidences*/
		$learning_evidences = $learning->LearningModuleEvidences;
		if (count($learning_evidences) > 0) {
			foreach ($learning_evidences as $evidence) {
				$learning_module_evidence =  \Models\LearningModuleEvidence
					::where('id', $evidence->id)
					->where('manager', 1)
					->first();
				if ($learning_module_evidence) {
					$learning_module_evidence_clone = $learning_module_evidence->replicate()->fill(
						[
							'learning_modules_id' => $learning_clone->id,
							'hash' => bin2hex(random_bytes(16)),
						]
					);
					$learning_module_evidence_clone->save();
				}

				/*Duplicate Learning Resource Evidence files*/

				if (is_file($this->get('settings')["LMSEvidencePath"] .'/'.$evidence->hash.'.'.$evidence->extension)) {
					copy($this->get('settings')["LMSEvidencePath"] .'/'.$evidence->hash.'.'.$evidence->extension, $this->get('settings')["LMSEvidencePath"] .'/'.$learning_module_evidence_clone->hash.'.'.$learning_module_evidence_clone->extension);
				}
			}
		}

		/*Replicate Learning Resource Courses*/
		$learning_courses = $learning->Courses;
		if (count($learning_courses) > 0) {
			foreach ($learning_courses as $course) {
				$learning_course =  \Models\LearningCourseModule::find($course->id);
				if ($learning_course) {
					$learning_module_course_clone = $learning_course->replicate()->fill(
						[
							'learning_module_id' => $learning_clone->id,
						]
					);
					$learning_module_course_clone->save();
				}
			}
		}

		/*Replicate Learning Resource Department*/
		$data = $learning->Departments;
		if (count($data) > 0) {
			foreach ($data as $result) {
				$obj =  \Models\DepartmentLearningModule::find($result->id);
				if ($obj) {
					$obj_clone = $obj->replicate()->fill(
						[
							'learning_module_id' => $learning_clone->id,
						]
					);
					$obj_clone->save();
				}
			}
		}

		/*Replicate Learning Resource Groups*/
		$data = $learning->Groups;
		if (count($data) > 0) {
			$obj_clone = null;
			$obj = null;
			foreach ($data as $result) {
				$obj =  \Models\GroupLearningModule::find($result->id);
				if ($obj) {
					$obj_clone = $obj->replicate()->fill(
						[
							'learning_module_id' => $learning_clone->id,
						]
					);
					$obj_clone->save();
				}
			}
		}

		$response->getBody()->write(
				json_encode(
					[
						'l_name' => $learning_clone->name,
						'l_id' => $learning_clone->id,
					]
				)
			);
		return $response->withHeader('Content-Type', 'text/html');

	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));


	/*Import Legacy Course Data*/
	$group->post('/module/import', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		if (isset($_FILES['importFile']) && $_FILES['importFile']['error'] === UPLOAD_ERR_OK) { // Check for upload success
			$uploadedFile = $_FILES['importFile'];
			$tempFilePath = $uploadedFile['tmp_name'];
			$originalFileName = $uploadedFile['name'];
			$fileExtension = pathinfo($originalFileName, PATHINFO_EXTENSION);

			$tempStoragePath = $this->get('settings')["LMSTempPath"];

			// Flysystem Adapter for temporary storage
			$tempAdapter = new LocalFilesystemAdapter($tempStoragePath);
			$tempFilesystem = new Filesystem($tempAdapter);

			$import_file_id = uniqid();
			$import_file_name = $import_file_id . '.' . $fileExtension;
			$importFileDestination = $import_file_name; // Path within the temp filesystem
			$tempFullFilePath = $tempStoragePath . $importFileDestination; // Full path for file operations

			$notify_roles = [];
			try {
				// Upload to temporary storage using Flysystem
				$stream = fopen($tempFilePath, 'r+');
				$tempFilesystem->writeStream($importFileDestination, $stream);
				fclose($stream);

			} catch (\League\Flysystem\FilesystemException $e) {
				return \APP\Tools::returnCode($request, $response, 500, 'File upload error: ' . $e->getMessage());
			} catch (\Exception $e) { // Catch other exceptions if any during upload
				return \APP\Tools::returnCode($request, $response, 500, 'An error occurred during file upload: ' . $e->getMessage());
			}


			// Log import files!
			\Models\LogExportImport::insertRecord(file_get_contents($tempFullFilePath), '.' . $fileExtension, $originalFileName, false, 'imports');

			// Import data
			$n_records = [];
			try {
				$n_records = \APP\Import::learning_modules($tempFullFilePath, $this->get('settings'), $response);
			} catch (\Exception $e) {
				$tempFilesystem->delete($importFileDestination); // Clean up temp file on import error
				return \APP\Tools::returnCode($request, $response, 500, 'Import process error: ' . $e->getMessage());
			}


			// Clean up temporary file after import (success or failure of import logic)
			$tempFilesystem->delete($importFileDestination);


			$response->getBody()->write(
				json_encode(
					[
						'updated' => $n_records['n_record_updated'] ?? 0, // Use null coalescing operator to avoid errors if keys are not set
						'inserted' => $n_records['n_record_inserted'] ?? 0,
						'disabled' => $n_records['n_record_disabled'] ?? 0,
						'rejected' => $n_records['n_record_rejected'] ?? 0,
						'deleted' => $n_records['n_record_deleted'] ?? 0,
						'message' => $n_records['message'] ?? '',
						'log' => $n_records['log'] ?? ''
					]
				)
			);
			return $response->withHeader('Content-Type', 'application/json');
		};
		return \APP\Tools::returnCode($request, $response, 400, 'No import file uploaded.'); // Return error if no file in $_FILES
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));


	$group->get('/get_skill_comments/{user_id:[0-9]+}/{result_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$learningModuleId = \Models\LearningResult::where("id", $args["result_id"])->value("learning_module_id");
		$commentsOfLearningModule = \Models\LearningResultsComment
			::where("learning_module_id", $learningModuleId)
			->where("created_for_user_id", $args["user_id"])
			->where('learning_results_id', '!=' ,$args["result_id"])
			->with(["createdby" => function ($query) {
				$query
					->select("id", "fname", "lname", "role_id")
					->with('role');
			},
                "certificateFiles"
            ])->get();
		$learning_result = \Models\LearningResult
            ::where("id", $args["result_id"])
            ->select('learning_results.*',DB::raw('(select lr.created_at from  learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 ORDER BY lr.id DESC LIMIT 1) as previous_created_at'),
                DB::raw('(select lr.sign_off_manager_at from  learning_results AS lr WHERE lr.learning_module_id = learning_results.learning_module_id AND lr.user_id = learning_results.user_id AND lr.refreshed = 1 ORDER BY lr.id DESC LIMIT 1) as previous_completed_at'),
                 DB::raw("(
    SELECT CONCAT(u.fname, ' ', u.lname)
    FROM learning_results AS lr
    JOIN users AS u ON u.id = lr.sign_off_manager_by
    WHERE lr.learning_module_id = learning_results.learning_module_id
      AND lr.user_id = learning_results.user_id
      AND lr.refreshed = 1
    ORDER BY lr.id DESC
    LIMIT 1
) as previous_completed_by")
            )
			->with('SignOffManagerBy')
			->with(["comments" => function ($query) use ($args) {
				$query
					->where('created_for_user_id', $args["user_id"])
					->where('status', true)
                    ->orderBy('created_at', 'desc')
					->with(["createdby" => function ($query) {
						$query
							->select("id", "fname", "lname", "role_id")
							->with('role')
						;
					},
                        "certificateFiles"
                    ])
				;
			}])
			->first()
		;
		if ($learning_result) {
			$commentsCollection = collect($learning_result->comments);
			// Merge additional comments from the learning module
			$mergedComments = $commentsCollection->merge($commentsOfLearningModule);
			// Set the merged comments back to the $learning_result
			$learning_result->setRelation('comments', $mergedComments);
		}


		$response->getBody()->write(json_encode([
			"learning_result" => $learning_result,
		]));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results', 'lessons-and-learning-resources'], 'select'));



	$group->put('/update-last_signoff', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$learning = LearningResult::where('id', $data['result_id'])->first();
		$learningStandard = ApprenticeshipIssuesLearningModules::with('Issue.IssueCategory.Standard')->where('learning_modules_id', $learning->learning_module_id)->first();
		$repeatation_period = null;
		if (
			$learningStandard &&
			$learningStandard->Issue &&
			$learningStandard->Issue->IssueCategory &&
			$learningStandard->Issue->IssueCategory->Standard
		) {
			$standard =  $learningStandard->Issue->IssueCategory->Standard;
			$repeatation_period = $standard->repetition_period; // If the programme/skill repatation period exist then use that repetition period
			if (!$repeatation_period) // If its empty
			{
				$repeatation_period = $standard->default_skill_repetition_period; //Use default repetitation period
			}
		}
		if (!$repeatation_period) {
			$repeatation_period = $learning->Module->repetition_period;
		}
		$due_date = $learning->due_at;
		if ($repeatation_period) {
			$due_date = Carbon::parse($data['date'])->addMonths($repeatation_period);
		}
		\Models\LearningResult::where('id', $data['result_id'])->update(['sign_off_manager_at' => $data['date'], 'due_at' => $due_date]);

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	$group->put('/update-comment/{comment_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		\Models\LearningResultsComment::where('id', $args['comment_id'])->update(['comment' => $data['comment']]);

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->put('/track/{module_id}',function(Request  $request, Response $response, $args){
		$module_id = $args['module_id'];
		$user = Auth::getUser();
		$learning_module = LearningModule::where('id', $module_id)->first();
		if (!$learning_module) {
			return \APP\Tools::returnCode($request, $response, 404, 'No resource found');
		}
		LearningModuleLastAccess::create(["learning_module_id" => $learning_module->id, "resource_name" => $learning_module->name, "user_id" => $user->id, "full_name" => $user->full_name]);
		return $response;
	})->add(\APP\Auth::getSessionCheck());

	//recalculate progress for all assigned users to this resource, that are not not started
	$group->get('/module/{module_id:[0-9]+}/recalculate/{min_passing_percentage:[0-9]+}', function (Request $request, Response $response, $args) {
		// Only superadmin should do this.
		if (!\APP\Auth::isAdmin()) {
			return \APP\Tools::returnCode($request, $response, '403');
		}
		$resource = \Models\LearningModule
			::where('status', true)
			->find($args['module_id']);
		if (!$resource) {
			return \APP\Tools::returnCode($request, $response, '404');
		}

		$resource_material = json_decode(json_encode($resource->material), true);
		$resource_material["min_passing_percentage"] = intval($args['min_passing_percentage']);
		$resource->material = $resource_material;
		$resource->saveWithoutEvents();

		$entries_recalculated = 0;
		$course = \APP\Course::get($resource->id);
		if ($course) {
			\Models\LearningResult
				::where('refreshed', 0)
				->where('completion_status', '!=', 'not attempted')
				->where('learning_module_id', $resource->id)
				->chunkById(100, function ($results) use ($course, &$entries_recalculated) {
					foreach ($results as $result) {
						$course->updateUserResult($result->user_id, true);
						$entries_recalculated++;
					}
				});
		}

		$response->getBody()->write('Entries recalculated: ' . $entries_recalculated);
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

    $group->post('/save-linked-template',function(Request $request, Response $response){
        try{
        $data = $request->getParsedBody();
        $linked_template = new LinkedLearningTemplate();
        $linked_template->name = $data['name'];
        $linked_template->template = $data['linked'];
        $linked_template->save();

		$response->getBody()->write(json_encode(["message"=>"Template saved successfully"]));
		return $response->withHeader('Content-Type', 'application/json');
        }catch(\Exception $e){

			$response->getBody()->write(json_encode(["message"=>"Template saved successfully"]));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
        }
    })->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));

    $group->get('/get-linked-template', function (Request $request, Response $response) {
        $templates = LinkedLearningTemplate::get();
		$response->getBody()->write(json_encode($templates));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));


    $group->post('/upload-skill-certificate', function (Request $request, Response $response) {
        $data = $request->getParsedBody();
        $files = $request->getUploadedFiles();
        $certificate = $files['skillCertificate'] ?? null;

        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $uploadDir = $privatePath . '/skill_certificates';

        if ($certificate && $certificate->getError() === UPLOAD_ERR_OK) {

            // Ensure target directory exists
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0775, true);
            }

            // Get original filename and extension
            $originalName = $certificate->getClientFilename();
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);

            // Generate a unique hash for the filename
            $hashedName = hash('sha256', uniqid('', true) . bin2hex(random_bytes(16))) . '.' . $extension;

            // Move the uploaded file
            $certificate->moveTo($uploadDir . DIRECTORY_SEPARATOR . $hashedName);

            // Save file info to database
            $file = new \Models\File();
            $file->table_row_id = $data['commentID'];
            $file->table_name = 'learning_results_comments__skill_certificates';
            $file->group = null;
            $file->file = $originalName; // Save original filename
            $file->hash = pathinfo($hashedName, PATHINFO_FILENAME); // Save the hash without extension
            $file->extension = $extension;
            $file->added_by = \APP\Auth::getUserId();
            $file->added_for = $data['userID'];
            $file->save();

            $response->getBody()->write(json_encode([
                'message' => 'File uploaded successfully',
                'file' => $file
            ]));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
        }

        $response->getBody()->write(json_encode([
            'message' => 'No valid file uploaded'
        ]));
        return $response->withHeader('Content-Type', 'application/json')->withStatus(400);

    })->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->delete('/remove-skill-certificate/{id:[0-9]+}/{user_id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$id = $args['id'];
		$user_id = $args['user_id'];

		try {
			$file = \Models\File::where('table_name', 'learning_results__skill_certificates')
				->where('table_row_id', $id)
				->where('added_for', $user_id)
				->first();

			if ($file) {
				$filename = $file->file . "." . $file->extension;

				$filePath = $this->get('settings')["LMSPrivatePath"] . '/skill_certificates/' . $filename;

				if (file_exists($filePath)) {
					unlink($filePath); // Remove the file from the folder
				}

				$file->delete(); // Remove the file record from the database

				$response->getBody()->write(json_encode(['message' => 'File deleted successfully']));
				return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
			}

			$response->getBody()->write(json_encode(['message' => 'File not found']));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(404);

		} catch (\Exception $e) {
			$response->getBody()->write(json_encode([
				'message' => 'An error occurred while deleting the file',
				'error' => $e->getMessage()
			]));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
		}

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

    $group->get('/download-skill-certificate/{file_id:[0-9]+}', function (Request $request, Response $response, array $args) {

        $file = \Models\File::find($args['file_id']);
        if (!$file) {
            return \APP\Tools::returnCode($request, $response, 404, 'File not found');
        }

        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $filePath = $privatePath . '/skill_certificates/' . $file->hash . '.' . $file->extension;

        if (!file_exists($filePath)) {
            return \APP\Tools::returnCode($request, $response, 404, 'File not found on server');
        }

        $filename = $file->original_name ?? ($file->file . '.' . $file->extension);
        $stream = new \Slim\Psr7\Stream(fopen($filePath, 'rb'));

        return $response
            ->withHeader('Content-Type', mime_content_type($filePath))
            ->withHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->withHeader('Content-Length', filesize($filePath))
            ->withBody($stream);
    })->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

});
