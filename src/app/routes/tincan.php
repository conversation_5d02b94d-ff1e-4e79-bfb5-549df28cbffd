<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/process",  function ($group) {
	$group->put("/tincan/statements" , function (Request $request, Response $response, $args) {

		if (\APP\Auth::isDemoUser()) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
			;
		}

		$data = $request->getParsedBody();

		if (is_array($data)) {
			\APP\Tincan::ProcessStatement($data);
		}

		return $response;
	});
})->add(\APP\Auth::getSessionCheck());