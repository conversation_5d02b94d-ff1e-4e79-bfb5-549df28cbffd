<?php

use Illuminate\Database\Capsule\Manager as DB;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

$app->group("/mapping-to-programme", function ($group) {

    $group->post('/list', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();

        $dateRangeQuery = function ($query, $field, $secondaryField, $table) use ($params) {
            if (isset($params["search"][$field]) && $params["search"][$field]) {
                $params["search"][$field] = json_decode($params["search"][$field], true);
                if (isset($params["search"][$field][$secondaryField]) && $params["search"][$field][$secondaryField]) {
                    $carbonDate = \Carbon\Carbon::parse($params["search"][$field][$secondaryField]);
                    $created_at = $secondaryField === 'period_to' ? $carbonDate->endOfDay() : $carbonDate->startOfDay();
                    $query->where("${table}.${field}", $secondaryField === 'period_to' ? "<=" : ">=", $created_at);
                }
            }
        };

        $apprenticeshipIssuesUserLearningModulesQuery = \Models\ApprenticeshipIssuesUserLearningModules::select(
            'apprenticeship_issues_user_learning_modules.id',
            'apprenticeship_issues_user_learning_modules.user_id',
            'apprenticeship_issues_user_learning_modules.created_at',
            'apprenticeship_issues_user_learning_modules.deleted_at',
            'apprenticeship_issues_user_learning_modules.created_by',
            'apprenticeship_issues_user_learning_modules.deleted_by',
            'apprenticeship_issues_user_learning_modules.learning_modules_id',
            DB::raw("CONCAT(user.fname, ' ', user.lname) as user_name"),
            DB::raw("CONCAT(creator.fname, ' ', creator.lname) as created_by_name"),
            DB::raw("CONCAT(deleter.fname, ' ', deleter.lname) as deleted_by_name"),
            'learning_modules.name as module_name',
            'apprenticeship_issues.name as issue_name',
            'apprenticeship_standards.name as standard_name',
            'apprenticeship_issue_categories.name as issue_category_name',
            DB::raw("DATE_FORMAT(apprenticeship_issues_user_learning_modules.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "  %H:%i') AS created_at_uk"),
            DB::raw("DATE_FORMAT(apprenticeship_issues_user_learning_modules.deleted_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "  %H:%i') AS deleted_at_uk")
        )->withTrashed()
            ->leftJoin('users as user', 'user.id', '=', 'apprenticeship_issues_user_learning_modules.user_id')
            ->leftJoin('users as creator', 'creator.id', '=', 'apprenticeship_issues_user_learning_modules.created_by')
            ->leftJoin('users as deleter', 'deleter.id', '=', 'apprenticeship_issues_user_learning_modules.deleted_by')
            ->join('learning_modules', 'learning_modules.id', '=', 'apprenticeship_issues_user_learning_modules.learning_modules_id')
            ->join('apprenticeship_issues', 'apprenticeship_issues.id', '=', 'apprenticeship_issues_user_learning_modules.apprenticeship_issues_id')
            ->join('apprenticeship_issue_categories', 'apprenticeship_issue_categories.id', '=', 'apprenticeship_issues.issue_category_id')
            ->join('apprenticeship_standards', 'apprenticeship_standards.id', '=', 'apprenticeship_issue_categories.standard_id');

        if (isset($params["search"]["main_id"])) {
            $apprenticeshipIssuesUserLearningModulesQuery->where('apprenticeship_issues_user_learning_modules.id', $params["search"]["main_id"]);
        }

        // $dateRangeQuery($apprenticeshipIssuesUserLearningModulesQuery, 'created_at', 'period_from', 'apprenticeship_issues_user_learning_modules');
        // $dateRangeQuery($apprenticeshipIssuesUserLearningModulesQuery, 'created_at', 'period_to', 'apprenticeship_issues_user_learning_modules');
        // $dateRangeQuery($apprenticeshipIssuesUserLearningModulesQuery, 'deleted_at', 'period_from', 'apprenticeship_issues_user_learning_modules');
        // $dateRangeQuery($apprenticeshipIssuesUserLearningModulesQuery, 'deleted_at', 'period_to', 'apprenticeship_issues_user_learning_modules');

        $query = \Models\ApprenticeshipIssuesEvidence
            ::select(
                'apprenticeship_issues_evidence.id',
                'apprenticeship_issues_evidence.user_id',
                'apprenticeship_issues_evidence.created_at',
                'apprenticeship_issues_evidence.deleted_at',
                'apprenticeship_issues_evidence.created_by',
                'apprenticeship_issues_evidence.deleted_by',
                'apprenticeship_issues_evidence.learning_modules_id',
                DB::raw("CONCAT(user.fname, ' ', user.lname) as user_name"),
                DB::raw("CONCAT(creator.fname, ' ', creator.lname) as created_by_name"),
                DB::raw("CONCAT(deleter.fname, ' ', deleter.lname) as deleted_by_name"),
                'learning_modules.name as module_name',
                'apprenticeship_issues.name as issue_name',
                'apprenticeship_standards.name as standard_name',
                'apprenticeship_issue_categories.name as issue_category_name',
                DB::raw("DATE_FORMAT(apprenticeship_issues_evidence.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "  %H:%i') AS created_at_uk"),
                DB::raw("DATE_FORMAT(apprenticeship_issues_evidence.deleted_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "  %H:%i') AS deleted_at_uk")
            )->withTrashed()
            ->union($apprenticeshipIssuesUserLearningModulesQuery)
            ->leftJoin('users as user', 'user.id', '=', 'apprenticeship_issues_evidence.user_id')
            ->leftJoin('users as creator', 'creator.id', '=', 'apprenticeship_issues_evidence.created_by')
            ->leftJoin('users as deleter', 'deleter.id', '=', 'apprenticeship_issues_evidence.deleted_by')
            ->join('learning_modules', 'learning_modules.id', '=', 'apprenticeship_issues_evidence.learning_modules_id')
            ->join('apprenticeship_issues', 'apprenticeship_issues.id', '=', 'apprenticeship_issues_evidence.apprenticeship_issues_id')
            ->join('apprenticeship_issue_categories', 'apprenticeship_issue_categories.id', '=', 'apprenticeship_issues.issue_category_id')
            ->join('apprenticeship_standards', 'apprenticeship_standards.id', '=', 'apprenticeship_issue_categories.standard_id');

        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        // $dateRangeQuery($query, 'created_at', 'period_from', 'apprenticeship_issues_evidence');
        // $dateRangeQuery($query, 'created_at', 'period_to', 'apprenticeship_issues_evidence');
        // unset($params["search"]["created_at"]);
        //
        // $dateRangeQuery($query, 'deleted_at', 'period_from', 'apprenticeship_issues_evidence');
        // $dateRangeQuery($query, 'deleted_at', 'period_to', 'apprenticeship_issues_evidence');
        // unset($params["search"]["deleted_at"]);


        if (isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
            if ($params["sort"]["predicate"] === 'created_at') {
                $query->orderBy('created_at', $params["sort"]["reverse"] ? 'desc' : 'asc');
            } elseif ($params["sort"]["predicate"] === 'deleted_at') {
                $query->orderBy('deleted_at', $params["sort"]["reverse"] ? 'desc' : 'asc');
            }
            unset($params["sort"]);
        }

        if (isset($params["search"]["main_id"])) {
            $query->where('apprenticeship_issues_evidence.id', $params["search"]["main_id"]);
            unset($params["search"]["main_id"]);
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);

        $response->getBody()->write($p->toJson());
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-failed-login-attempts', 'select'));


});
