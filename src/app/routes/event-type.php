<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\EventTypeWorkflow;

$app->group("/event-type",  function ($group) {

	$group->put('/{status:disable|enable}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\EventType::find($args["id"]);
		$entry->status = 0;
		if ($args['status'] == 'enable') {
			$entry->status = 1;
		}
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-types', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\EventType::with('FormWorkFlow')->find($args["id"]);

		$response->getBody()->write(json_encode($entry));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-types', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$entry = new \Models\EventType;
		$fields = [
			"name",
			"event_category_id"
    ];
		$entry->slug = \APP\Tools::safeName($data['name']) . '_' . \APP\Tools::unsecureRandom();
		\APP\Tools::setObjectFields($entry, $fields, $data, false, true);
    $entry->save();
    foreach ($data['form_workflows'] as $value) {
      EventTypeWorkflow::create(['event_type_id'=>$entry->id,'form_workflow_id'=>$value['id']]);
    }
		$response->getBody()->write(json_encode($entry->id));
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-types', 'insert'));

	// Update entry
	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$entry = \Models\EventType::find($args["id"]);

		$fields = [
			"name",
			"event_category_id",
		];

		// Slug can be changed only to non-system critical event types
		if (!$entry->system) {
			$fields[] = "slug";
		}

		\APP\Tools::setObjectFields($entry, $fields, $data, false, true);

    $entry->save();
    $form_workflows=[];
    foreach($data['form_workflows'] as $value){
      EventTypeWorkflow::updateOrCreate(['event_type_id'=>$entry->id,'form_workflow_id'=>$value['id']],[]);
      $form_workflows[]=$value['id'];
    }
    EventTypeWorkflow::where('event_type_id',$entry->id)->whereNotIn('form_workflow_id',$form_workflows)->delete();
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-types', 'update'));


	$group->get('/all', function (Request $request, Response $response, $args) {
		session_write_close();
		$data = \Models\EventType
			::where("status", true)
		;

		$data = $data
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionRegisterCheck());

	$group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\EventType
			::where("event_types.id", ">", "0")
			//->with('Category')
			->leftJoin('picklists', 'picklists.id', '=', 'event_types.event_category_id')
			->select(
				'event_types.*',
				'picklists.value as category_name'
			)
		;
		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-types', 'select'));
});
