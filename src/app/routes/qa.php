<?php

use APP\Auth;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Role;
use Models\UserFormSignoff;

$app->group("/qa",  function ($group) {

	$group->post('/in-progress-list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]["additionalSearchParams"])) {
			$additional_search_params = $params["search"]["additionalSearchParams"];
			unset($params["search"]["additionalSearchParams"]);
		}

		$query = \Models\LearningResult
			::select(
				'users.id as user_id',
				'users.fname as fname',
				'users.lname as lname',
				'learning_modules.name as module_name',
				'learning_modules.id as module_id',
				'learning_results.qa as qa',
				'learning_module_types.name as type_name',
				'learning_results.completion_status as completion_status',
				'learning_results.id as learning_result_id',
				DB::raw("DATE_FORMAT(learning_results.updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk"),
				DB::raw("DATE_FORMAT(learning_results.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
			)
			->whereHas("UserLearningModules", function($query) {
				$query
					->whereRaw("user_learning_modules.user_id = learning_results.user_id")
					->whereRaw('user_learning_modules.deleted_at is null')
				;
			})
			->join("users", function($join) {
				$join
					->on("users.id", "=", "learning_results.user_id")
				;
			})
			->join("learning_modules", function($join) {
				$join
					->on("learning_modules.id", "=", "learning_results.learning_module_id")
				;
			})
			->join("learning_module_types", function($join) {
				$join
					->on("learning_module_types.id", "=", "learning_modules.type_id")
				;
			})
			->where('learning_results.refreshed', 0)
			->where('users.status', true)
			->where('learning_modules.status', true)
			->where('learning_modules.type_id', '>', 6)
			->where('learning_modules.type_id', '<', 10)
			->whereNotNull('learning_results.qa')
			->where('learning_results.qa', 'Rejected')
		;

		if (
			isset($additional_search_params['manager_id']) &&
			!empty($additional_search_params['manager_id'])
		) {
			$query = $query
				->whereIn('users.id',
					\Models\ManagerUser
						::select('user_id')
						->where('manager_id', $additional_search_params['manager_id'])
						->get()
				)
			;
		}

		if (
			isset($params['search']['standard_id']) &&
			!empty($params['search']['standard_id'])
		) {

			// Will list users assigned to this standard
			$query = $query
				->join("apprenticeship_standards_users", function($join) use ($params) {
					$join
						->on("apprenticeship_standards_users.user_id", "=", "users.id")
						->where('apprenticeship_standards_users.standard_id', $params['search']['standard_id'])
					;
				})

				// Only resources that are assigned to said standard.
				->where(function ($query) use ($params) {
					$query
						->whereIn('learning_modules.id',
							\Models\ApprenticeshipIssuesEvidence::modulesInStandards($params['search']['standard_id'])
						)
						->orWhereIn('learning_modules.id',
							\Models\ApprenticeshipIssuesUserLearningModules::modulesInStandards($params['search']['standard_id'])
						)
						->orWhereIn('learning_modules.id',
							\Models\ApprenticeshipIssuesLearningModules::modulesInStandards($params['search']['standard_id'])
						)
					;
				})
			;
			// Need to list only resources assigned to that standard also!
			// check "apprenticeship_issues_evidence"
			// check "apprenticeship_issues_learning_modules"
			// check "apprenticeship_issues_user_learning_modules"

			unset($params['search']['standard_id']);
		}

		if (isset($params["search"]) && is_array($params["search"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);


		foreach ($p as $key => $entry) {
			$p[$key]['standards'] = \Models\LearningModule::Standards($entry->module_id, $entry->user_id);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-qa', 'select'));

	// QA IN PROGRESS CRITERIA LIST
	$group->post('/in-progress-criteria-list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]["additionalSearchParams"])) {
			$additional_search_params = $params["search"]["additionalSearchParams"];
			unset($params["search"]["additionalSearchParams"]);
		}

		$query = \Models\QualityControlReportView
			::select(
				'user_id',
				'type_id as id',
				'learner_name',
				'type_name as name',
				'type_name',
				'type_id',
				'standard_name',
				'standard_id',
				'qctype',
				'type',
				'qa',
				DB::raw("DATE_FORMAT(updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk"),
				DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
			)->where('qa','=','Rejected');

			if (
				isset($params['search']['standard_id']) &&
				!empty($params['search']['standard_id'])
			) {
				$query = $query->where('standard_id',$params['search']['standard_id']);
				unset($params['search']['standard_id']);
			}
			if (isset($params["search"]) && is_array($params["search"])) {
				unset($params["search"]["refresh"]);
			}

			if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
				$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
			}
			$p = \APP\SmartTable::searchPaginate($params, $query);

			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-qa', 'select'));


// QA IN PROGRESS CRITERIA LIST FOR MANAGERS
    $group->post('/in-progress-criteria-list-manager', function (Request $request, Response $response, array $args) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["additionalSearchParams"])) {
            $additional_search_params = $params["search"]["additionalSearchParams"];
            unset($params["search"]["additionalSearchParams"]);
        }

        $query = \Models\QualityControlReportView
            ::select(
                'quality_controls_report_view.user_id',
                'quality_controls_report_view.type_id as id',
                'quality_controls_report_view.learner_name',
                'quality_controls_report_view.type_name as name',
                'quality_controls_report_view.type_name',
                'quality_controls_report_view.type_id',
                'quality_controls_report_view.standard_name',
                'quality_controls_report_view.standard_id',
                'quality_controls_report_view.qctype',
                'quality_controls_report_view.type',
                'quality_controls_report_view.qa',
                DB::raw("DATE_FORMAT(quality_controls_report_view.updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk"),
                DB::raw("DATE_FORMAT(quality_controls_report_view.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
            )->where('quality_controls_report_view.qa','=','Rejected');

        if(\APP\Auth::isManager()){
//            $query->join("users", function($join) {
//                $join
//                    ->on("users.id", "=", "quality_controls_report_view.user_id")
//                ;
//            });
            $query->whereIn('quality_controls_report_view.user_id',
                \Models\ManagerUser
                    ::select('user_id')
                    ->where('manager_id', \APP\Auth::getUserId())
                    ->get()
            );
        }


        if (
            isset($params['search']['standard_id']) &&
            !empty($params['search']['standard_id'])
        ) {
            $query = $query->where('standard_id',$params['search']['standard_id']);
            unset($params['search']['standard_id']);
        }
        if (isset($params["search"]) && is_array($params["search"])) {
            unset($params["search"]["refresh"]);
        }

        if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
            $query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
        }
        $p = \APP\SmartTable::searchPaginate($params, $query);

        $response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


    })->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));



	$group->post('/qa-reports-list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]["additionalSearchParams"])) {
			$additional_search_params = $params["search"]["additionalSearchParams"];
			unset($params["search"]["additionalSearchParams"]);
		}

		$query = \Models\ManagerReview
			::select(
				"manager_reviews.id",
				"manager_reviews.user_id as user_id",
				"manager_reviews.completion_status",
				"manager_reviews.created_at",
				"manager_reviews.updated_at",
				"apprenticeship_standards.name as standards_name",
				"created_by.fname as created_by_fname",
				"created_by.lname as created_by_lname",
				"created_for.fname",
				"created_for.lname",
				DB::raw("DATE_FORMAT(manager_reviews.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"),
				DB::raw("DATE_FORMAT(manager_reviews.updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk")
			)

			->join("apprenticeship_standards_users", function($join) {
				$join
					->on("apprenticeship_standards_users.user_id", "=", "manager_reviews.user_id")
					->on("apprenticeship_standards_users.standard_id", "=", "manager_reviews.standard_id")
				;
			})

			->join("apprenticeship_standards", function($join) {
				$join
					->on("apprenticeship_standards.id", "=", "apprenticeship_standards_users.standard_id")
				;
			})

			->join("users as created_by", function($join) {
				$join
					->on("created_by.id", "=", "manager_reviews.created_by")
				;
			})

			->join("users as created_for", function($join) {
				$join
					->on("created_for.id", "=", "manager_reviews.user_id")
				;
			})


			->where('manager_reviews.visit_type', 'QA Report')
			->where(function($query) {
				$query
					->where('manager_reviews.completion_status', 'In Progress')
					->orWhere('manager_reviews.completion_status', 'Not Started')
				;
			})
			->where('manager_reviews.status', true)
			->where('apprenticeship_standards.status', true)
			->where('created_for.status', true)
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (
			isset($additional_search_params['manager_id']) &&
			!empty($additional_search_params['manager_id'])
		) {
			$query = $query
				->whereIn('created_for.id',
					\Models\ManagerUser
						::select('user_id')
						->where('manager_id', $additional_search_params['manager_id'])
						->get()
				)
			;
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-qa', 'select'));

	// Get action count!
	// Lazy man's not so impressive queries.
	$group->get("/action-count", function (Request $request, Response $response) {

		// Get approval count
		$query = \Models\LearningResult
			::select('learning_results.*')
			->where("refreshed", "=", 0)
			->whereHas("UserLearningModules", function($query) {
				$query
					->whereRaw("user_learning_modules.user_id = learning_results.user_id")
					->whereRaw('user_learning_modules.deleted_at is null')
				;
			})
			->join("users", function($join) {
				$join
					->on("learning_results.user_id", "=", "users.id")
					->where("users.status", "=", 1)
				;
				if (!\APP\Auth::accessAllCompanies() && !\APP\Auth::accessAllLearners()) {
					$join->where("users.company_id", "=", \APP\Auth::getUser()->company_id);
				}
			})
			->join("learning_modules", function($join) {
				$join
					->on("learning_results.learning_module_id", "=", "learning_modules.id")
					->where("learning_modules.status", "=", 1)
				;
			})
			->join("learning_module_evidence_meetings", function($join) {
				$join
					->on("learning_module_evidence_meetings.learning_modules_id", "=", "learning_results.learning_module_id")
					->on("learning_module_evidence_meetings.user_id", "=", "learning_results.user_id")
				;
			})
		;
		if (!\APP\Auth::accessAllLearners()) {
			if (\APP\Auth::isManager() || \APP\Auth::isAdmin()) {
				$query->whereIn(
					"learning_results.user_id",
					\Models\ManagerUser
						::where('manager_id', '=', \APP\Auth::getUserId())
						->select('user_id')
						->get()
				);
			}
		}



		$meetings = clone $query;
		$meetings = $meetings
			->whereNotNull("learning_module_evidence_meetings.manager_accepted")
			->where('learning_module_evidence_meetings.manager_accepted', '=', 0)
			->groupBy('learning_results.id')
		;


		$qa_reports = \Models\ManagerReview::join("apprenticeship_standards_users", function($join) {
				$join
					->on("apprenticeship_standards_users.user_id", "=", "manager_reviews.user_id")
					->on("apprenticeship_standards_users.standard_id", "=", "manager_reviews.standard_id")
				;
			})

			->join("apprenticeship_standards", function($join) {
				$join
					->on("apprenticeship_standards.id", "=", "apprenticeship_standards_users.standard_id")
				;
			})

			->join("users as created_by", function($join) {
				$join
					->on("created_by.id", "=", "manager_reviews.created_by")
				;
			})

			->join("users as created_for", function($join) {
				$join
					->on("created_for.id", "=", "manager_reviews.user_id")
				;
			})


			->where('manager_reviews.visit_type', 'QA Report')
			->where(function($query) {
				$query
					->where('manager_reviews.completion_status', 'In Progress')
					->orWhere('manager_reviews.completion_status', 'Not Started')
				;
			})
			->where('manager_reviews.status', true)
			->where('apprenticeship_standards.status', true)
			->where('created_for.status', true)
		;

		$qa_in_progress = \Models\LearningResult::join("user_learning_modules", function($join) {
				$join
					->on("user_learning_modules.user_id", "=", "learning_results.user_id")
					->on("user_learning_modules.learning_module_id", "=", "learning_results.learning_module_id")
					->whereRaw('user_learning_modules.deleted_at is null')
				;
			})
			->join("users", function($join) {
				$join
					->on("users.id", "=", "learning_results.user_id")
				;
			})
			->join("learning_modules", function($join) {
				$join
					->on("learning_modules.id", "=", "learning_results.learning_module_id")
				;
			})
			->join("learning_module_types", function($join) {
				$join
					->on("learning_module_types.id", "=", "learning_modules.type_id")
				;
			})
			->where('learning_results.refreshed', 0)
			->where('users.status', true)
			->where('learning_modules.status', true)
			->where('learning_modules.type_id', '>', 6)
			->where('learning_modules.type_id', '<', 10)
			->whereNotNull('learning_results.qa')
			->where('learning_results.qa', 'Rejected')
		;

		$qa_in_progress_criteria =  \Models\QualityControlReportView::where('qa','=','Rejected');

		$actionObject = new \stdClass;
		$actionObject->meetings = count($meetings->get());
		$actionObject->qa_reports = count($qa_reports->get());
		$actionObject->qa_in_progress = count($qa_in_progress->get()) + count($qa_in_progress_criteria->get()) ;


		// user form count section


		$user = Auth::getUser();
		$role_id = $user->shadow_role_id==null?$user->role_id:$user->shadow_role_id;

		$isAccessAllLearners = Role::find($role_id)->access_all_learners;
		if($isAccessAllLearners){
			$matchedUserFormIds = [];
			$userFormIds = DB::table('forms')
						->select('user_forms.id', 'forms.has_sign_off_order')
						->join('form_signoff_roles', 'form_signoff_roles.form_id', '=', 'forms.id')
						->join('user_forms', 'user_forms.form_id', '=', 'forms.id')
						->where('form_signoff_roles.role_id', $role_id)
						->get();
			$userFromIdsWithNoOrder = $userFormIds->filter(function($item){ return $item->has_sign_off_order == 0; });
			$userFormIdsWithOrder 	= $userFormIds->filter(function($item){ return $item->has_sign_off_order == 1; });
			if(count($userFormIdsWithOrder) > 0) {
				$userFormIdsWithOrder_aa = DB::table('user_forms')
						->select('user_forms.id as user_form_id', 'form_signoff_roles.role_id', 'form_signoff_roles.order')
						->join('form_signoff_roles', 'form_signoff_roles.form_id', '=', 'user_forms.form_id')
						->whereIn('user_forms.id', $userFormIdsWithOrder->pluck('id'))
						->get();
				if (count($userFormIdsWithOrder_aa) > 0) {
					foreach ($userFormIdsWithOrder_aa as $key => $value) {
						if (!isset($newFormat[$value->user_form_id])) {
							$newFormat[$value->user_form_id] = [
								"user_form_id" => $value->user_form_id,
								"roles" => []
							];
						}

						$newFormat[$value->user_form_id]['roles'][] = [
							"role_id" => $value->role_id,
							"order" => $value->order
						];
					}
				}
				if (count($newFormat) > 0) {

					foreach ($newFormat as $key => $userForm) {
						$userFormId = $userForm['user_form_id'];
						$roles = $userForm['roles'];
						foreach ($roles as $key => $role) {
							if ($role_id == $role['role_id']) {
								$currentOrder = $role['order'];
								// If the current order is 0, directly add the user_form_id
								if ($currentOrder == 0) {
									$matchedUserFormIds[] = $userFormId;
									break;
								}
									else {
									$previousRole = collect($roles)->firstWhere('order', $currentOrder - 1);
									$previousRoleId = $previousRole['role_id'];
									if (UserFormSignoff::where(['user_form_id' => $userFormId, 'signoff_role' => $previousRoleId])->exists()) {
										$matchedUserFormIds[] = $userFormId;
										break;
									}
								}
							}
						}
					}
				}
			}
			$matchedUserFormIds = array_merge($userFromIdsWithNoOrder->pluck('id')->toArray(), $matchedUserFormIds);
		}

		$userForms = \Models\UserForm
			::with('FormValue')
			->with(['Form'=>function($query){
				$query->with('FormSignOffRoles.role');
			}])
			->leftjoin('forms','forms.id','user_forms.form_id')
			->leftjoin('users','users.id','user_forms.user_id')
			->with('User')
		;

		if (
			!\APP\Auth::isAdmin()
		) {
			$userForms = $userForms
				->where(function($query) use ($user, $role_id, $isAccessAllLearners, $matchedUserFormIds) {
					// Logic applied to all non admin roles.
					$query = $query
						->where(function($query) use ($user, $role_id) {
							$query = $query
								->where(function($query) use ($user) {
									$query->where(function ($subquery) {
										$subquery->whereExists(function ($existsQuery) {
											$existsQuery->select(DB::raw(1))
												->from('schedule_links')
												->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
												->where('user_forms.type','schedule')
												->where('link_id', \APP\Auth::getUserId())
												->whereNull('deleted_by')
												->where('schedule_links.type', 'managers')
												->whereNull('schedule_links.deleted_at');
										})
											->where(function ($innerSubquery) {
												$innerSubquery->whereExists(function ($nestedSubquery) {
													$nestedSubquery->from('schedules')
														->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
														->whereColumn('user_forms.type_id', '=', 'schedules.id')
														->where('user_forms.type', 'schedule')
														->where('forms.restricted_form', 1);
												});
											});
									})
										->orWhere(function ($subquery) {
											$subquery->whereNotExists(function ($notExistsQuery) {
												$notExistsQuery->select(DB::raw(1))
													->from('schedule_links')
													->where('user_forms.type','schedule')
													->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
													->where('schedule_links.type', 'managers');
											})
												->whereIn('user_forms.user_id', function ($whereInQuery) {
													$whereInQuery->select('user_id')
														->from('manager_users')
														->where('manager_id', \APP\Auth::getUserId())
														->whereNull('manager_users.deleted_at');
												});
										})
										->orWhere(function ($innerSubquery) {
											$innerSubquery->whereExists(function ($nestedSubquery) {
												$nestedSubquery->from('schedules')
													->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
													->whereColumn('user_forms.type_id', '=', 'schedules.id')
													->where('user_forms.type', 'schedule')
													->where('forms.restricted_form', 0);
											})
												->whereIn('user_forms.user_id', function ($query) {
													$query->select('user_id')
														->from('manager_users')
														->where('manager_id', \APP\Auth::getUserId())
														->whereNull('manager_users.deleted_at');
												});
										})
									;
								})
								// This will show only sign off form if current role is in "Management Roles To Sign-off Form"
								//FOR ISSUE 3260: This is commented because of it needs estio but in wrap ,not shows with out sign off forms

								// ->whereHas("Form.FormSignOffRoles", function ($query) use ($user) {
								// 	$query = $query
								// 		->where('role_id', \APP\Auth::roleId())
								// 	;
								// })
								//END OF FIX
							;

							//need to identify manager approval or not
							$query = $query
								->where(function($query) use ($user,$role_id) {
									$query
										->where(function($query) use ($user,$role_id) {
											$query
												->WhereDoesntHave("UserFormSignOff", function ($query) use ($user,$role_id) {
													$query = $query
														->where('signoff_role', '=', $role_id)
														->where('user_id', '=', $user->id)
													;
												})
												->where("user_forms.user_form_status", "Awaiting Sign-off")
											;
										})
										->orWhere("user_forms.user_form_status", "!=", "Awaiting Sign-off")
									;
								})
							;
						})
					;
					// If manager, then also show forms assigned to him
					if (\APP\Auth::isManager()) {
						$query = $query
							->orWhere('user_forms.user_id', \APP\Auth::getUserId())
						;
					}
					if ($isAccessAllLearners) {
						$query = $query->orWhereIn('user_forms.id',$matchedUserFormIds);
					}
				})
			;
		}

		$userForms = $userForms
			->where("user_forms.user_form_status", "Awaiting Sign-off")
			->count()
		;
		$actionObject->signOffForms = $userForms;



		$response->getBody()->write(json_encode($actionObject));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));


	// This is expensive query!
	// Manager reviews and learning results are queried, then combined/union for results.
	$group->post('{router:managers/list|/qa-managers-list}{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'qamanagersList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		$query = \APP\SmartTable::searchPaginate($params, $query, false, false);

		if (isset($args["download"]) && $args["download"] == "/download") {

			$export_fields = [
				"Learning" => "learning_name",
				"Apprentice" => "apprentice_name",
				"Report/Learning" => "qa_type",
				"Date Due" => "due_at_uk",
				"Status" => "completion_status",
				"Favourite" => "improvements",
			];


			$download_file_name = uniqid("qa.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$query,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			// After union paginate does not work as paginate counts first part of query not whole union, too complicated, will have to run full query in LengthAwarePaginator.
			$page = isset($params["page"]) ? $params["page"] : 1;
			$nPage = isset($params["nPage"]) ? $params["nPage"] : 10;
			$results = new \Illuminate\Pagination\LengthAwarePaginator(
				array_slice(
					$query,
					($page - 1) * $nPage,
					$nPage
				),
				count($query),
				$nPage,
				$page,
				[
					"path" => "search"
				]
			);
		}

		$response->getBody()->write($results->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));
});