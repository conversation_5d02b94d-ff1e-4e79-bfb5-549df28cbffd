<?php

use APP\Auth;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\CreditUsage;
use Models\DepartmentLearningModule;
use Models\DesignationLearningModule;
use Models\GroupLearningModule;
use Models\GroupUser;
use Models\User;

// all about assigning learning resource to users
$app->group("/learningusers",  function ($group) {

	// Assign specific learning resources to user
	$group->put('/user/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		// Assing learning resource to specified user
		$data = $request->getParsedBody();
		$user = \Models\User::find($args["id"]);

        $module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
        $creditUsers = CreditUsage::checkCreditExist($module_ids,[$args['id']]);
        if(!$creditUsers){
			$response->getBody()->write(json_encode(['error' => 'Not enough credits']));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
		$module_ids = \APP\Learning::getAllModuleIds($module_ids);


		if (!empty($module_ids)) {
			\Models\UserLearningModule::linkResources($user->id, $module_ids, 'direct assign');
			\APP\Controllers\FormController::assignLessonFormToUser($module_ids, $user->id);
		}



		\APP\Learning::syncUserResults($user->id);

		// Send reminder to user
		if (empty($data["no_email"])) {
			\APP\Email::sendEmailReminder([$user->id], $module_ids);
        }
        CreditUsage::updateCredit($module_ids,$creditUsers);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));

	// remove learning resource assigmnentdskjhfdskjhf from user
	$group->put('/user/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$user = \Models\User::find($args["id"]);

		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
		$module_ids = \APP\Learning::getAllModuleIds($module_ids);
		if (!empty($module_ids)) {
			\Models\UserLearningModule::unlinkResources($user->id, $module_ids, 'direct remove');
        }
        CreditUsage::restoreCredit($module_ids,[$user->id]);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));

	// Assign learning resources to department
	$group->put('/department/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		set_time_limit(0);

		$data = $request->getParsedBody();
		$dep = \Models\Department::find($args["id"]);

		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
        $users = User::where('department_id',$dep->id)->pluck('id')->toArray();
        $creditUsers = CreditUsage::checkCreditExist($module_ids,$users);
        if(!$creditUsers){
			$response->getBody()->write(json_encode(['error' => 'Not enough credits']));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
		foreach ($module_ids as $value) {
			DepartmentLearningModule::updateOrCreate(['department_id' => $dep->id, 'learning_module_id' => $value, 'assigner_id' => \APP\Auth::getUserId()], ['cron_status' => true, 'is_assigner_admin' => \APP\Auth::isAdmin(), 'removed_status' => false]);
        }
        CreditUsage::updateCredit($module_ids,$creditUsers);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));


	// remove specified modules from department
	$group->put('/department/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$dep = \Models\Department::find($args["id"]);

        $module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
        $isAdmin = \APP\Auth::isAdmin();
		$module_ids = \APP\Learning::getAllModuleIds($module_ids);
		foreach ($module_ids as   $module) {
			DepartmentLearningModule::where('department_id', $dep->id)
			->where('learning_module_id', $module)
                ->where(function ($query)use($isAdmin) {
                if(!$isAdmin) {
				$query->where("assigner_id", \APP\Auth::getUserId())
                    ->orwhere("assigner_id", null);
                }
			})->update(['removed_status' => true, "assigner_id" => \APP\Auth::getUserId(), 'is_assigner_admin' => \APP\Auth::isAdmin()]);
        }
        $users = User::where('department_id',$dep->id)->pluck('id')->toArray();
        CreditUsage::restoreCredit($module_ids,$users);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));

	// Assign learning resource to designation
	$group->put('/designation/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		set_time_limit(0);

		$data = $request->getParsedBody();
		$entry = \Models\Designation::find($args["id"]);

		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
        $users = User::where('designation_id',$entry->id)->pluck('id')->toArray();
        $creditUsers = CreditUsage::checkCreditExist($module_ids,$users);
        if(!$creditUsers){
			$response->getBody()->write(json_encode(['error' => 'Not enough credits']));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
		foreach ($module_ids as $value) {
			DesignationLearningModule::updateOrCreate(['designation_id' => $entry->id, 'learning_module_id' => $value, 'assigner_id' => \APP\Auth::getUserId()], ['cron_status' => true, 'is_assigner_admin' => \APP\Auth::isAdmin(), 'removed_status' => false]);
		}
		//$entry->modules()->syncWithoutDetaching($module_ids);

		//$entry_user_ids = \Models\User::where("designation_id", $entry->id)->get()->pluck('id')->toArray();
		//\Models\UserLearningModule::linkResources($entry_user_ids, $module_ids, 'designation assign');

		//\APP\Learning::syncUserResults($entry_user_ids);

		// Send e-mail to users
		//\APP\Email::sendEmailReminder($entry_user_ids, $module_ids);
        CreditUsage::updateCredit($module_ids,$creditUsers);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));


	// remove specified modules from designation
	$group->put('/designation/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
        $entry = \Models\Designation::find($args["id"]);
        $isAdmin = \APP\Auth::isAdmin();

		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
		$module_ids = \APP\Learning::getAllModuleIds($module_ids);
		foreach ($module_ids as  $module) {
			DesignationLearningModule::where('designation_id', $entry->id)
			->where('learning_module_id', $module)
                ->where(function ($query)use($isAdmin) {
                    if(!$isAdmin) {
				$query->where("assigner_id", \APP\Auth::getUserId())
                    ->orwhere("assigner_id", null);
                }
			})
			->update(['removed_status' => true,"assigner_id" => \APP\Auth::getUserId(), 'is_assigner_admin' => \APP\Auth::isAdmin()]);
        }
        $users = User::where('designation_id',$entry->id)->pluck('id')->toArray();
        CreditUsage::restoreCredit($module_ids,$users);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));


	// Assign learning resource to group
	$group->put('/group/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$group = \Models\Group::find($args["id"]);

		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
        $users = GroupUser::where('group_id',$group->id)->pluck('user_id')->toArray();
        $creditUsers = CreditUsage::checkCreditExist($module_ids,$users);
        if(!$creditUsers){
			$response->getBody()->write(json_encode(['error' => 'Not enough credits']));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(400);
        }
		foreach ($module_ids as $module) {
			//remove_status set to false to make sure this is updated during assigning.
			GroupLearningModule::updateOrCreate(['group_id' => $group->id, 'learning_module_id' => $module, 'assigner_id' => \APP\Auth::getUserId()], ['cron_status' => true, 'is_assigner_admin' => \APP\Auth::isAdmin(), 'removed_status' => false]);
        }
        CreditUsage::updateCredit($module_ids,$creditUsers);
		//$group->modules()->syncWithoutDetaching($module_ids);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));

	$group->put('/group/delete/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
        $group = \Models\Group::find($args["id"]);
        $isAdmin = \APP\Auth::isAdmin();

		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
		$module_ids = \APP\Learning::getAllModuleIds($module_ids);
		foreach ($module_ids as  $module) {
			GroupLearningModule::where('group_id', $group->id)
                ->where(function ($query)use($isAdmin) {
                    if(!$isAdmin) {
					$query->where("assigner_id", \APP\Auth::getUserId())
                    ->orwhere("assigner_id", null);
                    }
				})
				->where('learning_module_id', $module)
				->update(['removed_status' => true, "assigner_id" => \APP\Auth::getUserId(),'is_assigner_admin' => \APP\Auth::isAdmin()]);
        }
        $users = GroupUser::where('group_id',$group->id)->pluck('user_id')->toArray();
        CreditUsage::restoreCredit($module_ids,$users);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));

	$group->post('/users/list', function (Request $request, Response $response) {

		$params = $request->getParsedBody();

		if (
			isset($params["search"]) &&
			isset($params["search"]["module_ids"])
		) {
			$module_ids = $params["search"]["module_ids"];
			unset($params["search"]["module_ids"]);
		} else {
			$module_ids = false;
		}


		if (
			isset($params["search"]) &&
			isset($params["search"]["issue_ids"])
		) {
			$issue_ids = $params["search"]["issue_ids"];
			unset($params["search"]["issue_ids"]);
		} else {
			$issue_ids = false;
		}

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}


		$standard_id = false;
		if (isset($params["search"]["standard_id"])) {
			$standard_id = $params["search"]["standard_id"];
			unset($params["search"]["standard_id"]);
		}


		$query = \Models\User
			::select("users.id", "fname", "lname", "email", "company_id", "designation_id")
			->where("users.status", true)
			->with(["company" => function ($query) {
				$query->select(["companies.id", "companies.name"]);
			}])

			->with(["groups" => function ($query) {
				$query->select("groups.id");
			}]);

		if ($standard_id) {
			$query = $query
				->whereIn(
					'users.id',
					\Models\ApprenticeshipStandardUser::select('user_id')
						->where('standard_id', $standard_id)
						->get()
				);
		}


		if (
			!$issue_ids
		) {
			if (!$module_ids) {
				$module_ids = [];
			}
			$query = $query
				->with(["modules" => function ($query) use ($module_ids) {
					$query
						->select(["learning_modules.id", "learning_modules.name"])
						->whereIn("learning_modules.id", $module_ids);
				}]);
		} else if (
			$issue_ids &&
			$module_ids
		) {
			// Looks like logic here will list all entries that have issue and module ids, therefore if all entries count equals needed count, then it passes, is this even sane?
			// Was possibly drunk when I did this? But it works!
			$query = $query
				->withCount(["ApprenticeshipIssuesUserLearningModules" => function ($query) use ($module_ids, $issue_ids) {
					$query
						->whereIn("apprenticeship_issues_user_learning_modules.learning_modules_id", $module_ids)
						->whereIn("apprenticeship_issues_user_learning_modules.apprenticeship_issues_id", $issue_ids)
						->having(DB::raw('count(apprenticeship_issues_user_learning_modules.id)'), '=', (count($module_ids) * count($issue_ids)));
				}]);
		}

		if (
			\APP\Auth::isManager() &&
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::accessAllLearners()
		) {
			$query = $query
				->whereIn("users.id", function ($query) {
					$query
						->select("user_id")
						->from("manager_users")
						->where("manager_id", "=", \APP\Auth::getUserId())
						->whereNull('manager_users.deleted_at');
				});
		}


		if (
			isset($params["search"]) &&
			isset($params["search"]["assigned"])
		) {
			if (!$issue_ids) {
				if (!$module_ids) {
					$module_ids = [];
				}
				$query
					->leftjoin("user_learning_modules", function ($join) use ($module_ids, $params) {
						$join->on("user_learning_modules.user_id", "=", "users.id");
						$join->whereIn("user_learning_modules.learning_module_id", $module_ids);
						$join->whereNull("user_learning_modules.deleted_at");
					});
			}



			if ($params["search"]["assigned"] == "1") {
				if (
					$issue_ids &&
					$module_ids
				) {
					$query = $query
						->whereHas('ApprenticeshipIssuesUserLearningModules', function ($query) use ($module_ids, $issue_ids) {
							$query
								->whereIn("apprenticeship_issues_user_learning_modules.learning_modules_id", $module_ids)
								->whereIn("apprenticeship_issues_user_learning_modules.apprenticeship_issues_id", $issue_ids)
								->having(DB::raw('count(apprenticeship_issues_user_learning_modules.id)'), '=', (count($module_ids) * count($issue_ids)));
						});
				} else {
					$query->havingRaw("MAX(user_learning_modules.learning_module_id) IS NOT NULL");
				}
			}
			if ($params["search"]["assigned"] == "0") {
				if (
					$issue_ids &&
					$module_ids
				) {
					$query = $query
						->whereDoesntHave('ApprenticeshipIssuesUserLearningModules', function ($query) use ($module_ids, $issue_ids) {
							$query
								->whereIn("apprenticeship_issues_user_learning_modules.learning_modules_id", $module_ids)
								->whereIn("apprenticeship_issues_user_learning_modules.apprenticeship_issues_id", $issue_ids)
								->having(DB::raw('count(apprenticeship_issues_user_learning_modules.id)'), '=', (count($module_ids) * count($issue_ids)));
						});
				} else {
					$query->havingRaw("MAX(user_learning_modules.learning_module_id) IS NULL");
				}
			}

			$query->groupBy("users.id");
			unset($params["search"]["assigned"]);
		}



		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'select'));


	// Retrieve list of jobs/designations or departments
	$group->post('/{type:designation|department|group}/list', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();
		if (isset($params["search"]["module_ids"])) {
			$module_ids = $params["search"]["module_ids"];
			unset($params["search"]["module_ids"]);
		} else {
			$module_ids = [];
		}
		$module_ids[] = 0;

		if (isset($params["search"]["issue_ids"])) {
			$issue_ids = $params["search"]["issue_ids"];
			unset($params["search"]["issue_ids"]);
		} else {
			$issue_ids = [];
		}

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		switch ($args['type']) {
			case 'designation':
				$query = \Models\Designation
					::where('designations.id', '>', 0);
				break;

			case 'department':
				$query = \Models\Department
					::where('departments.id', '>', 0);
				break;

			case 'group':
				$query = \Models\Group
					::where('groups.id', '>', 0);
				break;
		}

		$query = $query
			->selectRaw($args['type'] . "s.*")
			->where($args['type'] . "s.status", true)
			->with(["modules" => function ($query) use ($module_ids) {
				$query
					->select(["learning_modules.id", "learning_modules.name"])
					->whereIn("learning_modules.id", $module_ids);;
			}]);

		if (isset($params["search"]) && isset($params["search"]["assigned"])) {
			$query->leftjoin($args['type'] . "_learning_modules", function ($join) use ($module_ids, $params, $args) {
				$join->on($args['type'] . "_learning_modules." . $args['type'] . "_id", "=", $args['type'] . "s.id");
				$join->whereIn($args['type'] . "_learning_modules.learning_module_id", $module_ids);
			});

			if ($params["search"]["assigned"] == "1") {
				$query->havingRaw("MAX(" . $args['type'] . "_learning_modules.learning_module_id) IS NOT NULL");
			}

			if ($params["search"]["assigned"] == "0") {
				$query->havingRaw("MAX(" . $args['type'] . "_learning_modules.learning_module_id) IS NULL");
			}

			$query->groupBy($args['type'] . "s.id");
			unset($params["search"]["assigned"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'select'));


	$group->post('/list', function (Request $request, Response $response) {


		$params = $request->getParsedBody();

		$query = \Models\LearningModule::select("learning_modules.*")
			->with("category")
			->with("FPCategory")
			->with("type")
			->with("competencies")
			->with("company")
			// Maybe there was a legit reason to use this?
			// I cut it off and with thousands of users per system it took up to 10 seconds for record to complete.

			/*->with(["users" => function ($query) {
				$query->select("users.id");
			}])

			->with(["departments" => function ($query) {
				$query->select("departments.id");
			}])
			->with(["groups" => function ($query) {
				$query->select("groups.id");
			}])
			*/
			->where("status", true);

		/*
		if ($this->get('settings')['licensing']['isSMCR']) {
			$query = $query
				->whereNull('f_p_category_id')
			;
		}
		*/
		// If you are manager/admin, show evidence created by you for your trainees or by you for re-use, or by other managers for reuse
		// If you are trainee, show evidence created for you or managers/admin
		if (
			!\APP\Auth::isAdmin() ||
			(
				\APP\Auth::isAdmin() &&
				!\APP\Auth::showAllResources()
			)
		) {
			$query = \Models\LearningModule::filterEvidenceforRoles($query);
		}
		if(!Auth::isAdmin()){
			$query->where(function($query){
					$query->where('learning_modules.created_by',Auth::getUserId());
					$query->orWhere('learning_modules.company_id',Auth::getUser()->company_id);
					$query->orWhere('learning_modules.company_id',null);
			});
		}


		if (isset($params["search"])) {
			if (isset($params["search"]["competency_id"])) {
				$query->join("learning_module_competencies", function ($join) use ($params) {
					$join->on("learning_module_competencies.learning_module_id", "=", "learning_modules.id")
						->where("learning_module_competencies.competency_id", "=", $params["search"]["competency_id"]);
				});

				unset($params["search"]["competency_id"]);
			}

			if (isset($params["search"]["company_id"])) {
				$query->where("company_id", "=", $params["search"]["company_id"])
					->orWhereNull("company_id");

				unset($params["search"]["company_id"]);
			}
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$json = $p->toJson();

		$response->getBody()->write($json);
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'select'));

	/*todo: Code review*/
	$group->put('/update-resource-status/user/{id:[0-9]+}/{module_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::find($args["id"]);
		$learningResult = \Models\LearningResult::where([['user_id', $user->id], ['learning_module_id', $args['module_id']], ['refreshed', 0]])->first();
		$learningResult->completion_status = 'completed';
		$learningResult->save();

		return $response;
	});

});
