<?php
/*
	too big file!
*/

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Illuminate\Database\Capsule\Manager as DB;
use Models\ApprenticeshipIssuesLearningModules;
use Models\ApprenticeshipIssueCategories;
use Models\CreditUsage;
use Models\GroupUser;
use Slim\Psr7\Stream;

$app->group("/apprenticeshipissues", function ($group) {


	// Get paginated list of departments with indication to what issues/resources assigned
	$group->post('/{link:departments|groups|designations}/list', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		// Used to refresh table!
		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		// Specific search parameters that I must ommit from passing to pagination, but use before pagination
		$searches = [
			'module_ids',
			'issue_ids',
			'standard_id'
		];
		foreach ($searches as $key => $search) {
			${$search} = false;
			if (
				isset($params["search"]) &&
				isset($params["search"][$search])
			) {
				${$search} = $params["search"][$search];
				unset($params["search"][$search]);
			}
		}

		// Get all standards, should be one, from list of passed issues
		$standard_ids = \Models\ApprenticeshipStandard
			::where('status', true)
			->whereIn('id',
				\Models\ApprenticeshipIssueCategories
					::select('standard_id')
					->where('status', true)
					->whereIn('id',
						\Models\ApprenticeshipIssues
							::select('issue_category_id')
							->where('status', true)
							->whereIn('id', $issue_ids)
							->get()
					)
					->get()
			)
			->get()
			->pluck('id')
			->toArray()
		;


		switch ($args['link']) {
			case 'groups':
				$parameter = $args['link'];
				$query = \Models\Group
					::where($parameter . '.status', 1);
				break;

			case 'departments':
				$parameter = $args['link'];
				$query = \Models\Department
					::where($parameter . '.status', 1);
				break;

			case 'designations':
				$parameter = $args['link'];
				$query = \Models\Designation
					::where($parameter . '.status', 1);
				break;
		}


		$query = $query
			->withCount(["ApprenticeshipIssues" . ucfirst($parameter) => function ($query) use ($module_ids, $issue_ids, $parameter) {
				$query
					->whereIn("apprenticeship_issues_$parameter.learning_module_id", $module_ids)
					->whereIn("apprenticeship_issues_$parameter.apprenticeship_issue_id", $issue_ids)
					->having(
						DB::raw("count(apprenticeship_issues_$parameter.id)"), '=', (count($module_ids) * count($issue_ids))
					);
			}])
			->withCount(['Users' => function ($query) use ($standard_ids) {
				$query = $query
					->validuser()
					->acessfilter()
				;
				if (count($standard_ids) > 0) {
					$query = $query
						->whereIn('users.id',
							\Models\ApprenticeshipStandardUser
								::select('user_id')
								->whereIn('standard_id', $standard_ids)
								->get()
						)
					;
				}
			}])
			->where(function ($query) use ($standard_ids, $parameter) {
				if (
					!\APP\Auth::isAdmin() &&
					!\APP\Auth::accessAllLearners()
				) {
					$query
						->whereHas("Users", function ($query) use ($standard_ids) {
							$query = $query
								->validuser()
								->acessfilter()
							;
							if (count($standard_ids) > 0) {
								$query = $query
									->whereIn('users.id',
										\Models\ApprenticeshipStandardUser
											::select('user_id')
											->whereIn('standard_id', $standard_ids)
											->get()
									)
								;
							}
						})
						->orWhere(function ($query) use ($standard_ids, $parameter) {
							switch ($parameter) {
								case 'groups':
									$query
										->whereIn("groups.id",
											\Models\ManagerGroup
												::select('group_id')
												->where("manager_id", \APP\Auth::getUserId())
												->get()
										)
									;
									break;

								case 'departments':
									$query
										->whereIn("departments.id",
											\Models\ManagerDepartment
												::select('department_id')
												->where("manager_id", \APP\Auth::getUserId())
												->get()
										)
									;
									break;
							}
						})
					;
				}
			})
		;


		if (isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query = $query
					->whereHas(("ApprenticeshipIssues" . ucfirst($parameter)), function ($query) use ($module_ids, $issue_ids, $parameter) {
						$query
							->whereIn("apprenticeship_issues_$parameter.learning_module_id", $module_ids)
							->whereIn("apprenticeship_issues_$parameter.apprenticeship_issue_id", $issue_ids)
							->having(
								DB::raw("count(apprenticeship_issues_$parameter.id)"), '=', (count($module_ids) * count($issue_ids))
							);
					});
			}

			if ($params["search"]["assigned"] == "0") {
				$query = $query
					->whereDoesntHave("ApprenticeshipIssues" . ucfirst($parameter), function ($query) use ($module_ids, $issue_ids, $parameter) {
						$query
							->whereIn("apprenticeship_issues_$parameter.learning_module_id", $module_ids)
							->whereIn("apprenticeship_issues_$parameter.apprenticeship_issue_id", $issue_ids)
							->having(
								DB::raw("count(apprenticeship_issues_$parameter.id)"), '=', (count($module_ids) * count($issue_ids))
							);
					});
			}

			unset($params["search"]["assigned"]);
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);
		$json = $p->toJson();
		$response->getBody()->write($json);
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Assign issue specific resource to department and department users
	$group->post('/link/{link:.+}/{link_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (count($data['issues']) == 0) {
			$data['issues'] = false;
		}
		if (empty($data['start_date'])) {
			$data['start_date'] = false;
		}
		if (empty($data['expected_completion_date'])) {
			$data['expected_completion_date'] = false;
		}


		$entry = false;
		switch ($args['link']) {
            case 'department':
                $users = \Models\User::where('department_id',$args['link_id'])->pluck('id')->toArray();
                $creditUsers = \Models\CreditUsage::checkCreditExist($data['resources'],$users);
                if(!$creditUsers || empty($creditUsers)){
                    return $response->withStatus(400)->write('No credits available for this user');
                }
				\Models\ApprenticeshipIssuesDepartment::linkEntry(
					$data['issues'],
					$data['resources'],
					$args['link_id'],
					$data['start_date'],
					$data['expected_completion_date'],
					(isset($data['signoff']) ? $data['signoff'] : 0),
					(isset($data['disable_email']) ? $data['disable_email'] : false)
                );
                CreditUsage::updateCredit($data['resources'],$users);
				break;

            case 'group':
                $users = GroupUser::where('group_id',$args['link_id'])->pluck('user_id')->toArray();
                $creditUsers = \Models\CreditUsage::checkCreditExist($data['resources'],$users);
                if(!$creditUsers || empty($creditUsers)){
                    return $response->withStatus(400)->write('No credits available for this user');
                }
				\Models\ApprenticeshipIssuesGroup::linkEntry(
					$data['issues'],
					$data['resources'],
					$args['link_id'],
					$data['start_date'],
					$data['expected_completion_date'],
					(isset($data['signoff']) ? $data['signoff'] : 0)
                );
                CreditUsage::updateCredit($data['resources'],$users);
				break;

            case 'designation':
                $users = \Models\User::where('designation_id',$args['link_id'])->pluck('id')->toArray();
                $creditUsers = \Models\CreditUsage::checkCreditExist($data['resources'],$users);
                if(!$creditUsers || empty($creditUsers)){
                    return $response->withStatus(400)->write('No credits available for this user');
                }
				\Models\ApprenticeshipIssuesDesignation::linkEntry(
					$data['issues'],
					$data['resources'],
					$args['link_id'],
					$data['start_date'],
					$data['expected_completion_date'],
					(isset($data['signoff']) ? $data['signoff'] : 0)
                );
                CreditUsage::updateCredit($data['resources'],$users);
				break;
		}

		return
			$response;

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


	// Remove issue specific resource frem department and department users
	$group->post('/delete/{link:.+}/{link_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$entry = false;
		switch ($args['link']) {
			case 'department':
                \Models\ApprenticeshipIssuesDepartment::deleteEntry($data['issues'], $data['resources'], $args['link_id'], $data);
                $users = \Models\User::where('department_id',$args['link_id'])->pluck('id')->toArray();
                CreditUsage::restoreCredit($data['resources'],$users);
				break;

			case 'group':
                \Models\ApprenticeshipIssuesGroup::deleteEntry($data['issues'], $data['resources'], $args['link_id']);
                $users = GroupUser::where('group_id',$args['link_id'])->pluck('user_id')->toArray();
                CreditUsage::restoreCredit($data['resources'],$users);
				break;

			case 'designation':
                \Models\ApprenticeshipIssuesDesignation::deleteEntry($data['issues'], $data['resources'], $args['link_id']);
                $users = \Models\User::where('designation_id',$args['link_id'])->pluck('id')->toArray();
                CreditUsage::restoreCredit($data['resources'],$users);
				break;
		}

		return
			$response;

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


	// Update/create/remove department schedule
	$group->post('/department-schedule/{type:new|update|delete}{id:[\/0-9]*}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (isset($args['id'])) {
			$args['id'] = str_replace('/', '', $args['id']);
		}

		if ($args['type'] == 'delete') {
			\Models\ApprenticeshipIssuesDepartmentSchedule::find($args['id'])->delete();
		} else {
			if ($args['type'] == 'new') {
				$query = new \Models\ApprenticeshipIssuesDepartmentSchedule;
			} else {
				$query = \Models\ApprenticeshipIssuesDepartmentSchedule::find($args['id']);
			}
			if ($query) {
				$query->apprenticeship_issues_department_id = $data['apprenticeship_issues_department_id'];
				$query->start_date = $data['start_date'];
				$query->lesson_duration = $data['lesson_duration'];
				$query->save();
			}
		}

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


	// Get paginated list of modules assigned to issue
	$group->post('/modules/list/{issue:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		$query = \Models\LearningModule::select("learning_modules.*")
			->where('status', '=', 1)
			->with("category")
			->with("type")
			->with("competencies")
			->with("company")
			->with(['issues' => function ($query) use ($args) {
				$query
					->where('apprenticeship_issues.id', '=', $args["issue"]);
			}]);

		$query = \Models\LearningModule::filterEvidenceforRoles($query);

		if (isset($params["search"])) {
			if (isset($params["search"]["show_assigned"])) {
				$query->whereHas('issues', function ($query) use ($args) {
					$query
						->where('apprenticeship_issues.id', '=', $args["issue"]);
				});
				unset($params["search"]["show_assigned"]);
			}
			if (isset($params["search"]["is_skill"])) {
				$query
						->where('learning_modules.is_skill', '=', $params["search"]["is_skill"]);
				unset($params["search"]["is_skill"]);
			}
			if (isset($params["search"]["competency_id"])) {
				$query->join("learning_module_competencies", function ($join) use ($params) {
					$join->on("learning_module_competencies.learning_module_id", "=", "learning_modules.id")
						->where("learning_module_competencies.competency_id", "=", $params["search"]["competency_id"]);
				});

				unset($params["search"]["competency_id"]);
			}

			if (isset($params["search"]["company_id"])) {
				$query->where(function($query)
                {
					$query->where("company_id", "=", $params["search"]["company_id"])
					->orWhereNull("company_id")
				   ;
                });

				unset($params["search"]["company_id"]);
			}
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$json = $p->toJson();
		$response->getBody()->write($json);
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Get full list of modules assigned to issue
	$group->get('/modules/list/{issue:[0-9]+}', function (Request $request, Response $response, $args) {

		$query = \Models\ApprenticeshipIssues
			::where('id', $args["issue"])
			->with(['modules' => function ($query) use ($args) {
				$query
					->where('status', true);
			}])
			->first();
		$response->getBody()->write(json_encode($query->modules));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));


	// Per user disabled issues and therefore removal/adding of assigned resources
	$group->post('/user/state', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		if (!empty($params['user_id'])) {
			$user = \Models\User::findOrFail($params['user_id']);

			if (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($user->id) ||
				\APP\Auth::accessAllLearners()
			) {
				$modules_affected = 0;
				if (!empty($params['issues_ids'])) {
					foreach ($params['issues_ids'] as $key => $issue) {
						$apprenticeshipIssue = \Models\ApprenticeshipIssues::find($issue['id']);
						if ($apprenticeshipIssue)  {
							$apprenticeshipIssueCategoriesUser =  \Models\ApprenticeshipIssueCategoriesUser::where([['user_id',$user->id],['issue_category_id', $apprenticeshipIssue->issue_category_id]])->first();
							if ($apprenticeshipIssueCategoriesUser) {
								$apprenticeshipIssueCategoriesUser->disable_not_allowed = 1;
								$apprenticeshipIssueCategoriesUser->save();
							}
						}
						if ($issue['state'] == 1) {
							// Add entry that will indicate issue is disabled
							$disabled_issue = \Models\ApprenticeshipIssuesUserDisabled::firstOrCreate(
								[
									'apprenticeship_issues_id' => $issue['id'],
									'user_id' => $user->id
								]
							);


							// Get modules assigned to issue that is disabled
							$issue_modules = \Models\ApprenticeshipIssuesLearningModules
								::where('apprenticeship_issues_id', '=', $issue['id'])
								->get();

							// Collect all modules belonging to standard, except not to disabled issue and other disabled issues
							$preserve_modules = \Models\ApprenticeshipIssuesLearningModules
								::where('apprenticeship_issues_id', '!=', $issue['id'])
								->whereIn('apprenticeship_issues_id',
									\Models\ApprenticeshipIssues
										::whereIn('issue_category_id',
											\Models\ApprenticeshipIssueCategories
												::where('standard_id', '=', $params['standard_id'])
												->select('id')
												->get()
										)
										->where('status', '=', 1)
										->select('id')
										->get()
								)
								->whereNotIn('apprenticeship_issues_id',
									\Models\ApprenticeshipIssuesUserDisabled
										::where('user_id', '=', $user->id)
										->select('apprenticeship_issues_id')
										->get()
								)
								->get();

							$remove_from_detach_modules = [];
							if ($preserve_modules) {
								foreach ($preserve_modules as $key => $preserve_module) {
									$remove_from_detach_modules[] = $preserve_module->learning_modules_id;
								}
								$remove_from_detach_modules = \APP\Learning::getAllModuleIds($remove_from_detach_modules);
								$remove_from_detach_modules = array_unique($remove_from_detach_modules);
							}


							$detach_modules = [];
							$detached_user_modules=[];
							if ($issue_modules) {
								foreach ($issue_modules as $key => $issue_module) {
									$detach_modules[] = $issue_module->learning_modules_id;
								}

								$detach_modules = \APP\Learning::getAllModuleIds($detach_modules);
								$detach_modules = array_unique($detach_modules);

								// remove any modules that are found in other issues and remove them from list
								$detach_modules = array_diff($detach_modules, $remove_from_detach_modules);

								// I have to find out all modules that are attached to other issues in this standard and remove them from "$detach_modules".
								if (count($detach_modules) > 0) {
									\Models\UserLearningModule::unlinkResources($user->id, $detach_modules, 'programme - disable issue for user - remove related resources');
									$modules_affected = $modules_affected + count($detach_modules);
								}

							}
						} else {
							// remove entry from disabled list
							$disabled_issue = \Models\ApprenticeshipIssuesUserDisabled
								::where('user_id', '=', $params['user_id'])
								->where('apprenticeship_issues_id', '=', $issue['id'])
								->delete();

							// Add learning resources back to user
							$issue_modules = \Models\ApprenticeshipIssuesLearningModules
								::where('apprenticeship_issues_id', '=', $issue['id'])
								->get();

							$attach_modules = [];
							if ($issue_modules) {
								foreach ($issue_modules as $key => $issue_module) {
									$attach_modules[] = $issue_module->learning_modules_id;
								}

								$attach_modules = \APP\Learning::getAllModuleIds($attach_modules);
								$attach_modules = array_unique($attach_modules);

								if (count($attach_modules) > 0) {
									\Models\UserLearningModule::linkResources($user->id, $attach_modules, 'programme - enable issue for user - add related resources');
									$modules_affected = $modules_affected + count($attach_modules);
								}
							}
						}
					}
				}



				// if (!empty($params['user_issues_ids'])) {
				// 	    $detached_user_modules = [];

				// 		foreach ($params['user_issues_ids'] as $key => $user_issue_module) {
				// 			if($user_issue_module['state']==1){
				// 					foreach ($issue_modules as $key => $issue_module) {
				// 						$detached_user_modules[] = $user_issue_module->id;
				// 					}
				// 			}
			    //         }
				// 		if(count($detached_user_modules)>0){
				// 			$detached_user_modules = \APP\Learning::getAllModuleIds($detached_user_modules);
				// 			$detached_user_modules = array_unique($detached_user_modules);
				// 			\Models\UserLearningModule::unlinkResources($user->id, $detached_user_modules,  'removed by disabling outcome/criteria');
				// 		}
				// }
			}

		} else {
			return
				$response->withStatus(500)
					->withHeader('Content-Type', 'text/html')
					->write('user id is not in request');
		}

		//$response = $response->write($modules_affected);

		return
			$response;

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// Shows all modules attached to given issues ID's
	$group->post('/modules/list/', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$standard_user = \Models\ApprenticeshipStandardUser
			::where('user_id', \APP\Auth::getUserId())
			->first();;

		// get standard user entry, figure out when he have started standard.

		// show issue if start_day + start_at is less than now.

		// whereIn id,

		// Where start_at + start_day > now

		$query = \Models\ApprenticeshipIssues
			::whereIn('id', $params['issues'])
			->with(['modules' => function ($query) use ($user, $params, $standard_user) {
				$query
					->where('status', '=', 1)
					->with('category')
					->with("type")
					->with("competencies")
					->with("company")
					->with(['LearningResult' => function ($query) {
						$query
							->where('user_id', '=', \APP\Auth::getUserId())
							->where('refreshed', 0);
					}])
					->whereHas('userlearningmodules', function ($query) use ($user) {
						$query
							->where('user_id', '=', $user->id);
					})
					->whereHas('issues', function ($query) use ($user, $params, $standard_user) {
						$query
							->whereIn('apprenticeship_issues.id', $params['issues'])
							->where(function ($query) use ($user, $standard_user) {
								$query
									->where(function ($query) use ($user, $standard_user) {
										$query
											->where('apprenticeship_issues.visible_resource', false)
											->where(function ($query) use ($user, $standard_user) {
												$query
													->where(function ($query) use ($user, $standard_user) {
														$query
															->where('apprenticeship_issues_learning_modules.custom_work_window', true)
															->whereRaw('(SELECT DATE_ADD("' . $standard_user->start_at . '", INTERVAL apprenticeship_issues_learning_modules.start_day DAY)) <= NOW()');
													})
													->orWhere(function ($query) use ($user, $standard_user) {
														$query
															->where('apprenticeship_issues_learning_modules.custom_work_window', false)
															->whereRaw('(SELECT DATE_ADD("' . $standard_user->start_at . '", INTERVAL apprenticeship_issues.start_day DAY)) <= NOW()');
													});
											});
									})
									->orWhere(function ($query) use ($user) {
										$query
											->where('apprenticeship_issues.visible_resource', true);
									});
							});
					});
			}])
			->with(['evidencemodule' => function ($query) use ($user) {
				$query
					->where('status', '=', 1)
					->with("category")
					->with("type")
					->with("competencies")
					->with("company")
					->wherePivot('user_id', '=', $user->id)
					->with(['LearningResult' => function ($query) {
						$query
							->where('user_id', '=', \APP\Auth::getUserId());
					}])
					->distinct();
			}])
			->with(['usermodules' => function ($query) use ($user) {
				$query
					->wherePivot('user_id', '=', $user->id)
					->where('status', '=', 1)
					->with("category")
					->with("type")
					->with("competencies")
					->with("company")
					->with(['LearningResult' => function ($query) {
						$query
							->where('user_id', '=', \APP\Auth::getUserId());
					}])
					->whereHas('userlearningmodules', function ($query) use ($user) {
						$query
							->where('user_id', '=', $user->id);
					})
					->distinct();
			}])
			->get();
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));


	// When slider is moved in view standards -> view issue modules, this action is called and timings are updated, course due times also needs to be updated, lots to do.
	$group->post("/updateissueduetime", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$query = \Models\ApprenticeshipIssues::find($data['id']);
		$query->end_day = $data['end_day'];
		$query->start_day = $data['start_day'];
		$query->save();

       \Models\ApprenticeshipStandard::updateDurationUpdatedFlag($query->IssueCategory->standard_id,1);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Link resource against issue for specific user only and assign resource to that user
	$group->post("/issueuserresource/{user:[0-9]+}", function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();
        $creditUsers = \Models\CreditUsage::checkCreditExist($data['resources'],[$args['user']]);
        if(!$creditUsers || empty($creditUsers)){
			return \APP\Tools::returnCode($request, $response, 400, 'No credits available for this user');
        }

		\Models\ApprenticeshipIssuesUserLearningModules::linkEntry(
			$data['issues'],
			$data['resources'],
			$args["user"],
			(isset($data['expected_completion_date']) ? $data['expected_completion_date'] : false),
			(isset($data['signoff']) ? $data['signoff'] : 0),
			false,
			(isset($data['start_date']) ? $data['start_date'] : false)
		);
        CreditUsage::updateCredit($data['resources'],$creditUsers);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));


	// Link learning resource against issue and assign that module to all users in standard
	$group->put("/{issue:[0-9]+}/module/{module:[0-9]+}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		\Models\ApprenticeshipIssues::addModuleToIssue($args['issue'], $args['module']);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));

	// Multiple add!
	$group->put("/{issue:[0-9]+}/modules/{module_ids}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$module_ids = json_decode($args['module_ids'], TRUE);
		foreach ($module_ids as $key => $module_id) {
			\Models\ApprenticeshipIssues::addModuleToIssue($args['issue'], $module_id);
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));


	// Remove issue, remove assigned modules, detach users from module if only module in standard
	$group->delete("/{issue_id:[0-9]+}/standard/{standard_id:[0-9]+}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
     ApprenticeshipIssuesLearningModules::deleteModuleUserWorkflow($args['issue_id'],[],false,$args['standard_id']);
		// Get "to be deleted" issue
		$issue = \Models\ApprenticeshipIssues
			::find($args["issue_id"])
		;

		// Get all users assigned to standard
		$standard_users = \Models\ApprenticeshipStandardUser
			::where('standard_id', '=', $args["standard_id"])
			->get()
		;

		// Get modules assigned to issue sent for deletion.
		// Check if there are same module assigned to other issues in standard, return only modules if there is no other association
		$issue_modules = \Models\ApprenticeshipIssuesLearningModules::where('apprenticeship_issues_id', '=', $args["issue_id"])
			->whereNotIn('learning_modules_id',
				\Models\ApprenticeshipIssuesLearningModules // Look into relation table for same module, assigned to different issue
				::where('apprenticeship_issues_id', '!=', $args["issue_id"])
					->whereIn('apprenticeship_issues_id',
						\Models\ApprenticeshipIssues // issue must be into category that is in same standard
						::whereIn('issue_category_id',
							\Models\ApprenticeshipIssueCategories
								::where('standard_id', '=', $args["standard_id"])
								->select('id')
								->get()
						)
							->select('id')
							->get()
					)
					->select('learning_modules_id')
					->get()
			)
			->get()
		;

		//detach modules from users
		foreach ($standard_users as $key => $standard_user) {
			$user = \Models\User::find($standard_user->user_id);
			foreach ($issue_modules as $key => $issue_module) {
				$module_ids = \APP\Learning::getAllModuleIds([$issue_module->learning_modules_id]);
				\Models\UserLearningModule::unlinkResources($user->id, $module_ids, 'standard - issue deleted - remove resources from user' . $issue->id);
			}
		}

		// Delete Modules from issue
		\Models\ApprenticeshipIssuesLearningModules
			::where('apprenticeship_issues_id', $args["issue_id"])
			->delete()
		;
   		// Disable issue, not delete, for now.
		$issue->status = 0;
		$issue->save();


		return
			$response
				->withHeader('Content-Type', 'application/json')//->write(json_encode($issue_modules))
			;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// remove module assigned to issue
	$group->delete("/{issue:[0-9]+}/module/{module:[0-9]+}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		
		// Delete from apprenticeship_issues_learning_modules
		\Models\ApprenticeshipIssuesLearningModules
			::where("apprenticeship_issues_id", "=", $args["issue"])
			->where("learning_modules_id", "=", $args["module"])
			->delete()
		;

		// Get apprenticeship issue with users
		// NOTE: The 'users' relationship already filters out soft-deleted pivot records (apprenticeship_standards_users.deleted_at IS NULL)
		$apprenticeshipissue = \Models\ApprenticeshipIssues
			::where('id', '=', $args["issue"])
			->with(['issuecategory.standard.users' => function($query) {
				$query->where('users.status', 1); // Only active users
			}])
			->first()
		;

		// Get all module IDs
		$module_ids = \APP\Learning::getAllModuleIds([$args["module"]]);

		// Process each user to determine who needs unlinking
		$users_to_unlink = [];
		
		foreach ($apprenticeshipissue->issuecategory->standard->users as $key => $user) {
			// Check if resource is linked to user by other programme using EXISTS for better performance
			$exists_in_other_programme = \Models\LearningModule
				::where('id', $args["module"])
				->whereExists(function($query) use ($user, $apprenticeshipissue) {
					$query->selectRaw('1')
						->from('apprenticeship_issues_learning_modules as ailm')
						->join('apprenticeship_issues as ai', 'ailm.apprenticeship_issues_id', '=', 'ai.id')
						->join('apprenticeship_issue_categories as aic', 'ai.issue_category_id', '=', 'aic.id')
						->join('apprenticeship_standards as as_std', 'aic.standard_id', '=', 'as_std.id')
						->join('apprenticeship_standards_users as asu', 'as_std.id', '=', 'asu.standard_id')
						->whereRaw('ailm.learning_modules_id = learning_modules.id')
						->where('ai.status', 1)
						->where('aic.status', 1)
						->where('as_std.status', 1)
						->where('as_std.id', '!=', $apprenticeshipissue->issuecategory->standard->id)
						->where('asu.user_id', $user->id);
				})
				->first()
			;

			// Check if resource is linked to issue in current programme, exclude issue this resource is being deleted from
			$exists_in_current_programme = \Models\LearningModule
				::where('id', $args["module"])
				->whereExists(function($query) use ($user, $apprenticeshipissue, $args) {
					$query->selectRaw('1')
						->from('apprenticeship_issues_learning_modules as ailm')
						->join('apprenticeship_issues as ai', 'ailm.apprenticeship_issues_id', '=', 'ai.id')
						->join('apprenticeship_issue_categories as aic', 'ai.issue_category_id', '=', 'aic.id')
						->join('apprenticeship_standards as as_std', 'aic.standard_id', '=', 'as_std.id')
						->join('apprenticeship_standards_users as asu', 'as_std.id', '=', 'asu.standard_id')
						->whereRaw('ailm.learning_modules_id = learning_modules.id')
						->where('ailm.apprenticeship_issues_id', '!=', $args["issue"])
						->where('ai.status', 1)
						->where('aic.status', 1)
						->where('as_std.status', 1)
						->where('as_std.id', $apprenticeshipissue->issuecategory->standard->id)
						->where('asu.user_id', $user->id);
				})
				->first()
			;

			if (
				!$exists_in_other_programme &&
				!$exists_in_current_programme
			) {
				$users_to_unlink[] = $user->id;
			}
		}
		
		// Batch unlink: Call unlinkResources once with all users that need unlinking
		if (!empty($users_to_unlink)) {
			\Models\UserLearningModule::unlinkResources(
				$users_to_unlink, 
				$module_ids, 
				'standard(' . $apprenticeshipissue->issuecategory->standard->id . ') - remove module assigned to criteria(' . $apprenticeshipissue->id . ') from edit programme screen.'
			);
		}

		// Delete workflow
		ApprenticeshipIssuesLearningModules::deleteModuleUserWorkflow($args['issue'],[$args['module']],false,$apprenticeshipissue->issuecategory->standard->id);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// remove multiple module assigned to issue
	$group->delete("/{issue:[0-9]+}/modules/{module_ids}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$module_ids = json_decode($args['module_ids'], TRUE);
		foreach ($module_ids as $key => $module_id) {
			\Models\ApprenticeshipIssuesLearningModules
				::where("apprenticeship_issues_id", "=", $args["issue"])
				->where("learning_modules_id", $module_id)
				->delete()
			;

			// Get all users assigned to standard and detach module from them.
			$apprenticeshipissue = \Models\ApprenticeshipIssues
				::where('id', $args["issue"])
				->with('issuecategory.standard.users')
				->first()
			;

			$detach_module_ids = \APP\Learning::getAllModuleIds([$module_id]);

			foreach ($apprenticeshipissue->issuecategory->standard->users as $key => $value) {
				$user = \Models\User::find($value->id);
				\Models\UserLearningModule::unlinkResources($user->id, $detach_module_ids, 'standard - remove multiple module assigned to issue - ' . $apprenticeshipissue->id);
			}
		}


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// remove module assigned to issue for individual user
	// ALSO, this request will allso look into "apprenticeship_issues_evidence" table for match and then remove it too.

	$group->delete("/{issue:[0-9]+}/module/{module:[0-9]+}/user/{user:[0-9]+}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($args["user"])
		) {
			$user = \Models\User::find($args['user']);

			// remove specified link from user modules table (these modules are shared across users)
			\Models\ApprenticeshipIssuesUserLearningModules
				::where("apprenticeship_issues_id", "=", $args["issue"])
				->where("learning_modules_id", "=", $args["module"])
				->where('user_id', '=', $args["user"])
				->delete();;

			// remove specified link from user evidence table (these are user specific modules only)
			\Models\ApprenticeshipIssuesEvidence
				::where("apprenticeship_issues_id", "=", $args["issue"])
				->where("learning_modules_id", "=", $args["module"])
				->where('user_id', '=', $args["user"])
				->delete();;

			//Both of these tables are now very similar, I should bring them together to make everything less confusing.

			// check if there are more links, if none, detach module from user
			$usermodules = \Models\ApprenticeshipIssuesUserLearningModules
				::where("learning_modules_id", "=", $args["module"])
				->where('user_id', '=', $args["user"])
				->get();
			// check if there is evidence resource assigned to user
			$evidencemodules = \Models\ApprenticeshipIssuesEvidence::where("learning_modules_id", "=", $args["module"])
				->where('user_id', '=', $args["user"])
				->get();

			// If there are no more issues having this module, detach it from user.
			if (count($usermodules) == 0 && count($evidencemodules) == 0) {
				\Models\UserLearningModule::unlinkResources($user->id, $args["module"], 'programme - remove module assigned to issue for individual user - ' . $args["issue"]);
			}

		}
		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// Remove link in bulk from ApprenticeshipIssuesUserLearningModules for specific user
	$group->post("/deleteissueresource/{user:[0-9]+}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		\Models\ApprenticeshipIssuesUserLearningModules::deleteEntry($data['issues'], $data['resources'], $args["user"]);
        CreditUsage::restoreCredit($data['resources'],[$args['user']]);
		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// Get issue with its category???!!
	$group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$apprenticeshipissues = \Models\ApprenticeshipIssues
			::with(["issuecategory" => function ($query) {
				$query
					->where("status", ">", 0);
			}])->find($args["id"]);

		$response->getBody()->write(json_encode($apprenticeshipissues));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));


	// Update issue sort order
	$group->put("/updatesort", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		foreach ($data as $key => $value) {
			$apprenticeshipissues = \Models\ApprenticeshipIssues::where('id', '=', $value['id'])
				->update(["sort" => $value['sort']]);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Disable issue
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$apprenticeshipissues = \Models\ApprenticeshipIssues::find($args["id"]);
		$apprenticeshipissues->status = 0;
		$apprenticeshipissues->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// enable issue
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$apprenticeshipissues = \Models\ApprenticeshipIssues::find($args["id"]);
		$apprenticeshipissues->status = 1;
		$apprenticeshipissues->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// Add issue/criteria
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$apprenticeshipissues = new \Models\ApprenticeshipIssues;

		if (isset($data["name"])) {
			$apprenticeshipissues->name = $data["name"];
		}

		if (isset($data["week"])) {
			$apprenticeshipissues->week = $data["week"];
		}

		if (isset($data["guidelines"])) {
			$apprenticeshipissues->guidelines = $data["guidelines"];
		}

		if (isset($data["issue_category_id"])) {
			$apprenticeshipissues->issue_category_id = $data["issue_category_id"];
		}

		if (isset($data["parent_id"])) {
			$apprenticeshipissues->parent_id = $data["parent_id"];
		}

		if(isset($data["apprenticeship_type"])){
			if($data["apprenticeship_type"]=="Skills Monitoring"){
				$apprenticeshipISSUECategories = ApprenticeshipIssueCategories::with('Standard')->where('id',$data['issue_category_id'])->first();
				$apprenticeshipissues->start_day=0;
        		$apprenticeshipissues->end_day=30*$apprenticeshipISSUECategories->Standard->repetition_period;
			}
		}

		$apprenticeshipissues->status = 1;
		$apprenticeshipissues->save();

		// Scrap, as for now: https://bitbucket.org/emilrw/scormdata/issues/669/16-subcriteria
		/*
		// Add 2 evidence's resources against this issue.
		// "Evidence Learning #1 of CRITERIA" and "Evidence Learning #2 of CRITERIA"
		// Use "guidelines" as description for these evidence's
		if ($apprenticeshipissues->parent_id > 0 ) {
			$sub_criteria_name  = $apprenticeshipissues->name;
			if (count($apprenticeshipissues->name) > 35) {
				$sub_criteria_name =  substr($apprenticeshipissues->name, 0, 35) . '...';
			}


			for ($x = 0; $x <= 1; $x++) {
				$evidence = new \Models\LearningModule;
				$evidence->id = \APP\Course::getFreeCourseId($this->get('settings')["CourseIdStart"]);
				$evidence->name = 'Evidence Learning #' . ($x + 1) . ' of "' . $sub_criteria_name . '"';
				$evidence->category_id = null;
				$evidence->description = $apprenticeshipissues->guidelines;
				$evidence->type_id = 7;
				$evidence->created_by = \APP\Auth::getUserId();
				$evidence->guideline = true;
				$evidence->status = true;
				$evidence->save();

				if ($apprenticeshipissues->id && $evidence->id) {
					\Models\ApprenticeshipIssues::addModuleToIssue($apprenticeshipissues->id, $evidence->id);
				}
			}
		}
		*/

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));


	// Update issue name/category
	$group->put("/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$apprenticeshipissues = \Models\ApprenticeshipIssues::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$apprenticeshipissues->name = $data["name"];
		}

		if (isset($data["guidelines"])) {
			$apprenticeshipissues->guidelines = $data["guidelines"];
		}
		if (isset($data["week"])) {
			$apprenticeshipissues->week = $data["week"];
		}


		if (isset($data["issue_category_id"])) {
			$apprenticeshipissues->issue_category_id = $data["issue_category_id"];
		}

		$apprenticeshipissues->save();


		// Scrap: https://bitbucket.org/emilrw/scormdata/issues/669/16-subcriteria
		/*
		// If this is sub-criteria, update guidelines text as description for 2 evidences.
		if ($apprenticeshipissues->parent_id > 0 ) {
			// Find issue/module relationship and look for modules that has "guideline" as true.
			$modules = \Models\LearningModule
				::where('guideline', true)
				->where('status', true)
				->whereIn('id',
					\Models\ApprenticeshipIssuesLearningModules
						::select('learning_modules_id')
						->where('apprenticeship_issues_id', $apprenticeshipissues->id)
						->get()
				)
				->get()
			;

			foreach ($modules as $key => $module) {
				$evidence = \Models\LearningModule::find($module->id);
				$evidence->description = $apprenticeshipissues->guidelines;
				$evidence->save();
			}
		}
		*/

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


	// Get list of all issues, ALL issues.
	$group->get('/all', function (Request $request, Response $response) {
		$data = \Models\ApprenticeshipIssues::where("status", ">", 0)->get();

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Download list of issues, filters applied, in excel format.
	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\ApprenticeshipIssues::with(['issuecategory' => function ($query) {
			$query
				->with(["standard" => function ($query) {

				}]);
		}]);


		// I could not find any way to filter out standard issues based on standard_id that is
		$query->leftjoin('apprenticeship_issue_categories', function ($join) use ($args) {
			$join
				->on('apprenticeship_issue_categories.id', '=', 'apprenticeship_issues.issue_category_id');
		});

		$query->leftjoin('apprenticeship_standards', function ($join) use ($args) {
			$join
				->on('apprenticeship_standards.id', '=', 'apprenticeship_issue_categories.standard_id');
		});

		$query->select(
			'apprenticeship_issues.*',
			'apprenticeship_issue_categories.id as category_id',
			'apprenticeship_standards.id as standard_id'
		);

		if (isset($params["search"]) && is_array($params["search"])) {
			foreach ($params["search"] as $field => $value) {

				// as I have multiple tables joined, I need to specify what table to use for search, in case of specific request for standard_id, look for "apprenticeship_standards.id"
				$master_table = 'apprenticeship_issues.';
				if ($field == 'standard_id') {
					$master_table = 'apprenticeship_standards.';
					$field = 'id';
				}

				if (is_numeric($value) && $field != 'name') { // changed "is_int" to "is_numeric" as angular smart table passes numbers as strings, another problem is when you are free text searching and entering number, tragic.
					// So, adding custom logic to skip integer look-up if field is name, something to ponder on in future.
					// Or replace ng-repeat for options to ng-options in selectbox, then check is "integer:" is part of value and use that logic.
					$query->where($master_table . $field, "=", $value);
				} else {
					$query->where($master_table . $field, "LIKE", "%{$value}%");
				}
			}
			unset($params["search"]);
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Apprenticeship Issue Category Name" => "name",
				"Standard Name" => "apprenticeship_standards.name",
			];


			$download_file_name = uniqid("apprenticeshipissues.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Get user reviews, used if apprenticeship is enabled
	$group->get('/user/review/{user_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$user = \Models\User
			::findOrFail($args['user_id'])
		;

		$reviews = [];
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($user->id) ||
			\APP\Auth::accessAllLearners() ||
			$args['user_id'] == \APP\Auth::getUserId()
		) {
			$reviews = \Models\ManagerReview
				::where('user_id', '=', $args['user_id'])
				->where('status', '=', 1)
				->with(['visitor' => function ($query) use ($args) {
					$query
						->select('id', 'fname', 'lname');
				}])
				->with(['comments' => function ($query) {
					$query
						->with(['commentedby' => function ($query) {
							$query
								->select('id', 'fname', 'lname');
						}]);
				}]);

			// If learner is requesting this, then he get specific reviews added into calendar.
			// Training, Development, Progress Reviews will show up in learner's calendar.
			if (
				$args['user_id'] == \APP\Auth::getUserId() &&
				!\APP\Auth::isAdminInterface()
			) {
				$reviews = $reviews
					->where(function ($query) {
						$query
							->where('visit_type', '!=', 'QA Report');
					})
					->select(
						'manager_reviews.id',
						'manager_reviews.visit_type',
						'manager_reviews.date',
						DB::raw("DATE_FORMAT(manager_reviews.date,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS date_uk"),
						'manager_reviews.time_start',
						'manager_reviews.time_end',
						'manager_reviews.standard_id',
						'manager_reviews.visitor_id',
						'manager_reviews.completion_status',
						'manager_reviews.report_file',
						'manager_reviews.off_the_job_training'
					)
					->with(['programme' => function ($query) use ($args) {
						$query
							->select('id', 'name');
					}]);
			} else {
				$reviews = $reviews
					->withCount(['Favorite as qa_favorite' => function ($query) use ($args) {
						$query
							->where('type', 'review-qa')
							->where('user_id', \APP\Auth::getUserId())
							->where('status', true);
					}]);
			}

			$reviews = $reviews
				->get();
		}

		$response->getBody()->write(json_encode($reviews));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-calendar'], 'select'));

	// Add new user review
	$group->post('/user/review', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$user = \Models\User::find($params['user_id']);
		if (!$user) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		if (
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($user->id) ||
				\APP\Auth::accessAllLearners()
			) &&
			(
				(
					$params['visit_type'] == 'QA Report' &&
					\APP\Auth::isQa()
				) ||
				(
					$params['visit_type'] != 'QA Report'
				)
			)
		) {
			$review = new \Models\ManagerReview;
			$review->visit_type = $params['visit_type'];
			$review->user_id = $params['user_id'];
			$review->visitor_id = $params['visitor_id'];
			$review->created_by = \APP\Auth::getUserId();
			$review->date = \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']);
			$review->standard_id = isset($params['standard_id']) ? $params['standard_id'] : null;
			$review->time_start = isset($params['time_end']) ? \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['time_start']) : null;
			$review->time_end = isset($params['time_end']) ? \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['time_end']) : null;
			$review->travel_time = isset($params['travel_time']) ? $params['travel_time'] : '';
			$review->expenses = isset($params['expenses']) ? $params['expenses'] : '';
			$review->off_the_job_training = isset($params['off_the_job_training']) ? true : false;

			if (isset($params['completion_status'])) {
				if (
					!(
						$review->visit_type == 'QA Report' &&
						$params['completion_status'] == 'Completed'
					) ||
					\APP\Auth::isQa()
				) {
					$review->completion_status = $params['completion_status'];
					$review->checked_by = \APP\Auth::getUserId();
				}
			}


			if (isset($_FILES['toUpload'])) {
				$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSReviewsPath"]);
				$report_file = new \Upload\File('toUpload', $storage);
				$report_file->setName(\APP\Tools::safeName($_FILES['toUpload']['name']) . '_' . uniqid());
				$fileSizeValidation = new \Upload\Validation\Size('800M');
				$fileTypeValidation = new \Upload\Validation\Mimetype(\APP\Tools::documentMime());
				$fileTypeValidation->setMessage("Invalid file type: '" . $report_file->getMimetype() . "'!");
				$report_file->addValidations([
					$fileTypeValidation,
					$fileSizeValidation
				]);

				try {
					$report_file->upload();
					$review->report_file = $report_file->getNameWithExtension();
				} catch (\Upload\Exception\UploadException $e) {
					$errors = $report_file->getErrors();
					return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
				}

			}
			$review->save();

			/*
			V 1
			// Send email to user that review is added.
			if (
				$review->visit_type != 'QA Report' &&
				!\APP\Auth::isLearner()
			) {
				\APP\Email::sendEmailReminder([$user->id], false, $review->id);
			}
			*/

			// V2
			// Send user email notification if following reviews are added
			\APP\Email::sendReviewsStatusEmail($user->id, $review);

			// If completed review is added, update last contact date to one of meetings dates
			if (
				isset($params['completion_status']) &&
				$params['completion_status'] == 'Completed' &&
				isset($params['date']) &&
				$params['date'] &&
				$user->last_contact_date < \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']) &&
				\Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']) <= \Carbon\Carbon::now()
			) {
				$user->last_contact_date = \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']);
				$user->save();

				// If logged in user is not learner, update contact date too
				if (!\APP\Auth::isLearner()) {
					$manager_update_date = \Models\User::find(\APP\Auth::getUserId());
					$manager_update_date->last_contact_date = \Carbon\Carbon::now();
					$manager_update_date->save();
				}
			}

			if (isset($params['comment']) && $params['comment'] && $review->id) {
				$comment = new \Models\ManagerReviewsComment;
				$comment->review_id = $review->id;
				$comment->comment = $params['comment'];
				$comment->commented_by = \APP\Auth::getUserId();
				$comment->save();
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return
			$response
				->withHeader('Content-Type', 'application/json')//->write(json_encode($reviews))
			;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'misc-permissions-reviews'], 'insert'));

	// Update user review
	$group->post('/user/review/{review_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$user = \Models\User
			::findOrFail($params['user_id']);
		$review = \Models\ManagerReview
			::find($args['review_id']);
		if (
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($user->id) ||
				\APP\Auth::accessAllLearners()
			) &&
			(
				(
					$review->visit_type == 'QA Report' &&
					\APP\Auth::isQa()
				) ||
				(
					$review->visit_type != 'QA Report'
				)
			)
		) {

			$send_learner_notification = false;

			$review->visit_type = $params['visit_type'];
			$review->visitor_id = $params['visitor_id'];
			$review->date = \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']);
			$review->updated_by = \APP\Auth::getUserId();
			$review->standard_id = isset($params['standard_id']) ? $params['standard_id'] : null;
			$review->time_start = isset($params['time_start']) && $params['time_start'] ? \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['time_start']) : null;
			$review->time_end = isset($params['time_end']) && $params['time_end'] ? \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['time_end']) : null;
			$review->travel_time = isset($params['travel_time']) ? $params['travel_time'] : '';
			$review->expenses = isset($params['expenses']) ? $params['expenses'] : '';
			$review->off_the_job_training = isset($params['off_the_job_training']) ? true : false;


			if (isset($params['completion_status'])) {
				if (
					!(
						$review->visit_type == 'QA Report' &&
						$params['completion_status'] == 'Completed'
					) ||
					\APP\Auth::isQa()
				) {

					if (
						$review->completion_status != $params['completion_status'] &&
						$params['completion_status'] == 'Completed'
					) {
						$send_learner_notification = true;
					}

					$review->completion_status = $params['completion_status'];
					$review->checked_by = \APP\Auth::getUserId();
				}
			}

			if (isset($_FILES['toUpload'])) {
				$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSReviewsPath"]);
				$report_file = new \Upload\File('toUpload', $storage);
				$report_file->setName(\APP\Tools::safeName($_FILES['toUpload']['name']) . '_' . uniqid());
				$fileSizeValidation = new \Upload\Validation\Size('800M');
				$fileTypeValidation = new \Upload\Validation\Mimetype(\APP\Tools::documentMime());
				$fileTypeValidation->setMessage("Invalid file type: '" . $report_file->getMimetype() . "'!");
				$report_file->addValidations([
					$fileTypeValidation,
					$fileSizeValidation
				]);

				try {
					$report_file->upload();
					$review->report_file = $report_file->getNameWithExtension();
				} catch (\Upload\Exception\UploadException $e) {
					$errors = $report_file->getErrors();
					return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
				}

			}
			$review->save();
			// Send user that review is completed!
			if ($send_learner_notification) {
				\APP\Email::sendReviewsStatusEmail($user->id, $review);
			}

			if (
				$params['completion_status'] == 'Completed' &&
				isset($params['date']) &&
				$params['date'] &&
				$user->last_contact_date < \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']) &&
				\Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']) <= \Carbon\Carbon::now()
			) {
				$user->last_contact_date = \Carbon\Carbon::createFromFormat('D M d Y H:i:s T +', $params['date']);
				$user->save();
			}

			if (isset($params['comment']) && $params['comment'] && $review->id) {
				$comment = new \Models\ManagerReviewsComment;
				$comment->review_id = $review->id;
				$comment->comment = $params['comment'];
				$comment->commented_by = \APP\Auth::getUserId();
				$comment->save();
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return
			$response
			//->withHeader('Content-Type', 'application/json')
			//->write(json_encode($reviews))
			;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'misc-permissions-reviews'], 'update'));

	// Disable user review
	$group->delete('/user/review/{review_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$review = \Models\ManagerReview
			::find($args['review_id']);
		$user = \Models\User::find($review->user_id);
		if (
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($user->id) ||
				\APP\Auth::accessAllLearners()
			) &&
			(
				(
					$review->visit_type == 'QA Report' &&
					\APP\Auth::isQa()
				) ||
				(
					$review->visit_type != 'QA Report'
				)
			)
		) {
			$review->status = false;
			$review->save();
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'misc-permissions-reviews'], 'disable'));

	// Get reviews file name if you are users admin
	$group->get('/reviewfile/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$review = \Models\ManagerReview
			::find($args['id']);

		$user = \Models\User::find($review->user_id);
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($user->id) ||
			\APP\Auth::accessAllLearners() ||
			\APP\Auth::getUserId() == $review->user_id
		) {

		// Define the file path
		$file = $this->get('settings')['LMSReviewsPath'] . $review->report_file;

		// Check if the file exists to avoid errors
		if (!file_exists($file)) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		// Open the file in read-only binary mode
		$fh = fopen($file, 'rb');

		// Create a stream instance for the response body
		$stream = new Stream($fh);

		// Return the response with appropriate headers
		$response
			->withBody($stream);

		return $response
			->withHeader('Content-Type', 'application/octet-stream')
			->withHeader('Content-Description', 'File Transfer')
			->withHeader('Content-Transfer-Encoding', 'binary')
			->withHeader('Content-Disposition', 'attachment; filename="' . basename($file) . '"')
			->withHeader('Expires', '0')
			->withHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
			->withHeader('Pragma', 'public')
		;

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	// Add new user review comment
	$group->post('/user/review/comment', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$review = \Models\ManagerReview
			::findOrFail($params['review_id']);
		$user = \APP\Auth::getUser();
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($review->user_id) ||
			\APP\Auth::accessAllLearners()
		) {

			// if qa review and not qa role leaves comment, if status is not completed, set in progress.
			if (
				$review->visit_type == 'QA Report' &&
				!\APP\Auth::isQa() &&
				$review->completion_status != 'Completed'
			) {

				// Send e-mail to QA that status changes to progress
				if ($review->completion_status != 'In Progress') {
					$template = \Models\EmailTemplate
						::where('name', 'QA Report ready for Quality Approval')
						->where('status', true)
						->first();
					if ($template && $template->id) {
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->recipients = [$review->created_by];
						$email_queue->from = \APP\Auth::getUserId();
						$email_queue->custom_variables = json_encode([
							'QUALITY_REPORT' => $review->visit_type,
							'LEARNER_NAME' => $review->user->fname . ' ' . $review->user->lname,
							'COACH_NAME' => $user->fname . ' ' . $user->lname,
						]);
						$email_queue->save();
					}
				}

				$review->completion_status = 'In Progress';
				$review->save();
			}
			$comment = new \Models\ManagerReviewsComment;
			$comment->review_id = $params['review_id'];
			$comment->comment = $params['comment'];
			$comment->commented_by = \APP\Auth::getUserId();
			$comment->save();
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'misc-permissions-reviews'], 'insert'));

	// Update issue "visible_resource" property that indicated show/hide resources untill start date.
	$group->patch('/{issue_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$issue = \Models\ApprenticeshipIssues
			::findOrFail($args['issue_id']);
		$issue->visible_resource = $data['visible_resource'];
		$issue->save();

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));
	// Update issue "hide_learner" property that indicated show/hide resources for learner.
	$group->patch('/hide_learner/{issue_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$issue = \Models\ApprenticeshipIssues
			::findOrFail($args['issue_id']);
		$issue->hide_learner = $data['hide_learner'];
		$issue->save();

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

// Update issue "hide_progressbar from learner" property that indicated show/hide resources for learner.
	$group->patch('/hide_progressbar/{issue_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$issue = \Models\ApprenticeshipIssues
			::findOrFail($args['issue_id']);
		$issue->hide_progressbar = $data['hide_progressbar'];
		$issue->save();

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

// Update issue "hide_progressbar for OUTCOME: ISSUE CATEGORIES from learner" property that indicated show/hide resources for learner.
	$group->patch('/hide_progressbar_outcome/{issue_category_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$issue = \Models\ApprenticeshipIssueCategories
			::findOrFail($args['issue_category_id']);
		$issue->hide_progressbar = $data['hide_progressbar'];
		$issue->save();

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Disable outcome.
	$group->patch('/disable_outcome/{issue_category_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$outcome = \Models\ApprenticeshipIssueCategories::find($args['issue_category_id']);
		if ($outcome) {
			$outcome->disabled = $data['disable_status'];
			$outcome->save();

			$users = \Models\ApprenticeshipStandardUser
				::where('standard_id', $outcome->standard_id)
				->get()
			;

			foreach ($users as $key => $user) {
				\Models\ApprenticeshipStandardUser::disableOutcome($user->user_id, $outcome->id, $outcome->standard_id,  $data['disable_status'] ? 1 : 0);
			}
		}


		if (!$data['disable_status']) {
			// Find all ApprenticeshipIssuesUserDisabled entries that are under this outcome and mark them for deletion.
			// When assignToStandard is executed, re-enable those issues.
			/*
			$ApprenticeshipIssuesUserDisabled = \Models\ApprenticeshipIssuesUserDisabled
				::wherIn('apprenticeship_issues_id',
					\Models\ApprenticeshipIssues
						::select('id')
						->where('issue_category_id', $outcome->id)
						->get()
				)
				->get()
			;
			*/

			//\Models\ApprenticeshipStandardUser::disableOutcome($user_id, $outcome->id, $outcome->standard_id,  1);
		} else {
			//\Models\ApprenticeshipStandardUser::disableOutcome($user_id, $issue_category_id, $standard_id,  1);
		}

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


	// Update custom start/end day for specific module in specific issue.
	$group->put("/module/work-window", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		if (isset($data['id'])) {
			$pivot = \Models\ApprenticeshipIssuesLearningModules
				::find($data['id']);
			if ($pivot) {
				if (isset($data['custom_work_window'])) {
					$pivot->custom_work_window = $data['custom_work_window'];
				}

				if (
					isset($data['end_day']) &&
					isset($data['start_day'])
				) {
					$pivot->end_day = $data['end_day'];
					$pivot->start_day = $data['start_day'];
				}

				$pivot->save();

				// Need to update due_at and grace_at for this module.

			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	$group->post("/reset_duration_updated_flag/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$query = \Models\ApprenticeshipStandard::find($args['id']);

		if ($query) {
			\Models\ApprenticeshipStandard::updateDurationUpdatedFlag($query->id, 0);
			$response->getBody()->write(json_encode(['message' => 'Duration updated flag reset successfully.']));
			return $response->withStatus(200)->withHeader('Content-Type', 'application/json');
		} else {
			$response->getBody()->write(json_encode(['error' => 'ApprenticeshipStandard not found.']));
			return $response->withStatus(404)->withHeader('Content-Type', 'application/json');
		}
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	$group->get('/cron_status', function (Request $request, Response $response) {
		$cron = \Models\Cron::where('function', 'regenerateProgrammeCompletionTimes')->first();
		$status = $cron ? $cron->status : 0;

		$response->getBody()->write(json_encode(['status' => $status]));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));
});
