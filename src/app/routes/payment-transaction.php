<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/payment-transaction",  function ($group) {

	$group->post('/download', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$query = \Models\UserPaymentTransaction
			::with(['PaidFor' => function ($query) {
				$query->select('id', 'fname', 'lname', 'email');
			}])
			->with(['PaidBy' => function ($query) {
				$query->select('id', 'fname', 'lname', 'email');
			}])
			->leftJoin('schedules', function($join) {
				$join->on('schedules.id', '=', 'user_payment_transactions.type_id')
					->where('user_payment_transactions.type_reference_table', '=', 'schedules');
			})
			->leftJoin('learning_modules', function($join) {
				$join->on('learning_modules.id', '=', 'user_payment_transactions.type_id')
					->where('user_payment_transactions.type_reference_table', '=', 'learning_modules');
			})
			->leftJoin('apprenticeship_standards', function($join) {
				$join->on('apprenticeship_standards.id', '=', 'user_payment_transactions.type_id')
					->where('user_payment_transactions.type_reference_table', '=', 'apprenticeship_standards');
			})
			->select('user_payment_transactions.*',
				DB::raw("CASE
					WHEN user_payment_transactions.type_reference_table = 'schedules' THEN schedules.name
					WHEN user_payment_transactions.type_reference_table = 'learning_modules' THEN learning_modules.name
					WHEN user_payment_transactions.type_reference_table = 'apprenticeship_standards' THEN apprenticeship_standards.name
					ELSE NULL
				END AS item_name"));

		if (isset($params["created_at"]) && $params["created_at"]) {
			$created_at = json_decode($params["created_at"], true);
			if (isset($created_at['period_from']) && $created_at['period_from']) {
				$created_at_from = \Carbon\Carbon::parse($created_at['period_from'])->startOfDay();
				$query->where("user_payment_transactions.created_at", ">=", $created_at_from);
			}
			if (isset($created_at['period_to']) && $created_at['period_to']) {
				$created_at_to = \Carbon\Carbon::parse($created_at['period_to'])->endOfDay();
				$query->where("user_payment_transactions.created_at", "<=", $created_at_to);
			}
		}

		$transactions = $query->get();

		$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
		$sheet = $spreadsheet->getActiveSheet();

		// Set headers
		$sheet->setCellValue('A1', 'ID');
		$sheet->setCellValue('B1', 'Paid For');
		$sheet->setCellValue('C1', 'Paid By');
		$sheet->setCellValue('D1', 'Type');
		$sheet->setCellValue('E1', 'Linked ID');
		$sheet->setCellValue('F1', 'Item Name');
		$sheet->setCellValue('G1', 'Amount');
		$sheet->setCellValue('H1', 'Response Code');
		$sheet->setCellValue('I1', 'Reference');
		$sheet->setCellValue('J1', 'Created At');

		// Add data
		$row = 2;
		foreach ($transactions as $transaction) {
			$sheet->setCellValue('A' . $row, $transaction->id ?? '');
			$sheet->setCellValue('B' . $row, $transaction->PaidFor ? ($transaction->PaidFor->fname . ' ' . $transaction->PaidFor->lname) : '');
			$sheet->setCellValue('C' . $row, $transaction->PaidBy ? ($transaction->PaidBy->fname . ' ' . $transaction->PaidBy->lname) : '');
			$sheet->setCellValue('D' . $row, $transaction->type ?? '');
			$sheet->setCellValue('E' . $row, $transaction->type_id ?? '');
			$sheet->setCellValue('F' . $row, isset($transaction->item_name) ? $transaction->item_name : '');
			$sheet->setCellValue('G' . $row, $transaction->payment_amount ?? '');
			$sheet->setCellValue('H' . $row, $transaction->response_code ?? '');
			$sheet->setCellValue('I' . $row, $transaction->scpReference ?? '');
			$sheet->setCellValue('J' . $row, $transaction->created_at ?? '');
			$row++;
		}

		// Apply auto-filter to header row - this creates the dropdown filters
		if ($row > 2) { // Only apply if we have data
			$sheet->setAutoFilter('A1:J' . ($row - 1));
		}

		$writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

		$response = $response->withHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		$response = $response->withHeader('Content-Disposition', 'attachment; filename="payment-transactions.xlsx"');

		ob_start();
		$writer->save('php://output');
		$content = ob_get_clean();

		$response->getBody()->write($content);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-payment-transactions', 'select'));

   $group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$query = \Models\UserPaymentTransaction
			::with(['PaidFor' => function ($query) {
				$query
					->select('id', 'fname', 'lname', 'email')
				;
			}])
			->with(['PaidBy' => function ($query) {
				$query
					->select('id', 'fname', 'lname', 'email')
				;
			}])
			->leftJoin('schedules', function($join) {
				$join->on('schedules.id', '=', 'user_payment_transactions.type_id')
					->where('user_payment_transactions.type_reference_table', '=', 'schedules');
			})
			->leftJoin('learning_modules', function($join) {
				$join->on('learning_modules.id', '=', 'user_payment_transactions.type_id')
					->where('user_payment_transactions.type_reference_table', '=', 'learning_modules');
			})
			->leftJoin('apprenticeship_standards', function($join) {
				$join->on('apprenticeship_standards.id', '=', 'user_payment_transactions.type_id')
					->where('user_payment_transactions.type_reference_table', '=', 'apprenticeship_standards');
			})
			->select('user_payment_transactions.*',
				DB::raw("CASE
					WHEN user_payment_transactions.type_reference_table = 'schedules' THEN schedules.name
					WHEN user_payment_transactions.type_reference_table = 'learning_modules' THEN learning_modules.name
					WHEN user_payment_transactions.type_reference_table = 'apprenticeship_standards' THEN apprenticeship_standards.name
					ELSE NULL
				END AS item_name"))
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		// Handle item_name search specially since it's a computed field
		if (isset($params["search"]["item_name"]) && !empty($params["search"]["item_name"])) {
			$item_name_search = $params["search"]["item_name"];
			$search_pattern = \APP\SmartTable::wildcardPosition($item_name_search);

			$query->where(function($q) use ($search_pattern) {
				$q->where('schedules.name', 'LIKE', $search_pattern)
				  ->orWhere('learning_modules.name', 'LIKE', $search_pattern)
				  ->orWhere('apprenticeship_standards.name', 'LIKE', $search_pattern);
			});

			unset($params["search"]["item_name"]);
		}

		// Created at filter for learning results
		if (
			isset($params["search"]["created_at"]) &&
			$params["search"]["created_at"]
		) {
			$params["search"]["created_at"] = json_decode($params["search"]["created_at"], true);
			if (
				isset($params["search"]["created_at"]['period_from']) &&
				$params["search"]["created_at"]['period_from']
			) {
				$created_at_from = \Carbon\Carbon::parse($params["search"]["created_at"]['period_from'])->startOfDay();
				$query
					->where("user_payment_transactions.created_at", ">=", $created_at_from)
				;
			}
			if (
				isset($params["search"]["created_at"]['period_to']) &&
				$params["search"]["created_at"]['period_to']
			) {
				$created_at_to = \Carbon\Carbon::parse($params["search"]["created_at"]['period_to'])->endOfDay();
				$query
					->where("user_payment_transactions.created_at", "<=", $created_at_to)
				;
			}
			unset($params["search"]["created_at"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-payment-transactions', 'select'));
});