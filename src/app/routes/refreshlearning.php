<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;


$app->group("/refreshlearning",  function ($group) {

	$group->post('/refresh', function (Request $request, Response $response) {

		$data = $request->getParsedBody();

		$module_ids = isset($data["module_ids"]) ? $data["module_ids"] : [];
		$module_ids = \APP\Learning::getAllModuleIds($module_ids);
		$user_ids = isset($data["user_ids"]) ? $data["user_ids"] : [];

		\APP\Refresh::refreshResults(true, $user_ids, $module_ids, false, false, 'Manual force refresh, discontinued - 1');

		return $response;
	});

	$group->post('/users/list', function (Request $request, Response $response) {

		$params = $request->getParsedBody();
		if (isset($params["search"]) && isset($params["search"]["module_ids"]))
		{
			$module_ids = $params["search"]["module_ids"];
		}
		else
		{
			$module_ids = [];
		}
		$module_ids[] = 0;
		unset($params["search"]["module_ids"]);

		$query = \Models\User::selectRaw("users.*")
			->where("users.status", ">", "0")
			->whereIn("id", function($query) use($module_ids) {
				$query
					->select("user_id")
					->from("user_learning_modules")
					->whereIn("learning_module_id", $module_ids)
					->whereRaw('user_learning_modules.deleted_at is null')
				;
			})
			->with(["modules" => function($query) use($module_ids) {
				$query
					->select(["learning_modules.id", "learning_modules.name"])
					->whereIn("learning_modules.id", $module_ids);
			;
		}])
		;

		if (isset($params["search"]) && isset($params["search"]["assigned"]))
		{
			$query->leftjoin("user_learning_modules", function($join) use ($module_ids, $params) {
				$join->on("user_learning_modules.user_id", "=", "users.id");
				$join
					->whereIn("user_learning_modules.learning_module_id", $module_ids)
					->whereRaw('user_learning_modules.deleted_at is null')
				;
			});

				if ($params["search"]["assigned"] == "1")
				{
					$query->havingRaw("MAX(user_learning_modules.learning_module_id) IS NOT NULL");
				}
				if ($params["search"]["assigned"] == "0")
				{
					$query->havingRaw("MAX(user_learning_modules.learning_module_id) IS NULL");
				}

				$query->groupBy("users.id");
				unset($params["search"]["assigned"]);
		}



		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	});

	$group->post('/list', function (Request $request, Response $response) {


		$params = $request->getParsedBody();

		$query = \Models\LearningModule::select("learning_modules.*")
			->with(["users" => function ($query) {
				$query->select("users.id");
			}])
			->where("status", "=", 1)
			->where("refresh_period", "<>", 0)
		;


		$p = \APP\SmartTable::searchPaginate($params, $query);
		$json = $p->toJson();

		return
			$response
				->withHeader('Content-Type', 'application/json')
				->write($json);

	});

})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-refresh-learning', 'update'));