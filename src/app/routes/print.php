<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/print",  function ($group) {
	$group->GET('/{print_template:[\/a-z0-9-]*}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$vars = \APP\Templates::getVariables($this->get('settings'));
		$vars["print_template"] = $args['print_template'] . '-print.html';
		return $this->get('view')->render($response, 'html/print-base.html', $vars);
	})->add(\APP\Auth::getStructureAccessCheck(['review', 'trainee-modules'], 'select'));
});