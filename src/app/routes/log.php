<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/log",  function ($group) {

   $group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$query = \Models\Log
			::with(['User' => function ($query) {
				$query
					->select('id', 'fname', 'lname', 'email');
			}])
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["search"]["fname_lname"])) {
			$query = $query
				->whereHas('user', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["fname_lname"] . "%'")
					;
				})
			;
			unset($params["search"]["fname_lname"]);
		}


		// Created at filter for learning results
		if (
			isset($params["search"]["created_at"]) &&
			$params["search"]["created_at"]
		) {
			$params["search"]["created_at"] = json_decode($params["search"]["created_at"], true);
			if (
				isset($params["search"]["created_at"]['period_from']) &&
				$params["search"]["created_at"]['period_from']
			) {
				$created_at_from = \Carbon\Carbon::parse($params["search"]["created_at"]['period_from'])->startOfDay();
				$query
					->where("logs.created_at", ">=", $created_at_from)
				;
			}
			if (
				isset($params["search"]["created_at"]['period_to']) &&
				$params["search"]["created_at"]['period_to']
			) {
				$created_at_to = \Carbon\Carbon::parse($params["search"]["created_at"]['period_to'])->endOfDay();
				$query
					->where("logs.created_at", "<=", $created_at_to)
				;
			}
			unset($params["search"]["created_at"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-request-log', 'select'));

	$group->post('/log-js-error', function (Request $request, Response $response, $args) {
		\APP\Auth::checkSession();

		$params = $request->getParsedBody();
		$userId = \APP\Auth::getUserId() && \APP\Auth::getUserId() > 0 ? \APP\Auth::getUserId() : null;

		session_write_close();

		$existing = \Models\JavascriptErrorLog::where('user_id', $userId)
			->where('url', $params['url'] ?? '')
			->where('source', $params['source'] ?? '')
			->where('line', $params['line'] ?? null)
			->where('column', $params['column'] ?? null)
			->where('type', $params['type'] ?? '')
			->where('message', $params['message'] ?? '')
			->where('stack', $params['stack'] ?? '')
			->first();

		if ($existing) {
			$existing->counter += 1;
			$existing->save();
		} else {
			$entry = new \Models\JavascriptErrorLog();
			$entry->user_id = $userId;
			$entry->url = $params['url'] ?? '';
			$entry->source = $params['source'] ?? '';
			$entry->line = $params['line'] ?? null;
			$entry->column = $params['column'] ?? null;
			$entry->type = $params['type'] ?? '';
			$entry->message = $params['message'] ?? '';
			$entry->stack = $params['stack'] ?? '';
			$entry->user_agent = $params['userAgent'] ?? '';
			$entry->ip = \APP\Auth::getIp() ?? '';
			$entry->counter = 1;
			$entry->save();
		}

		return $response;
	});

	$group->post('/log-js-error/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$query = \Models\JavascriptErrorLog
			::with(['User' => function ($query) {
				$query
					->select('id', 'fname', 'lname', 'email');
			}])
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["search"]["fname_lname"])) {
			$query = $query
				->whereHas('user', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["fname_lname"] . "%'")
					;
				})
			;
			unset($params["search"]["fname_lname"]);
		}


		// Created at filter for learning results
		if (
			isset($params["search"]["updated_at"]) &&
			$params["search"]["updated_at"]
		) {
			$params["search"]["updated_at"] = json_decode($params["search"]["updated_at"], true);
			if (
				isset($params["search"]["updated_at"]['period_from']) &&
				$params["search"]["updated_at"]['period_from']
			) {
				$updated_at_from = \Carbon\Carbon::parse($params["search"]["updated_at"]['period_from'])->startOfDay();
				$query
					->where("javascript_error_logs.updated_at", ">=", $updated_at_from)
				;
			}
			if (
				isset($params["search"]["updated_at"]['period_to']) &&
				$params["search"]["updated_at"]['period_to']
			) {
				$updated_at_to = \Carbon\Carbon::parse($params["search"]["updated_at"]['period_to'])->endOfDay();
				$query
					->where("javascript_error_logs.updated_at", "<=", $updated_at_to)
				;
			}
			unset($params["search"]["updated_at"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-javascript-errors', 'select'));


});