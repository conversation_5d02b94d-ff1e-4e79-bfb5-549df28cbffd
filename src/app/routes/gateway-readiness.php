<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/gateway-readiness",  function ($group) use ($app) {
	$group->get('/all', function (Request $request, Response $response) {
		$query = \Models\GatewayReadiness
			::where("status", true)
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->put('/user/update', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		// Get existing entry, if exists!
		$query = \Models\UserGatewayReadiness
			::where('user_id', $data['user_id'])
			->where('link_id', $data['link_id'])
			->where('type', $data['type'])
			->first()
		;

		if ($query) {
			if (empty($data['gateway_readiness_id'])) {
				$query->delete();
			} else {
				$query->gateway_readiness_id = $data['gateway_readiness_id'];
				$query->save();
			}
		} else {
			if (!empty($data['gateway_readiness_id'])) {
				$query = new \Models\UserGatewayReadiness;
				$query->user_id = $data['user_id'];
				$query->link_id = $data['link_id'];
				$query->type = $data['type'];
				$query->gateway_readiness_id = $data['gateway_readiness_id'];
				$query->save();
			}
		}

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));


});
