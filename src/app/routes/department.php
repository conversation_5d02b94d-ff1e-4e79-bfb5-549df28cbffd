<?php

use APP\Form;
use Models\Department;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/department", function($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$department = \Models\Department::find($args["id"]);

		\Models\TableExtension::returnAllFields('departments', $department->id, $department);

		$response->getBody()->write(json_encode($department));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'select'));

	// update department
	$group->post("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$department = \Models\Department::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$department->name = $data["name"];
		}

		if (isset($data["company_id"])) {
			$department->company_id = $data["company_id"];

			if (
				\APP\Auth::isManager() &&
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::accessAllCompanies() &&
				\APP\Auth::getUserCompanyId()
			) {
				$department->company_id = \APP\Auth::getUserCompanyId();
			}

		}

		if (isset($data["email"])) {
			$department->email = $data["email"];
		}

		if (isset($data["address"])) {
			$department->address = $data["address"];
		}

		if (isset($data["phone"])) {
			$department->phone = $data["phone"];
		}

		if (isset($data["parent_id"])) {
			$department->parent_id = $data["parent_id"];
		}

		if (isset($data["discount_percentage"])) {
			$department->discount_percentage = $data["discount_percentage"];
		}

		$department->save();

		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('departments', $department->id, $field_name, $value);
			}
		}
        if(isset($data['custom-field'])){
            Form::saveCustomForm($data['custom-field'],'department',$department->id);
        }
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'select'));

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$department = \Models\Department::find($args["id"]);
		$department->status = 0;
		$department->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$department = \Models\Department::find($args["id"]);
		$department->status = 1;
		$department->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'disable'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$query = \Models\Department
			::where("status", 1)
			->where('parent_id', null)
			->with('Children')
		;

		if (
			!\APP\Auth::isAdmin() &&
			(
				\APP\Auth::isLearner() ||
				!\APP\Auth::accessAllCompanies()
			)
		) {
			$query = $query
				->whereIn('company_id', [\APP\Auth::getUserCompanyId()])
			;
			$query = $query->get();
		} else {
			$query = cache()->remember('department_all', 600, function () use ($query) {
				return $query->get()->toArray();
			});
		}


		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'select'));

	$group->get('/all/{company_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = \Models\Department
			::where("status", 1)
			->where("company_id", $args["company_id"])
			->where('parent_id', null)
			->with('Children')
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$department = new \Models\Department;
		if (isset($data["name"])) {
			$department->name = $data["name"];
		}

		if (isset($data["company_id"])) {
			$department->company_id = $data["company_id"];

			if (
				\APP\Auth::isManager() &&
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::accessAllCompanies() &&
				\APP\Auth::getUserCompanyId()
			) {
				$department->company_id = \APP\Auth::getUserCompanyId();
			}
		}

		if (isset($data["email"])) {
			$department->email = $data["email"];
		}

		if (isset($data["address"])) {
			$department->address = $data["address"];
		}

		if (isset($data["phone"])) {
			$department->phone = $data["phone"];
		}

		if (isset($data["discount_percentage"])) {
			$department->discount_percentage = $data["discount_percentage"];
		}
		if (isset($data["parent_id"])) {
			$department->parent_id = $data["parent_id"];
		}

		$department->status = 1;

		$department->save();
        if(isset($data['custom-field'])){
            Form::saveCustomForm($data['custom-field'],'department',$department->id);
        }
		$response->getBody()->write(json_encode($department->id));
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'insert'));

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Department
			::with(['company' => function($query) {
				$query
					->where("status", 1)
					->select('id', 'name')
				;
			}])
			->where('parent_id', null)
			->with(['Children' => function($query) {
				$query
					->where("status", 1)
				;
			}])
		;


		if (
			\APP\Auth::isManager() &&
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::accessAllCompanies() &&
			\APP\Auth::getUserCompanyId()
		) {
			$query = $query
				->where("departments.company_id", \APP\Auth::getUserCompanyId())
			;
		}


		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$query = \Models\Schedule::countAndConditions($query, $params);

		if (isset($params["search"]) && is_array($params["search"]))
		{
			foreach($params["search"] as $field => $value)
			{
				if (is_int($value))
				{
					$query->where($field, "=", $value);
				}
				else
				{
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"]))
		{
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download")
		{
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
					"ID" => "id",
					"Department Name" => "name",
					"Company Name" => "company.name",
			];


			$download_file_name = uniqid("departments.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		}
		else
		{
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'select'));


	// Assign user to given sub department
	$group->put('/{department_id:[0-9]+}/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		$department = \Models\Department::find($args["department_id"]);
		$user = \Models\User::find($args["user_id"]);

		if (
			!$department ||
			!$user
		) {
			return
				$response
					->withStatus(404)
					->withHeader('Content-Type', 'text/html')
					->write('404 Not Found')
			;
		}

		$entry = new \Models\UserSubDepartment;
		$entry->user_id = $args["user_id"];
		$entry->department_id = $args["department_id"];
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'insert'));

	// Remove sub department link from user.
	$group->delete('/{department_id:[0-9]+}/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		$department = \Models\Department::find($args["department_id"]);
		if ($department) {
			\Models\UserSubDepartment
				::where("user_id", $args["user_id"])
				->where("department_id", $args["department_id"])
				->get()
				->each(function($user_sub_dep) {
					$user_sub_dep
						->delete()
					;
				})
			;
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'disable'));

	$group->get('/sub_departments' ,function(Request $request,Response $response){
		$data = Department::where('status',1)->whereNotNull('parent_id')->get();
		$response->getBody()->write(json_encode($data));
		return $response
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('review', 'select'));

  	/*
		This will return all departments, flat, children will be returned with intendations.
	*/
	$group->get('/flat/all', function (Request $request, Response $response) {
		$data = \Models\Department
			::where("status", 1)
			->select(
				'id',
				'name',
				'company_id',
				'parent_id'
			)
			->where('parent_id', null)
			->with('ChildrenOrdered')
			->orderBy('name')
			->get()
		;

		$flat_data = [];

		foreach ($data as $parent) {
			if (count($parent->ChildrenOrdered) > 0) {
				$new_entry = new \stdClass;
				$new_entry->id = $parent->id;
				$new_entry->name = $parent->name;
				$new_entry->company_id = $parent->company_id;
				$new_entry->parent_id = $parent->parent_id;
				$new_entry->offset = 0;
				$new_entry->disabled = true;
				$flat_data[] = $new_entry;

				$flat_data = \Models\Department::returnFlatChildren($flat_data, $parent->ChildrenOrdered, 10);
			}
		}
		$response->getBody()->write(json_encode($flat_data));

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-departments', 'select'));
});
