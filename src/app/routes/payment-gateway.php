<?php

use APP\Controllers\GlobalPaymentController;
use APP\Controllers\StripeController;
use APP\Controllers\Payment360Controller;
use Models\PaymentRefundConfiguration;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\PurchasePaymentTransaction;
use Models\ScheduleLink;

$app->group("/payment-gateway", function ($group) {

    /*CIVICA PAYMENT METHOD*/
    /*
     * REQUEST payload
   ** Calling Application ID:	Fixed	The Civica Calling Application ID (CPD)	CPD
   ** Payment Total:	Variable	Total of ALL payment lines including VAT in ££.pp	1.00
   ** Network User ID:	Fixed	Use a fixed value of Internet for all payments	Internet
   ** Return URL:	Fixed	The WMB Learning URL the users should be re-directed back to (example uses google.co.uk)	http://google.co.uk
   ** Payment Source Code:	Fixed	This will be fixed value of 01 for all Internet payments	01
   ** Payment_1:	(Below)	Account Reference|Fund Code|Amount|Vat Code|Narrative  (Repeat for payment_2,_3 etc.)	See below 5 fields
   ** Account Reference:	Variable	The Oracle GL Code held against the product on the website - format is CostCentreSubjectiveActivity (if using Activity)	DB06883001 or *************** (if using Activity)
   ** Fund Code:	Fixed	Fixed code of SL for all payments	SL
   ** Amount:	Variable	Amount including VAT in ££.pp	1.00
   ** Vat Code:	Variable	Vat code relating to product (see vat codes below) default to use is 05 zero rated	05
   ** Narrative:	Variable	Description and unique transaction reference (for Audit trail)	Training course - 123456
   ** URL Example: https://www.civicaepay.co.uk/SuffolkPartnershipXMLTest/Paylinkxmlui/Default.aspx?CallingApplicationID=CPD
     &PaymentTotal=1.00&Network_User_ID=Internet&ReturnURL=http://google.co.uk&PaymentSourceCode=01&Payment_1=DB06883001|SL|1.00|05|Training course - 123456

    *RESPONSE Payload
   ** PaymentAuthorisationCode=100000
   ** IncomeManagementReceiptNumber=CPD000000002
   ** OriginatorsReference=**********
   ** CardScheme=MCRD
   ** CardType=C
   ** PaymentAmount=1.00
   ** ResponseCode=00000
   ** ResponseDescription=The%20Payment%20has%20been%20Authorised.%20
   ** gws_rd=ssl
   */
    $group->post('/globalpayment/pay', \APP\Controllers\GlobalPaymentController::class . ':pay')->add(\APP\Auth::getSessionCheck());
    $group->post('/globalpayment/response', \APP\Controllers\GlobalPaymentController::class . ':response')->add(\APP\Auth::getSessionCheck());
    $group->get('/globalpayment/country',GlobalPaymentController::class.':getCountry');
    $group->post('/globalpayment/billing-address',GlobalPaymentController::class.':getBillingAddress')->add(\APP\Auth::getSessionCheck());
    $group->post('/stripe/pay',StripeController::class.':pay')->add(\APP\Auth::getSessionCheck());
    $group->get('/stripe/callback',StripeController::class.':callback')->add(\APP\Auth::getSessionCheck());
    $group->get('/stripe/credit-callback',StripeController::class.':creditCallback')->add(\APP\Auth::getSessionCheck());
    $group->post('/stripe/webhook',StripeController::class.':stripeWebhook');
    $group->get('/stripe/credits',StripeController::class.':getCredits')->add(\APP\Auth::getSessionCheck());
    $group->get('/stripe/plan',StripeController::class.':getPlan')->add(\APP\Auth::getSessionCheck());
    $group->post('/stripe/cancel-subscription',StripeController::class.':cancelSubscription')->add(\APP\Auth::getSessionCheck());
    $group->post('/stripe/subscriptions',StripeController::class.':listSubscriptions')->add(\APP\Auth::getSessionCheck());
    $group->post('/stripe/customer-portal',StripeController::class.':createCustomerPortal')->add(\APP\Auth::getSessionCheck());
    $group->post('/stripe/manage-subscription',StripeController::class.':manageSubscription')->add(\APP\Auth::getSessionCheck());

    $group->post('/pay360/pay', Payment360Controller::class . ':pay')->add(\APP\Auth::getSessionCheck());
    $group->get('/pay360/callback/{system_generated_transaction_id}', Payment360Controller::class . ':callback')->add(\APP\Auth::getSessionCheck());
    $group->get('/pay360/response/{system_generated_transaction_id}', Payment360Controller::class . ':response')->add(\APP\Auth::getSessionCheck());

    $group->post('/civica/pay', function (Request $request, Response $response, array $args) {
       // try{
        $params = $request->getParsedBody();
        if(isset($params['learner_requirement']) && $params['learner_requirement']){
        $_SESSION['learner_requirement'] = $params['learner_requirement'];
        }else{
          if(isset($_SESSION['learner_requirement'])){
              unset($_SESSION['learner_requirement']);
          }
        }
        $civicaPaymentsEngineRequestURL = \APP\Tools::getConfig('civicaPaymentsEngineRequestURL');
        $civicaPaymentsEngineAccountReference = \APP\Tools::getConfig('civicaDefaultGeneralLedgerCode');
        $civicaPaymentsEngineVatCode = \APP\Tools::getConfig('civicaPaymentsEngineVatCode');
        $civicaPaymentsFundCode = "SL";
        $userPaymentTransactionObj = new \Models\UserPaymentTransaction;

        /*Test Transaction check*/
        if($civicaPaymentsEngineRequestURL){
               $url=parse_url($civicaPaymentsEngineRequestURL);
               $uri= explode('/', $url['path']);
               $uri = $uri[1];
               if($uri == "SuffolkPartnershipXMLTest"){
                   $userPaymentTransactionObj->is_test = 1;
               }
           }

        if (isset($params["type"]) && isset($params["type_id"])) {

            /*Discount Calculation*/
            $totaldiscount = 0;
            $totalManagerDiscount=0;
            $zeroPaymentMessage="Applied ";

            //if admin ,then we need to fetch the discount of manager and user
            if (
                \APP\Auth::isAdminInterface()
            ) {
                $user_id=$params["user_id"];
                $totalManagerDiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
                $totalManagerDiscount=($totalManagerDiscount > 100) ? 100 : $totalManagerDiscount;

                $zeroPaymentMessage .= "manager discount of {$totalManagerDiscount}% and ";
            }else{
                $user_id=\APP\Auth::getUserId();
            }

            $totalUserDiscount = \Models\User::calculateTotalDiscount($user_id);
            $totalUserDiscount=($totalUserDiscount > 100) ? 100 : $totalUserDiscount;
            $zeroPaymentMessage .= "trainee discount {$totalUserDiscount}%";
            $totaldiscount=$totalManagerDiscount+$totalUserDiscount;

            $totaldiscount=($totaldiscount > 100) ? 100 : $totaldiscount;



            if ($params["type"] == "schedules") {
                $scheduleObj = \Models\Schedule::find($params["type_id"]);
                if ($scheduleObj) {
                    $userPaymentTransactionObj->item_cost = $scheduleObj->cost;
                    $userPaymentTransactionObj->user_id = \APP\Auth::getUserId();
                    $userPaymentTransactionObj->payer_organisation_id = \APP\Auth::getUserCompanyId();
                    $userPaymentTransactionObj->type = $scheduleObj->type;
                    $userPaymentTransactionObj->type_id = $scheduleObj->id;
                    $userPaymentTransactionObj->calling_application_id = "CPD";
                    $userPaymentTransactionObj->type_reference_table = "schedules";
                    $userPaymentTransactionObj->item_discount = $totaldiscount;
                    $userPaymentTransactionObj->item_cost = $scheduleObj->cost;
                    $userPaymentTransactionObj->paid_for = $params["paid_for"]==null?\APP\Auth::getUserId():$params["paid_for"];

                    $couponCode = $params['coupon_code'] ?? '';
                    if ($couponCode != ''){
                        $coupon = \Models\Coupon::where('code',$couponCode)->first();
                        if(!$coupon){
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Invalid coupon code']));
                            return $response->withHeader('Content-Type', 'application/json');

                        }
                        $couponUsage = \Models\CouponUsage::where('user_id',\APP\Auth::getUserId())->where('coupon_id')->first();
                        if ($coupon->use_once_per == "user" && $couponUsage){
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'You have already used this coupon once']));
                            return $response->withHeader('Content-Type', 'application/json');
                        }
                        $couponForModuleExists = $coupon->Schedules()->where('schedules.id',$scheduleObj->id)->first();
                        if($couponForModuleExists){
                            $couponUsage = \Models\CouponUsage::create([
                                'user_id' => \APP\Auth::getUserId(),
                                'type' => 'schedule',
                                'type_id' => $scheduleObj->id,
                                'cost'=>$scheduleObj->cost,
                                'coupon_id' => $coupon->id,
                                'coupon_name' => $coupon->name,
                                'coupon_percentage_discount' => $coupon->percentage_discount,
                                'coupon_use_once_per' => $coupon->use_once_per,
                                // 'transaction_id' => $userPaymentTransactionObj->id
                            ]);
                            $couponDiscount = $coupon->percentage_discount;
                        }else{
                            // coupon exists, but not linked it with any resource
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Invalid coupon code']));
                            return $response->withHeader('Content-Type', 'application/json');
                        }
                    }

                    $userPaymentTransactionObj->payment_total = $scheduleObj->cost;
                    if (isset($couponDiscount) && $couponDiscount > 0){
                        $couponUsage->cost = $scheduleObj->payment_total;
                        $userPaymentTransactionObj->payment_total = $scheduleObj->cost - round(($scheduleObj->cost * $couponDiscount) / 100,2);;
                        $couponUsage->cost_after_coupon_apply = $userPaymentTransactionObj->payment_total;
                    }
                    /*Applying Discount*/
                    if ($totaldiscount > 0) {
                        $userPaymentTransactionObj->payment_total = $userPaymentTransactionObj->payment_total - round(($userPaymentTransactionObj->payment_total * $totaldiscount) / 100,2);
                    }
                    if($userPaymentTransactionObj->payment_total<=0){
                        $userPaymentTransactionObj->response_description=$zeroPaymentMessage;
                    }

                    $userPaymentTransactionObj->return_url = $params["url"];
                    $userPaymentTransactionObj->network_user_id = "Internet";
                    $userPaymentTransactionObj->payment_1 = $civicaPaymentsEngineAccountReference . "|" . $civicaPaymentsFundCode . "|" . $userPaymentTransactionObj->payment_total . "|" . $civicaPaymentsEngineVatCode . "|" . $scheduleObj->type . "-" . $scheduleObj->name . "-" . $scheduleObj->id;
                    $userPaymentTransactionObj->payment_gateway = "civica";
                    $userPaymentTransactionObj->status = "0";
                    $userPaymentTransactionObj->save();
                    $UpdateObj = \Models\UserPaymentTransaction::find($userPaymentTransactionObj->id);
                    $UpdateObj->system_generated_transaction_id = substr(md5(time()), 0, 10) . $userPaymentTransactionObj->id;
                    /*$redirectURL is the URL that is sent to CIVICA payment gateway */
                    // $baseUrl = $request->getUri()->getScheme() . '://' . $request->getUri()->getHost();
                    $baseUrl = $this->get('settings')['LMSUrl'];
                    $callBackURL = $baseUrl . 'payment-gateway/civica/callback/' . $UpdateObj->system_generated_transaction_id;
                    $redirectURL = $civicaPaymentsEngineRequestURL . "?CallingApplicationID=" . $userPaymentTransactionObj->calling_application_id
                    . "&" . "PaymentTotal=" . $userPaymentTransactionObj->payment_total . "&Network_User_ID=Internet&ReturnURL=" .
                    $callBackURL . "&PaymentSourceCode=01&Payment_1=" . $userPaymentTransactionObj->payment_1;
                    $UpdateObj->redirect_url = $redirectURL;
                    $UpdateObj->return_url = $UpdateObj->return_url . "?cpr=" . $UpdateObj->system_generated_transaction_id;
                    $UpdateObj->save();
                    if(isset($couponUsage)){
                        $couponUsage->transaction_id = $UpdateObj->id;
                        $couponUsage->save();
                    }
                }
            }
            if ($params["type"] == "learning_modules") {
                $learningModuleObj = \Models\LearningModule::find($params["type_id"]);
                if ($learningModuleObj) {
                    $userPaymentTransactionObj->item_cost = $learningModuleObj->cost;
                    $userPaymentTransactionObj->user_id = \APP\Auth::getUserId();
                    $userPaymentTransactionObj->payer_organisation_id = \APP\Auth::getUserCompanyId();
                    $userPaymentTransactionObj->type = "learning module";
                    $userPaymentTransactionObj->type_id = $learningModuleObj->id;
                    $userPaymentTransactionObj->calling_application_id = "CPD";
                    $userPaymentTransactionObj->type_reference_table = "learning_modules";
                    $userPaymentTransactionObj->paid_for = $params["paid_for"]==null?\APP\Auth::getUserId():$params["paid_for"];

                    $couponCode = $params['coupon_code'] ?? '';
                    if ($couponCode != ''){
                        $coupon = \Models\Coupon::where('code',$couponCode)->first();
                        if(!$coupon){
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Invalid coupon code']));
                            return $response->withHeader('Content-Type', 'application/json');

                        }
                        $couponUsage = \Models\CouponUsage::where('user_id', \APP\Auth::getUserId())->where('coupon_id', $coupon->id)->first();
                        if ($coupon->use_once_per == "user" && $couponUsage) {
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'You have already used this coupon once']));
                            return $response->withHeader('Content-Type', 'application/json');

                        } else if ($coupon->use_once_per == "company") {
                            $user = \Models\User::find(\APP\Auth::getUserId());
                            $couponExists = DB::table('coupon_usages')
                                ->join('users', 'coupon_usages.user_id', '=', 'users.id')
                                ->join('companies', 'users.company_id', '=', 'companies.id')
                                ->where('coupon_usages.coupon_id', $coupon->id)
                                ->where('companies.id', $user->company_id)
                                ->first();
                            if ($couponExists) {
                                $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Company have already used this coupon once']));
                                return $response->withHeader('Content-Type', 'application/json');
                            }
                        }
                        $couponForModuleExists = $coupon->Modules()->where('learning_modules.id',$learningModuleObj->id)->first();
                        if($couponForModuleExists){
                            $couponUsage = \Models\CouponUsage::create([
                                'user_id' => \APP\Auth::getUserId(),
                                'type' => 'learning_module',
                                'type_id' => $learningModuleObj->id,
                                'coupon_id' => $coupon->id,
                                'coupon_name' => $coupon->name,
                                'cost' => $learningModuleObj->cost,
                                'coupon_percentage_discount' => $coupon->percentage_discount,
                                'coupon_use_once_per' => $coupon->use_once_per,
                                // 'transaction_id' => $userPaymentTransactionObj->id
                            ]);
                            $couponDiscount = $coupon->percentage_discount;
                        }else{
                            // coupon exists, but not linked it with any resource
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Invalid coupon code']));
                            return $response->withHeader('Content-Type', 'application/json');
                        }
                    }

                    $userPaymentTransactionObj->item_discount = $totaldiscount;
                    $userPaymentTransactionObj->item_cost = $learningModuleObj->cost;
                    if(!empty($learningModuleObj->code)){
                        $civicaPaymentsEngineAccountReference = $learningModuleObj->code;
                    }

                    if (isset($couponDiscount) && $couponDiscount > 0){
                        $couponUsage->cost = $learningModuleObj->payment_total;
                        $userPaymentTransactionObj->payment_total = $learningModuleObj->cost - round(($learningModuleObj->cost * $couponDiscount) / 100,2);;
                        $couponUsage->cost_after_coupon_apply = $userPaymentTransactionObj->payment_total;
                    }else{
                        $userPaymentTransactionObj->payment_total = $learningModuleObj->cost;
                    }

                    /*Applying Discount*/
                    if ($totaldiscount > 0) {
                        $userPaymentTransactionObj->payment_total = $userPaymentTransactionObj->payment_total - round(($userPaymentTransactionObj->payment_total * $totaldiscount) / 100,2);
                    } else {
                        $userPaymentTransactionObj->payment_total = $learningModuleObj->cost;
                    }

                    if($userPaymentTransactionObj->payment_total<=0){
                        $userPaymentTransactionObj->response_description=$zeroPaymentMessage;
                    }

                    $userPaymentTransactionObj->return_url = $params["url"];
                    $userPaymentTransactionObj->network_user_id = "Internet";
                    $userPaymentTransactionObj->payment_1 = $civicaPaymentsEngineAccountReference . "|" . $civicaPaymentsFundCode . "|" . $userPaymentTransactionObj->payment_total . "|" . $civicaPaymentsEngineVatCode . "|" . $userPaymentTransactionObj->type . "-" . $learningModuleObj->name . "-" . $learningModuleObj->id;
                    $userPaymentTransactionObj->payment_gateway = "civica";
                    $userPaymentTransactionObj->status = "0";
                    $userPaymentTransactionObj->save();
                    $UpdateObj = \Models\UserPaymentTransaction::find($userPaymentTransactionObj->id);
                    $UpdateObj->system_generated_transaction_id = substr(md5(time()), 0, 10) . $userPaymentTransactionObj->id;
                    $UpdateObj->return_url = $UpdateObj->return_url . "?cpr=" . $UpdateObj->system_generated_transaction_id;
                    /*$redirectURL is the URL that is sent to CIVICA payment gateway */
                    // $baseUrl = $request->getUri()->getScheme() . '://' . $request->getUri()->getHost();
                    $baseUrl = $this->get('settings')['LMSUrl'];
                    $callBackURL = $baseUrl . 'payment-gateway/civica/callback/' . $UpdateObj->system_generated_transaction_id;
                    $redirectURL = $civicaPaymentsEngineRequestURL . "?CallingApplicationID=" . $userPaymentTransactionObj->calling_application_id . "&" . "PaymentTotal=" . $userPaymentTransactionObj->payment_total . "&Network_User_ID=Internet&ReturnURL=" . $callBackURL . "&PaymentSourceCode=01&Payment_1=" . $userPaymentTransactionObj->payment_1;
                    $UpdateObj->redirect_url = $redirectURL;
                    $UpdateObj->save();
                    if(isset($couponUsage)){
                        $couponUsage->transaction_id = $UpdateObj->id;
                        $couponUsage->save();
                    }
                }
            } else if ($params["type"] == "programme") {
                $apprenticeshipStandardObj = \Models\ApprenticeshipStandard::find($params["type_id"]);
                if ($apprenticeshipStandardObj) {
                    $userPaymentTransactionObj->item_cost = $apprenticeshipStandardObj->cost;
                    $userPaymentTransactionObj->user_id = \APP\Auth::getUserId();
                    $userPaymentTransactionObj->payer_organisation_id = \APP\Auth::getUserCompanyId();
                    $userPaymentTransactionObj->type = "apprenticeship_standards";
                    $userPaymentTransactionObj->type_id = $apprenticeshipStandardObj->id;
                    $userPaymentTransactionObj->calling_application_id = "CPD";
                    $userPaymentTransactionObj->type_reference_table = "apprenticeship_standards";
                    $userPaymentTransactionObj->paid_for = $params["paid_for"] == null ? \APP\Auth::getUserId() : $params["paid_for"];

                    $couponCode = $params['coupon_code'] ?? '';
                    if ($couponCode != ''){
                        $coupon = \Models\Coupon::where('code',$couponCode)->first();
                        if(!$coupon){
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Invalid coupon code']));
                            return $response->withHeader('Content-Type', 'application/json');

                        }
                        $couponUsage = \Models\CouponUsage::where('user_id', \APP\Auth::getUserId())->where('coupon_id', $coupon->id)->first();
                        if ($coupon->use_once_per == "user" && $couponUsage) {
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'You have already used this coupon once']));
                            return $response->withHeader('Content-Type', 'application/json');

                        } else if ($coupon->use_once_per == "company") {
                            $user = \Models\User::find(\APP\Auth::getUserId());
                            $couponExists = DB::table('coupon_usages')
                                ->join('users', 'coupon_usages.user_id', '=', 'users.id')
                                ->join('companies', 'users.company_id', '=', 'companies.id')
                                ->where('coupon_usages.coupon_id', $coupon->id)
                                ->where('companies.id', $user->company_id)
                                ->first();
                            if ($couponExists) {
                                $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Company have already used this coupon once']));
                                return $response->withHeader('Content-Type', 'application/json');
                            }
                        }
                        $couponForModuleExists = $coupon->Standards()->where('apprenticeship_standards.id',$apprenticeshipStandardObj->id)->first();
                        if($couponForModuleExists){
                            $couponUsage = \Models\CouponUsage::create([
                                'user_id' => \APP\Auth::getUserId(),
                                'type' => 'programme',
                                'type_id' => $apprenticeshipStandardObj->id,
                                'cost' => $apprenticeshipStandardObj->cost,
                                'coupon_id' => $coupon->id,
                                'coupon_name' => $coupon->name,
                                'coupon_percentage_discount' => $coupon->percentage_discount,
                                'coupon_use_once_per' => $coupon->use_once_per,
                                // 'transaction_id' => $userPaymentTransactionObj->id
                            ]);
                            $couponDiscount = $coupon->percentage_discount;
                        }else{
                            // coupon exists, but not linked it with any resource
                            $response->getBody()->write(json_encode(['status' => '404', 'msg' => 'Invalid coupon code']));
                            return $response->withHeader('Content-Type', 'application/json');

                        }
                    }

                    $userPaymentTransactionObj->item_discount = $totaldiscount;
                    $userPaymentTransactionObj->item_cost = $apprenticeshipStandardObj->cost;
                    // if(!empty($apprenticeshipStandardObj->reference_code)){
                    // i'm not sure about this code
                    // $civicaPaymentsEngineAccountReference = $apprenticeshipStandardObj->reference_code;
                    // }

                    if (isset($couponDiscount) && $couponDiscount > 0){
                        $couponUsage->cost = $apprenticeshipStandardObj->cost;
                        $userPaymentTransactionObj->payment_total = $apprenticeshipStandardObj->cost - round(($apprenticeshipStandardObj->cost * $couponDiscount) / 100,2);;
                        $couponUsage->cost_after_coupon_apply = $userPaymentTransactionObj->payment_total;
                        // if needed fetch total from user_payment_transactions payment_total
                    }else{
                        $userPaymentTransactionObj->payment_total = $apprenticeshipStandardObj->cost;
                    }

                    /*Applying Discount*/
                    if ($totaldiscount > 0) {
                        $userPaymentTransactionObj->payment_total = $userPaymentTransactionObj->payment_total - round(($userPaymentTransactionObj->payment_total * $totaldiscount) / 100,2);
                    } else {
                        $userPaymentTransactionObj->payment_total = $apprenticeshipStandardObj->cost;
                    }

                    if ($userPaymentTransactionObj->payment_total <= 0) {
                        $userPaymentTransactionObj->response_description = $zeroPaymentMessage;
                    }

                    $userPaymentTransactionObj->return_url = $params["url"];
                    $userPaymentTransactionObj->network_user_id = "Internet";
                    $userPaymentTransactionObj->payment_1 = $civicaPaymentsEngineAccountReference . "|" . $civicaPaymentsFundCode . "|" . $userPaymentTransactionObj->payment_total . "|" . $civicaPaymentsEngineVatCode . "|" . $userPaymentTransactionObj->type . "-" . $apprenticeshipStandardObj->name . "-" . $apprenticeshipStandardObj->id;
                    $userPaymentTransactionObj->payment_gateway = "civica";
                    $userPaymentTransactionObj->status = "0";
                    $userPaymentTransactionObj->save();
                    $UpdateObj = \Models\UserPaymentTransaction::find($userPaymentTransactionObj->id);
                    $UpdateObj->system_generated_transaction_id = substr(md5(time()), 0, 10) . $userPaymentTransactionObj->id;
                    $UpdateObj->return_url = $UpdateObj->return_url . "?cpr=" . $UpdateObj->system_generated_transaction_id;
                    /*$redirectURL is the URL that is sent to CIVICA payment gateway */
                    // $baseUrl = $request->getUri()->getScheme() . '://' . $request->getUri()->getHost();
                    $baseUrl = $this->get('settings')['LMSUrl'];
                    $callBackURL = $baseUrl . 'payment-gateway/civica/callback/' . $UpdateObj->system_generated_transaction_id;
                    $redirectURL = $civicaPaymentsEngineRequestURL . "?CallingApplicationID=" . $userPaymentTransactionObj->calling_application_id . "&" . "PaymentTotal=" . $userPaymentTransactionObj->payment_total . "&Network_User_ID=Internet&ReturnURL=" . $callBackURL . "&PaymentSourceCode=01&Payment_1=" . $userPaymentTransactionObj->payment_1;
                    $UpdateObj->redirect_url = $redirectURL;
                    $UpdateObj->save();
                    if(isset($couponUsage)){
                        $couponUsage->transaction_id = $UpdateObj->id;
                        $couponUsage->save();
                    }
                }
            }
        }

        if($userPaymentTransactionObj->payment_total<=0){
            $response_content=["status"=>0,"redirectUrl"=>'',
            'system_generated_transaction_id'=>$UpdateObj->system_generated_transaction_id,
            'message'=>$zeroPaymentMessage
        ];
        }else{
            $response_content=["status"=>1,"redirectUrl"=>$redirectURL,'system_generated_transaction_id'=>$UpdateObj->system_generated_transaction_id];
        }

        $response->getBody()->write(json_encode($response_content));
		return $response->withHeader('Content-Type', 'application/json');

        // return
        //     $response
        //         ->withStatus(200)
        //         ->withHeader('Content-Type', 'text/html')
        //         ->write($response_content);
    // }catch(\Exception $e){
    //     print_r($e->getMessage());die;
    // }
    })->add(\APP\Auth::getSessionCheck());;

    $group->get('/civica/callback/{id}', function (Request $request, Response $response, array $args) {
        $params = $request->getQueryParams();
        $system_generated_transaction_id = $args['id'];
        $PaymentAuthorisationCode = isset($params['PaymentAuthorisationCode'])?$params['PaymentAuthorisationCode']:'';
        $IncomeManagementReceiptNumber = isset($params['IncomeManagementReceiptNumber'])?$params['IncomeManagementReceiptNumber']:'';
        $OriginatorsReference = isset($params['OriginatorsReference'])?$params['OriginatorsReference']:'';
        $CardScheme = isset($params['CardScheme'])?$params['CardScheme']:'';
        $CardType = isset($params['CardType'])?$params['CardType']:'';
        $PaymentAmount = isset($params['PaymentAmount'])?$params['PaymentAmount']:'';
        $ResponseCode = isset($params['ResponseCode'])?$params['ResponseCode']:'';
        $ResponseDescription = isset($params['ResponseDescription'])?$params['ResponseDescription']:'';

        $userPaymentTransactions = \Models\UserPaymentTransaction::where('system_generated_transaction_id', $system_generated_transaction_id)->first();

        /*Validations*/
        if ($userPaymentTransactions) {

//!isset($params["discount_type"])) &&
            if (!isset($params["discount_type"]) && ($userPaymentTransactions->response_code || $userPaymentTransactions->response_description ||
            $userPaymentTransactions->income_management_receipt_number)) {
                PurchasePaymentTransaction::updateData($userPaymentTransactions->paid_for?$userPaymentTransactions->paid_for:$userPaymentTransactions->user_id,$userPaymentTransactions->type_reference_table,$userPaymentTransactions->type_id,$userPaymentTransactions->item_cost,$userPaymentTransactions->system_generated_transaction_id,'civica',$userPaymentTransactions->id);
                header("Location:$userPaymentTransactions->return_url");
                exit();

            } //               if(true)
            else {

                $userPaymentTransactions->payment_authorisation_code = $PaymentAuthorisationCode;
                $userPaymentTransactions->income_management_receipt_number = $IncomeManagementReceiptNumber;
                $userPaymentTransactions->originators_reference = $OriginatorsReference;
                $userPaymentTransactions->card_scheme = $CardScheme;
                $userPaymentTransactions->card_type = $CardType;
                $userPaymentTransactions->payment_amount = $PaymentAmount;
                $userPaymentTransactions->response_code = $ResponseCode;
                $userPaymentTransactions->response_description = !empty($userPaymentTransactions->response_description)?$userPaymentTransactions->response_description:
                $ResponseDescription;
                /*Response code based status check*/
                /*0000 Approved*/
                /*0021 Referred*/
                /*0022 Declined*/
                /*0022 Expired Card*/

                /*Actions for Success Response*/
                if ($ResponseCode == '0000') {
                    $userPaymentTransactions->status = 1;
                    $couponUsage = \Models\CouponUsage::where('transaction_id',$userPaymentTransactions->id)->first();
                    if ($couponUsage){
                        if (!$couponUsage->has_used){
                            $couponUsage->has_used = true;
                            $couponUsage->save();
                        }else{
                            return "User already used the coupon";
                        }
                    }
                    /*Enroll User*/
                    if ($userPaymentTransactions->type_reference_table == "learning_modules") {
                        $user = \Models\User::find($userPaymentTransactions->user_id);
                        $modules = $userPaymentTransactions->type_id;
                        $modules = (array)$modules;
                        $user->modules()->syncWithoutDetaching($modules);
                        \APP\Learning::syncUserResults($userPaymentTransactions->paid_for);
                        \Models\LearningResult
                        ::where("learning_results.user_id", "=", $userPaymentTransactions->paid_for)
                            ->whereIn("learning_results.learning_module_id", function ($query) use ($modules) {
                                $query
                                    ->select("id")
                                    ->from("learning_modules")
                                    ->whereIn("id", $modules)
                                    ->where("approval", "=", true);
                            })
                            ->update([
                               // "approved" => false,
                                "is_paid"=>'1'
                            ]);

                    } else if ($userPaymentTransactions->type_reference_table == "apprenticeship_standards") {
                        $user = \Models\User::find($userPaymentTransactions->user_id);
                        \Models\ApprenticeshipStandardUser::assignToStandard($user->id, $userPaymentTransactions->type_id);
                    } else if ($userPaymentTransactions->type_reference_table == "schedules") {
                        $data["schedule_id"] = $userPaymentTransactions->type_id;
                        $data["type"] = "users";
                        $data["link_id"] = $userPaymentTransactions->paid_for;
                        $data["is_paid"] = $userPaymentTransactions->status;
                        if(isset($_SESSION['learner_requirement'])){
                            $data["learner_requirement"] = $_SESSION['learner_requirement'];
                            unset($_SESSION['learner_requirement']);
                        }
                        $schedule = \Models\Schedule
                            ::where('id', $userPaymentTransactions->type_id)
                            ->with(['Users' => function ($query) {
                                $query = $query
                                    ->select(
                                        'users.id'
                                    )
                                    ->where('users.status', true);
                            }])->first();
                        if (isset($schedule->users)) {
                            if ($schedule->maxclass > 0) {
                if ($schedule->maxclass <= count($schedule->users)) {
                                    $data["type"] = 'users_queue';
                                }
                            }
                        }
                        $existModelLink=\Models\ScheduleLink::where("type","users")
                        ->where("schedule_id",$userPaymentTransactions->type_id)
                        ->where("link_id", $userPaymentTransactions->paid_for)->first();

                         //Add existing link events to user
            $ids = \Models\Schedule::getChild($data["schedule_id"]);

                         if(count($ids)>0){
                            $schedules = \Models\Schedule
                            ::whereIn("id", $ids)
                            ->get();

                            $waitingList = 0;
                            if(count($schedules)>0){
                                foreach ($schedules as $key => $linked_schedule) {
                                    if ($linked_schedule->id !=  $data["schedule_id"]) {
                                        $insert = $data;
                                        if(isset($insert['is_paid'])){
                                            unset($insert['is_paid']);
                                        }
                                        $insert["schedule_id"] = $linked_schedule->id;

                                        if (isset($linked_schedule->users)) {
                                            if ($linked_schedule->maxclass > 0) {
                                                if ($linked_schedule->maxclass <= count($linked_schedule->users)) {
                                                    $insert["type"] = 'users_queue';
                                                    $waitingList = 1;
                                                }
                                            }
                                        }
                                        if($linked_schedule->cost>0){
                                            $insert["is_paid"] = '0';
                                        }
                    $insert["approval"] = $linked_schedule->approval;
                    $scheduleLinkParent = \Models\ScheduleLink::whereIn("type",["users","users_queue"])->where('schedule_id',$data['schedule_id'])->where('link_id',$data['link_id'])->first();
                                        if($scheduleLinkParent){
                                                $insert['learner_requirement'] = $scheduleLinkParent->learner_requirement;
                                        }
                                        if(isset($data['learner_requirement'])){
                                            $insert['learner_requirement'] = $data['learner_requirement'];
                                        }
                                        \Models\ScheduleLink::addNewLink($insert);
                                    }
                                }
                            }
                        }
                        //Add existing link events to user
            if (!$existModelLink) {
                            \Models\ScheduleLink::addNewLink($data);
                        }else{
                            $existModelLink->is_paid="1";
                            $existModelLink->type="users";
                            $existModelLink->status = 1;
                            $existModelLink->deleted_by = null;
                            if (\APP\Tools::getConfig('ApproveEventUserAfterPayment')) {
                                $existModelLink->approved = 1;
                            }
                            $existModelLink->save();
                        }


                        //Assign form to users

				         \Models\Schedule::assignUsersToForm($data["schedule_id"], $userPaymentTransactions->user_id);

                    }

                PurchasePaymentTransaction::updateData($userPaymentTransactions->paid_for?$userPaymentTransactions->paid_for:$userPaymentTransactions->user_id,$userPaymentTransactions->type_reference_table,$userPaymentTransactions->type_id,$userPaymentTransactions->item_cost,$userPaymentTransactions->system_generated_transaction_id,'civica',$userPaymentTransactions->id);
                }
                $userPaymentTransactions->save();
                if(isset($params["discount_type"]) && $params["discount_type"]=="discount_zero_payment"){
                    $response->getBody()->write(json_encode($userPaymentTransactions));
                    return $response->withStatus(200)->withHeader('Content-Type', 'application/json');

                }else{
                    header("Location: $userPaymentTransactions->return_url");
                    exit();
                }
            }
        }

    })->add(\APP\Auth::getSessionCheck());

    $group->post('/civica/response', function (Request $request, Response $response, array $args) {
    $params = $request->getParsedBody();
        if (isset($params['id'])) {
            $userPaymentTransactions = \Models\UserPaymentTransaction
                ::where('system_generated_transaction_id', $params['id'])
                ->where('user_id', \APP\Auth::getUserId())/*Validate user to ensure unauthorised response access*/
                ->select(
                    'status',
                    'response_description',
                    'income_management_receipt_number',
                    'type',
                    'type_id',
                    'paid_for',
                    'payment_amount',
                    'user_id'
                )
                ->first()
            ;

            $paid_for = false;
            if ($userPaymentTransactions->paid_for !== $userPaymentTransactions->user_id) {
                $paid_for = \Models\User::find($userPaymentTransactions->paid_for);
            }
            if ($userPaymentTransactions) {
        $template = \Models\EmailTemplate::getTemplate('confirmation_of_purchase_learner');
        if($paid_for){
          $template = \Models\EmailTemplate::getTemplate('confirmation_of_purchase_manager');
        }
                if ($template) {
                    $custom_variables = [];
                    //set default format or use dd/mm/yyyy
          $format = \APP\Tools::getConfig('defaultDateFormat')?\APP\Tools::getConfig('defaultDateFormat'):'d/m/Y';
                    $custom_variables['TODAYSDATE'] = \Carbon\Carbon::now()->format($format);
                    if ($userPaymentTransactions->type == "lesson") {
                        $schedule = \Models\Schedule::where('id', $userPaymentTransactions->type_id)->first();
                        if ($schedule) {

                            $event_date = '';
                            $event_time = '';
                            if ($schedule->start_date) {
                                $event_start_date = strtotime($schedule->start_date);
                                $event_date = date(\APP\Tools::getConfig('defaultDateFormat'), $event_start_date);
                                $event_time = date('H:i:s', $event_start_date);
                            }
                            $custom_variables['EVENT_NAME'] = $schedule->name;
                            $custom_variables['LEARNING_NAME'] = $schedule->name;
                            $custom_variables['TYPE'] = "Event";
                            $custom_variables['MANAGER_FNAME'] = \APP\Auth::getUser()->fname;
                            $custom_variables['MANAGER_LNAME'] = \APP\Auth::getUser()->lname;

                            if ($paid_for) {
                                $custom_variables['PAID_FOR_FNAME'] = $paid_for->fname;
                                $custom_variables['PAID_FOR_LNAME'] = $paid_for->lname;
                                $custom_variables['LEARNER_NAME'] = $paid_for->fname." ".$paid_for->lname;
                                $custom_variables['USER_FNAME'] = $paid_for->fname;

                            }
                            $custom_variables['SESSION_LOCATION'] = $schedule->location;
                            $custom_variables['RECEIPT_NUMBER'] = $userPaymentTransactions->income_management_receipt_number;
                            $custom_variables['EVENT_DATE'] = $event_date;
                            $custom_variables['EVENT_TIME'] = $event_time;
                            $custom_variables['FEE'] = $userPaymentTransactions->payment_amount;
                        }
                    }
                    if ($userPaymentTransactions->type == 'learning module') {
                        $learning_module = \Models\LearningModule::where('id',$userPaymentTransactions->type_id)->first();
                        if($learning_module) {
                            if($paid_for){
                                $custom_variables['LEARNER_NAME'] = $paid_for->fname." ".$paid_for->lname;
                            }
                            $custom_variables['TYPE'] = "Resource";
                            $custom_variables['LEARNING_NAME']=$learning_module->name;
                            $custom_variables['USER_FNAME']=\APP\Auth::getUser()->fname;
                            $custom_variables['SESSION_LOCATION']='';
                            $custom_variables['TODAYSDATE']=\Carbon\Carbon::now()->format(\APP\Tools::getConfig('defaultDateFormat'));
                            $custom_variables['RECEIPT_NUMBER']=$userPaymentTransactions->income_management_receipt_number;
                            $custom_variables['FEE'] = $userPaymentTransactions->payment_amount;
                        }
                    }
                    if ($userPaymentTransactions->type == 'programme') {
                        $apprenticeshipStandard = \Models\ApprenticeshipStandard::where('id',$userPaymentTransactions->type_id)->first();
                        if($apprenticeshipStandard) {
                            $custom_variables['TYPE'] = "Programme";
                            $custom_variables['LEARNING_NAME']=$apprenticeshipStandard->name;
                            $custom_variables['USER_FNAME']=\APP\Auth::getUser()->fname;
                            $custom_variables['SESSION_LOCATION']='';
                            $custom_variables['TODAYSDATE']=\Carbon\Carbon::now()->format(\APP\Tools::getConfig('defaultDateFormat'));
                            $custom_variables['RECEIPT_NUMBER']=$userPaymentTransactions->income_management_receipt_number;
                            $custom_variables['FEE'] = $userPaymentTransactions->payment_amount;
                        }
                    }
          if($userPaymentTransactions->status==1){
                    $email_queue = new \Models\EmailQueue;
                    $email_queue->email_template_id = $template->id;
                    $email_queue->recipients =[\APP\Auth::getUserId()];
                    $email_queue->custom_variables=json_encode($custom_variables);
            $email_queue->save();
          }
                }

                $response->getBody()->write(json_encode($userPaymentTransactions));
                return $response->withStatus(200)->withHeader('Content-Type', 'application/json');
            } else {
                $response->getBody()->write("Something went wrong!. Please try again after some time.");
                return $response->withStatus(401)->withHeader('Content-Type', 'text/html');
            }

        }

    })->add(\APP\Auth::getSessionCheck());
    $group->post('/configure-refund',function (Request $request,Response $response){
        $data = $request->getParsedBody();
        if(!is_array($data)) {
            return $response->withStatus('400');
        }
        PaymentRefundConfiguration::query()->forceDelete();
        foreach ($data as $value){
                PaymentRefundConfiguration::create([
                    'start_date'=>$value['start_date'],
                    'end_date'=>$value['end_date'],
                    'percentage'=>$value['percentage']
                ]);
        }
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'insert'));
    $group->get('/configure-refund',function (Request $request,Response $response){
        $data = PaymentRefundConfiguration::get();
        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-configuration', 'insert'));
	$group->group('/govukpay',function($group){
		$group->post('/pay',\APP\Controllers\GovUKPayController::class . ':pay')->add(\APP\Auth::getSessionCheck());
		$group->get('/callback/{uid}',\APP\Controllers\GovUKPayController::class . ':callback')->add(\APP\Auth::getSessionCheck());
	});
});
