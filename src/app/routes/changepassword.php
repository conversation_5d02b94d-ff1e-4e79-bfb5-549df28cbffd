<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->put("/changepassword/",  function (Request $request, Response $response) {

	$data = $request->getParsedBody();
	$user = Models\User::find(\APP\Auth::getUserId());

	$old_password = $data["old_password"];
	$new_password = $data["new_password"];



	if (
		password_verify($old_password, $user->password) &&
		preg_match('/' . $this->get('settings')["LMSPasswordPattern"] .'/', $new_password)
	) {
		$password_check_response = APP\Tools::passwordStrengthCheck($new_password, $request, $response);
		if ($password_check_response) {
			return $password_check_response;
		}

		$user->password = password_hash($new_password, PASSWORD_BCRYPT, ['cost' => 12]);
        $user->password_changed_at = \Carbon\Carbon::now();
        $user->save();

		return $response;
	} else {
		$response->getBody()->write("wrong password");
		return $response->withStatus(403);
	}

})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-change-password', 'update'));