<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

// Helper function for file detection
function looksLikeFileName($str) {
    if (!is_string($str) || empty($str)) {
        return false;
    }

    // Use centralized allowed extensions from Tools
    $extensions = \APP\Tools::allowExtensions();
    $extension = strtolower(pathinfo($str, PATHINFO_EXTENSION));

    return in_array($extension, $extensions);
}

$app->group("/disk-space", function ($group) {

    $group->get("/stats", function (Request $request, Response $response, array $args) {
        $stats = [];

        // Get the base paths from configuration
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $publicPath = $this->get('settings')['LMSPublicPath'];
        $tempPath = $this->get('settings')['LMSTempPath'];

        // Calculate disk space for different directories (client-relevant storage)
        $directories = [
            // Major storage consumers (GB/TB level)
            'evidence' => $privatePath . 'evidence/',
            'form_files' => $privatePath . 'form_files/',
            'form_logs' => $privatePath . 'form_logs/',
            'files' => $privatePath . 'files/',
            'scorm_data' => $publicPath . 'scormdata/',

            // Medium storage consumers (MB/GB level)
            'temp_files' => $tempPath,
            'batch_report_data' => $privatePath . 'batch_report_data/',
            'logs' => $this->get('settings')['AppFilePath'] . 'logs/',
        ];

        $totalSize = 0;
        $stats['directories'] = [];

        foreach ($directories as $name => $path) {
            if (is_dir($path)) {
                $size = \APP\Tools::getDirectorySize($path);
                $stats['directories'][] = [
                    'name' => ucwords(str_replace('_', ' ', $name)),
                    // 'path' => $path,
                    'size' => $size,
                    'size_formatted' => \APP\Tools::formatBytes($size),
                    'file_count' => \APP\Tools::getFileCount($path)
                ];
                $totalSize += $size;
            }
        }

        // Get database statistics for file references
        $stats['database'] = [];

        try {
            // Learning Module Evidence files (all records as files exist regardless of status)
            $evidenceCount = DB::table('learning_module_evidences')
                ->whereNotNull('file_size') // Only files with size data
                ->where('file_size', '>', 0) // Only files with actual size
                ->count();

            $evidenceTotalSize = DB::table('learning_module_evidences')
                ->sum('file_size');

            $stats['database'][] = [
                'name' => 'Learning Module Evidence Files',
                'count' => $evidenceCount,
                'total_size' => $evidenceTotalSize,
                'total_size_formatted' => \APP\Tools::formatBytes($evidenceTotalSize ?: 0)
            ];
        } catch (\Exception $e) {
            $stats['database'][] = [
                'name' => 'Learning Module Evidence Files',
                'count' => 'N/A (table not found)',
                'total_size' => 0,
                'total_size_formatted' => 'N/A'
            ];
        }

        // Total statistics (client storage only)
        $stats['total'] = [
            'size' => $totalSize,
            'size_formatted' => \APP\Tools::formatBytes($totalSize)
        ];

        $response->getBody()->write(json_encode($stats));
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    $group->post('/list', function (Request $request, Response $response, array $args) {
        $params = $request->getParsedBody();

        // Remove refresh parameter before passing to SmartTable
        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        // For now, return empty results if tables don't exist
        // This can be enhanced later when the database schema is available
        try {
            // Get detailed file information from learning_module_evidences table (all records)
            $query = \Models\LearningModuleEvidence
                ::select([
                    'learning_module_evidences.id',
                    'learning_module_evidences.evidence as file_name',
                    'learning_module_evidences.file_size',
                    'learning_module_evidences.evidence_type as file_type',
                    'learning_module_evidences.created_at',
                    'users.username',
                    'users.id as user_id',
                    DB::raw("CONCAT(users.fname, ' ', users.lname) as uploaded_by"),
                    'learning_modules.name as module_name',
                    DB::raw("'Learning Module Evidence' as file_category"),
                    DB::raw("CASE WHEN learning_module_evidences.evidence_type = 'file' THEN
                        (CASE WHEN learning_module_evidences.evidence IS NOT NULL AND learning_module_evidences.evidence != '' THEN 'Has DB Record' ELSE 'No DB Record' END)
                        ELSE 'Not a file' END as orphan_status")
                ])
                ->leftJoin('users', 'learning_module_evidences.user_id', '=', 'users.id')
                ->leftJoin('learning_modules', 'learning_module_evidences.learning_modules_id', '=', 'learning_modules.id')
                ->whereNotNull('learning_module_evidences.file_size') // Only files with size data
                ->where('learning_module_evidences.file_size', '>', 0); // Only files with actual size

            $p = \APP\SmartTable::searchPaginate($params, $query);

            $response->getBody()->write($p->toJson());
            return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            // Table doesn't exist, return empty result
            $emptyResult = (object) [
                'data' => [],
                'total' => 0,
                'numberOfPages' => 0
            ];

            $response->getBody()->write(json_encode($emptyResult));
            return $response->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // New endpoint to get orphaned files (all files returned for client-side sorting)
    $group->get('/orphaned-files', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $evidencePath = $privatePath . 'evidence/';

        $orphanedFiles = [];
        $totalOrphanedSize = 0;

        try {
            // Get all file references from database
            // Include both hash.extension format and just hash
            $dbFilesWithExt = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->whereNotNull('extension')
                ->where('hash', '!=', '')
                ->where('extension', '!=', '')
                ->select(DB::raw("CONCAT(hash, '.', extension) as filename"))
                ->pluck('filename')
                ->toArray();

            // Also get just the hashes for files that might not have extensions
            $dbHashes = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->where('hash', '!=', '')
                ->pluck('hash')
                ->toArray();

            // Combine both arrays
            $dbFiles = array_merge($dbFilesWithExt, $dbHashes);

            // Convert to hash map for faster lookup
            $dbFileMap = array_flip($dbFiles);

            // Scan evidence directory
            if (is_dir($evidencePath)) {
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($evidencePath, \RecursiveDirectoryIterator::SKIP_DOTS),
                    \RecursiveIteratorIterator::LEAVES_ONLY
                );

                foreach ($iterator as $file) {
                    if ($file->isFile()) {
                        $relativePath = str_replace($evidencePath, '', $file->getPathname());
                        $fileName = basename($relativePath);

                        // Skip .gitignore files and other system files
                        if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                            continue;
                        }

                        // Check if this file exists in database
                        if (!isset($dbFileMap[$fileName])) {
                            $fileSize = $file->getSize();
                            $orphanedFiles[] = [
                                'file_name' => $fileName,
                                'file_size' => $fileSize,
                                'file_size_formatted' => \APP\Tools::formatBytes($fileSize),
                                'modified_date' => date('Y-m-d H:i:s', $file->getMTime()),
                                'file_type' => pathinfo($fileName, PATHINFO_EXTENSION)
                            ];
                            $totalOrphanedSize += $fileSize;
                        }
                    }
                }
            }

            $result = [
                'orphaned_files' => $orphanedFiles,
                'total_orphaned_count' => count($orphanedFiles),
                'total_orphaned_size' => $totalOrphanedSize,
                'total_orphaned_size_formatted' => \APP\Tools::formatBytes($totalOrphanedSize)
            ];

            $response->getBody()->write(json_encode($result));
            return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            $errorResult = [
                'error' => 'Failed to scan for orphaned files: ' . $e->getMessage(),
                'orphaned_files' => [],
                'total_orphaned_count' => 0,
                'total_orphaned_size' => 0,
                'total_orphaned_size_formatted' => '0 B'
            ];

            $response->getBody()->write(json_encode($errorResult));
            return $response->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Delete orphaned file endpoint (by filename)
    $group->delete('/orphaned-files/{fileName:.+}', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $evidencePath = $privatePath . 'evidence/';
        $fileName = urldecode($args['fileName']);

        try {
            // Log for debugging
            error_log("Attempting to delete file: " . $fileName);
            error_log("Evidence path: " . $evidencePath);

            // Parse filename to extract hash and extension
            $fileParts = pathinfo($fileName);
            $extension = $fileParts['extension'] ?? '';
            $hash = $fileParts['filename'] ?? '';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                error_log("Evidence directory does not exist: " . $evidencePath);
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Skip system files
            if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                $response->getBody()->write(json_encode(['error' => 'Cannot delete system files']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Directly check if the specific file exists
            $fileToDelete = $evidencePath . $fileName;

            if (!is_file($fileToDelete)) {
                error_log("File not found: " . $fileToDelete);
                $response->getBody()->write(json_encode(['error' => 'File not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Check if file is orphaned by doing a direct database query for this specific file
            $dbFile = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->where('hash', $hash)
                ->where('extension', $extension)
                ->first();

            if ($dbFile) {
                error_log("File is not orphaned - has database record: " . $fileName);
                $response->getBody()->write(json_encode(['error' => 'File is not orphaned - has database record']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            error_log("Found orphaned file to delete: " . $fileToDelete);

            // Security check - ensure file path is within evidence directory
            $realPath = realpath($fileToDelete);
            $realEvidencePath = realpath($evidencePath);

            if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                error_log("Security check failed - invalid file path: " . $fileToDelete);
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Delete the file
            if (unlink($fileToDelete)) {
                error_log("Successfully deleted file: " . $fileToDelete);
                $response->getBody()->write(json_encode(['success' => true, 'message' => 'File deleted successfully']));
                return $response->withHeader('Content-Type', 'application/json');
            } else {
                error_log("Failed to unlink file: " . $fileToDelete);
                $response->getBody()->write(json_encode(['error' => 'Failed to delete file - unlink failed']));
                return $response
                    ->withStatus(500)
                    ->withHeader('Content-Type', 'application/json');
            }

        } catch (\Exception $e) {
            error_log("Exception in file deletion: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            $response->getBody()->write(json_encode(['error' => 'Failed to delete file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'disable'));

    // View orphaned file endpoint (secure proxy)
    $group->get('/orphaned-files/view/{fileName:.+}', function (Request $request, Response $response, array $args) {

        try {
            $fileName = urldecode($args['fileName']);

            // Check if settings exist
            if (!isset($this->get('settings')['LMSPrivatePath'])) {
                $response->getBody()->write(json_encode(['error' => 'Configuration error: LMSPrivatePath not set']));
                return $response
                    ->withStatus(500)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $evidencePath = $privatePath . 'evidence/';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Parse filename to extract hash and extension
            $fileParts = pathinfo($fileName);
            $extension = $fileParts['extension'] ?? '';
            $hash = $fileParts['filename'] ?? '';

            // Skip system files
            if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                $response->getBody()->write(json_encode(['error' => 'Cannot view system files']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Directly check if the specific file exists
            $fileToView = $evidencePath . $fileName;

            if (!is_file($fileToView)) {
                $response->getBody()->write(json_encode(['error' => 'File not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Check if file is orphaned by doing a direct database query for this specific file
            $dbFile = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->where('hash', $hash)
                ->where('extension', $extension)
                ->first();

            if ($dbFile) {
                $response->getBody()->write(json_encode(['error' => 'File is not orphaned - has database record']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }


            // Security check - ensure file path is within evidence directory
            $realPath = realpath($fileToView);
            $realEvidencePath = realpath($evidencePath);

            if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Check if file exists and is readable
            if (!file_exists($fileToView) || !is_readable($fileToView)) {
                $response->getBody()->write(json_encode(['error' => 'File not found or not readable']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Determine content type based on file extension
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            $contentType = 'application/octet-stream'; // Default

            switch ($extension) {
                case 'pdf':
                    $contentType = 'application/pdf';
                    break;
                case 'jpg':
                case 'jpeg':
                    $contentType = 'image/jpeg';
                    break;
                case 'png':
                    $contentType = 'image/png';
                    break;
                case 'gif':
                    $contentType = 'image/gif';
                    break;
                case 'txt':
                    $contentType = 'text/plain';
                    break;
                case 'html':
                case 'htm':
                    $contentType = 'text/html';
                    break;
                case 'css':
                    $contentType = 'text/css';
                    break;
                case 'js':
                    $contentType = 'application/javascript';
                    break;
                case 'json':
                    $contentType = 'application/json';
                    break;
                case 'xml':
                    $contentType = 'application/xml';
                    break;
                case 'doc':
                    $contentType = 'application/msword';
                    break;
                case 'docx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    break;
                case 'xls':
                    $contentType = 'application/vnd.ms-excel';
                    break;
                case 'xlsx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    break;
                case 'mp4':
                    $contentType = 'video/mp4';
                    break;
                case 'mp3':
                    $contentType = 'audio/mpeg';
                    break;
                case 'zip':
                    $contentType = 'application/zip';
                    break;
            }

            // Get file size
            $fileSize = filesize($fileToView);

            // Clear any output buffers to prevent corruption
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Set headers for file viewing
            header('Content-Type: ' . $contentType);
            header('Content-Length: ' . $fileSize);
            header('Content-Disposition: inline; filename="' . addslashes($fileName) . '"');
            header('Cache-Control: private, max-age=3600');
            header('X-Content-Type-Options: nosniff');

            // For binary files, use readfile for better handling
            // Get binary file extensions (images, audio, video, binary documents, archives)
            $binaryExtensions = array_merge(
                \APP\Tools::allowExtensions(['images']),
                \APP\Tools::allowExtensions(['audio']),
                \APP\Tools::allowExtensions(['video']),
                \APP\Tools::allowExtensions(['misc'])
            );

            // Add binary document formats (exclude text-based ones like txt, csv, rtf)
            $binaryDocuments = array_diff(
                \APP\Tools::allowExtensions(['documents']),
                ['txt', 'csv', 'rtf'] // These are text-based and should be streamed
            );
            $binaryExtensions = array_merge($binaryExtensions, $binaryDocuments);

            if (in_array($extension, $binaryExtensions)) {
                readfile($fileToView);
            } else {
                // For text files, we can use echo
                $fileHandle = fopen($fileToView, 'rb');
                if ($fileHandle) {
                    while (!feof($fileHandle)) {
                        echo fread($fileHandle, 8192);
                        flush();
                    }
                    fclose($fileHandle);
                }
            }

            exit; // Important: exit to prevent Slim from adding extra output

        } catch (\Exception $e) {
            error_log("Exception in file viewing: " . $e->getMessage());
            $response->getBody()->write(json_encode(['error' => 'Failed to view file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }

    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Update missing file sizes endpoint
    $group->post('/update-file-sizes', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $evidencePath = $privatePath . 'evidence/';

        try {
            // Get all evidence entries that are files but have no file size
            $evidenceEntries = DB::table('learning_module_evidences')
                ->where('evidence_type', 'file')
                ->where(function($query) {
                    $query->whereNull('file_size')
                          ->orWhere('file_size', 0)
                          ->orWhere('file_size', '');
                })
                ->whereNotNull('hash')
                ->where('hash', '!=', '')
                ->select(['id', 'hash', 'extension', 'evidence'])
                ->get();

            $totalEntries = count($evidenceEntries);
            $updatedCount = 0;
            $notFoundCount = 0;
            $errors = [];

            foreach ($evidenceEntries as $entry) {
                $fileFound = false;
                $fileSize = 0;
                $actualFileName = null;

                // First try exact matches
                // Try 1: hash.extension format (if extension exists)
                if (!empty($entry->extension)) {
                    $fileName = $entry->hash . '.' . $entry->extension;
                    $filePath = $evidencePath . $fileName;

                    if (is_file($filePath)) {
                        $fileFound = true;
                        $fileSize = filesize($filePath);
                        $actualFileName = $fileName;
                    }
                }

                // Try 2: just hash without extension
                if (!$fileFound) {
                    $fileName = $entry->hash;
                    $filePath = $evidencePath . $fileName;

                    if (is_file($filePath)) {
                        $fileFound = true;
                        $fileSize = filesize($filePath);
                        $actualFileName = $fileName;
                    }
                }

                // Try 3: Search for any file that starts with the hash
                if (!$fileFound && is_dir($evidencePath)) {
                    $pattern = $evidencePath . $entry->hash . '*';
                    $matches = glob($pattern);

                    if (!empty($matches)) {
                        // Use the first match
                        $filePath = $matches[0];
                        if (is_file($filePath)) {
                            $fileFound = true;
                            $fileSize = filesize($filePath);
                            $actualFileName = basename($filePath);

                            // If multiple matches found, log it
                            if (count($matches) > 1) {
                                error_log("Multiple files found for hash " . $entry->hash . ": " . implode(', ', array_map('basename', $matches)));
                            }
                        }
                    }
                }

                // Update database if file was found
                if ($fileFound) {
                    $updateData = ['file_size' => $fileSize];

                    // If the actual file has an extension but DB doesn't, update it
                    if (empty($entry->extension) && $actualFileName) {
                        $fileParts = pathinfo($actualFileName);
                        if (!empty($fileParts['extension']) && $fileParts['filename'] === $entry->hash) {
                            $updateData['extension'] = $fileParts['extension'];
                            error_log("Updated missing extension for hash " . $entry->hash . " to: " . $fileParts['extension']);
                        }
                    }

                    DB::table('learning_module_evidences')
                        ->where('id', $entry->id)
                        ->update($updateData);

                    $updatedCount++;
                } else {
                    $notFoundCount++;
                    // Log files that don't exist in any format
                    error_log("Evidence file not found in any format for hash: " . $entry->hash . " (ID: " . $entry->id . ")");
                }
            }

            $result = [
                'success' => true,
                'total_entries' => $totalEntries,
                'updated_count' => $updatedCount,
                'not_found_count' => $notFoundCount,
                'message' => "Updated file sizes for $updatedCount entries. $notFoundCount files were not found."
            ];

            $response->getBody()->write(json_encode($result));
            return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            error_log("Error updating file sizes: " . $e->getMessage());
            $response->getBody()->write(json_encode([
                'error' => 'Failed to update file sizes: ' . $e->getMessage()
            ]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'update'));

    // View learning module evidence file endpoint
    $group->get('/learning-module-files/view/{fileId}', function (Request $request, Response $response, array $args) {
        try {
            $fileId = $args['fileId'];

            // Get file details from database
            $file = DB::table('learning_module_evidences')
                ->where('id', $fileId)
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->first();

            if (!$file) {
                $response->getBody()->write(json_encode(['error' => 'File not found in database']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $evidencePath = $privatePath . 'evidence/';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Try to find the file using different naming patterns
            $fileFound = false;
            $fileToView = null;
            $fileName = null;

            // Try 1: hash.extension format (if extension exists)
            if (!empty($file->extension)) {
                $fileName = $file->hash . '.' . $file->extension;
                $fileToView = $evidencePath . $fileName;

                if (is_file($fileToView)) {
                    $fileFound = true;
                }
            }

            // Try 2: just hash without extension
            if (!$fileFound) {
                $fileName = $file->hash;
                $fileToView = $evidencePath . $fileName;

                if (is_file($fileToView)) {
                    $fileFound = true;
                }
            }

            // Try 3: Search for any file that starts with the hash
            if (!$fileFound) {
                $pattern = $evidencePath . $file->hash . '*';
                $matches = glob($pattern);

                if (!empty($matches)) {
                    $fileToView = $matches[0];
                    $fileName = basename($fileToView);
                    $fileFound = true;
                }
            }

            if (!$fileFound) {
                $response->getBody()->write(json_encode(['error' => 'Physical file not found on disk']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Security check - ensure file path is within evidence directory
            $realPath = realpath($fileToView);
            $realEvidencePath = realpath($evidencePath);

            if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Determine content type based on file extension
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            $contentType = 'application/octet-stream'; // Default

            switch ($extension) {
                case 'pdf':
                    $contentType = 'application/pdf';
                    break;
                case 'jpg':
                case 'jpeg':
                    $contentType = 'image/jpeg';
                    break;
                case 'png':
                    $contentType = 'image/png';
                    break;
                case 'gif':
                    $contentType = 'image/gif';
                    break;
                case 'txt':
                    $contentType = 'text/plain';
                    break;
                case 'html':
                case 'htm':
                    $contentType = 'text/html';
                    break;
                case 'doc':
                    $contentType = 'application/msword';
                    break;
                case 'docx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    break;
                case 'xls':
                    $contentType = 'application/vnd.ms-excel';
                    break;
                case 'xlsx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    break;
                case 'mp4':
                    $contentType = 'video/mp4';
                    break;
                case 'mp3':
                    $contentType = 'audio/mpeg';
                    break;
                case 'zip':
                    $contentType = 'application/zip';
                    break;
            }

            // Get file size
            $fileSize = filesize($fileToView);

            // Clear any output buffers to prevent corruption
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Set headers for file viewing
            header('Content-Type: ' . $contentType);
            header('Content-Length: ' . $fileSize);
            header('Content-Disposition: inline; filename="' . addslashes($fileName) . '"');
            header('Cache-Control: private, max-age=3600');
            header('X-Content-Type-Options: nosniff');

            // For binary files, use readfile for better handling
            // Get binary file extensions (images, audio, video, binary documents, archives)
            $binaryExtensions = array_merge(
                \APP\Tools::allowExtensions(['images']),
                \APP\Tools::allowExtensions(['audio']),
                \APP\Tools::allowExtensions(['video']),
                \APP\Tools::allowExtensions(['misc'])
            );

            // Add binary document formats (exclude text-based ones like txt, csv, rtf)
            $binaryDocuments = array_diff(
                \APP\Tools::allowExtensions(['documents']),
                ['txt', 'csv', 'rtf'] // These are text-based and should be streamed
            );
            $binaryExtensions = array_merge($binaryExtensions, $binaryDocuments);

            if (in_array($extension, $binaryExtensions)) {
                readfile($fileToView);
            } else {
                // For text files, stream the content
                $fileHandle = fopen($fileToView, 'rb');
                if ($fileHandle) {
                    while (!feof($fileHandle)) {
                        echo fread($fileHandle, 8192);
                        flush();
                    }
                    fclose($fileHandle);
                }
            }

            exit; // Important: exit to prevent Slim from adding extra output

        } catch (\Exception $e) {
            error_log("Exception in learning module file viewing: " . $e->getMessage());
            $response->getBody()->write(json_encode(['error' => 'Failed to view file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Delete learning module evidence file endpoint
    $group->delete('/learning-module-files/{fileId}', function (Request $request, Response $response, array $args) {
        try {
            $fileId = $args['fileId'];

            // Get file details from database
            $file = DB::table('learning_module_evidences')
                ->where('id', $fileId)
                ->where('evidence_type', 'file')
                ->whereNotNull('hash')
                ->first();

            if (!$file) {
                $response->getBody()->write(json_encode(['error' => 'File not found in database']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $evidencePath = $privatePath . 'evidence/';

            // Check if evidence directory exists
            if (!is_dir($evidencePath)) {
                $response->getBody()->write(json_encode(['error' => 'Evidence directory not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Try to find the file using different naming patterns
            $fileFound = false;
            $fileToDelete = null;
            $fileName = null;

            // Try 1: hash.extension format (if extension exists)
            if (!empty($file->extension)) {
                $fileName = $file->hash . '.' . $file->extension;
                $fileToDelete = $evidencePath . $fileName;

                if (is_file($fileToDelete)) {
                    $fileFound = true;
                }
            }

            // Try 2: just hash without extension
            if (!$fileFound) {
                $fileName = $file->hash;
                $fileToDelete = $evidencePath . $fileName;

                if (is_file($fileToDelete)) {
                    $fileFound = true;
                }
            }

            // Try 3: Search for any file that starts with the hash
            if (!$fileFound) {
                $pattern = $evidencePath . $file->hash . '*';
                $matches = glob($pattern);

                if (!empty($matches)) {
                    $fileToDelete = $matches[0];
                    $fileName = basename($fileToDelete);
                    $fileFound = true;
                }
            }

            if (!$fileFound) {
                // File doesn't exist on disk, but we can still remove the database record
                error_log("Physical file not found for learning module evidence ID {$fileId}, removing database record only");
            }

            // Security check if file exists - ensure file path is within evidence directory
            if ($fileFound) {
                $realPath = realpath($fileToDelete);
                $realEvidencePath = realpath($evidencePath);

                if (!$realPath || !$realEvidencePath || strpos($realPath, $realEvidencePath) !== 0) {
                    $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                    return $response
                        ->withStatus(400)
                        ->withHeader('Content-Type', 'application/json');
                }
            }

            // Begin transaction for data integrity
            DB::beginTransaction();

            try {
                // Delete the database record first
                DB::table('learning_module_evidences')
                    ->where('id', $fileId)
                    ->delete();

                // Delete the physical file if it exists
                if ($fileFound && !unlink($fileToDelete)) {
                    // If physical deletion fails, rollback database deletion
                    DB::rollback();
                    error_log("Failed to delete physical file for learning module evidence ID {$fileId}: {$fileToDelete}");
                    $response->getBody()->write(json_encode(['error' => 'Failed to delete physical file']));
                    return $response
                        ->withStatus(500)
                        ->withHeader('Content-Type', 'application/json');
                }

                // Commit the transaction
                DB::commit();

                $message = $fileFound
                    ? 'File and database record deleted successfully'
                    : 'Database record deleted successfully (physical file was already missing)';

                error_log("Successfully deleted learning module evidence ID {$fileId}" . ($fileFound ? " and file {$fileName}" : " (no physical file)"));

                $response->getBody()->write(json_encode(['success' => true, 'message' => $message]));
                return $response->withHeader('Content-Type', 'application/json');

            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            error_log("Exception in learning module file deletion: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            $response->getBody()->write(json_encode(['error' => 'Failed to delete file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'disable'));

    // ===== FORM FILES ENDPOINTS =====

    // Get orphaned form files
    $group->get('/form-files/orphaned', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $formFilesPath = $privatePath . 'form_files/';

        $orphanedFiles = [];
        $totalOrphanedSize = 0;

        try {
            // Get all file references from database
            // Form files are stored in user_custom_form_values as JSON
            $dbFiles = [];

            // Get all custom form values that might contain files
            $customFormValues = DB::table('user_custom_form_values')
                ->whereNotNull('values')
                ->get();

            // Extract file references from JSON values
            foreach ($customFormValues as $formValue) {
                $values = json_decode($formValue->values, true);
                if (is_array($values)) {
                    foreach ($values as $fieldData) {
                        if (is_array($fieldData)) {
                            foreach ($fieldData as $item) {
                                if (isset($item['dataURL']) && !empty($item['dataURL'])) {
                                    // dataURL contains the actual filename
                                    $dbFiles[] = $item['dataURL'];
                                }
                            }
                        }
                    }
                }
            }

            // Also check user_form_values for simple file references
            $simpleFormValues = DB::table('user_form_values')
                ->where('type', 'file')
                ->whereNotNull('value')
                ->where('value', '!=', '')
                ->pluck('value')
                ->toArray();

            $dbFiles = array_merge($dbFiles, $simpleFormValues);

            // Convert to hash map for faster lookup
            $dbFileMap = array_flip($dbFiles);

            // Scan form_files directory
            if (is_dir($formFilesPath)) {
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($formFilesPath, \RecursiveDirectoryIterator::SKIP_DOTS),
                    \RecursiveIteratorIterator::LEAVES_ONLY
                );

                foreach ($iterator as $file) {
                    if ($file->isFile()) {
                        $fileName = basename($file->getPathname());

                        // Skip system files
                        if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                            continue;
                        }

                        // Check if this file exists in database
                        if (!isset($dbFileMap[$fileName])) {
                            $fileSize = $file->getSize();
                            $orphanedFiles[] = [
                                'file_name' => $fileName,
                                'file_size' => $fileSize,
                                'file_size_formatted' => \APP\Tools::formatBytes($fileSize),
                                'modified_date' => date('Y-m-d H:i:s', $file->getMTime()),
                                'file_type' => pathinfo($fileName, PATHINFO_EXTENSION)
                            ];
                            $totalOrphanedSize += $fileSize;
                        }
                    }
                }
            }

            $result = [
                'orphaned_files' => $orphanedFiles,
                'total_orphaned_count' => count($orphanedFiles),
                'total_orphaned_size' => $totalOrphanedSize,
                'total_orphaned_size_formatted' => \APP\Tools::formatBytes($totalOrphanedSize)
            ];

            $response->getBody()->write(json_encode($result));
            return $response
                ->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            $errorResult = [
                'error' => 'Failed to scan for orphaned form files: ' . $e->getMessage(),
                'orphaned_files' => [],
                'total_orphaned_count' => 0,
                'total_orphaned_size' => 0,
                'total_orphaned_size_formatted' => '0 B'
            ];

            $response->getBody()->write(json_encode($errorResult));
            return $response
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Delete orphaned form file
    $group->delete('/form-files/orphaned/{fileName:.+}', function (Request $request, Response $response, array $args) {
        $privatePath = $this->get('settings')['LMSPrivatePath'];
        $formFilesPath = $privatePath . 'form_files/';
        $fileName = urldecode($args['fileName']);

        try {
            // Skip system files
            if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                $response->getBody()->write(json_encode(['error' => 'Cannot delete system files']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            $fileToDelete = $formFilesPath . $fileName;

            if (!is_file($fileToDelete)) {
                $response->getBody()->write(json_encode(['error' => 'File not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Check if file is truly orphaned
            $isOrphaned = true;

            // Check in user_custom_form_values
            $customFormValues = DB::table('user_custom_form_values')
                ->whereNotNull('values')
                ->where('values', 'like', '%' . $fileName . '%')
                ->first();

            if ($customFormValues) {
                $isOrphaned = false;
            }

            // Check in user_form_values
            if ($isOrphaned) {
                $simpleFormValue = DB::table('user_form_values')
                    ->where('type', 'file')
                    ->where('value', $fileName)
                    ->first();

                if ($simpleFormValue) {
                    $isOrphaned = false;
                }
            }

            if (!$isOrphaned) {
                $response->getBody()->write(json_encode(['error' => 'File is not orphaned - has database record']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Security check
            $realPath = realpath($fileToDelete);
            $realFormFilesPath = realpath($formFilesPath);

            if (!$realPath || !$realFormFilesPath || strpos($realPath, $realFormFilesPath) !== 0) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Delete the file
            if (unlink($fileToDelete)) {
                $response->getBody()->write(json_encode(['success' => true, 'message' => 'File deleted successfully']));
                return $response
                    ->withHeader('Content-Type', 'application/json');
            } else {
                $response->getBody()->write(json_encode(['error' => 'Failed to delete file']));
                return $response
                    ->withStatus(500)
                    ->withHeader('Content-Type', 'application/json');
            }

        } catch (\Exception $e) {
            $response->getBody()->write(json_encode(['error' => 'Failed to delete file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'disable'));

    // View orphaned form file
    $group->get('/form-files/orphaned/view/{fileName:.+}', function (Request $request, Response $response, array $args) {
        try {
            $fileName = urldecode($args['fileName']);
            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $formFilesPath = $privatePath . 'form_files/';

            // Skip system files
            if ($fileName === '.gitignore' || $fileName === '.DS_Store' || $fileName === 'Thumbs.db') {
                $response->getBody()->write(json_encode(['error' => 'Cannot view system files']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            $fileToView = $formFilesPath . $fileName;

            if (!is_file($fileToView)) {
                $response->getBody()->write(json_encode(['error' => 'File not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Security check
            $realPath = realpath($fileToView);
            $realFormFilesPath = realpath($formFilesPath);

            if (!$realPath || !$realFormFilesPath || strpos($realPath, $realFormFilesPath) !== 0) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Determine content type
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            $contentType = 'application/octet-stream'; // Default

            switch ($extension) {
                case 'pdf':
                    $contentType = 'application/pdf';
                    break;
                case 'jpg':
                case 'jpeg':
                    $contentType = 'image/jpeg';
                    break;
                case 'png':
                    $contentType = 'image/png';
                    break;
                case 'gif':
                    $contentType = 'image/gif';
                    break;
                case 'txt':
                    $contentType = 'text/plain';
                    break;
                case 'doc':
                    $contentType = 'application/msword';
                    break;
                case 'docx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    break;
                case 'xls':
                    $contentType = 'application/vnd.ms-excel';
                    break;
                case 'xlsx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    break;
                case 'zip':
                    $contentType = 'application/zip';
                    break;
            }

            // Get file size
            $fileSize = filesize($fileToView);

            // Clear output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Set headers
            header('Content-Type: ' . $contentType);
            header('Content-Length: ' . $fileSize);
            header('Content-Disposition: inline; filename="' . addslashes($fileName) . '"');
            header('Cache-Control: private, max-age=3600');
            header('X-Content-Type-Options: nosniff');

            // Output file
            readfile($fileToView);
            exit;

        } catch (\Exception $e) {
            $response->getBody()->write(json_encode(['error' => 'Failed to view file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // List form files with database records
    $group->post('/form-files/list', function (Request $request, Response $response, array $args) {
        $params = $request->getParsedBody();

        // Handle both updateReport format and direct SmartTable format
        if (isset($params['page']) && isset($params['nPage'])) {
            // updateReport format - convert to SmartTable format
            $page = max(1, intval($params['page']));
            $perPage = max(1, intval($params['nPage']));
            $start = ($page - 1) * $perPage;

            $convertedParams = [
                'pagination' => [
                    'start' => $start,
                    'number' => $perPage
                ],
                'sort' => $params['sort'] ?? ['predicate' => 'created_at', 'reverse' => true],
                'search' => $params['search'] ?? []
            ];
            $params = $convertedParams;
        }

        // Remove refresh parameter
        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        try {
            // Build query for form files
            // Note: Form files are stored differently - in JSON within user_custom_form_values
            // We need to extract and flatten this data for the table

            $formFiles = [];

            // Get all custom form values with potential files - using correct table structure
            // Join with user_forms to get the actual user who uploaded the files
            $customFormValues = DB::table('user_custom_form_values')
                ->select([
                    'user_custom_form_values.id',
                    'user_custom_form_values.form_id',
                    'user_custom_form_values.values',
                    'user_custom_form_values.created_at',
                    'user_forms.form_id as form_id',
                    'user_forms.id as user_form_id',
                    'user_forms.user_form_status as user_form_status',
                    // 'forms.name as form_name',
                    DB::raw('COALESCE(forms.name, "Custom Form") as form_name'),
                    'user_forms.user_id',
                    DB::raw('CONCAT(users.fname, " ", users.lname) as uploaded_by')
                ])
                ->leftJoin('user_forms', 'user_forms.user_custom_form_value_id', '=', 'user_custom_form_values.id')
                ->leftJoin('forms', 'user_forms.form_id', '=', 'forms.id')
                ->leftJoin('users', 'user_forms.user_id', '=', 'users.id')
                ->whereNotNull('user_custom_form_values.values')
                ->where(function($query) {
                    // Look for dataURL indicator (primary file format)
                    $query->where('user_custom_form_values.values', 'like', '%dataURL%');

                    // Add dynamic extension checks using centralized allowExtensions
                    $extensions = \APP\Tools::allowExtensions();
                    foreach ($extensions as $ext) {
                        $query->orWhere('user_custom_form_values.values', 'like', '%.' . $ext . '%');
                    }
                })
                ->get();

            // Extract individual files from JSON - handle different structures
            foreach ($customFormValues as $formValue) {
                $values = json_decode($formValue->values, true);
                if (is_array($values)) {
                    foreach ($values as $fieldKey => $fieldData) {
                        // Handle different data structures
                        if (is_array($fieldData)) {
                            // Array of items (multiple files or file objects)
                            foreach ($fieldData as $itemIndex => $item) {
                                $fileName = null;
                                $originalName = null;

                                // Handle object with dataURL (new format)
                                if (is_array($item) && isset($item['dataURL']) && !empty($item['dataURL'])) {
                                    $fileName = $item['dataURL'];
                                    $originalName = $item['name'] ?? $fileName;
                                    // Use pre-calculated file_size if available, otherwise fallback to file system check
                                    $fileSize = isset($item['file_size']) ? intval($item['file_size']) :
                                        (file_exists($this->get('settings')['LMSPrivatePath'] . 'form_files/' . $fileName) ? filesize($this->get('settings')['LMSPrivatePath'] . 'form_files/' . $fileName) : 0);
                                }
                                // Handle string that looks like a filename
                                elseif (is_string($item) && looksLikeFileName($item)) {
                                    $fileName = $item;
                                    $originalName = $item;
                                    // For string filenames, we still need to check file system as there's no place to store file_size
                                    $fileSize = file_exists($this->get('settings')['LMSPrivatePath'] . 'form_files/' . $fileName) ? filesize($this->get('settings')['LMSPrivatePath'] . 'form_files/' . $fileName) : 0;
                                }

                                if ($fileName) {

                                    $formFiles[] = [
                                        'id' => $formValue->id . '_' . $fieldKey . '_' . $itemIndex . '_' . $fileName,
                                        'file_name' => $originalName,
                                        'form_id' => $formValue->form_id,
                                        'user_form_id' => $formValue->user_form_id,
                                        'user_form_status' => $formValue->user_form_status,
                                        'file_path' => $fileName,
                                        'file_size' => $fileSize,
                                        'file_type' => pathinfo($originalName, PATHINFO_EXTENSION),
                                        'form_name' => $formValue->form_name,
                                        'field_name' => $fieldKey,
                                        'uploaded_by' => $formValue->uploaded_by ?: 'Unknown',
                                        'user_id' => $formValue->user_id,
                                        'created_at' => $formValue->created_at,
                                        'user_custom_form_value_id' => $formValue->id,
                                        'field_key' => $fieldKey,
                                        'item_index' => $itemIndex
                                    ];
                                }
                            }
                        }
                        // Handle single string value that looks like a filename
                        elseif (is_string($fieldData) && looksLikeFileName($fieldData)) {
                            // For string filenames, we still need to check file system as there's no place to store file_size
                            $filePath = $this->get('settings')['LMSPrivatePath'] . 'form_files/' . $fieldData;
                            $fileSize = file_exists($filePath) ? filesize($filePath) : 0;

                            $formFiles[] = [
                                'id' => $formValue->id . '_' . $fieldKey . '_0_' . $fieldData,
                                'file_name' => $fieldData,
                                'file_path' => $fieldData,
                                'file_size' => $fileSize,
                                'file_type' => pathinfo($fieldData, PATHINFO_EXTENSION),
                                'form_name' => $formValue->form_name,
                                'field_name' => $fieldKey,
                                'uploaded_by' => $formValue->uploaded_by ?: 'Unknown',
                                'user_id' => $formValue->user_id,
                                'created_at' => $formValue->created_at,
                                'user_custom_form_value_id' => $formValue->id,
                                'field_key' => $fieldKey,
                                'item_index' => 0
                            ];
                        }
                    }
                }
            }

            // Apply search filters if provided
            if (!empty($params['search'])) {
                $formFiles = array_filter($formFiles, function($file) use ($params) {
                    foreach ($params['search'] as $field => $searchValue) {
                        if (!empty($searchValue)) {
                            $fieldMap = [
                                'file_name' => 'file_name',
                                'form_name' => 'form_name',
                                'field_name' => 'field_name',
                                'uploaded_by' => 'uploaded_by'
                            ];

                            if (isset($fieldMap[$field]) && isset($file[$fieldMap[$field]])) {
                                if (stripos($file[$fieldMap[$field]], $searchValue) === false) {
                                    return false;
                                }
                            }
                        }
                    }
                    return true;
                });
            }

            // Apply sorting
            $sortField = $params['sort']['predicate'] ?? 'created_at';
            $sortReverse = $params['sort']['reverse'] ?? true;

            usort($formFiles, function($a, $b) use ($sortField, $sortReverse) {
                $result = 0;
                if (isset($a[$sortField]) && isset($b[$sortField])) {
                    if (is_numeric($a[$sortField])) {
                        $result = $a[$sortField] - $b[$sortField];
                    } else {
                        $result = strcasecmp($a[$sortField], $b[$sortField]);
                    }
                }
                return $sortReverse ? -$result : $result;
            });

            // Apply pagination
            $page = isset($params['pagination']['start']) ? intval($params['pagination']['start']) : 0;
            $perPage = isset($params['pagination']['number']) ? intval($params['pagination']['number']) : 25;
            $total = count($formFiles);

            $paginatedFiles = array_slice($formFiles, $page, $perPage);

            // Calculate pagination values
            $totalPages = ceil($total / $perPage);
            $currentPage = floor($page / $perPage) + 1;
            $from = $total > 0 ? $page + 1 : null;
            $to = min($page + $perPage, $total);

            // Return Laravel paginator format to match Evidence API
            $result = [
                'current_page' => $currentPage,
                'data' => $paginatedFiles,
                'first_page_url' => '?page=1',
                'from' => $from,
                'last_page' => $totalPages,
                'last_page_url' => $totalPages > 1 ? "?page={$totalPages}" : null,
                'next_page_url' => $currentPage < $totalPages ? "?page=" . ($currentPage + 1) : null,
                'path' => '/disk-space/form-files/list',
                'per_page' => $perPage,
                'prev_page_url' => $currentPage > 1 ? "?page=" . ($currentPage - 1) : null,
                'to' => $to,
                'total' => $total,
                // Keep these for backward compatibility
                'numberOfPages' => $totalPages
            ];

            $response->getBody()->write(json_encode($result));
            return $response
                ->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            $emptyResult = [
                'data' => [],
                'total' => 0,
                'numberOfPages' => 0,
                'error' => $e->getMessage()
            ];

            $response->getBody()->write(json_encode($emptyResult));
            return $response
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // View form file
    $group->get('/form-files/view/{fileId}', function (Request $request, Response $response, array $args) {
        try {
            $fileId = $args['fileId'];

            // Parse the composite ID (format: customFormValueId_fieldKey_itemIndex_fileName)
            $parts = explode('_', $fileId, 4);
            if (count($parts) < 4) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file ID format']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            $customFormValueId = $parts[0];
            $fieldKey = $parts[1];
            $itemIndex = intval($parts[2]);
            $fileName = $parts[3];

            // Verify the file exists in database
            $formValue = DB::table('user_custom_form_values')
                ->where('id', $customFormValueId)
                ->first();

            if (!$formValue) {
                $response->getBody()->write(json_encode(['error' => 'Form value not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $values = json_decode($formValue->values, true);
            $fileFound = false;
            $originalFileName = $fileName;

            if (is_array($values) && isset($values[$fieldKey])) {
                $fieldData = $values[$fieldKey];

                if (is_array($fieldData) && isset($fieldData[$itemIndex])) {
                    $item = $fieldData[$itemIndex];

                    // Check if it's an object with dataURL
                    if (is_array($item) && isset($item['dataURL']) && $item['dataURL'] === $fileName) {
                        $fileFound = true;
                        $originalFileName = $item['name'] ?? $fileName;
                    }
                    // Check if it's a direct string match
                    elseif (is_string($item) && $item === $fileName) {
                        $fileFound = true;
                        $originalFileName = $fileName;
                    }
                }
                // Handle case where field data is a single string
                elseif (is_string($fieldData) && $fieldData === $fileName && $itemIndex == 0) {
                    $fileFound = true;
                    $originalFileName = $fileName;
                }
            }

            if (!$fileFound) {
                $response->getBody()->write(json_encode(['error' => 'File reference not found in database']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $filePath = $privatePath . 'form_files/' . $fileName;

            if (!file_exists($filePath)) {
                $response->getBody()->write(json_encode(['error' => 'Physical file not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Security check
            $realPath = realpath($filePath);
            $realFormFilesPath = realpath($privatePath . 'form_files/');

            if (!$realPath || !$realFormFilesPath || strpos($realPath, $realFormFilesPath) !== 0) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            // Determine content type based on file extension
            $extension = strtolower(pathinfo($originalFileName, PATHINFO_EXTENSION));
            $contentType = 'application/octet-stream'; // Default

            switch ($extension) {
                case 'pdf':
                    $contentType = 'application/pdf';
                    break;
                case 'jpg':
                case 'jpeg':
                    $contentType = 'image/jpeg';
                    break;
                case 'png':
                    $contentType = 'image/png';
                    break;
                case 'gif':
                    $contentType = 'image/gif';
                    break;
                case 'txt':
                case 'csv':
                    $contentType = 'text/plain';
                    break;
                case 'mp4':
                    $contentType = 'video/mp4';
                    break;
                case 'mp3':
                    $contentType = 'audio/mpeg';
                    break;
                case 'm4a':
                    $contentType = 'audio/mp4';
                    break;
                case 'docx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    break;
                case 'doc':
                    $contentType = 'application/msword';
                    break;
                case 'xlsx':
                    $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    break;
                case 'xls':
                    $contentType = 'application/vnd.ms-excel';
                    break;
                case 'zip':
                    $contentType = 'application/zip';
                    break;
                default:
                    $contentType = 'application/octet-stream';
                    break;
            }

            // Get file size
            $fileSize = filesize($filePath);

            // Clear output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Set headers
            header('Content-Type: ' . $contentType);
            header('Content-Length: ' . $fileSize);
            header('Content-Disposition: inline; filename="' . addslashes($originalFileName) . '"');
            header('Cache-Control: private, max-age=3600');
            header('X-Content-Type-Options: nosniff');

            // Output file
            readfile($filePath);
            exit;

        } catch (\Exception $e) {
            $response->getBody()->write(json_encode(['error' => 'Failed to view file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'select'));

    // Delete form file
    $group->delete('/form-files/{fileId}', function (Request $request, Response $response, array $args) {
        try {
            $fileId = $args['fileId'];

            // Parse the composite ID
            $parts = explode('_', $fileId, 4);
            if (count($parts) < 4) {
                $response->getBody()->write(json_encode(['error' => 'Invalid file ID format']));
                return $response
                    ->withStatus(400)
                    ->withHeader('Content-Type', 'application/json');
            }

            $customFormValueId = $parts[0];
            $fieldKey = $parts[1];
            $itemIndex = intval($parts[2]);
            $fileName = $parts[3];

            // Get the form value record
            $formValue = DB::table('user_custom_form_values')
                ->where('id', $customFormValueId)
                ->first();

            if (!$formValue) {
                $response->getBody()->write(json_encode(['error' => 'Form value not found']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $values = json_decode($formValue->values, true);
            $fileFound = false;

            if (is_array($values) && isset($values[$fieldKey])) {
                $fieldData = $values[$fieldKey];

                if (is_array($fieldData) && isset($fieldData[$itemIndex])) {
                    $item = $fieldData[$itemIndex];

                    // Check if it's an object with dataURL
                    if (is_array($item) && isset($item['dataURL']) && $item['dataURL'] === $fileName) {
                        $fileFound = true;
                    }
                    // Check if it's a direct string match
                    elseif (is_string($item) && $item === $fileName) {
                        $fileFound = true;
                    }
                }
                // Handle case where field data is a single string
                elseif (is_string($fieldData) && $fieldData === $fileName && $itemIndex == 0) {
                    $fileFound = true;
                }
            }

            if (!$fileFound) {
                $response->getBody()->write(json_encode(['error' => 'File reference not found in database']));
                return $response
                    ->withStatus(404)
                    ->withHeader('Content-Type', 'application/json');
            }

            $privatePath = $this->get('settings')['LMSPrivatePath'];
            $filePath = $privatePath . 'form_files/' . $fileName;

            // Security check
            if (file_exists($filePath)) {
                $realPath = realpath($filePath);
                $realFormFilesPath = realpath($privatePath . 'form_files/');

                if (!$realPath || !$realFormFilesPath || strpos($realPath, $realFormFilesPath) !== 0) {
                    $response->getBody()->write(json_encode(['error' => 'Invalid file path']));
                    return $response
                        ->withStatus(400)
                        ->withHeader('Content-Type', 'application/json');
                }
            }

            // Begin transaction
            DB::beginTransaction();

            try {
                // Remove file reference from database
                if (is_array($values[$fieldKey]) && isset($values[$fieldKey][$itemIndex])) {
                    unset($values[$fieldKey][$itemIndex]);
                    $values[$fieldKey] = array_values($values[$fieldKey]); // Re-index array

                    // If field is now empty, remove it
                    if (empty($values[$fieldKey])) {
                        unset($values[$fieldKey]);
                    }
                }
                // Handle single string value
                elseif (is_string($values[$fieldKey]) && $values[$fieldKey] === $fileName && $itemIndex == 0) {
                    unset($values[$fieldKey]);
                }

                // Update database
                DB::table('user_custom_form_values')
                    ->where('id', $customFormValueId)
                    ->update(['values' => json_encode($values)]);

                // Delete physical file if it exists
                if (file_exists($filePath)) {
                    if (!unlink($filePath)) {
                        DB::rollback();
                        $response->getBody()->write(json_encode(['error' => 'Failed to delete physical file']));
                        return $response
                            ->withStatus(500)
                            ->withHeader('Content-Type', 'application/json');
                    }
                }

                DB::commit();

                $response->getBody()->write(json_encode(['success' => true, 'message' => 'File deleted successfully']));
                return $response
                    ->withHeader('Content-Type', 'application/json');

            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            $response->getBody()->write(json_encode(['error' => 'Failed to delete file: ' . $e->getMessage()]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'disable'));

    // Update form file sizes endpoint - similar to Evidence files
    $group->post('/update-form-file-sizes', function (Request $request, Response $response, array $args) {
        try {
            $formFilesPath = $this->get('settings')['LMSPrivatePath'] . 'form_files/';

            // Initialize counters
            $processedCount = 0;
            $updatedCount = 0;
            $notFoundCount = 0;

            // Get all custom form values that might contain files
            $customFormValues = DB::table('user_custom_form_values')
                ->select(['id', 'values'])
                ->whereNotNull('values')
                ->where(function($query) {
                    // Look for dataURL indicator (primary file format)
                    $query->where('values', 'like', '%dataURL%');

                    // Add dynamic extension checks using centralized allowExtensions
                    $extensions = \APP\Tools::allowExtensions();
                    foreach ($extensions as $ext) {
                        $query->orWhere('values', 'like', '%.' . $ext . '%');
                    }
                })
                ->get();

            foreach ($customFormValues as $formValue) {
                $values = json_decode($formValue->values, true);
                $updated = false;

                if (!is_array($values)) {
                    continue;
                }

                $processedCount++;

                foreach ($values as $fieldKey => &$fieldData) {
                    if (is_array($fieldData)) {
                        // Handle array of items (multiple files or file objects)
                        foreach ($fieldData as $itemIndex => &$item) {
                            if (is_array($item) && isset($item['dataURL']) && !empty($item['dataURL'])) {
                                // File object with dataURL
                                $fileName = $item['dataURL'];

                                // Only update if file_size is missing or 0
                                if (!isset($item['file_size']) || $item['file_size'] == 0) {
                                    $filePath = $formFilesPath . $fileName;
                                    if (file_exists($filePath)) {
                                        $fileSize = filesize($filePath);
                                        $item['file_size'] = $fileSize;
                                        $updated = true;
                                        $updatedCount++;
                                    } else {
                                        // File doesn't exist, set to 0
                                        $item['file_size'] = 0;
                                        $updated = true;
                                        $notFoundCount++;
                                    }
                                }
                            }
                        }
                    }
                }

                // Update the database record if any files were processed
                if ($updated) {
                    DB::table('user_custom_form_values')
                        ->where('id', $formValue->id)
                        ->update(['values' => json_encode($values)]);
                }
            }

            $result = [
                'success' => true,
                'processed_count' => $processedCount,
                'updated_count' => $updatedCount,
                'not_found_count' => $notFoundCount,
                'message' => "Updated file sizes for $updatedCount files. $notFoundCount files were not found on disk."
            ];

            $response->getBody()->write(json_encode($result));
            return $response
                ->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            error_log("Error updating form file sizes: " . $e->getMessage());
            $response->getBody()->write(json_encode([
                    'error' => 'Failed to update form file sizes: ' . $e->getMessage()
                ]));
            return $response
                ->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }
    })->add(\APP\Auth::getStructureAccessCheck(['system-setup-audit-disk-space'], 'update'));

});