<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/cron",  function ($group) {


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$configuration = \Models\Cron::find($args["id"]);
		$configuration->status = 0;
		$configuration->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-cron-jobs', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$configuration = \Models\Cron::find($args["id"]);
		$configuration->status = 1;
		$configuration->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-cron-jobs', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$configuration = \Models\Cron::find($args["id"]);

		$response->getBody()->write(json_encode($configuration));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-cron-jobs', 'select'));


	$group->post('/list', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Cron::where("id", ">", "0");

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');



	})->add(\APP\Auth::getStructureAccessCheck('system-setup-cron-jobs', 'select'));

	// Force Run
	$group->get('/run-now/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$cron = \Models\Cron::find($args['id']);
		if ($cron->locked) {
			return \APP\Tools::returnCode($request, $response, 403, 'Cron task is already running!');
		}
		$cron->force_run = true;
		$cron->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-cron-jobs', 'select'));
});