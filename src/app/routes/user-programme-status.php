<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;

use Slim\Routing\RouteContext;
use Upload\Storage\FileSystem;
use Upload\File;
use Upload\Validation\Size;
use Upload\Validation\Mimetype;
use Upload\Validation\Extension;
use Models\UserCustomProgrammeStatus;
use APP\Tools;
use APP\Auth;

$app->group("/user-programme-status",  function ($group) {

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$custom_programme_status = \Models\UserCustomProgrammeStatus::find($args["id"]);

		$response->getBody()->write(json_encode($custom_programme_status));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'select'));

	$group->post('/{user_id:[0-9]+}/new', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$custom_programme_status = new \Models\UserCustomProgrammeStatus;
		$fields = [
			"custom_programme_status_id", "notes"
		];

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$custom_programme_status->$field = $data[$field]; // != '---' ? $data[$field] : null;
			}

		}

		$custom_programme_status->status = 1;
		$custom_programme_status->user_id = $args["user_id"];
		$custom_programme_status->created_by = \APP\Auth::getUserId();
		$custom_programme_status->is_active = 1;
		$custom_programme_status->updated_by = \APP\Auth::getUserId();
		$update_custom_programme_status = \Models\UserCustomProgrammeStatus::where([['user_id',$args["user_id"]], ['is_active',1]])->first();
		if($update_custom_programme_status){
			$update_custom_programme_status->is_active = 0;
			$update_custom_programme_status->save();
		}

		$custom_programme_status->save();

		$response->getBody()->write("" . $custom_programme_status->id);
		return $response;

		//return $response->write($custom_programme_status->id);

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'insert'));

	// Update competency
	$group->POST('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$custom_programme_status = \Models\UserCustomProgrammeStatus::find($args["id"]);

		 $fields = [
			"name", "description", "order"
		];

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$custom_programme_status->$field = $data[$field]; // != '---' ? $data[$field] : null;
			} else {
				if ($field == 'badge') {
					$custom_programme_status->$field = null;
				}
			}
		}

		$custom_programme_status->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'update'));

	$group->get('/{id:[0-9]+}/download-file/{hash:[0-9a-z_]+}', function (Request $request, Response $response, $args)
	{
		if ($this->get('settings')["DisableFileDownload"]) return \APP\Tools::returnCode($request, $response, '403');

		session_write_close();

		$custom_programme_status = \Models\UserCustomProgrammeStatus::find($args["id"]);

		$attachment = null;

		if ($custom_programme_status && $custom_programme_status->attachments != null) foreach(json_decode($custom_programme_status->attachments, true) as $att)
		{
			if ($att['hash'] == $args["hash"]) $attachment = $att;
		}

		if ($attachment)
		{
			$filename = $this->get('settings')["LMSFilePath"] . $attachment['hash'] . '.' . $attachment['extension'];

			if (is_file($filename))
			{
				if (
					\APP\Auth::isManagerOf($custom_programme_status->user_id)
					|| \APP\Auth::isAdmin()
					|| \APP\Auth::accessAllLearners()
					|| $custom_programme_status->user_id == \APP\Auth::getUserId()
					|| $custom_programme_status->created_by == \APP\Auth::getUserId()
				) {
					$fileStream = new OpenStream($filename, 'r');
					$finfo = finfo_open(FILEINFO_MIME_TYPE);
					$response = $response
						->withHeader('Content-Length', filesize($filename))
						->withHeader('Content-Disposition', "inline; filename=\"".$attachment['name']."\"")
						->withHeader('Content-Type', finfo_file($finfo, $filename))
						->withBody($fileStream);
				}
				else $response = $response->withStatus(403);
			}
			else $response = $response->withStatus(404);
		}
		else $response = $response->withStatus(404);

		return $response;

	})->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));



	$group->post('/{id:[0-9]+}/add-file', function (Request $request, Response $response, array $args): Response {
		$data = $request->getParsedBody();
		$custom_programme_status = UserCustomProgrammeStatus::find($args["id"]);

		if (
			$custom_programme_status &&
			isset($_FILES['file']) &&
			$_FILES['file']['name']
		) {
			$settings = $this->get('settings')["LMSFilePath"]; // Replace with appropriate DI container usage
			$storage = new FileSystem($settings, true);
			$customProgStatFile = new File('file', $storage);

			$hash = bin2hex(random_bytes(16));
			$file_name = $customProgStatFile->getNameWithExtension();
			$extension = $customProgStatFile->getExtension();
			$customProgStatFile->setName($hash);

			$fileSizeValidation = new Size('800M');
			$fileTypeValidation = new Mimetype(Tools::documentMime());
			$fileExtValidation = new Extension(Tools::allowExtensions());

			$fileTypeValidation->setMessage("Invalid file type: " . $customProgStatFile->getMimetype());
			$customProgStatFile->addValidations([$fileTypeValidation, $fileSizeValidation, $fileExtValidation]);

			try {
				$customProgStatFile->upload();
				$response->getBody()->write($customProgStatFile->getNameWithExtension());

				$attachments = $custom_programme_status->attachments ? json_decode($custom_programme_status->attachments, true) : [];
				$attachments[] = [
					"name" => $file_name,
					"hash" => $hash,
					"extension" => $extension,
				];

				$custom_programme_status->attachments = json_encode($attachments);
				$custom_programme_status->save();
			} catch (\Upload\Exception\UploadException $e) {
				$errors = implode("\n", $customProgStatFile->getErrors());
				$response->getBody()->write($errors);
				return $response->withStatus(500)->withHeader('Content-Type', 'text/html');
			}
		} else {
			return Tools::returnCode($request, $response, '403');
		}

		return $response;
	})->add(Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'update'));


	$group->get('/all', function (Request $request, Response $response) {
		$data = \Models\UserCustomProgrammeStatus::where("status",">",0)->get();
		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'select'));



	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\UserCustomProgrammeStatus::where("id", ">", "0");

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name",
				// "Order" =>"order",
				"Description" => "description",
			];


			$download_file_name = uniqid("competencies.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'select'));

    $group->delete('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $custom_programme_status = \Models\UserCustomProgrammeStatus::find($args["id"]);
        $deleteStatus = $custom_programme_status->delete();
        $custom_programme_status->deleted_by = \APP\Auth::getUserId();
        $custom_programme_status->save();
        return
            $response
                ->withHeader('Content-Type', 'application/json')
                ->write(json_encode($deleteStatus))
            ;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-programme-statuses', 'update'));

});