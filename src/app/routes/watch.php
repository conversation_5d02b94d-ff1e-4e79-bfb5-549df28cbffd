<?php

use APP\Form;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/watch",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\Watch::find($args["id"]);
		$entry->status = 0;
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-watch', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$watch = \Models\Watch::find($args["id"]);
		$watch->status = 1;
		$watch->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-watch', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$watch = \Models\Watch::find($args["id"]);

      $response->getBody()->write(json_encode($watch));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-watch', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$watch = new \Models\Watch;
		$watch->name = $data["name"];
		$watch->status = 1;
		$watch->save();
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-watch', 'insert'));

	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$watch = \Models\Watch::find($args["id"]);

		$watch->name = $data["name"];
		$watch->save();
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-watch', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$data = \Models\Watch
			::where("status",">",0)
			->get()
		;

      $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-watch', 'select'));

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$query = \Models\Watch::where("id", ">", "0");

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Watch Name" => "name",
			];


			$download_file_name = uniqid("watch.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

	      $response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');

		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

	   $response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-watch', 'select'));
});
