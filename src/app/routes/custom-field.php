<?php

use APP\Auth;
use APP\Controllers\CustomReportController;
use APP\Form;
use APP\Templates;
use Models\UserFormValue;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Field;
use Models\FieldCategory;
use Slim\Http\Request as HttpRequest;
use Slim\Http\Response as HttpResponse;

$app->group("/custom-field", function ($group) {
    $group->post('/list', function (Request $request, Response $response) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        $query= \Models\Field::where('custom', 1)->select("fields.*","field_category.name AS field_category_name")
                ->leftJoin("field_category","field_category_id","=","field_category.id")
        ;

        $data = \APP\SmartTable::searchPaginate($params, $query, false);
        $data = $data->toArray();
        $data['data']=array_map(function($value){
          $value['field_category_name'] = Templates::translateOrReplace($value['field_category_name']);
          return $value;
        },$data['data']);
        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'select'));
    $group->get('/get-field/{category_slug}/[{user_id}]', function (Request $request, HttpResponse $response, $args) {
        $category = FieldCategory::where('slug', $args['category_slug'])->first();
        if (!$category) {
            return $response->withStatus(404, 'Not Found');
        }

        $fields = Field::enabled()->custom()->with('FieldOption')->where('field_category_id', $category->id)->get();

        $response->getBody()->write(json_encode($fields));
		return $response->withHeader('Content-Type', 'application/json');

        ;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));
    $group->put('/{id}', function (Request $request, Response $response, $args) {
        try {
            $field = Field::where('id', $args['id'])->first();

            $params = $request->getParsedBody();
            $field->name=$params['name'];
            $entry = Field::where('id','!=',$field->id)->where('name', $params['name'])
                ->first()
            ;
            if ($entry) {
                $errorData = ['error' => 'This Field already exists, please choose a different name.'];
                $response->getBody()->write(json_encode($errorData));
                return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
            }
            $field->field_category_id=$params['field_category_id'];
            if($field->field_category_id==1)
            {
                $field->display_type=$params['user_role'];
            }
            elseif($field->field_category_id==2){
                $field->display_type=$params['event_type'];
            }elseif($field->field_category_id==3){
                $field->display_type=$params['program_type'];
            }
            $field->type=$params['type'];
            $field->save();
            \Models\FieldOption::where('field_id', $field->id)->delete();
            if ($params['values']) {
                foreach ($params['values'] as $value) {
                    $field_option=new \Models\FieldOption();
                    $field_option->field_id=$field->id;
                    $field_option->key=$value['key'];
                    $field_option->value=$value['value'];
                    $field_option->save();
                }
            }
            $response->getBody()->write(json_encode(['success' => true]));
            return $response->withHeader('Content-Type', 'application/json')->withStatus(200);

        } catch (Exception $exception) {
            return \APP\Tools::returnCode($request, $response, 500, $exception->getMessage());
        }
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'insert'));
    $group->post('/new', function (Request $request, Response $response) {
        try {
            $params = $request->getParsedBody();
            $slug = \APP\Tools::safeName($params['name']);
            $slug = CustomReportController::fixSlug($slug);
            $entry = Field::where('name', $params['name'])
                ->first()
            ;
            if ($entry) {
                $errorData = ['error' => 'This Field already exists, please choose a different name.'];
                $response->getBody()->write(json_encode($errorData));
                return $response->withHeader('Content-Type', 'application/json')->withStatus(500);
            }
            $field=new Field();
            $field->name=$params['name'];
            $field->slug=$slug;
            $field->field_category_id=$params['field_category_id'];
            if($field->field_category_id==1)
            {
                $field->display_type=$params['user_role'];
            }
            elseif($field->field_category_id==2){
                $field->display_type=$params['event_type'];
            }elseif($field->field_category_id==3){
                $field->display_type=$params['program_type'];
            }else
            {
                $field->display_type=[];
            }
            $field->type=$params['type'];
            $field->option_group="Custom";
            $field->custom=1;
            $field->save();

            if ($params['values']) {
                foreach ($params['values'] as $value) {
                    $field_option=new \Models\FieldOption();
                    $field_option->field_id=$field->id;
                    $field_option->key=$value['key'];
                    $field_option->value=$value['value'];
                    $field_option->save();
                }
            }
            $response->getBody()->write(json_encode(['id' => $field->id]));
		    return $response->withHeader('Content-Type', 'application/json')->withStatus(200);

        } catch (Exception $exception) {
            return \APP\Tools::returnCode($request, $response, 500, $exception->getMessage());
        }
    });
    $group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $field = Field::with('FieldOption')->find($args["id"]);

        $response->getBody()->write(json_encode($field));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'select'));
    $group->get('/field_display/{slug}/[{type_id}]', function (Request  $request, Response  $response, $args) {
        $slug=$args['slug'];
        $type_id=isset($args['type_id']) ? $args['type_id'] : 0;
        $user_id=Auth::getUserId();
        $category=\Models\FieldCategory::where('slug', $slug)->first();
        if (!$category) {
            return $response->withStatus(400)->write('Bad Request');
        }
        $fields =Field::enabled()->with('FieldOption')->where('field_category_id', $category->id)
    ->custom()
    ->with(['CustomFieldValues'=>function ($query) use ($type_id, $slug, $user_id) {
        $query->where('type_id', $type_id)->where('type', $slug);
        if($slug=="event")
        {
         $custom_value_check= \Models\CustomFieldValue::where('user_id',$user_id)->where('type_id',$type_id)->where('type','event')->first();
          if($custom_value_check && !\APP\Auth::isManager() )
          {
          $query->where('user_id',$user_id);
          }
          else
          {
          $query->where('user_id',0);
          }
        }

    }])
            ->get();
        $response
            ->getBody()
            ->write(json_encode($fields))
        ;
		return $response;

    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-modules'], 'select'));
    $group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $form_type =Field::find($args["id"]);
        $form_type->status = 0;
        $form_type->save();
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'disable'));

    $group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $form_type = Field::find($args["id"]);
        $form_type->status = 1;
        $form_type->save();

        return $response;
  })->add(\APP\Auth::getStructureAccessCheck('system-setup-custom-learning-resources-types', 'disable'));

  $group->post('/save-file',function(HttpRequest $request,HttpResponse $response){
      dd($request->getUploadedFiles(),"data");
  })->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

  $group->post('/default_value',function(HttpRequest $request,HttpResponse $response){
      $data = $request->getParsedBody();
      $defaultValue  = Form::fetchFromSqlDefaultValue($data['user_id'],$data['custom_field_query']['id'],$data['custom_field_query']['form_field_id'],$data['user_form_id'],true,$data['data']);
      $response->getBody()->write($defaultValue);
  })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources','trainee-standards'], 'select'));

  $group->get('/field-options/{slug}',function(Request $request,Response $response,$args){
      $field =  Field::where('slug',$args['slug'])->with('FieldOption')->first();
      if(!$field){
          return $response->withStatus(404);
      }
        $response
        ->getBody()
        ->write($field->FieldOption->toJson())
    ;
      return $response->withHeader('Content-Type', 'application/json');
  })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources','trainee-standards'], 'select'));
});
