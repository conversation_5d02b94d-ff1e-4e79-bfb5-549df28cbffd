<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/evidence-type",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$evidence_type = \Models\EvidenceType::find($args["id"]);

		$response->getBody()->write(json_encode($evidence_type));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->post("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$evidence_type = \Models\EvidenceType::find($args["id"]);
		$data = $request->getParsedBody();

		$fields = [
			"name",
			"perishable",
			"life_of_data",
			"life_after",
		];

		\APP\Tools::setObjectFields($evidence_type, $fields, $data, false, true);

		$evidence_type->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-evidence-types', 'update'));

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$evidence_type = \Models\EvidenceType::find($args["id"]);
		$evidence_type->status = 0;
		$evidence_type->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-evidence-types', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$evidence_type = \Models\EvidenceType::find($args["id"]);
		$evidence_type->status = 1;
		$evidence_type->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-evidence-types', 'disable'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$query = \Models\EvidenceType
			::where("status", true)
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$evidence_type = new \Models\EvidenceType;

		$fields = [
			"name",
			"perishable",
			"life_of_data",
			"life_after"
		];

		\APP\Tools::setObjectFields($evidence_type, $fields, $data, false, true);

		$evidence_type->status = 1;
		$evidence_type->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-evidence-types', 'insert'));

   $group->post('/list', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\EvidenceType::where('id', '>', 0);

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-evidence-types', 'select'));
});