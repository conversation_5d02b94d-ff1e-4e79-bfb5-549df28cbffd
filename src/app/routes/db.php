<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group('/db', function($group) {

    set_time_limit(0);

    $group->get('/create/{table:[a-zA-Z0-9\_]+}', function (Request $request, Response $response, array $args) {

        $this->logger->info("creating table");
        $schema = new DB\Schema();
        $schema->createTable($args['table']);
        $schema->createForeignKeys();
        $this->logger->info("table created");

        return $response;
    })->add(\APP\Auth::getDevelopmentModeCheck());

    $group->get('/create', function (Request $request, Response $response) {
        $this->logger->info("creating database");
        $schema = new DB\Schema();
        $schema->createTable("error_log");
        $schema->createDB();
        $this->logger->info("database created");

        return $response;
    })->add(\APP\Auth::getDevelopmentModeCheck());

    $group->get('/drop', function (Request $request, Response $response) {

        $this->logger->info("droppping database");
        $schema = new DB\Schema();
        $schema->dropDB();
        $this->logger->info("database dropped");

        return $response;
    })->add(\APP\Auth::getDevelopmentModeCheck());

    $group->get('/recreate', function (Request $request, Response $response) {
        $this->logger->info("recreating database");
        $schema = new DB\Schema();
        $schema->dropDB();
        $schema->createTable("error_log");
        $schema->createDB();
        $sample_data = new DB\SampleData();
        $sample_data->settings = $this->get('settings');
        $sample_data->populateDB();
        $this->logger->info("database recreated");

        return
            $response
                ->write('Database recreateded!')
        ;
    })->add(\APP\Auth::getDevelopmentModeCheck());

    $group->get('/populate', function (Request $request, Response $response) {

        $this->logger->info("populating database with sample data");
        $sample_data = new DB\SampleData();
        $sample_data->settings = $this->get('settings');
        $sample_data->populateDB();
        $this->logger->info("database populated");

        return $response;
    })->add(\APP\Auth::getDevelopmentModeCheck());

});
