<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/holiday",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$holiday = \Models\Holiday::find($args["id"]);

		$response->getBody()->write($holiday->toJson());
		return $response->with<PERSON>eader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-holidays', 'select'));

	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$holiday = \Models\Holiday::find($args["id"]);

		if (isset($data["from_text"]) && isset($data["to_text"])) {
			$holiday->from_text = $data["from_text"];
			$holiday->to_text = $data["to_text"];
		}

		$holiday->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-holidays', 'update'));


	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$holiday = new \Models\Holiday;

		$fields = [
			"name",
			"start_date",
			"end_date",
		];
		if (isset($data['start_date'])) {
			$data['start_date'] = \Carbon\Carbon::parse($data['start_date']);
			$data['end_date'] = \Carbon\Carbon::parse($data['end_date']);
		}

		\APP\Tools::setObjectFields($holiday, $fields, $data, false, true);

		$holiday->added_by = \APP\Auth::getUserId();
		$holiday->save();

		return
			$response
				->withHeader('Content-Type', 'text/html')
				->write($holiday->id)
		;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-holidays', 'insert'));



	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$holiday = \Models\Holiday::find($args["id"]);
		$holiday->status = false;
		$holiday->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-holidays', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$holiday = \Models\Holiday::find($args["id"]);
		$holiday->status = true;
		$holiday->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-holidays', 'disable'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$query = [];

		if (\APP\Auth::isAdminInterface()) {
			$query = \Models\Holiday
				::where('status', true)
				->get()
			;
		}

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$query = \Models\Holiday::where("id", ">", 0);

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-holidays', 'select'));
});