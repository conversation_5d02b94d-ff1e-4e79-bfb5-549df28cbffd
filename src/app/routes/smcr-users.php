<?php

/*
Still questionable if I need this!
*/

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

// I guess these choices are because I had great idea of having nice URL in requests, but report functionality had some issues with nonalphanumerical characters
$app->group("/{router:smcrusers|smcr-users}",  function ($group) {

	// Get spcific SMCR function
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$function = \Models\SmcrFunction::find($args["id"]);

		$response->getBody()->write(json_encode($function));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// Sign off user!
	// User is generated new certificate or existing one is updated, start date from today, end date from timings.
	// All F&P resources are refreshed that are checked as refreshed.
	$group->post("/sign-off/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$user = \Models\User::find($params["user_id"]);

		// Only admin, manager of user or access all users
		if (
			$user &&
			(
				\APP\Auth::isAdmin() ||
				(
					\APP\Auth::signOffLearnerStatus() &&
					(
						\APP\Auth::isManagerOf($user->id) ||
						\APP\Auth::accessAllLearners()
					)
				)
			) &&
			$user->staff_type_id > 0
		) {
			if (
				isset($params['confirm_sign_off']) &&
				$params['confirm_sign_off']
			) {
				$user->last_completion_date = \Carbon\Carbon::now();
				if (isset($params['expire_at'])) {
					$user->last_completion_date = \Carbon\Carbon::parse($params['expire_at']);
				}
				if (isset($params['last_completion_date'])) {
					$user->last_completion_date = \Carbon\Carbon::parse($params['last_completion_date']);
				}

				// Get timing date, depending what staff type user is.
				$timing_key = false;
				switch ($user->staff_type_id) {
					case 1: // Standard (Conduct Rules)
						$timing_key = 'smcr_standard_conduct_rules_reassessed';
					break;
					case 2: // Certification Staff
						$timing_key = 'smcr_certification_staff_reassessed';
					break;
					case 3: // Senior Manager
						$timing_key = 'smcr_senior_manager_reassessed';
					break;
				}

				if ($timing_key) {
					$timing = \Models\Timing::where('key', $timing_key)
						->first()
					;
					if (
						$timing &&
						$timing->timing
					) {
						// Set next completion date, usualy a year after last completion date.
						$user->next_completion_date = \Carbon\Carbon::parse($user->last_completion_date)->addDays($timing->timing);
					}
				}
				$user->save();


				// Signing off the learner will set all associated functions and responsibilities to completed.
				// Get all responsibilites/functions, set them to completed!

				$responsibilities = \Models\SmcrStaffFunctionResponsibility
					::where('user_id', $user->id)
					->where('status', true)
					->where('completion_status', 'Not Accepted')
					->update(
						[
							'completion_status' => 'Accepted',
							'completion_date' => \Carbon\Carbon::now(),
							'accepted_by' => \APP\Auth::getUserId()
						]
					)
				;

				$functions = \Models\SmcrStaffFunctionResponsibility
					::where('user_id', $user->id)
					->where('status', true)
					->where(function ($query) {
						$query
							->where('completion_status', 'Failed')
							->orWhere('completion_status', 'Not Completed')
						;
					})
					->update(
						[
							'completion_status' => 'Certified',
							'completion_date' => \Carbon\Carbon::now(),
							'accepted_by' => \APP\Auth::getUserId()
						]
					)
				;
			}


			// Create new report or force existing incomplete one as completed, set dates accordingly
			$incomplete_certificate = \Models\SmcrReport
				::where('user_id', $user->id)
				->where('status', true)
				->where('completion_status', '!=', 'Completed')
				->where('completion_status', '!=', 'Archived')
				->where('type_id', 1)
				->first()
			;

			if (!$incomplete_certificate) {
				$incomplete_certificate = new \Models\SmcrReport;
				$incomplete_certificate->type_id = 1;
			}
			$incomplete_certificate->user_id = $user->id;
			$incomplete_certificate->completion_status = 'In Progress';
			if (
				isset($params['confirm_sign_off']) &&
				$params['confirm_sign_off']
			) {
				$incomplete_certificate->start_at = $user->last_completion_date;
				$incomplete_certificate->expire_at = $user->next_completion_date;
				$incomplete_certificate->completion_status = 'Completed';
				$incomplete_certificate->certified_by_id = \APP\Auth::getUserId();
			}
			if (isset($params['sign_off_date'])) {
				$incomplete_certificate->sign_off_date = \Carbon\Carbon::parse($params['sign_off_date']);
			}
			if (isset($params['approved_authority'])) {
				$incomplete_certificate->approved_authority = $params['approved_authority'];
			}
			if (isset($params['comments'])) {
				$incomplete_certificate->comments = $params['comments'];
			}
			$incomplete_certificate->save();


			// Refresh F&P Category resources that needs refreshing.
			if (
				isset($params['confirm_sign_off']) &&
				$params['confirm_sign_off']
			) {

				\APP\Smcr::resetUserLearning($user, 'SMCR - Manually refresh learning when signed off.');

				$user->self_attested = false;
				$user->certified = true;
				$user->learning_status = 'Not Started';
				$user->save();

				// Send e-mail!
				if (
					$user->StaffType &&
					$user->StaffType->id == 2
				) {
					$template_name = 'Certification Staff Approval';
					$template = \Models\EmailTemplate
						::where('name', $template_name)
						->where('status', true)
						->first()
					;
					if ($template) {
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->recipients = [$user->id];
						$email_queue->custom_variables = json_encode([
							'SMCR_TYPE' => $user->StaffType->name,
						]);
						$email_queue->save();
					}
				}
			}
		}

		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($function))
		;
	})->add(\APP\Auth::getSessionCheck());


	// Self attesting functionality for user that has completed learning!
	$group->post("/self-attest/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$user = \Models\User::find($args["id"]);

		// Only admin, manager of user or access all users
		if (
			$user &&
			$user->learning_status == 'Completed' &&
			!$user->self_attested &&

			(
				(
					$user->id == \APP\Auth::getUserId()
				) ||
				(
					\APP\Auth::isAdmin() ||
					\APP\Auth::isManagerOf($user->id) ||
					\APP\Auth::accessAllLearners()
				)
			)
		) {
			// Find latest certificate and update self attested date!
			$incomplete_certificate = \Models\SmcrReport
				::where('user_id', $user->id)
				->where('status', true)
				->where('completion_status', '!=', 'Completed')
				->where('completion_status', '!=', 'Archived')
				->where('type_id', 1)
				->first()
			;

			// If incomplte certificate exists, self attest!
			if ($incomplete_certificate) {
				// Set user learning status to completed
				$user->self_attested = true;
				$user->self_attested_reminder_sent = false;
				$user->save();

				$incomplete_certificate->self_attested_date = \Carbon\Carbon::now();
				$incomplete_certificate->save();
			}

		} else {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
			;
		}

		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($function))
		;
	})->add(\APP\Auth::getSessionCheck());


	// Get all list of users that are assigned to Staff Type.
	$group->get('/all', function (Request $request, Response $response) {
		$query = \Models\User
			::where("status", true)
			->select(
				'users.id',
				'users.fname',
				'users.lname',
				'users.staff_type_id'
			)
		;

		if ($this->get('settings')['licensing']['isSMCR']) {
			$query = $query
				->whereNotNull('staff_type_id')
				->with('StaffType')
			;
		}

		$query = $query
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());



   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();
		$query_id = 'smcrusersList';
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);


		if (isset($args["download"]) && $args["download"] == "/download") {

			$query
				->selectRaw("CONCAT(users.fname, ' ', users.lname) as trainee_name")
			;

			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"Name" => "trainee_name",
				"Job Title" => "designation.name",
				"Staff Type" => "StaffType.name",
				"Approved" => '',
				"Status" => 'learning_status',
			];


			$download_file_name = uniqid("smcr-users.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'select'));
});