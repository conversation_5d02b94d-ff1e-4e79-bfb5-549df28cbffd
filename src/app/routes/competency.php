<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/competency",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$competency = \Models\Competency::find($args["id"]);
		$competency->status = 0;
		$competency->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-competencies', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$competency = \Models\Competency::find($args["id"]);
		$competency->status = 1;
		$competency->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-competencies', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$competency = \Models\Competency::find($args["id"]);

		$response->getBody()->write(json_encode($competency));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-competencies', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$competency = new \Models\Competency;

		$fields = [
			"name", "description", "required_points", "badge"
		];

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$competency->$field = $data[$field]; // != '---' ? $data[$field] : null;
			} else {
				if ($field == 'badge') {
					$competency->$field = null;
				}
			}
		}

		$competency->status = 1;
		$competency->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-competencies', 'insert'));

	// Update competency
	$group->POST('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$competency = \Models\Competency::find($args["id"]);

		 $fields = [
			"name", "description", "required_points", "badge"
		];

		foreach($fields as $field) {
			if (isset($data[$field])) {
				$competency->$field = $data[$field]; // != '---' ? $data[$field] : null;
			} else {
				if ($field == 'badge') {
					$competency->$field = null;
				}
			}
		}

		$competency->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-competencies', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$data = \Models\Competency
			::where("status",">",0)
		;

		if (\APP\Auth::isLearner()) {
			$data = $data
				->whereHas('UserCompetencies', function ($query) {
					$query
						->where('user_id', \APP\Auth::getUserId())
					;
				})
			;
		}

		$data = $data
			->get()
		;
		// UserCompetencies

		if (\APP\Auth::isLearner()) {
			$data->makeHidden(['badge']);
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-competencies', 'select'));

	$group->get('/leaderboard/{user_id:[0-9]+}', function (Request $request, Response $response, $args)
	{
		if (!\Models\Role::getRoleParam('lfp_show_leaderboard')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

		$query = \Models\LearningResult
			::where('refreshed', false)
			->where('completion_status', 'completed')
			->select(
				'users.fname',
				'users.lname',
				'users.id as user_id',
				DB::raw('
					CAST(sum(learning_module_competencies.points) as UNSIGNED) as points
				')
			)
			->join("users", function($join) {
				$join
					->on("learning_results.user_id", "=", "users.id")
					->where("users.status", "=", 1)
				;
			})
			->rightJoin("learning_module_competencies", function($join) {
				$join
					->on("learning_module_competencies.learning_module_id", "=", "learning_results.learning_module_id")
				;
			})
			->whereExists(function ($query) {
				$query->select(DB::raw(1))
					->from('user_learning_modules')
					->whereRaw('user_learning_modules.user_id = learning_results.user_id')
					->whereRaw('user_learning_modules.learning_module_id = learning_results.learning_module_id')
					->whereRaw('user_learning_modules.deleted_at is null')
				;
			})
			->whereIn(
				'learning_results.learning_module_id',
				\Models\LearningModule
					::where('status', true)
					->pluck('id')
					->toArray()
			)
			->orderBy('points', 'DESC')
			->groupBy('users.id')
			//->limit(10)

		;

		$query = $query
			->get()
		;
		$rank_list = [];

		// Check if passed user exists in results, if not, add him at the end.
		$user_exists = false;
		$rank = 1;
		foreach ($query as $key => $entry) {
			$entry->rank = $rank;

			if ($entry->user_id == $args['user_id']) {
				$user_exists = true;
			}

			if ($rank > 10) {
				if ($entry->user_id == $args['user_id']) {
					$rank_list[] = $entry;
				}
			} else {
				$rank_list[] = $entry;
			}
			$rank++;
		}

		if (!$user_exists) {
			$request_user = \Models\User::find($args['user_id']);
			$new_user = new \stdClass();
			$new_user->fname = $request_user->fname;
			$new_user->lname = $request_user->lname;
			$new_user->user_id = $request_user->id;
			$new_user->points = 0;
			$new_user->rank = $rank;
			$rank_list[] = $new_user;
		}

		//die();

		$response->getBody()->write(json_encode($rank_list));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\Competency::where("id", ">", "0");

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Compatency Name" => "name",
				"CPD points" => "required_points",
				"Description" => "description",
			];


			$download_file_name = uniqid("competencies.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-competencies', 'select'));
});