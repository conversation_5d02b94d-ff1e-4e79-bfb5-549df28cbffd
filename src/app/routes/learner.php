<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;
use \Barryvdh\DomPDF\Facade\Pdf;
use Models\LearningResult;
use Models\LearningModule;
use Models\User;
/*
Some actions for learner/netflix interface
*/

$app->group("/learner",  function ($group) {
	/*

	For resource types "classroom / on the job training / website / book-cd-dvd", learner and manager sign-off is required, this action will submit learning result, code is checking if result is assigned to current user and if module assigned to this are that specific type (3,4,5,6).

	*/
	$group->put("/sign-off", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());

		$signOffStatus = \APP\Learning::signOffLearningResult($data["id"], $user->id, $data["response"]);
		if (!$signOffStatus) {
			$response = $response->withStatus(500);
		}

		return
			$response
				->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'update'));

	// Remove evidence file, usualy either by admin or manager who created evidence resource.
	$group->get("/evidence/remove/{evidence_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());

		$evidence = \Models\LearningModuleEvidence::find($args['evidence_id']);
		if ($evidence) {
			$learning_module = \Models\LearningModule::find($evidence->learning_modules_id);

			// If user is admin or manager who created evidence
			if (
				\APP\Auth::isAdmin()
				|| (
					$learning_module &&
					\APP\Auth::isManager() &&
					$learning_module->created_by == $user->id &&
					$evidence->user_id == $user->id // current manager have uploaded the evidence
				)
			) {
				$filename = $this->get('settings')["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension;
				if (
					is_file($filename)
				) {
					unlink($filename);
				}
				$grading = new \APP\Grading(true);
				$grading->deleteOpenAIData($evidence);
				// Delete record anyway, even if file does not exists.
				$evidence->delete();
			} else {
				return \APP\Tools::returnCode($request, $response, 403, "You do not have permission to delete this evidence");
			}
		}
		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes'], 'disable'));

	// Add evidence, blog post or reflective log resource.
	$group->post("/resource/add", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		//get current user
		$user = false;
		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManager() ||
			\APP\Auth::accessAllLearners()
		) {
			if (isset($data['userId'])) {
				$user = \Models\User::findOrFail($data['userId']);
			} else {
				$user = false; // If manager/admin adds resource, do not assign resource to admin/manager
			}
		} else {
			$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		}

		if (isset($data['name'])) {
			// add new learning resource and assign it to current user, IF LEARNER OR ADMIN SUPPLIED USER, only name, type, category is mandatory
			$learning = new \Models\LearningModule;
			$learning->name = $data['name'];

			// this need to be thought about, it is not really relevant what category it is, categories differ for each client.
			// I should check for category name, if exists use it's ID, if not, create new one.
			if (isset($data['category_id']) && $data['category_id']) {
				$learning->category_id = $data['category_id'];
			} else {
				$evidence_category = \Models\LearningModuleCategory
					::where('name', '=', 'Further evidence and resources')
					->first()
				;
				$learning->category_id = $evidence_category && $evidence_category->id ? $evidence_category->id : 14;
			}

			if (isset($data['description']) && $data['description']) {
				$learning->description = $data['description'];
			}

			if (isset($data['form_id']) && $data['form_id']) {
				$learning->form_id = $data['form_id'];
			}

			if (isset($data['evidence_type_id']) && $data['evidence_type_id']) {
				$learning->evidence_type_id = $data['evidence_type_id'];
			}

			if (isset($data['event_type_id'])) {
				$learning->event_type_id = $data['event_type_id'];
				// This is event, pass as event type!
				$learning->type_id = \Models\LearningModuleType::getId('event');
			}

			if (isset($data['visible_learner'])) {
				$learning->visible_learner = $data['visible_learner'];
			}

			if (isset($data['material']) && $data['material']) {
				$learning->material = $data['material'];
			}

			if (isset($data['keywords']) && $data['keywords']) {
				$learning->keywords = $data['keywords'];
			}

			if (isset($data['cost']) && $data['cost']) {
				$learning->cost = $data['cost'];
			}

			$learning->id = \APP\Course::getNewCourseId(
				$learning->name,
				$this->get('settings')["FixedCourseIds"],
				$this->get('settings')["CourseIdStart"]
			);


			// Only Evidence, Reflective Log or Blog Entry can be added here, Learner can specify witch type.
			if (!$learning->type_id) {
				$learning->type_id = \Models\LearningModuleType::getId('upload');
				if (
					isset($data['type_id']) &&
					(
						$data['type_id'] == \Models\LearningModuleType::getId('upload') ||
						$data['type_id'] == \Models\LearningModuleType::getId('blog_entry') ||
						$data['type_id'] == \Models\LearningModuleType::getId('reflective_log') ||
						$data['type_id'] == \Models\LearningModuleType::getId('form')
					)
				) {
					$learning->type_id = $data['type_id'];
				}
			}
			$learning->jackdaw = 0;
			$learning->status = 1;

			// If reuse, use logged in user ID as created_by, can be re-used by other managers/admins
			if (
				empty($data['reuse']) &&
				$user
			) {
				$learning->created_by = $user->id;
				$learning->created_in_learner_interface = true;
			} else {
				$learning->created_by = \APP\Auth::getUserId();
			}

			// If duration is specified, apply it to learning
			if (isset($data['duration_hours']) && $data['duration_hours']) {
				$learning->duration_hours = $data['duration_hours'];
			}
			if (isset($data['duration_minutes']) && $data['duration_minutes']) {
				$learning->duration_minutes = $data['duration_minutes'];
			}

			// If upload/evidence is added by managers, set this as project.
			if (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManager() ||
				\APP\Auth::accessAllLearners()
			) {
				$learning->project = true;
			} else {
				// Else, this is created in learner's interface
				$learning->created_in_learner_interface = true;
			}

			// Only for bulk assign when creating upload, this will make resource not viewable on learning library - administration interface.
			if (
				isset($data['show_in_library']) &&
				$data['show_in_library'] === false
			) {
				$learning->created_in_learner_interface = true;
			}

			$learning->save();

			//attach module to user
			if (
				$learning->id &&
				$user
			) {
				\Models\UserLearningModule::linkResources($user->id, $learning->id, 'Add evidence, blog post, form  or reflective log resource and assign to user');
				\APP\Learning::syncUserResults($user->id);
			}


			// Send e-mail to user, if not learner
			if (
				!\APP\Auth::isLearner() &&
				$user
			) {
				\APP\Email::sendEmailReminder([$user->id], [$learning->id]);
			}


			//Change learning result to in progress and if module is assigned to standard issue, set due date.

			// get start day from "apprenticeship_issues" table to set due at for learning result, get lowest value
			if ($user) {

				if (!empty($data['matchedIssues'])) {
					$standardIssues = \Models\ApprenticeshipIssues
						::whereIn('id', $data['matchedIssues'])
						->orderBy('start_day')
						->first();
					;
				}

				$learningresult = \Models\LearningResult
					::where('learning_module_id', $learning->id)
					->where("user_id", $user->id)
					->first()
				;

				if (isset($data['start_date'])) {
					$learningresult->start_date = \Carbon\Carbon::parse($data['start_date']);
				}

				if (isset($data['credits'])) {
					$learningresult->credits = $data['credits'];
				}

				if (isset($data['processing_manager_id']) && $data['processing_manager_id']) {
					$learningresult->processing_manager_id = $data['processing_manager_id'];
				}

				if (
					isset($standardIssues->start_day) ||
					isset($standardIssues->end_day)
				) {
					// find standard use from issue and get his starting time
					$standard_user = \Models\ApprenticeshipStandardUser::where('user_id', $user->id)
						->whereIn('standard_id',
							\Models\ApprenticeshipStandard::select('id')
								->where('status', true)
								->whereIn('id',
									\Models\ApprenticeshipIssueCategories::select('standard_id')
										->where('status', true)
										->whereIn('id', [$standardIssues->issue_category_id])
										->get()
								)
								->get()
						)
						->first()
					;

					// Check if resource has custom start and end day
					$pivot_module = \Models\ApprenticeshipIssuesLearningModules::where('apprenticeship_issues_id', $standardIssues->id)
						->where('learning_modules_id', $learning->id)
						->where('custom_work_window', true)
						->orderBy('start_day')
						->first();
					;

					$start_day = $standardIssues->start_day;
					$end_day = $standardIssues->end_day;

					if (
						$pivot_module &&
						$pivot_module->start_day &&
						$pivot_module->end_day
					) {
						$start_day = $pivot_module->start_day;
						$end_day = $pivot_module->end_day;
					}

					if (isset($start_day)) {
						$learningresult->due_at = \Carbon\Carbon::parse($standard_user->start_at)->addDays($start_day);
					}
					if (isset($end_day)) {
						$learningresult->grace_at = \Carbon\Carbon::parse($standard_user->start_at)->addDays($end_day);
					}
				} else {
					// no issues matched, set due at from timings settings.
					$evidence_due = \Models\Timing::where("key", "=", "learningResource_evidence")
						->first()
					;
					$learningresult->due_at = \Carbon\Carbon::now()->addDays($evidence_due->timing);
				}
				if (\APP\Auth::isLearner()) {
					$learningresult->completion_status = 'in progress';
				}

				if (isset($data['completion_status'])) {
					$learningresult->completion_status = $data['completion_status'];
				}

				if(isset($data['form_data']) && !empty($data['form_data'])){
					$learningresult->form_data=$data['form_data'];
				}

				// If admin and he added sign-off, set to complete
				if (
					\APP\Auth::isAdmin() ||
					\APP\Auth::isManager() ||
					\APP\Auth::accessAllLearners()
				) {
					if (
						$learningresult &&
						isset($data['signoff']) &&
						$data['signoff']
					) {
						$learningresult->completion_status = 'completed';
						$learningresult->sign_off_trainee = 1;
						$learningresult->sign_off_trainee_at =  \Carbon\Carbon::now();
						$learningresult->sign_off_manager = 1;
						$learningresult->sign_off_manager_by =  \APP\Auth::getUserId();
						$learningresult->sign_off_manager_at =  \Carbon\Carbon::now();
						$learningresult->completed_at = \Carbon\Carbon::now();
					}
				}

				if (isset($data['log_learned'])) {
					$learningresult->log_learned = $data['log_learned'];
				}

				if (isset($data['log_to_learn'])) {
					$learningresult->log_to_learn = $data['log_to_learn'];
				}

				if (isset($data['log_used'])) {
					$learningresult->log_used = $data['log_used'];
				}
				if (
					isset($data['expected_completion_date']) &&
					$data['expected_completion_date']
				) {

					$learningresult->completion_date_custom = \Carbon\Carbon::parse($data['expected_completion_date']);
					$learningresult->save();
				}

				if (!\APP\Auth::isLearner()) {
					$learningresult->learner_action = true;
					$learningresult->learner_action_date = \Carbon\Carbon::now();
				}

				$learningresult->save();


				// When admin or manager adds evidence resource to user, match to standard parameter is passed with standard category issue ID, attach resource with that issue here.
				if (isset($data['matchedIssues']) && $data['matchedIssues']) {
					foreach ($data['matchedIssues'] as $key => $value) {
						\Models\ApprenticeshipIssuesEvidence::linkEntry([$value], [$learning->id], $user->id);
					}
				}


				//Update evidence table that links resources and users(files/comment)

				// There will be 2 evidence types in general, comment and files(that will differ in file types eventually),
				if (isset($data['comment']) && !empty($data['comment'])) {
					$evidence = new \Models\LearningModuleEvidence;
					$evidence->learning_modules_id = $learning->id;
					$evidence->user_id = $user->id;
					$evidence->hash = bin2hex(random_bytes(16));
					$evidence->evidence = $data['comment'];
					$evidence->evidence_type = 'comment';
					$evidence->status = 1;
					$evidence->save();
				}

				// Sign off evidence learning result if user checked checkbox upon uploading
				if (
					isset($data['signoff']) &&
					$data['signoff'] &&
					empty($data['userId'])
				) {
					\APP\Learning::signOffLearningResult($learningresult->id, $user->id);
				}

			}
		if (isset($learning)) {
			$response->getBody()->write(json_encode($learning->id));
			return $response->withHeader('Content-Type', 'application/json');
		}
	}

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	// Edit existing evidence. (add comments/files/modify)
	$group->put("/resource/edit/{evidence:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());

		$learning_id = $args['evidence'];

		$learning = \Models\LearningModule
			::where('id', '=', $learning_id)
			->where('status', '=', 1)
			->with(['learningresult' => function ($query) use ($user) {
				$query
					->where('user_id', '=', $user->id)
				;
			}])
		;
		$learning = $learning->first();

		if (!$learning) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$learningresult = \Models\LearningResult
			::where('learning_module_id', $learning->id)
			->where("user_id", $user->id)
			->where('refreshed', 0)
			->first()
		;

		if (!$learningresult) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		// If user has created evidence, he can edit/save it.
		if ($learning->created_by == $user->id) {
			// If user created resource in learner interface, he can edit name/category/type
			if ($learning->created_in_learner_interface) {
				if (isset($data['name']) && $data['name']) {
					$learning->name = $data['name'];
				}

				if (isset($data['category_id']) && $data['category_id']) {
					$learning->category_id = $data['category_id'];
				}

				if (isset($data['evidence_type_id']) && $data['evidence_type_id']) {
					$learning->evidence_type_id = $data['evidence_type_id'];
				}
			}

			if (isset($data['keywords']) && $data['keywords']) {
				$learning->keywords = $data['keywords'];
			}
			if (isset($data['description']) && $data['description']) {
				$learning->description = $data['description'];
			}

			// Duration data goes directly into learning_modules table, as this resource is exclusive only to this user.
			if (isset($data['duration_hours']) && $data['duration_hours']) {
				$learning->duration_hours = $data['duration_hours'];
			}
			if (isset($data['duration_minutes']) && $data['duration_minutes']) {
				$learning->duration_minutes = $data['duration_minutes'];
			}

			$learning->save();

		// If resource was not created for logged in user, he is able to save only duration in learning_results table
		} else {
			if (isset($data['duration_hours']) && $data['duration_hours']) {
				$learningresult->duration_hours = $data['duration_hours'];
			}
			if (isset($data['duration_minutes']) && $data['duration_minutes']) {
				$learningresult->duration_minutes = $data['duration_minutes'];
			}
		}

		/*Only evidence created user is allowed to change the process Manager*/
		if (isset($data['processing_manager_id']) && $data['processing_manager_id']) {
			$learningresult->processing_manager_id = $data['processing_manager_id'];
		}

		if (isset($data['credits'])) {
			$learningresult->credits = $data['credits'];
		}

		if ($learning->type_id == 9) {
			if (isset($data['log_learned'])) {
				$learningresult->log_learned = $data['log_learned'];
			}
			if (isset($data['log_to_learn'])) {
				$learningresult->log_to_learn = $data['log_to_learn'];
			}
			if (isset($data['log_used'])) {
				$learningresult->log_used = $data['log_used'];
			}
		}

		$learningresult->save();

		// Need to check if user is assigned to evidence before posting comment

		if (
			isset($data['comment']) &&
			!empty($data['comment']) &&
			$learningresult
		) {
			$evidence = new \Models\LearningModuleEvidence;
			$evidence->learning_modules_id = $learning_id;
			$evidence->user_id = $user->id;
			$evidence->added_by = \APP\Auth::getUserId();
			$evidence->hash = bin2hex(random_bytes(16));
			$evidence->evidence = $data['comment'];
			$evidence->evidence_type = 'comment';
			$evidence->status = 1;
			$evidence->save();
		}

		if (
			isset($data['signoff']) &&
			$data['signoff'] &&
			$learningresult->completion_status != 'completed'
		) {
			\APP\Learning::signOffLearningResult($learningresult->id, $user->id);
		}

		// When editing resource and matching against issue for specific standard, if standard is specified, dematch all issues and match against ones supplied
		if (isset($data['matchedIssues']) && $data['matchedIssues'] && isset($data['standard']) && $data['standard']) {

			// Delete evidence link to all issues in given standard
			\Models\ApprenticeshipIssuesEvidence::where('user_id', $user->id)
				->where('learning_modules_id', $learning->id)
				->whereIn('apprenticeship_issues_id',
					\Models\ApprenticeshipIssues
						::select('id')
						->whereIn('issue_category_id',
							\Models\ApprenticeshipIssueCategories
								::select('id')
								->where('status', true)
								->where('standard_id', $data['standard'])
								->get()
						)
						->where('status', true)
						->get()
				)
				->delete();
			;

			// Create link with matched issues;
			foreach ($data['matchedIssues'] as $key => $value) {
				\Models\ApprenticeshipIssuesEvidence::linkEntry([$value], [$learning->id], $user->id);
			}
		}

		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('learning_results', $learningresult->id, $field_name, $value);
			}
		}

		$response->getBody()->write(json_encode($learning_id));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'update'));

	//Upload thumbnail/promo image for learner's evidence
	$group->post("/resource/image/{evidence_image:[0-9a-z_]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$evidence_image = $args['evidence_image'];

		if (($evidence_image == 'promo_image' || $evidence_image == 'thumbnail') && isset($_FILES[$evidence_image])) {
			$image_path = $evidence_image == 'thumbnail' ? $this->get('settings')["LMSThumbPath"] : $this->get('settings')["LMSPromoPath"];

			if (isset($data['id'])) {
				$learning = \Models\LearningModule
					::where('id', '=', $data['id'])
					->where('status', '=', 1)
				;

				if (isset($data["user_id"]) && $data["user_id"] && (\APP\Auth::isAdmin() || \APP\Auth::isManagerOf($data["user_id"]))) {
					// you are logged in as admin or manager for said user and want to add evidence with promo/thumbnail, please do so!
				} else {
					// check if logged in user owns the resource
					$learning = $learning->where('created_by', '=', $user->id);
				}

				$learning = $learning->first();

				// If valid learning resource exists, proceed with deleting promo/thumbs and uploading them
				if ($learning) {
					// check if exiting image exists and delete it before uploading new one
					if (
						$learning->$evidence_image &&
						is_file($image_path . $learning->$evidence_image)
					) {
						unlink($image_path . $learning->$evidence_image);
					}

					// Upload new promo image using Flysystem
					$adapter = new LocalFilesystemAdapter($image_path);
					$filesystem = new Filesystem($adapter);

					$uploadedFile = $_FILES[$evidence_image];
					$stream = fopen($uploadedFile['tmp_name'], 'r+');
					$imageFileName = preg_replace('/[^a-zA-Z0-9]/', '_', pathinfo($uploadedFile['name'], PATHINFO_FILENAME));
					$imageFileName .= '.' . pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);

					// Validate file size and type
					$fileSize = $uploadedFile['size'];
					$fileType = mime_content_type($uploadedFile['tmp_name']);

					$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
					$maxFileSize = $evidence_image == 'thumbnail' ? 200000 : 500000;
					if (!in_array($fileType, $allowedTypes)) {
						return \APP\Tools::returnCode($request, $response, 400, 'Invalid file type. You must upload an image file.');
					}

					if ($fileSize > $maxFileSize) {
						return \APP\Tools::returnCode($request, $response, 400, 'File size exceeds the limit.');
					}

					try {
						$filesystem->writeStream($imageFileName, $stream);
						fclose($stream);

						$learning->$evidence_image = $imageFileName;
						$learning->save();

						$response->getBody()->write($imageFileName);
						return $response;
					} catch (\Exception $e) {
						return \APP\Tools::returnCode($request, $response, 500, json_encode($e->getMessage()));
					}
				}
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	// Delete promo/thumbnail images
	$group->delete('/image/{resource_id:[0-9]+}/{image:[0-9a-z_]+}', function (Request $request, Response $response, array $args) {

		$data = $request->getParsedBody();
		// "field" and "image" parameters are passed, one identifies field name that needs to be updated, other one will contain image name

		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$learning = \Models\LearningModule
			::where('id', '=', $args["resource_id"])
			->where('created_by', '=', $user->id)
			->where('status', '=', 1)
		;
		$learning = $learning->first();

		if ($learning) {
			// logic to determine what type of image is passed, currently there is only promo or thumbnail, so if else, if more types will be added, logic will need to be extended
			$imagepath = $args['image'] == 'promo_image' ? 'LMSPromoPath' : 'LMSThumbPath';

			if (
				is_file($this->get('settings')[$imagepath] .'/'. $learning->$args['image'])
			) {
				unlink($this->get('settings')[$imagepath] .'/'. $learning->$args['image']);
			}

			// empty field for update, only specific field is updated.
			$learning->$args['image'] = '';
			$learning->save();
		}


		return $response;

	})->add(\APP\Auth::getStructureAccessCheck(['trainee-standards'], 'update'));


	// Upload evidence files
	$group->post("/evidence/add/file", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$added_by = \APP\Auth::getUser();

		if (!isset($data['learningId'])) {
			return \APP\Tools::returnCode($request, $response, 500, 'learningId not provided!');
		}

		// If admin is logged in and want to add evidence on behalf of trainee
		if (
			 isset($data['userId']) &&
			 $data['userId'] &&
			 (
				  \APP\Auth::isAdmin() ||
				  \APP\Auth::isManagerOf($data['userId']) ||
				  \APP\Auth::accessAllLearners()
			 )
		) {
			 $user = User::findOrFail($data['userId']);
		} else {
			 $user = $added_by;
		}

		$learning = LearningModule::where('id', $data['learningId'])
			 ->where('status', true)
			 ->with(['learningresult' => function ($query) use ($user) {
				$query
					->where('user_id', $user->id)
					->where('completion_status', '!=', 'completed');
			}])
			->first();

		if (
			 $learning &&
			 isset($_FILES['file']) &&
			 $_FILES['file']['name'] &&
			 (
				  $learning->learningresult ||
				  \APP\Auth::isAdmin() ||
				  (
						(
							 \APP\Auth::isManager() ||
							 \APP\Auth::isCD()
						) &&
						\APP\Auth::checkStructureAccess('lessons-and-learning-resources', 'update')
				  )
			 )
		) {
			$adapter = new LocalFilesystemAdapter($this->get('settings')["LMSEvidencePath"]);
			$filesystem = new Filesystem($adapter);

			$file = $_FILES['file'];
			$hash = bin2hex(random_bytes(16));
			$file_name = pathinfo($file['name'], PATHINFO_FILENAME);
			$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
			$new_file_name = $hash . '.' . $extension;
			$stream = fopen($file['tmp_name'], 'r+');

			$filesystem->writeStream($new_file_name, $stream);
			fclose($stream);

			$evidence = new \Models\LearningModuleEvidence;
			$evidence->learning_modules_id = $data['learningId'];
			$evidence->user_id = $user->id;
			$evidence->added_by = $added_by->id;
			$evidence->evidence = $file_name;
			$evidence->hash = $hash;
			$evidence->evidence_type = 'file';
			$evidence->extension = $extension;
			$evidence->version = isset($data['version']) ? $data['version'] : 1;
			$evidence->file_size = filesize($this->get('settings')["LMSEvidencePath"] . $hash . '.' . $extension);
			$evidence->status = 1;
			$evidence->manager = $user->id == \APP\Auth::getUserId() && (\APP\Auth::isAdmin() || \APP\Auth::isManager() || \APP\Auth::isCD()) && empty($data['userId']);
			$evidence->save();

			$response->getBody()->write($new_file_name);
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}
		return $response;
  })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	//Copy Evidences for version of resource type uploads
	$group->post('/evidence/copy_evidence', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		if(isset($data['evidences']))
		{
			$added_by = \APP\Auth::getUser();

			if (
				isset($data['userId'])
				&& $data['userId']
				&& (
					\APP\Auth::isAdmin() ||
					\APP\Auth::isManagerOf($data['userId']) ||
					\APP\Auth::accessAllLearners()
				)
			) {
				$user = \Models\User::findOrFail($data['userId']);
			} else {
				$user = $added_by;
			}


			foreach($data['evidences'] as $key => $evidenceObj){
				$existing_evidence = \Models\LearningModuleEvidence::find($evidenceObj['id']);

				// add entry in evidence table as file uploded succesefully
				$hash = bin2hex(random_bytes(16));
				$evidence = new \Models\LearningModuleEvidence;
				$evidence->learning_modules_id = $existing_evidence->learning_modules_id;
				$evidence->user_id = $user->id;
				$evidence->added_by = $added_by->id;
				$evidence->evidence = $existing_evidence->evidence;
				$evidence->hash = $hash;
				$evidence->evidence_type = 'file';
				$evidence->extension = $existing_evidence->extension;
				$evidence->version = isset($data['version'])?$data['version']:1;
				$evidence->file_size = filesize($this->get('settings')["LMSEvidencePath"] . $hash . '.' . $extension);
				$evidence->status = 1;
				$evidence->manager = $user->id == \APP\Auth::getUserId() && (\APP\Auth::isAdmin() || \APP\Auth::isManager());
				$evidence->save();

				if (is_file($this->get('settings')["LMSEvidencePath"] .'/'.$existing_evidence->hash.'.'.$existing_evidence->extension)) {
					copy($this->get('settings')["LMSEvidencePath"] .'/'.$existing_evidence->hash.'.'.$existing_evidence->extension, $this->get('settings')["LMSEvidencePath"] .'/'.$evidence->hash.'.'.$evidence->extension);
				}
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));




	// Get all learning modules that are created by user or by admin/manager.
	// And also is assigned to user, so learning results are not only indicator.
	$group->get("/evidence/list", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$user = \Models\User::findOrFail(\APP\Auth::getUserId());

		//get learning results and modules with only resource type
		$learningresults = \Models\LearningResult
			::where('user_id', '=', $user->id)
			->with(["module" =>  function($query) use ($user) {
				$query
					->where('status', '=', 1)
					->where('type_id', '=', 7)
					->with(['LearningModuleEvidences' => function ($query) use ($user) {
						$query
							->where('status', '=', 1)
							->whereRaw(
								"user_id in (select created_by from learning_modules where id = learning_module_evidences.learning_modules_id)"
							)
							->orWhere('user_id', '=', $user->id)
							->orderBy('created_at', 'desc')
							->with(['user' => function ($query) {
								$query
									->select('id', 'fname', 'lname')
								;
							}])
						;
					}])
					// Evidence created by you or by manager/admin
					->where(function ($query) use ($user) {
						$query
							->where('created_by', '=', $user->id)
							->orWhere(function ($query) use ($user) {
								$query
									->whereIn('created_by',
										\Models\User
											::whereIn('role_id', // or created_by is manager or admin.
												\Models\Role
													::where('is_admin', '=', 1)
													->orWhere('is_manager', '=', 1)
													->select('id')
													->get()
											)
											->where('status', '=', 1)
											->select('id')
											->get()
									)
								;
							})
						;
					})
					->with(['LearningResult' => function ($query) use ($user) {
						$query
							->where('user_id', $user->id)
						;
					}])
					->with(['type' => function ($query) use ($user) {
					}])
				;
			}])

			->whereHas('userlearningmodules', function ($query) use ($user) {
				$query
					->where('user_id', '=', $user->id)
				;
			})
			->whereHas('module', function ($query) use ($user) {
				$query
					->where('status', '=', 1)
					->where('type_id', '=', 7)
				;
			})
		;

		// IF SMCR, filter out resources that are in hidden F&P Categories.
		if ($this->get('settings')['licensing']['isSMCR']) {
			$learningresults = $learningresults
				// If resource is assigned to F&P category and category is hidden, do not show these resources
				->where(function ($query) {
					$query
						->where(function ($query) {
							$query
								->whereIn('learning_results.learning_module_id',
									\Models\LearningModule::select('id')
										->whereNotNull('f_p_category_id')
										->whereIn('f_p_category_id',
											\Models\SmcrFPCategory::select('id')
												->where('status_learner', true)
												->get()
										)
										->get()
								)
							;
						})
						->orWhere(function ($query) {
							$query
								->whereIn('learning_results.learning_module_id',
									\Models\LearningModule::select('id')
										->whereNull('f_p_category_id')
										->get()
								)
							;
						})
						->orWhere(function ($query) {
							$query
								->whereIn('learning_results.learning_module_id',
									\Models\LearningModule::select('id')
										->whereNotNull('f_p_category_id')
										->where('type_id', '!=', 7)
										->whereIn('f_p_category_id',
											\Models\SmcrFPCategory::select('id')
												->where('status_learner', false)
												->get()
										)
										->get()
								)
							;
						})
					;
				})
			;
		}

		$learningresults = $learningresults
			->get()
		;


		$response->getBody()->write(json_encode($learningresults));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('trainee-standards', 'select'));


	//detach or attach evidence to standard issues using table "apprenticeship_issues_evidence"
	$group->post('/evidence/{evidence:[0-9]+}/issue/{issue:[0-9a-z_]+}/{status:[a-z]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// pull active evidence module and make sure current user made it, it is active and type is evidence
		$evidence = \Models\LearningModule
			::where('id', '=', $args['evidence'])
			->with(['LearningResult' => function ($query) {
				$query
					->whereNull('deleted_at')
				;
			}])
			->where('status', '=', 1)
			->first()
		;

		if (
			isset($data['user_id']) &&
			$data['user_id']
		) {
			if (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($data['user_id']) ||
				\APP\Auth::accessAllLearners() ||
				(
					$data['user_id'] == \APP\Auth::getUserId() &&
					(
						$args['status'] == 'attach' ||
						(
							$args['status'] == 'detach' &&
							$evidence->LearningResult->sign_off_trainee == false
						)
					)
				)
			) {
				$user = \Models\User::find($data['user_id']);
			} else {
				return \APP\Tools::returnCode($request, $response, 403);
			}
		} else {
			$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		}

		//echo json_encode($evidence);

		if ($args['status'] == 'attach') {
			\Models\ApprenticeshipIssuesEvidence::linkEntry([$args['issue']], [$args['evidence']], $user->id);
		} else {
			$entity = \Models\ApprenticeshipIssuesEvidence::where('learning_modules_id', '=', $args['evidence'])
				->where('apprenticeship_issues_id', '=', $args['issue'])
				->where('user_id', '=', $user->id)
				->first()
			;
            if ($entity){
                $entity->update(['deleted_by' => \APP\Auth::getUserId()]);
                $entity->delete();
            }
		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-standards'], 'update'));


	/*
		Request meeting with manager from evidence created by trainee
	*/
	$group->put("/evidence/meeting/add", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (
			!empty($data['userId'])
			&& (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($data['userId']) ||
				\APP\Auth::accessAllLearners()
			)

		) {
			$user = \Models\User::findOrFail($data['userId']);
		} else {
			$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		}

		// look for passed module id, find it in database, check if current user created it and it is evidence
		if (isset($data['module_id']) && $data['module_id']) {

			$learning = \Models\LearningModule
				::where('id', '=', $data['module_id'])
				->where('type_id', '=', 7)
				->where('status', '=', 1)
				->get()
			;

			if (!$learning->isEmpty()) {
				//create new evidence meeting
				$evidencemeeting = new \Models\LearningModuleEvidenceMeeting;
				$evidencemeeting->learning_modules_id = $data['module_id'];
				$evidencemeeting->user_id = $user->id;
				$evidencemeeting->created_by = \APP\Auth::getUserId();
				$evidencemeeting->name = $data['name'];
				if (isset($data['description'])) {
					$evidencemeeting->description = $data['description'];
				}
				$evidencemeeting->preferred_time = \Carbon\Carbon::parse($data["preferred_time"]);
				$evidencemeeting->status = 1;

				if (
					!empty($data['userId'])
					&& (
						\APP\Auth::isAdmin()
						|| \APP\Auth::isManagerOf($data['userId'])
					)
				) {
					$evidencemeeting->manager_accepted = 1;
					$evidencemeeting->approved_by = \APP\Auth::getUserId();
				} else {
					$evidencemeeting->trainee_accepted = 1;
				}

				$evidencemeeting->save();

				// If learner, update last active date in user's table
				if (\APP\Auth::isLearner()) {
					$user = \APP\Auth::getUser();
					$user->last_contact_date = \Carbon\Carbon::now();
					$user->save();

					\Models\ApprenticeshipStandardUser::lastUpdate($data['module_id'], $user->id);
				}

				// If logged in user is not learner, update contact date too
				if (!\APP\Auth::isLearner()) {
					$manager_update_date = \Models\User::find(\APP\Auth::getUserId());
					$manager_update_date->last_contact_date = \Carbon\Carbon::now();
					$manager_update_date->save();
				}

			} else {
				echo "module empty, not evidence \n";
				//echo "no insert \n";
				//echo $learning;
				// error I suppose
			}
		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results', 'misc-permissions-meetings'], 'insert'));

	// Get file, hash is to identify file and file name is just for saving file purposes.
	$group->get("/upload/{hash}/{evidence}", function (Request $request, Response $response, array $args) {

		if ($this->get('settings')["DisableFileDownload"]) {
			return \APP\Tools::returnCode($request, $response, '403');
		}

		session_write_close(); // This somehow downloads files in background and blocks all requests, need to investigate further, so unblocking from session.
		$evidence = \Models\LearningModuleEvidence
			::where('hash', $args['hash'])
			->where('status', true)
			//->where('evidence', $args['evidence'])
			->with('module')
			->first()
		;

		if ($evidence) {
			$filename = $this->get('settings')["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension;
		}

		if ($evidence && is_file($filename)) {
			if (
				\APP\Auth::isManagerOf($evidence->user_id) ||
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				$evidence->user_id == \APP\Auth::getUserId() ||
				$evidence->added_by == \APP\Auth::getUserId() ||
				$evidence->manager ||
				$evidence->user_id == $evidence->module->created_by
				// OR! or evidence is added in blog_entry by creator.
			) {
				$fileStream = new OpenStream($filename, 'r');
				$finfo = finfo_open(FILEINFO_MIME_TYPE);

				$response = $response
					//->withHeader('Content-Transfer-Encoding', 'Binary')
					//->withHeader('Content-Disposition', 'attachment; filename="' . basename($filename) . '"')
					//->withHeader('Expires', '0')
					//->withHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
					//->withHeader('Pragma', 'public')
					->withHeader('Content-Length', filesize($filename))
					//->withHeader('Content-Type', FILEINFO_MIME_TYPE)

					//->withHeader('Content-Type', 'application/force-download')
					//->withHeader('Content-Type', 'application/octet-stream')
					//->withHeader('Content-Type', 'application/download')
					//->withHeader('Content-Description', 'File Transfer')
					->withHeader('Content-Type', finfo_file($finfo, $filename))
					->withBody($fileStream)
				;
			} else {
				return \APP\Tools::returnCode($request, $response, '403');
			}
		} else {
			return \APP\Tools::returnCode($request, $response, '404');
		}
		return $response;
	})->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));

	$group->post('/evidence/list/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		if (!\APP\Auth::permission('learner_prifile__show_evidence')) {
			\APP\Tools::returnCode($request, $response, 403);
		}


		$user_id = $args["user_id"];
		$params = $request->getParsedBody();

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}
		$query = \Models\LearningModuleEvidence
			::where('user_id',$user_id)
			->with('module')
			->with(['LearningResult' => function ($query) use ($args) {
				$query
					->where('user_id', $args['user_id'])
					->where('refreshed', 0)
				;
			}])
		;
		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write(json_encode($p));
		return $response
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getSessionCheck());


	// Used mostly for reflective log, for now, compresses all text information added by user in pdf file
	$group->get("/pdf-info/{result_id}/{file_name}", function (Request $request, Response $response, array $args) {
		$result = \Models\LearningResult
			::where('id', $args['result_id'])
			->with(['module' => function ($query) {
				$query
					->with('type')
				;
			}])
			->with(["comments" => function ($query) use ($args) {
				$query
					->where('status', true)
					->select()
					->addSelect(DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"))
					->with(["createdby" => function ($query) {
						$query
							->select("id", "fname", "lname", "role_id")
							->with('role')
						;
					}])
					->whereIn('learning_results_comments.comment_by_user_id',
						\Models\LearningResult
							::select('user_id')
							->where('id', $args['result_id'])
							->get()
					)
				;
			}])
			->first()
		;

		if ($result) {
			if (
				\APP\Auth::isManagerOf($result->user_id) ||
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				$result->user_id == \APP\Auth::getUserId()
			) {
				\Models\LearningModule::saveDataAsPDF($result);
			} else {
				return \APP\Tools::returnCode($request, $response, 403, 'You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.');
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 404);
		}
		return $response;
	})->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));


	/*
		Get all meetings from current evidence type resource and current logged in user.
	*/
	$group->get("/evidence/{evidence:[0-9]+}/meeting/list", function (Request $request, Response $response, array $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$evidencemeetings = \Models\LearningModuleEvidenceMeeting
			::where('user_id', '=', $user->id)
			->where('learning_modules_id', '=', $args['evidence'])
			->where('status', '=', 1)
			->get()
		;
		$response->getBody()->write(json_encode($evidencemeetings));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	/*
		Delete unconfirmed meeting from evidence resource, accepted meeting can't be deleted.
	*/
	$group->delete("/evidence/{evidence:[0-9]+}/meeting/cancel/{meeting:[0-9]+}", function (Request $request, Response $response, array $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$evidencemeetings = \Models\LearningModuleEvidenceMeeting
			::where('id', '=', $args['meeting'])
			->where('user_id', '=', $user->id)
			->where('learning_modules_id', '=', $args['evidence'])
			->where('status', '=', 1)
			->where('manager_accepted', '=', 0)
			->firstOrFail()
		;
		$evidencemeetings->delete();
		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($evidencemeetings))
			;
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results'], 'disable'));

	/*
		Get all active evidence attached meetings from current logged in user.
	*/
	$group->get("/meeting/list", function (Request $request, Response $response, array $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$meetings = \Models\LearningModuleEvidenceMeeting
			::where('user_id', '=', $user->id)
			->where('status', '=', 1)
			->get();
		;
		$response->getBody()->write(json_encode($meetings));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


	/*
		Meeting approval by trainee or Manager
	*/
	$group->get("/meeting/approve/{meeting_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());

		$meeting = \Models\LearningModuleEvidenceMeeting
			::find($args['meeting_id'])
		;


		if (
			\APP\Auth::isAdmin()
			||
			\APP\Auth::isManagerOf($meeting->user_id)
		) {
			$meeting->approved_by = $user->id;
			$meeting->manager_accepted = 1;
			$meeting->save();
		} elseif ($user->id == $meeting->user_id) {
			$meeting->trainee_accepted = 1;
			$meeting->save();
		} else {
			return \APP\Tools::returnCode($request, $response, 401);
		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'update'));


	$group->get("/meeting/disable/{meeting_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$meeting = \Models\LearningModuleEvidenceMeeting
			::find($args['meeting_id'])
		;

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($meeting->user_id) ||
			\APP\Auth::accessAllLearners()
		) {
			$meeting->status = 0;
			$meeting->save();
		} else {
			return \APP\Tools::returnCode($request, $response, 401);
		}

		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'misc-permissions-meetings'], 'disable'));

	$group->get('/learnerwalkthrough', function (Request $request, Response $response, array $args) {
		return $this->get('view')->render($response, 'html/learnerwalkthrough.html', [
			"LMSUri" => $this->get('settings')["LMSUri"],
			"learner_introduction" => $this->get('settings')["licensing"]['learner_introduction']
		]);
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results'], 'select'));

	$group->get('/smcr-help', function (Request $request, Response $response, array $args) {
		$user = \APP\Auth::getUser();

		$staff_type_id = $user->staff_type_id;

		$smcr_staff_help = [
			1 => 'SMCR_ConductRulesWalkthrough', // Standard (Conduct Rules)
			2 => 'SMCR_CertificationStaffWalkthrough', // Certification Staff
			3 => 'SMCR_SeniorManagerWalkthrough', // Senior Manager
		];

		if (!$staff_type_id) {
			$help_file = $smcr_staff_help[1];
		} else {
			$help_file = $smcr_staff_help[$staff_type_id];
		}

		return $this->get('view')->render($response, 'html/' . $help_file . '.html', [
			"LMSUri" => $this->get('settings')["LMSUri"]
		]);

	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results'], 'select'));


	$group->get('/distributorwalkthrough', function (Request $request, Response $response, array $args) {
		return $this->get('view')->render($response, 'html/distributorwalkthrough.html', [
			"LMSUri" => $this->get('settings')["LMSUri"]
		]);
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results'], 'select'));


	$group->get('/video-tour', function (Request $request, Response $response, array $args) {
		return $this->get('view')->render($response, 'html/video-tour.html', [
			"LMSUri" => $this->get('settings')["LMSUri"],
			"licensing" => $this->get('settings')["licensing"]
		]);
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));


	// Remove evidence file, by user who created it.
	$group->get("/myevidence/remove/{evidence_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());

		$evidence = \Models\LearningModuleEvidence::find($args['evidence_id']);
		if ($evidence) {
			$learning_module = \Models\LearningModule::find($evidence->learning_modules_id);

			if (
				$evidence->added_by == $user->id ||
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManagerOf($evidence->added_by) ||
				(
					\APP\Auth::accessAllLearners() &&
					\APP\Auth::accessAllCompanies()
				)
			) {
				$filename = $this->get('settings')["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension;
				if (
					is_file($filename)
				) {
					unlink($filename);
				}
				$grading = new \APP\Grading(true);
				$grading->deleteOpenAIData($evidence);
				// Delete record anyway, even if file does not exists.
				$evidence->delete();
			} else {
				return \APP\Tools::returnCode($request, $response, 403);
			}
		}
		return
			$response
		;
	})->add(\APP\Auth::getSessionCheck());

	$group->put("/evidence/remove", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$deleted_list = [];

		if (
			isset($data['list']) &&
			is_array($data['list'])
		) {
			foreach ($data['list'] as $evidence_id) {
				$evidence = \Models\LearningModuleEvidence::find($evidence_id);
				if ($evidence) {
					if (
						$evidence->added_by == $user->id ||
						\APP\Auth::isAdmin() ||
						\APP\Auth::isManagerOf($evidence->added_by) ||
						(
							\APP\Auth::accessAllLearners() &&
							\APP\Auth::accessAllCompanies()
						)
					) {
						$filename = $this->get('settings')["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension;
						if (
							is_file($filename)
						) {
							unlink($filename);
						}

						$grading = new \APP\Grading();
						$grading->deleteOpenAIData($evidence);

						// Delete record anyway, even if file does not exists.
						$evidence->delete();
						$deleted_list[] = $evidence_id;
					} else {
						return \APP\Tools::returnCode($request, $response, 403);
					}
				}
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 400);
		}

		$response->getBody()->write(json_encode($deleted_list));
		return
			$response
		;
	})->add(\APP\Auth::getSessionCheck());

	$group->post("/programme-resources[/{type}]", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (empty($data['lr_ids'])) {
			return $response;
		}

		if (isset($data['user_id'])) {
			$user_id = $data['user_id'];
		}

		if (
			\APP\Auth::isLearner() ||
			empty($user_id)
		) {
			$user_id = \APP\Auth::getUserId();
		}
		$user = \Models\User::findOrFail($user_id);

		$response_data = [];

		$results = \Models\LearningResult
			::whereIn('id', $data['lr_ids'])
			->where('user_id', $user_id)
			->with(['module' => function ($query) {
				$query
					->select(
						'id',
						'name',
						'category_id',
						'type_id'
					)
					->with(['category' => function ($query) {
						$query
							->select(
								'id',
								'name'
							)
						;
					}])
					->with(['type' => function ($query) {
						$query
							->select(
								'id',
								'name'
							)
						;
					}])
				;
			}])
			->select(
				'learning_module_id',
				'completion_status',
				DB::raw('
					DATE_FORMAT((
						CASE
							WHEN
								learning_results.completion_date_custom IS NOT NULL
							THEN
								learning_results.completion_date_custom
							ELSE
								CASE
									WHEN
										learning_results.grace_at IS NOT NULL
									THEN
										learning_results.grace_at
									ELSE
										learning_results.due_at
								END
						END
					), "' . \APP\Tools::defaultDateFormatMYSQL() . '") AS expected_completion_date_uk
				')
			)
			->get()
		;

		if (
			$args['type'] == 'download' &&
			!empty($data['programme_name'])
		) {
			$download_file_name = uniqid(\APP\Tools::safeName($data['programme_name']) . ".") . ".xlsx";

			$export_fields = [
				"Name" => "module.name",
				"Category" => "module.category.name",
				"Type" => "module.type.name",
				"Status" => "completion_status",
				"Due Before" => "expected_completion_date_uk",
			];

			\APP\Tools::generateExcelDownload(
				$results,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);
			$response_data = $download_file_name;
		}

		if ($args['type'] == 'print') {
			$response_data = $results;
		}

		$response->getBody()->write(json_encode($response_data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


});
