<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;

$app->group("/export",  function ($group) {

	// Will list previous months and active programme/standard users in those months, those users can be exported as ILR entries.
	$group->post('/ilr/yearly/list', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		// TODO: Not necesary cron needed, check if latest entry is older than day, run everything again!

		$last_entry = \Models\YearlyIlrExport
			::orderBy('updated_at', 'desc')
			->first()
		;

		// if data has not been updated in last 3 hours, update it!
		if (isset($last_entry->updated_at)) {
			$last_entry_date = \Carbon\Carbon::parse($last_entry->updated_at);
		}

		if (
			//true
			empty($last_entry_date) ||
			$last_entry_date->diffInHours(\Carbon\Carbon::now()) > 3
		) {
			// Get all learning deliveries and update export data
			$learning_deliveries = \Models\IlrLearningDelivery
				::whereIn('user_id',
					\Models\User
						::select('id')
						->where('status', true)
						->where('exclude_from_reports', false)
						->where('exclude_from_ilr_export', false)
						->get()
				)
				//->where('user_id', 193)
				->get()
			;

			foreach ($learning_deliveries as $key => $LearningDelivery) {
				$LearnStartDate = \Carbon\Carbon::parse($LearningDelivery->LearnStartDate);
				$date_to = \Carbon\Carbon::parse($LearningDelivery->LearnPlanEndDate);
				$LearnActEndDate = false;
				if ($LearningDelivery->LearnActEndDate) {
					$date_to = \Carbon\Carbon::parse($LearningDelivery->LearnActEndDate); // LearnActEndDate takes priority
					$LearnActEndDate = true;
				}

				$WithdrawDate = false;
				if ($LearningDelivery->WithdrawDate) {
					$WithdrawDate = \Carbon\Carbon::parse($LearningDelivery->WithdrawDate);
				}

				$LearnStartDate = false;
				if ($LearningDelivery->LearnStartDate) {
					$LearnStartDate = \Carbon\Carbon::parse($LearningDelivery->LearnStartDate);
				}
				$LearnActEndDate = false;
				if ($LearningDelivery->LearnActEndDate) {
					$LearnActEndDate = \Carbon\Carbon::parse($LearningDelivery->LearnActEndDate);
				}
				$AchDate = false;
				if ($LearningDelivery->AchDate) {
					$AchDate = \Carbon\Carbon::parse($LearningDelivery->AchDate);
				}


				$starting_year = 2017;
				$ending_year = \Carbon\Carbon::now()->addYears(2)->year;
				while ($starting_year <= $ending_year) {
					$start_date = \Carbon\Carbon::create($starting_year, 8, 1, 0);
					$end_date = \Carbon\Carbon::create(($starting_year + 1), 7, 31, 0);
					if (
							(
								$LearnStartDate &&
								$LearnStartDate->lessThanOrEqualTo($end_date)
							) &&
							(

								(
									$LearnActEndDate &&
									$LearnActEndDate->greaterThan($start_date)
								) ||
								(
									$AchDate &&
									$AchDate->between($start_date, $end_date)
								) ||
								(
									$LearningDelivery->CompStatus &&
									$LearningDelivery->CompStatus == 3 &&
									$WithdrawDate &&
									$WithdrawDate->between($start_date, $end_date)
								) ||
								!$LearnActEndDate
							)
					) {
						$yearly = \Models\YearlyIlrExport::firstOrNew(
							[
								'funding_start' => $start_date,
								'funding_end' => $end_date,
							]
						);
						if (is_array(json_decode($yearly->users ?? "[]"))) {
							$yearly_users = json_decode($yearly->users ?? "[]", true);
							if (!in_array($LearningDelivery->user_id, $yearly_users)) {
								$yearly_users[] = $LearningDelivery->user_id;
								$yearly->users = json_encode($yearly_users);
							}
						} else {
							$yearly_users = [$LearningDelivery->user_id];
							$yearly->users = json_encode($yearly_users);
						}
						$yearly->user_count = count(json_decode($yearly->users, true));
						$yearly->save();

					}
					$starting_year++;
				}
			}

			// update last entry update_at date to keep track of when last update was done.
			if ($last_entry) {
				$last_entry->touch();
			}
		}

		$query = \Models\YearlyIlrExport
			//::where('id', '>', 0)
			::where('funding_start', '<=', \Carbon\Carbon::now())
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query
				->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC")
			;
		} else {
			$query
				->orderBy('funding_start', 'desc')
			;
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-export-ilr', 'select'));


	// Export selected year in ILR format
	$group->post('/ilr/yearly', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		if (isset($params['data'])) {
			$export_config = [];
			$export_config['data'] = $params['data'];
		}

		$export = \Models\YearlyIlrExport
			::find($params['export_id'])
		;

		$users = \Models\User
			::whereIn('id', json_decode($export->users, true))
			->get();
		;

		$export_response = \APP\Export::ilrUsers($users, $export_config, $this->get('settings')["LMSTempPath"], $this->get('settings')["ilr_UUID"]);


		if ($export_response) {
			$response->getBody()->write(json_encode($export_response));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			return \APP\Tools::returnCode($request, $response, 500);
		}


	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-export-ilr', 'select'));


	// Built for multiple export types, flexible
	// In reality only works for ILR as there is no other exports
	$group->post('/{type:[\/a-z]*}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();
		$params_original = $params;

		if (!empty($params['export_config'])) {
			$export_config = $params['export_config'];
			unset($params['export_config']);
		}
		$query_id = $export_config['query'];
		$params = $request->getParsedBody();
		$params['full_user_details'] = true;
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id . "List";
		$query = $QueryBuilder::generate($params, $args);
		$users = \APP\SmartTable::searchPaginate($params, $query, false, false);

		$export_response = false;

		switch($args["type"]) {
			case "ilr":
				$export_response = \APP\Export::ilrUsers(
					$users,
					$export_config,
					$this->get('settings')["LMSTempPath"],
					$this->get('settings')["ilr_UUID"],
					$params_original
				);
			break;
		}


		if ($export_response) {
			$response->getBody()->write(json_encode($export_response));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			return \APP\Tools::returnCode($request, $response, 500);
		}
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-export-ilr', 'select'));
});
