<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/trainingschedulereports",  function ($group) {

	$group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'trainingschedulereportsList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);


		switch($args["option"]) {
			case "/download":
				$query
					->selectRaw("CONCAT(users.fname, ' ', users.lname) as trainee_name")
				;

				$query = \APP\SmartTable::searchPaginate($params, $query, false, false, false, false, true);

				return \Models\CustomReview::streamDownloadFile(
					'trainingschedule',
					$query,
					$this->get('settings')["LMSTempPath"],
					$response
				);
		return $response->withHeader('Content-Type', 'application/json');
			break;
			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'training schedule reports tpl', // Template name
					['user', 'id'], // where to look for user's ID in this query
					$args
				);
			break;
		}


		$p = \APP\SmartTable::searchPaginate($params, $query, false, true, true);


		foreach ($p as $key => $entry) {
			$entry->setAppends(['standard_list']);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-resources-data', 'select'));

});