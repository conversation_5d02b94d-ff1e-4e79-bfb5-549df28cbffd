<?php

use APP\Auth;
use APP\Controllers\PowerBIController;
use APP\SmartTable;
use Models\Company;
use Models\ManagerUser;
use Models\PowerBiAccessToken;
use Models\User;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/powerbi-datasets", function ($group) use ($app) {

    $group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $dataset = \Models\PowerBiDataset::find($args["id"]);
        $dataset->status = 0;
        $dataset->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-datasets', 'disable'));

    $group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $dataset = \Models\PowerBiDataset::find($args["id"]);
        $dataset->status = 1;
        $dataset->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-datasets', 'disable'));


    $group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {

        $dataset = \Models\PowerBiDataset::with('courseSet')->find($args["id"]);
        if ($dataset) {
            $response->getBody()->write($dataset->toJson());
            return $response->withHeader('Content-Type', 'application/json');
        } else {
            return \APP\Tools::returnCode($request, $response, 404);
        }

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-datasets', 'select'));

    $group->post('/list', function (Request $request, Response $response) {

        $params = $request->getParsedBody();

        $query = \Models\PowerBiDataset::with('courseSet');

        if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
            $query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
        }

        if (isset($params["search"]) && is_array($params["search"])) {
            if (isset($params["search"]["refresh"])) {
                unset($params["search"]["refresh"]);
            }
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);

        $response->getBody()->write($p->toJson());
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-datasets', 'select'));

    // $this->post("/update-report/{id:[0-9]+}", PowerBIController::class . ':cloneOrUpdateReport')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-datasets', 'insert'));
    //
    // $this->delete('/{report_id:[0-9]+}', PowerBIController::class . ':deleteReport')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-power-bi-datasets', 'select'));

});
