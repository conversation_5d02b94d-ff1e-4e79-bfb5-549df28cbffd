<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/smcr-staff-type",  function ($group) {

	// Get spcific SMCR Staff Type
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$city = \Models\SmcrStaffType::find($args["id"]);

		$response->getBody()->write(json_encode($city));
		return $response->withHeader('Content-Type', 'application/json');
	});

	// Update existing SMCR Staff Type
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$city = \Models\SmcrStaffType::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$city->name = $data["name"];
		}

		$city->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-staff-type', 'update'));

	// Disable SMCR Staff Type
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$city = \Models\SmcrStaffType::find($args["id"]);
		$city->status = false;
		$city->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-staff-type', 'disable'));

	// Enable SMCR Staff Type
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$city = \Models\SmcrStaffType::find($args["id"]);
		$city->status = true;
		$city->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-staff-type', 'disable'));

	// Get all list of enabled SMCR Staff Type
	$group->get('/all', function (Request $request, Response $response) {
		$query = \Models\SmcrStaffType
			::where("status", true)
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	});


	// Add new SMCR function
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$city = new \Models\SmcrStaffType;

		if (isset($data["name"])) {
			$city->name = $data["name"];
		}

		$city->status = true;
		$city->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-staff-type', 'insert'));

   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\SmcrStaffType
			::where('id', '>', 0)
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name"
			];


			$download_file_name = uniqid("smcr-staff-type.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');

		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-staff-type', 'select'));
});