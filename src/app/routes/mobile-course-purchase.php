<?php

use APP\Services\LearningModuleService;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\AvailableModule;
use Models\LearningModule;

$app->group("/mobile-purchase", function ($group) {
    $group->post('/enroll', function (Request $request, Response $response) {
        try {
            $data = $request->getParsedBody();
            $userId = $data['userId'] ?? null;
            $courseId = $data['courseId'] ?? null;
            $price = $data['price'];
            $paymentIntent = $data['paymentIntent'];

            if (!$userId || !$courseId) {
                return \APP\Tools::returnCode($request, $response, 422, 'Missing userId or courseId');
            }

            $available_module = AvailableModule::getItem($courseId);
            if (!$available_module) {
                return \APP\Tools::returnCode($request, $response, 404, 'Course not found');
            }

            $settings = $this->get('settings');

            // Build module data
            $module_data = [
                'description' => $available_module->description ?: "This course has been imported from Open eLMS Catalogue",
                'cost' => empty($price) ? $available_module->price : $price,
                'keywords' => $available_module->keywords,
                'material' => [
                    'scorm_standard' => "2",
                    'min_passing_percentage' => "70",
                    'course_complete_status' => "0",
                ],
                'availableModuleId' => $courseId,
                'name' => $available_module->title,
                'type_id' => 1,
            ];

            // Override description from settings if available
            if (isset($settings["DefaultCourseDescriptions"][$available_module->title])) {
                $module_data['description'] = $settings["DefaultCourseDescriptions"][$available_module->title];
            }

            // Insert course
            $learning = LearningModule::insertImportModule($module_data, $settings, $response);
            if (isset($learning["error"])) {
                return \APP\Tools::returnCode($request, $response, 500, $learning["error"]);
            }

            // Enroll user
            LearningModuleService::enrolLearningModule($userId, $learning["id"], $price, $paymentIntent);
            return \APP\Tools::returnCode($request, $response, 200, 'Enrollment successful', ['learningModuleId' => $learning["id"]]);

        } catch (\Exception $e) {
            return \APP\Tools::returnCode($request, $response, 500, 'An unexpected error occurred: ' . $e->getMessage());
        }
    })->add(\APP\Auth::getBasicTokenCheck("APITokenAccessMediaLibrary"));
});
