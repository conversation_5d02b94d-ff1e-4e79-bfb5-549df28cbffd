<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
$app->group("/comment",  function ($group) {


	$group->post("/new", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (\APP\Auth::isDemoUser()) {
			return \APP\Tools::returnCode($request, $response, 403, '403 Forbidden');
		}

		$comment = new \Models\Comment;
		$comment->table_row_id = isset($data['table_row_id']) ? $data['table_row_id'] : null;
		$comment->table_name = isset($data['table_name']) ? $data['table_name'] : null;
		$comment->group = isset($data['group']) ? $data['group'] : null;
		$comment->comment = isset($data['comment']) ? $data['comment'] : null;
		$comment->added_by = \APP\Auth::getUserId();
		$comment->added_for = isset($data['added_for']) ? $data['added_for'] : null;
		$comment->visible_learner = isset($data['visible_learner']) ? $data['visible_learner'] : (\APP\Auth::isLearner() ? true : false);
		$comment->url = isset($data['url']) ? $data['url'] : false;
		$comment->save();
		$response->getBody()->write(json_encode($comment));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	$group->put("/visibility/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (
			\APP\Auth::isDemoUser() ||
			\APP\Auth::isLearner()
		) {
			return \APP\Tools::returnCode($request, $response, 403, '403 Forbidden');
		}

		$comment = \Models\Comment::find($args['id']);
		$comment->visible_learner = !$comment->visible_learner;
		$comment->save();

	})->add(\APP\Auth::getSessionCheck());

	$group->delete("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$comment = \Models\Comment
			::find($args['id'])
		;

		if (!$comment) {
			return \APP\Tools::returnCode($request, $response, 404, '404 Not Found');
		}

		if (
			$comment &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManager() ||
				\APP\Auth::accessAllLearners() ||
				$comment->added_by == \APP\Auth::getUserId()
			)
		) {
			// Return comment group if exists
			if ($comment->group) {
				$response->getBody()->write($comment->group);
			}
			$comment->delete();
		} else {
			return \APP\Tools::returnCode($request, $response, 403, '403 Forbidden');
		}
		return
			$response
		;
	})->add(\APP\Auth::getSessionCheck());

	// Retrieve list of comments for specific article
	$group->get("/list/{table_name}/{table_row_id:[0-9]+}[/{group}]", function (Request $request, Response $response, array $args) {

		// Fail is user is DEMO
		if (\APP\Auth::isDemoUser()) {
			return \APP\Tools::returnCode($request, $response, 403, '403 Forbidden');
		}

		$comments = \Models\Comment
			::where('table_name', $args['table_name'])
			->where('table_row_id', $args['table_row_id'])
			->where('status', true)
			->with(['AddedBy' => function($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id'
					)
					->with('role')
				;
			}])
		;

		if (!empty($args['group'])) {
			$comments = $comments
				->where('group', $args['group'])
			;
		}

		if (\APP\Auth::isLearner()) {
			$comments = $comments
				->where(function ($query) {
					$query
						->where('added_by', \APP\Auth::getUserId())
						->orWhere('added_for', \APP\Auth::getUserId())
					;
				})
			;
		}

		$comments = $comments
			->get()
		;
		$response->getBody()->write(json_encode($comments));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));

});
