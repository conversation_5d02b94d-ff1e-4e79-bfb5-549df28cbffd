<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/anders-pink",  function ($group) {

	$group->get("/{type:briefings|boards}[/{id:[0-9]+}]" , function (Request $request, Response $response, $args) {
		$api_key = \APP\Tools::getConfig('AndersPinkApiKey');
		$data = [];
		if (
			!$api_key ||
			$api_key == ''
		) {
			return \APP\Tools::returnCode($request, $response, 503, 'No API key defined!');
		}
		if (
			isset($args['id']) &&
			intval($args['id']) > 0
		) {
			$data = \APP\Tools::getCurlData('https://anderspink.com/api/v3/' . $args['type'] . '/' . $args['id'], ['X-Api-Key: ' . $api_key]);
		} else {
			$data = \APP\Tools::getCurlData('https://anderspink.com/api/v3/' . $args['type'], ['X-Api-Key: ' . $api_key]);
		}

		$response->getBody()->write($data);
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


});