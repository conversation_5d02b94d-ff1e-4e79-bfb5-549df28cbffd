<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/smcr-committee-role-person",  function ($group) {

	// Get specific committee role person assignment
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$committee_role_person = \Models\SmcrCommitteeRolePerson::find($args["id"]);

		$response->getBody()->write(json_encode($committee_role_person));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-role-person', 'select'));


	// Senior staff can remove himself from committees.
	$group->delete("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$user = \APP\Auth::getUser();
		if (
			$user->staff_type_id == 3
		) {
			$committee_role_person = \Models\SmcrCommitteeRolePerson
				::where('id', $args["id"])
				->where('user_id', $user->id)
				->first()
			;
			$committee_role_person->status = false;
			$committee_role_person->save();
		} else {
			return $response
				->withStatus(401)
				->withHeader('Content-Type', 'text/html')
				->write('401 Unauthorized')
			;
		}


		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($committee_role_person))
		;
	})->add(\APP\Auth::getSessionCheck());


	// SMCR staff 2/3 can reject assigned role for committee
	$group->put("/learner/{status:reject|accept}/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user = \APP\Auth::getUser();

		if (
			$user->staff_type_id == 2 ||
			$user->staff_type_id == 3
		) {
			$committee_role_person = \Models\SmcrCommitteeRolePerson
				::where('id', $args["id"])
				->where('user_id', $user->id)
				//->with('SmcrCommitteeRole.SmcrCommittee')
				->first()
			;

			if ($committee_role_person) {
				if ($args['status'] == 'reject') {
					$committee_role_person->completion_status = 'Rejected';

					// Send e-mail to all managers that committee is understaffed!
					if (!\APP\Auth::isAdminInterface()) {
						$manager_ids = \Models\User::where('status', true)
							->whereIn(
								'role_id',
								\Models\Role
									::select('id')
									->where('status', true)
									->where('is_manager', true)
									->get()
							)
							->pluck('id')
							->toArray()
						;
						if (
							$manager_ids &&
							count($manager_ids) > 0
						) {
							$template = \Models\EmailTemplate
								::where('name', 'Committee needs new member(s)')
								->where('status', true)
								->first()
							;
							if ($template) {
								$email_queue = new \Models\EmailQueue;
								$email_queue->email_template_id = $template->id;
								$email_queue->recipients = $manager_ids;
								$email_queue->custom_variables = json_encode([
									'COMMITTEE' => $committee_role_person->SmcrCommitteeRole->SmcrCommittee->name,
								]);
								$email_queue->save();
							}
						}
					}
				}
				if ($args['status'] == 'accept') {
					$committee_role_person->completion_status = 'Accepted';
					if (isset($data['assigned'])) {
						$committee_role_person->assigned = \Carbon\Carbon::parse($data['assigned']);
					}
				}
				$committee_role_person->save();
			}
		} else {
			return $response
				->withStatus(401)
				->withHeader('Content-Type', 'text/html')
				->write('401 Unauthorized')
			;
		}


		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($committee_role_person))
		;
	})->add(\APP\Auth::getSessionCheck());


	// Get a list of assignments for logged in user
	$group->get("/user" , function (Request $request, Response $response, $args) {
		$query = \Models\SmcrCommitteeRolePerson
			::with(['SmcrCommitteeRole' => function($query) use ($args) {
				$query
					->with(['SmcrCommittee' => function($query) use ($args) {
						$query
							->where('status', true)
						;
					}])
					->whereHas('SmcrCommittee', function ($query) use($args) {
						$query
							->where('status', true)
						;
					})
					->where('status', true)
				;
			}])
			->whereHas('SmcrCommitteeRole', function ($query) use($args) {
				$query
					->where('status', true)
				;
			})
			->select(
				'smcr_committee_role_personnel.*',
				DB::raw("DATE_FORMAT(assigned,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS assigned_uk"),
				DB::raw("DATE_FORMAT(updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk")
			)
			->where('user_id', \APP\Auth::getUserId())
			->where('status', true)
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// Update existing SMCR committee role personnel
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$person = \Models\SmcrCommitteeRolePerson::find($args["id"]);
		if (isset($data['user_id'])) {
			$person->assigned = \Carbon\Carbon::now();
			$person->vacated = null;
			$user = \Models\User::find($data['user_id']);

			$template = \Models\EmailTemplate::getTemplate('Committee position vacancy assigned - action needed');
			if ($template) {
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$user->id];
				$email_queue->from = null;
				$email_queue->save();
			}

		} else {
			$person->vacated = \Carbon\Carbon::now();
			$person->assigned = null;
		}

		// Reset completion status if user id changes
		if (
			is_null($data['user_id']) ||
			(
				isset($data['user_id']) &&
				$person->user_id != $data['user_id']
			)
		) {
			$person->completion_status = 'Assigned';
		}

		$person->user_id = isset($data['user_id']) ? $data['user_id'] : null;
		$person->save();

		if (isset($data['committee_id'])) {
			\Models\SmcrCommittee::updateCounts($data["committee_id"]);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-role-person', 'update'));


	// Modify SMCR role.
	$group->put('/{status:disable|accept|reject|update}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$committee_role_person = \Models\SmcrCommitteeRolePerson::find($args["id"]);
		if ($args['status'] == 'disable') {
			$committee_role_person->status = false;
		}
		if ($args['status'] == 'accept') {
			$committee_role_person->completion_status = 'Accepted';
		}
		if ($args['status'] == 'reject') {
			$committee_role_person->completion_status = 'Rejected';
		}
		if (
			$args['status'] == 'update' &&
			isset($data['assigned'])
		) {
			$committee_role_person->assigned = \Carbon\Carbon::parse($data['assigned']);
		}

		$committee_role_person->save();

		if (isset($data['committee_id'])) {
			\Models\SmcrCommittee::updateCounts($data["committee_id"]);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-role-person', 'disable'));

	// Enable SMCR committee role
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$committee_role_person = \Models\SmcrCommitteeRolePerson::find($args["id"]);
		$committee_role_person->status = 1;
		$committee_role_person->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-role-person', 'disable'));

	// Get all list of enabled SMCR functions role
	$group->get('/all', function (Request $request, Response $response) {
		$query = \Models\SmcrCommitteeRolePerson
			::where("status", true)
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-role-person', 'select'));


	// Add new SMCR committee role person
	$group->post('/new', function (Request $request, Response $response) {
		$user = \APP\Auth::getUser();
		$data = $request->getParsedBody();

		if (
			isset($data["smcr_committee_role_id"]) &&
			(
				\APP\Auth::checkStructureAccess(['system-setup-organisation-committee-role-person'], 'insert') ||
				(
					$user->staff_type_id == 3 &&
					isset($data["user_id"]) &&
					$data["user_id"] == $user->id
				)
			)
		) {
			$committee_role_person = new \Models\SmcrCommitteeRolePerson;
			$committee_role_person->smcr_committee_role_id = $data["smcr_committee_role_id"];
			$committee_role_person->user_id = isset($data["user_id"]) ? $data["user_id"] : null;
			$committee_role_person->vacated = null;
			$committee_role_person->assigned = null;
			$committee_role_person->status = true;
			$committee_role_person->save();

			if (isset($data["committee_id"])) {
				\Models\SmcrCommittee::updateCounts($data["committee_id"]);
			}
		} else {
			$response = $response
				->withStatus(401)
				->withHeader('Content-Type', 'text/html')
				->write('401 Unauthorized')
			;
		}



		return $response;
	})->add(\APP\Auth::getSessionCheck());

	// Get paginated list of cimittee role personnel
   $group->post('/list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\SmcrCommitteeRolePerson
			::where('id', '>', 0)
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-committee-role-person', 'select'));
});