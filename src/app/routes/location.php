<?php

use APP\Form;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/location",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$location = \Models\Location::find($args["id"]);
		$location->status = 0;
		$location->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-locations', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$location = \Models\Location::find($args["id"]);
		$location->status = 1;
		$location->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-locations', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$location = \Models\Location::find($args["id"]);

		$response->getBody()->write(json_encode($location));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-locations', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$location = new \Models\Location;

		if (isset($data["name"]))
		{
			$location->name = $data["name"];
		}
		$location->status = 1;
		$location->save();
        if(isset($data['custom-field'])){
            Form::saveCustomForm($data['custom-field'],'location',$location->id);
        }
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-locations', 'insert'));

	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$location = \Models\Location::find($args["id"]);

		$location->name = $data["name"];
		$location->save();
        if($data['custom-field']){
            Form::saveCustomForm($data['custom-field'],'location',$location->id);
        }
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-locations', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$data = \Models\Location
			::where("status",">",0)
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-locations', 'select'));

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Location::where("id", ">", "0");

		if (isset($params["search"]) && is_array($params["search"])) {

			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download")
		{
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
					"ID" => "id",
					"Location Name" => "name",
			];


			$download_file_name = uniqid("locations.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
						$data,
						$export_fields,
						$this->get('settings')["LMSTempPath"] . $download_file_name
					);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		}
		else
		{
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-locations', 'select'));
});
