<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
$app->group("/favorite",  function ($group) {


	// Create new review favorite
	$group->post("/{type}/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$favorite = \Models\Favorite::firstOrCreate([
			'type' => $args['type'],
			'user_id' => \APP\Auth::getUserId(),
			'relation_id' => $args['id']
		]);
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	$group->delete("/{type}/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$favorite = \Models\Favorite
			::where('type', $args['type'])
			->where('user_id', \APP\Auth::getUserId())
			->where('relation_id', $args['id'])
			->delete()
		;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

});
