<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/learningresourcepopularityreports",  function ($group) {

    $group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'learningresourcepopularityreportList';
		$params = $request->getParsedBody();
		$QueryBuilder = '\\APP\\QueryBuilder\\' . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		switch($args["option"]) {
			case "/download":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \Models\CustomReview::returnDownloadFile(
					'learningresourcepopularity',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);

				/*$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
				*/
			break;

			case "/powerbi":
				set_time_limit(0);

				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::exportToPowerBi(
					'learningresourcepopularity',
					$params["export_config"],
					$data,
					$response
				);
			break;

			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'Learning Resource Popularity Report', // Template name
					['user', 'id'], // where to look for user's ID in this query
					$args
				);
			break;
		}

		// group response by year/month if request is for making a chart.
		if (isset($params["chart"]) && $params["chart"] == 'true') {
			$query->groupBy(
				DB::raw('
					year, month
				')
			);
			$p = $query->get();
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-resources-data', 'select'));
});
// });