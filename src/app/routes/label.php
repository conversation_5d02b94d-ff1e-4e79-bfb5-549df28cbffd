<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/label",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$label = \Models\Label::find($args["id"]);

		$response->getBody()->write($label->toJson());
		return $response->with<PERSON>eader('Content-Type', 'application/json');

	});

	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$label = \Models\Label::find($args["id"]);

		if (isset($data["from_text"]) && isset($data["to_text"])) {
			$label->from_text = $data["from_text"];
			$label->to_text = $data["to_text"];
		}

		$label->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-labels', 'update'));


	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$label = new \Models\Label;

		if (isset($data["from_text"]) && isset($data["to_text"])) {
			$label->from_text = $data["from_text"];
			$label->to_text = $data["to_text"];
		}

		$label->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-labels', 'insert'));


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$group = \Models\Label::find($args["id"]);
		$group->status = false;
		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-labels', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$group = \Models\Label::find($args["id"]);
		$group->status = true;
		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-labels', 'disable'));


   $group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$query = \Models\Label::where("id", ">", 0);

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-labels', 'select'));
});