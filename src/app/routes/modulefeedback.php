<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/modulefeedback",  function ($group) {

	$group->post('/', function (Request $request, Response $response, array $args) {

		$user = \Models\User::findOrFail(\APP\Auth::getUserId());
		$params = $request->getParsedBody();
		$learning = new \Models\ModuleFeedback;

		if (empty($params['moduleid'])) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		// Check if user have this module assigned to him, if not admin/manager
		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isManager() &&
			!\APP\Auth::accessAllLearners()
		) {
			$module = \Models\LearningModule
				::where('id', $params['moduleid'])
				->where('status', true)
				->whereHas('userlearningmodules', function ($query) use ($user) {
					$query
						->where('user_id', $user->id)
					;
				})
				->first()
			;
			if (!$module) {
				return \APP\Tools::returnCode($request, $response, 404);
			}
		}


		$learning->module_id = $params['moduleid'];
		$learning->user_id = $user->id;
		$learning->feedback = $params['feedback'];
		if (isset($params['rating'])) {
			$learning->rating = $params['rating'];
		}
		$learning->save();

		// Update average rating for module
		$ratings = \Models\ModuleFeedback
			::where('module_id', $params['moduleid'])
			->where('rating', '>' , 0)
			->where('status', true)
			->get();
		;

		$total_rating = 0;
		foreach ($ratings as $key => $rating) {
			$total_rating = $total_rating + $rating->rating; // so many rating!
		}

		if (count($ratings) > 0 && $total_rating > 0) {
			$module = \Models\LearningModule
				::find($params['moduleid'])
			;
			$module->rating = round($total_rating / count($ratings),1);
			$module->save();
		}

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck(['trainee-feedback', 'system-setup-learning-feedback'], 'insert'));

/*
	$group->delete('/delete/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		\Models\ModuleFeedback
			::where("id", "=", $args["id"])
			->delete()
		;

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-feedback'], 'disable'));
*/


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$feedback = \Models\ModuleFeedback::find($args["id"]);
		$feedback->status = false;
		$feedback->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-feedback'], 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$feedback = \Models\ModuleFeedback::find($args["id"]);
		$feedback->status = true;
		$feedback->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-feedback'], 'disable'));


	$group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$query_id = 'modulefeedbackList';
		$params = $request->getParsedBody();

		if (isset($params["search"]["additionalSearchParams"]))
		{
			$additional_search_params = json_decode($params["search"]["additionalSearchParams"], true);
			unset($params["search"]["additionalSearchParams"]);
		}

		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		if (isset($additional_search_params["period_from"]) || isset($additional_search_params["period_to"]))
		{
			if (isset($additional_search_params["period_from"]))
			{
				$period_from = \Carbon\Carbon::parse($additional_search_params["period_from"])->startOfDay();
			}
			else
			{
				$period_from = \Carbon\Carbon::now()->subYears(100);
			}

			if (isset($additional_search_params["period_to"]))
			{
				$period_to = \Carbon\Carbon::parse($additional_search_params["period_to"])->endOfDay();
			}
			else
			{
				$period_to = \Carbon\Carbon::now()->addYears(100);
			}

			$query
				->whereBetween("learning_module_feedback.updated_at",[$period_from, $period_to])
			;
		}

		switch($args["option"]) {
			case "/download":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::returnDownloadFile(
					'modulefeedback',
					$data,
					$this->get('settings')["LMSTempPath"],
					$response
				);

			break;

			case "/powerbi":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				return \Models\CustomReview::exportToPowerBi(
					'modulefeedback',
					$params["export_config"],
					$data,
					$response
				);
			break;

			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'Feedback', // Template name
					['user', 'id'], // where to look for user's ID in this query
					$args
				);
			break;
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-feedback', 'select'));

});
