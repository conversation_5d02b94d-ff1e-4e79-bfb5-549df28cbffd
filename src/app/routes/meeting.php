<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
$app->group("/meeting",  function ($group) {


	// Adds new meeting, can be done by <PERSON><PERSON> or Manager body
	$group->post("/new", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$comment = new \Models\Meeting;
		$comment->table_row_id = isset($data['table_row_id']) ? $data['table_row_id'] : null;
		$comment->table_name = isset($data['table_name']) ? $data['table_name'] : null;
		$comment->added_by = \APP\Auth::getUserId();
		$comment->added_for = \APP\Auth::isLearner() ? \APP\Auth::getUserId() : (isset($data['added_for']) ? $data['added_for'] : null);
		$comment->name = isset($data['name']) ? $data['name'] : null;
		$comment->description = isset($data['description']) ? $data['description'] : null;
		$comment->preferred_time = isset($data['preferred_time']) ? $data['preferred_time'] : null;
		$comment->manager_accepted = \APP\Auth::isAdminInterface();
		$comment->learner_accepted = \APP\Auth::isLearner();
		$comment->approved_by = \APP\Auth::isAdminInterface() ? \APP\Auth::getUserId() : null;
		$comment->status = true;

		$comment->save();

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-meetings'], 'insert'));


	// Manager or Learner both need to approve meeting.
	$group->get("/approve/{id:[0-9]+}", function (Request $request, Response $response, array $args) {

		$meeting = \Models\Meeting
			::where('id', $args['id'])
			->where('status', true)
			->first()
		;

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($meeting->added_for) ||
			\APP\Auth::accessAllLearners()
		) {
			$meeting->approved_by = \APP\Auth::getUserId();
			$meeting->manager_accepted = true;
			$meeting->save();
		} elseif (
			\APP\Auth::getUserId() == $meeting->added_for
		) {
			$meeting->learner_accepted = true;
			$meeting->save();
		} else {
			$response = $response->withStatus(401);
			$response->getBody()->write('401 Unauthorized');
		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-meetings'], 'update'));


	// Manager can disable meeting
	$group->get("/disable/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$meeting = \Models\Meeting
			::find($args['id'])
		;

		if (
			\APP\Auth::isAdmin() ||
			\APP\Auth::isManagerOf($meeting->added_for) ||
			\APP\Auth::accessAllLearners()
		) {
			$meeting->status = false;
			$meeting->save();
		} else {
			$response = $response->withStatus(401);
			$response->getBody()->write('401 Unauthorized');
		}

		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-meetings'], 'disable'));


	// If meeting is created and is not yet accepted, it can be deleted (created by error)
	$group->delete("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {

		$meeting = \Models\Meeting
			::where('id', $args['id'])
			->where('status', true)
			->where('added_by', \APP\Auth::getUserId())
		;

		if (\APP\Auth::isLearner()) {
			$meeting = $meeting
				->where('added_for', \APP\Auth::getUserId())
				->where('learner_accepted', true)
				->where('manager_accepted', false)
				->whereNull('approved_by')
			;
		} elseif (\APP\Auth::isAdminInterface()) {
			$meeting = $meeting
				->where('manager_accepted', true)
				->where('learner_accepted', false)
				->where('approved_by', \APP\Auth::getUserId())
			;
		}

		$meeting = $meeting->first();

		if ($meeting) {
			$meeting->delete();
		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results'], 'disable'));

});
