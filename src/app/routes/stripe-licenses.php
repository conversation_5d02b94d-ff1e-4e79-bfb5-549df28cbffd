<?php

use APP\Controllers\PowerBIController;
use APP\Controllers\StripeController;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

$app->group("/stripe-licenses", function ($group) use ($app) {

    $group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {

        $report = \Models\LicensePlan::with(['LicensePlanFeatures.LicenseFeature'])->find($args["id"]);
        if ($report) {
            $response->getBody()->write($report->toJson());
            return $response->withHeader('Content-Type', 'application/json');
        } else {
            return \APP\Tools::returnCode($request, $response, 404);
        }

    })->add(\APP\Auth::getSessionCheck());

    $group->post('/list', function (Request $request, Response $response) {

        $params = $request->getParsedBody();

        $query = \Models\LicensePlan:: with(['LicensePlanFeatures']);

        if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
            $query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
        }

        if (isset($params["search"]) && is_array($params["search"])) {
            if (isset($params["search"]["refresh"])) {
                unset($params["search"]["refresh"]);
            }
        }

        $p = \APP\SmartTable::searchPaginate($params, $query);

        $response->getBody()->write($p->toJson());
        return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getSessionCheck());

    $group->post("/update-license/{id:[0-9]+}", StripeController::class . ':updateLicensePlan')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-stripe-licenses', 'select'));
    $group->post("/update-pricing/{id:[0-9]+}", StripeController::class . ':updatePricing')->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-stripe-licenses', 'select'));

    $group->post('/credits-payment', StripeController::class . ':createCreditsPayment')->add(\APP\Auth::getSessionCheck());
    $group->post('/change-license', StripeController::class . ':changeLicensePlan')->add(\APP\Auth::getSessionCheck());
    $group->post('/license-payment', StripeController::class . ':createLicensePayment')->add(\APP\Auth::getSessionCheck());

    $group->get("/logs", function (Request $request, Response $response, $args) {
        $logFile = $this->get('settings')['logger']['info.path'];
        $lines = file($logFile);
        $filteredLines = array_filter($lines, function($line) {
            return strpos($line, '!STRIPE!') !== false;
        });
        $last100Lines = array_slice($filteredLines, -100);

        if ($last100Lines) {
            $response->getBody()->write(json_encode($last100Lines));
            return $response->withHeader('Content-Type', 'application/json');
        } else {
            return \APP\Tools::returnCode($request, $response, 404);
        }

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-stripe-licenses', 'select'));

});
