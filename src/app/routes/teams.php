<?php

use APP\Teams;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Configuration;
use Slim\Http\Request as HttpRequest;
use Slim\Http\Response as HttpResponse;

$app->group("/teams",  function ($group) use($app) {

	$group->get('/install', function (Request $request, Response $response) {

        $teams = new \APP\Teams(false);
        unset($_SESSION["dt"]);
        unset($_SESSION["tm"]);
        unset($_SESSION["dur"]);

        return $response->withStatus(302)->withHeader('Location', $teams->get_install_link());

    })->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));

    $group->get('/onedrive/{resource_id:[0-9]+}', function (Request $request, Response $response, $args) use ($app) {
        $_SESSION["learningModuleId"] = $args["resource_id"];
        $teams = new \APP\Teams(false);

        setcookie("LMSSite", $this->get('settings')["LMSUri"], time()+3600, "/");

        return $response->withStatus(302)->withHeader('Location', $teams->get_onedrive_authorize_link());

    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->delete('/onedrive/{resource_id:[0-9]+}', function (Request $request, Response $response, $args) use ($app) {

        $lm = \Models\LearningModule::find($args["resource_id"]);
        if (isset($lm->material->onedrive_integration)){
            $material = new stdClass();
            foreach($lm->material as $key => $value) {
                $material->$key = $value;
            }
            $material->onedrive_integration = false;
            $lm->material = $material;
            $lm->save();
        }
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	$group->get('/globaloutlooktoken', function (Request $request, Response $response) use ($app) {

        $teams = new \APP\Teams(false);

        $params = $request->getQueryParams();


        if (isset($params["code"])) {

            $oauth_response = $teams->get_global_calendar_oauth_token($params["code"]);
            $response->getBody()->write($oauth_response->refresh_token);
            return $response;
        } else {
            setcookie("LMSSite", $this->get('settings')["LMSUrl"], time()+3600, "/");
            return $response->withStatus(302)->withHeader('Location', $teams->get_outlook_global_token_authorize_link());
        }

		return
			$response
        ;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->get('/onedrive', function (Request $request, Response $response) use ($app) {

        $teams = new \APP\Teams(false);
        $params = $request->getQueryParams();

        if (isset($params["code"]) && isset($_SESSION["learningModuleId"])) {

            $oauth_response = $teams->get_onedrive_oauth_token($params["code"]);

            $lm = \Models\LearningModule::find($_SESSION["learningModuleId"]);

            // Update material with refresh token
            $material = new \stdClass();
            foreach ($lm->material as $key => $value) {
                $material->$key = $value;
            }
            $material->onedrive_integration = true;
            $material->onedrive_refresh_token = $oauth_response->refresh_token;
            $lm->material = $material;
            $lm->save();

            // Update recordings — new logic:
            $teams->updateRecordings($lm);

            // Build HTML output
            $html = '<script>window.name = "close"; setTimeout(function(){ window.close(); }, 1000);</script>';
            $html .= (count($lm->material->recordings ?? []) . " recordings found.");

            $response->getBody()->write($html);
            return $response->withHeader('Content-Type', 'text/html');
        }

        // If code or session not present, return 400
        $errorResponse = [
            'error' => 'Missing "code" parameter or "learningModuleId" session value.'
        ];
        $response->getBody()->write(json_encode($errorResponse));

        return $response
            ->withStatus(400)
            ->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	$group->get('', function (Request $request, Response $response) use ($app) {

        $teams = new \APP\Teams(false);

        $params = $request->getQueryParams();


        if (isset($params["code"])){
            if (!isset($_SESSION["dt"])) {
                $_SESSION["teams_installed"] = 1;
                return $response->withStatus(302)->withHeader("Location", $this->get('settings')["LMSUrl"]);
            }

            $teams->get_oauth_token($params["code"]);


            $new_learning_module = new \Models\LearningModule();
            $new_learning_module->save();

            $meeting = $teams->createMeeting(
                "Open eLMS meeting {$_SESSION['dt']} {$_SESSION['tm']} ({$new_learning_module->id})",
                $_SESSION["dt"],
                $_SESSION["tm"],
                $_SESSION["dur"]
            );

            try
            {

                $new_learning_module->type_id = \Models\LearningModuleType
                    ::where("slug", "=", "microsoft_teams")
                    ->withoutGlobalScope('type_filter')
                    ->first()
                    ->id
                ;

                $category = \Models\LearningModuleCategory::firstOrCreate(
                    ['name' => 'Live Lessons'],
                    ['status' => 1]
                );
                $new_learning_module->category_id = $category->id;
                $new_learning_module->name = "Microsoft Teams meeting";
                $new_learning_module->name = ((isset($_SESSION["event_name"]) ?  $_SESSION["event_name"] . ' ' : '') . "Microsoft Teams meeting");
                $new_learning_module->is_course = 0;
                $new_learning_module->material = [
                    "sessions" => [],
                    "link" => $meeting->joinWebUrl,
                    "meeting_id" => $meeting->id
                ];
                $new_learning_module->status = 1;
                $new_learning_module->save();

                $_SESSION["teams_join_url"] = $meeting->joinWebUrl;
                //$_SESSION["teams_lesson_id"] = $new_lesson->id;
                $_SESSION["teams_resource_id"] = $new_learning_module->id;
                $_SESSION["teams_meeting_id"] = $meeting->id;
            }catch(\Exception $e){

                $new_learning_module->delete();
                $response->getBody()->write(json_encode(["error" => $e->getMessage()]));
                return $response;
            }

            $response->getBody()->write('<script>window.name = "close"; setTimeout(function(){ window.close(); }, 1000);</script>MS teams meeting created.');
            return $response;

        } else {
            $_SESSION["dt"] = $params["dt"];
            $_SESSION["tm"] = $params["tm"];
            $_SESSION["dur"] = $params["dur"];
            if (isset($params["event_name"])) {
                $_SESSION["event_name"] = $params["event_name"];
            }

            setcookie("LMSSite", $this->get('settings')["LMSUrl"], time()+3600, "/");


            return $response->withStatus(302)->withHeader('Location', $teams->get_install_link());
        }

		return
			$response
        ;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));


	$group->get('/outlook', function (Request $request, Response $response) use ($app) {

        $teams = new \APP\Teams(false);

        $params = $request->getQueryParams();


        if (isset($params["code"]) && isset($_SESSION["event_id"])) {

            $oauth_response = $teams->get_calendar_oauth_token($params["code"]);

            $event = \Models\Schedule::find($_SESSION["event_id"]);

            $event->outlook_integration = 1;
            $event->outlook_refresh_token = $oauth_response->refresh_token;

            $event->save();

            $response->getBody()->write('<script>window.name = "close"; setTimeout(function(){ window.close(); }, 1000);</script>Event added the to your Outlook calendar.');
            return $response;
        } else {
            $_SESSION["event_id"] = $params["event_id"];

            $event = \Models\Schedule::find($_SESSION["event_id"]);

            if (\APP\Tools::getConfig("enableGlobalOutlookIntegration")) {
                $event->outlook_integration = 1;
                $event->outlook_refresh_token = "use_global";
                $event->save();
            } elseif ($event->outlook_refresh_token){
                $event->outlook_integration = 1;
                $event->save();

                $response->getBody()->write('<script>window.name = "close"; setTimeout(function(){ window.close(); }, 1000);</script>Event added the to your Outlook calendar.');
                return $response;

            } else {
                setcookie("LMSSite",$this->get('settings')["LMSUrl"], time()+3600, "/");
                return $response->withStatus(302)->withHeader('Location', $teams->get_calendar_authorize_link());
            }
        }

		return
			$response
        ;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->delete('/outlook/{event_id:[0-9]+}', function (Request $request, Response $response, $args) use ($app) {

        $event = \Models\Schedule::find($args["event_id"]);
        if (isset($event->outlook_integration)){
            $event->outlook_integration = false;
            $event->save();
        }
        $response->getBody()->write(json_encode(['status' => true]));
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->get('/1', function (Request $request, Response $response) {
        $return_vars = [
            "start_url",
            "join_url",
            "meeting_id",
            "resource_id",
        ];
        $data = [];
        foreach ($return_vars as $key => $return_var) {
            if (isset($_SESSION["teams_" . $return_var])) {
                $data[$return_var] = $_SESSION["teams_" . $return_var];
            }
        }

        $response->getBody()->write(json_encode($data));
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	$group->get('/uninstall', function (Request $request, Response $response) {
        //do nothin
        return $response;
    })->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));
    $group->get('/rooms',function(HttpRequest $request,HttpResponse $response){
        $teams = new Teams();
        $params = $request->getQueryParams();
        if (isset($params["code"])) {
            $teams->redirect_url = $this->get('settings')['LMSUrl']."teams/rooms";
            $oauth_response = $teams->get_room_oauth_token($params['code']);
                Configuration::where('key','OutlookRoomAccessToken')->update(['value'=>$oauth_response->access_token]);
                Configuration::where('key','OutlookRoomRefreshToken')->update(['value'=>$oauth_response->refresh_token]);

            $response->getBody()->write('<script>window.name = "close"; setTimeout(function(){ window.close(); }, 1000);</script>Token Added successfully');
            return $response;
        }else
        {
            return $response->withStatus(401);
        }
    });
});
