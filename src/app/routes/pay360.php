<?php

use APP\Form;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

use APP\Controllers\Payment360Controller;

$app->group("/pay360",  function ($group) {
	$group->get('/initiate', Payment360Controller::class . ':initiatePayment')->add(\APP\Auth::getSessionCheck());
	$group->get('/callback', Payment360Controller::class . ':paymentCallback')->add(\APP\Auth::getSessionCheck());
});
