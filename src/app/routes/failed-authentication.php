<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/failed-authentication",  function ($group) {

	$group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$query = \Models\LogFailedAuthentication
			::select(
				'log_failed_authentications.*',
				DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "  %H:%i') AS created_at_uk")
			)
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		// Created at filter for learning results
		if (
			isset($params["search"]["created_at"]) &&
			$params["search"]["created_at"]
		) {
			$params["search"]["created_at"] = json_decode($params["search"]["created_at"], true);
			if (
				isset($params["search"]["created_at"]['period_from']) &&
				$params["search"]["created_at"]['period_from']
			) {
				$created_at_from = \Carbon\Carbon::parse($params["search"]["created_at"]['period_from'])->startOfDay();
				$query
					->where("log_failed_authentications.created_at", ">=", $created_at_from)
				;
			}
			if (
				isset($params["search"]["created_at"]['period_to']) &&
				$params["search"]["created_at"]['period_to']
			) {
				$created_at_to = \Carbon\Carbon::parse($params["search"]["created_at"]['period_to'])->endOfDay();
				$query
					->where("log_failed_authentications.created_at", "<=", $created_at_to)
				;
			}
			unset($params["search"]["created_at"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-failed-login-attempts', 'select'));
});