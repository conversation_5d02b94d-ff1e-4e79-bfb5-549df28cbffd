<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/country",  function ($group) {

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$country = \Models\Country::find($args["id"]);
		$country->status = 0;
		$country->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$country = \Models\Country::find($args["id"]);
		$country->status = 1;
		$country->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$country = \Models\Country::find($args["id"]);

		$response->getBody()->write(json_encode($country));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$country = new \Models\Country;

		if (isset($data["name"])) {
			$country->name = $data["name"];
			$country->country_group_id = $data["country_group_id"];
			$country->display_order = isset($data["display_order"]) ? $data["display_order"] : 10;
            if(isset($data['code'])){
                $country->code = $data['code'];
            }
		}
		$country->status = 1;
		$country->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'insert'));

	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$country = \Models\Country::find($args["id"]);
		$country->name = $data["name"];
		$country->country_group_id = $data["country_group_id"];
		$country->display_order = isset($data["display_order"]) ? $data["display_order"] : $country->country_group_id;
        if(isset($data['code'])){
            $country->code = $data['code'];
        }
		$country->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'update'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$data = \Models\Country::leftJoin('country_groups','countries.country_group_id','=','country_groups.id')->where("countries.status",">",0)
//            ->select('countries.*', 'country_groups.name as group_name','country_groups.position as group_order')->get();
			->selectRaw("countries.*, COALESCE(country_groups.name,'Other') as group_name, COALESCE(country_groups.position) as group_order")->get();

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionRegisterCheck());

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\Country::where("id", ">", "0");

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}


		if (isset($args["download"]) && $args["download"] == "/download")
		{
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
					"ID" => "id",
					"Country Name" => "name",
			];


			$download_file_name = uniqid("countries.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
					);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		}
		else
		{
			$p = \APP\SmartTable::searchPaginate($params, $query);
			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


		}

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-countries', 'select'));
});
