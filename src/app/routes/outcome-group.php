<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/outcome-group",  function ($group) {

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$entry = new \Models\ApprenticeshipOutcomeGroup;
		$entry->name = $data['name'];
		$entry->standard_id = $data['standard_id'];
		$entry->save();
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'insert'));

	$group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$entry = \Models\ApprenticeshipOutcomeGroup::find($args['id']);
		if ($entry) {
			$entry->name = $data['name'];
			$entry->save();
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	$group->get('/link/{group_id:[0-9]+}/{outcome_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$entry = \Models\ApprenticeshipOptionalOutcome
			::where('group_id', $args['group_id'])
			->where('outcome_id', $args['outcome_id'])
			->first()
		;
		if ($entry) {
			$entry->deleted_by = \APP\Auth::getUserId();
			$entry->save();
			$entry->delete();
		} else {
			$entry = new \Models\ApprenticeshipOptionalOutcome;
			$entry->group_id = $args['group_id'];
			$entry->outcome_id = $args['outcome_id'];
			$entry->save();
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	$group->delete('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\ApprenticeshipOutcomeGroup::find($args['id']);
		if ($entry) {
			$entry->deleted_by = \APP\Auth::getUserId();
			$entry->save();

			// Remove all ApprenticeshipOptionalOutcome as well
			$links = \Models\ApprenticeshipOptionalOutcome
				::where('group_id', $entry->id)
				->get()
			;
			foreach ($links as $key => $link) {
				$link->deleted_by = \APP\Auth::getUserId();
				$link->save();
				$link->delete();
			}

			$entry->delete();
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// Apprenticeship Standard Outcome groups
   $group->post('/list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\ApprenticeshipOutcomeGroup
			::withCount(['OptionalOutcomes' => function ($query) use ($params) {
				$query
					->where('outcome_id', $params["search"]["outcome_id"])
				;
			}])
			->where('standard_id', $params["search"]["standard_id"])
		;

		if (isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query = $query
					->whereHas(("OptionalOutcomes"), function ($query) use ($params) {
						$query
							->where('outcome_id', $params["search"]["outcome_id"])
						;
					})
				;
			}

			if ($params["search"]["assigned"] == "0") {
				$query = $query
					->whereDoesntHave("OptionalOutcomes", function ($query) use ($params) {
						$query
							->where('outcome_id', $params["search"]["outcome_id"])
						;
					})
				;
			}

			unset($params["search"]["assigned"]);
		}

		unset($params["search"]["outcome_id"]);
		unset($params["search"]["refresh"]);


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	$group->get('/link-user/{group_id:[0-9]+}/{standard_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$group = \Models\ApprenticeshipOutcomeGroup::find($args['group_id']);
		if (!$group) {
			return
				$response
					->withStatus(404)
					->withHeader('Content-Type', 'text/html')
					->write('404 Not Found')
			;
		}

		$entry = \Models\ApprenticeshipOptionalOutcomeUser
			::where('group_id', $args['group_id'])
			->where('user_id', \APP\Auth::getUserId())
			->where('standard_id', $args['standard_id'])
			->first()
		;
		if ($entry) {
			$entry->deleted_by = \APP\Auth::getUserId();
			$entry->save();
			$entry->delete();
		} else {
			$entry = new \Models\ApprenticeshipOptionalOutcomeUser;
			$entry->group_id = $args['group_id'];
			$entry->standard_id = $args['standard_id'];
			$entry->user_id = \APP\Auth::getUserId();
			$entry->save();
		}

		// Enable all outcomes that are linked with groups in apprenticeship_optional_outcome_users and disable the rest!
		$standard = \Models\ApprenticeshipStandard
			::where('id', $args['standard_id'])
			->with(["issuecategories" => function ($query) use ($args) {
				$query
					->where("status", 1)
					->orderBy('sort', 'ASC')
					->with(['OptionalUserOutcomes' => function ($query) use ($args) {
						$query
							->where('user_id', \APP\Auth::getUserId())
							->where('standard_id', $args['standard_id'])
						;
					}])
					->with(['OptionalOutcomes' => function ($query) use ($args) {
					}])
				;
			}])
			->first()
		;

		foreach ($standard->issuecategories as $key => $issuecategory) {
			// Needs optimising
			if (
				count($issuecategory->OptionalUserOutcomes) > 0 ||
				count($issuecategory->OptionalOutcomes) == 0
			) {
				\Models\ApprenticeshipStandardUser::disableOutcome(\APP\Auth::getUserId(), $issuecategory->id, $standard->id, 0);
			} else {
				\Models\ApprenticeshipStandardUser::disableOutcome(\APP\Auth::getUserId(), $issuecategory->id, $standard->id, 1);
			}

		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('trainee-learning-results', 'update'));

});
