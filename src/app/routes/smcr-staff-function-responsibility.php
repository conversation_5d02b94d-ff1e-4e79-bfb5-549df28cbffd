<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/{router:smcrstafffunctionresponsibility|smcr-staff-function-responsibility}",  function ($group) {

	// Get spcific SMCR function responsibility link
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$user = \APP\Auth::getUser();

		$query = \Models\SmcrStaffFunctionResponsibility
			::select(
				'id',
				'user_id',
				'function_responsibility_id',
				'status',
				'completion_status',
				'rejected_by',
				'accepted_by',
				DB::raw("DATE_FORMAT(updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS updated_at_uk"),
				DB::raw("DATE_FORMAT(completion_date,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS completion_date_uk"),
				DB::raw("DATE_FORMAT(learner_sign_off,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS learner_sign_off_uk"),
				DB::raw("DATE_FORMAT(rejected,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS rejected_uk")
			)
			->where('id', $args["id"])
			->with('FunctionResponsibility')
			->with(['User' => function($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'staff_type_id'
					)
					->with('StaffType')
				;
			}])
			->with(['RejectedBy' => function($query) {
				$query
					->select(
						'id',
						'fname',
						'lname'
					)
				;
			}])
			->with(['AcceptedBy' => function($query) {
				$query
					->select(
						'id',
						'fname',
						'lname'
					)
				;
			}])
			->with(['Comments' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->with(['Files' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->with(['Meetings' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
					->with(['ApprovedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
		;

		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isManager()
		) {
			$query = $query->where('user_id', $user->id);
		}

		$query = $query->first();

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// Update existing SMCR link
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		if (
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManager()
			) &&
			isset($params['user_id'])
		) {
			$user = \Models\User::find($params['user_id']);
		} else {
			$user = \APP\Auth::getUser();
		}

		if ($user) {
			$query = \Models\SmcrStaffFunctionResponsibility
				::where('id', $args["id"])
				->where('user_id', $user->id)
			;

			$query = $query->first();

			if ($query) {
				if (
					isset($params['type'])
				) {
					if ($params['type'] == 'reject') {

						if ($query->FunctionResponsibility->type == 'responsibility') {
							$query->completion_status = 'Not Accepted';
						} else {
							$query->completion_status = 'Failed';
						}
						if (\APP\Auth::isAdminInterface()) {
							$query->rejected = \Carbon\Carbon::now();
							$query->rejected_by = \APP\Auth::getUserId();
							$query->accepted_by = null;
							$query->completion_date = null;
							$query->learner_sign_off = null;
						} else {
							$query->rejected = \Carbon\Carbon::now();
							$query->rejected_by = \APP\Auth::getUserId();
							$query->accepted_by = null;
							$query->completion_date = null;
							$query->learner_sign_off = null;
						}
					}

					if ($params['type'] == 'accept') {
						$query->rejected = null;
						$query->rejected_by = null;
						$query->completion_date = \Carbon\Carbon::now();
						$query->accepted_by = \APP\Auth::getUserId();
						$query->learner_sign_off = \Carbon\Carbon::now();
						if ($query->FunctionResponsibility->type == 'responsibility') {
							$query->completion_status = 'Accepted';
							// Generate SOR certificate!
							$certificate = new \Models\SmcrReport;
							$certificate->type_id = 2;
							$certificate->user_id = \APP\Auth::getUserId();
							$certificate->start_at = \Carbon\Carbon::now();
							$certificate->completion_status = 'Completed';
							$certificate->save();

						} else {
							$query->completion_status = 'Certified';
						}
					}
				}

				$query->save();
			}
		}

		return $response;
	})->add(\APP\Auth::getSessionCheck());

   // Link responsibility/function ID to user.
	$group->put('/link/{rf_id:[0-9]+}/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args['user_id']);
		$fr = \Models\SmcrFunctionResponsibility::find($args['rf_id']);

		if (
			$user &&
			$fr
		) {
			$query = new \Models\SmcrStaffFunctionResponsibility;
			$query->user_id = $user->id;
			$query->function_responsibility_id = $fr->id;
			if ($fr->type == 'responsibility') {
				$query->completion_status = 'Accepted';
			} else {
				$query->completion_status = 'Not Completed';
			}
			$query->save();

			// Generate Statement of Responsibility!
			if ($fr->type == 'responsibility') {
				\Models\SmcrReport::updateSOR($user->id, ['id' => $args['rf_id'], 'type' => 'insert']);
			}


			// Send email to user_id
			$template = \Models\EmailTemplate
				::where('name', 'Learner needs to Accept Function/Responsibility')
				->where('status', true)
				->first()
			;
			if ($template) {
				$email_queue = new \Models\EmailQueue;
				$email_queue->email_template_id = $template->id;
				$email_queue->recipients = [$user->id];
				$email_queue->custom_variables = json_encode([
					'FUNCTION_RESPONSIBILITY' => $fr->name,
				]);
				$email_queue->save();
			}

		} else {
			$response = $response->withStatus(500);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'disable'));

	// unLink responsibility/function ID to user.
	$group->delete('/link/{rf_id:[0-9]+}/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\User::find($args['user_id']);
		$fr = \Models\SmcrFunctionResponsibility::find($args['rf_id']);

		$update_sor = false;

		if (
			$fr &&
			$fr->type == 'responsibility'
		) {
			$update_sor = true;
		}

		\Models\SmcrStaffFunctionResponsibility
			::where('user_id', $args['user_id'])
			->where('function_responsibility_id', $args['rf_id'])
			->delete()
		;

		if ($update_sor) {
			\Models\SmcrReport::updateSOR($user->id, ['id' => $args['rf_id'], 'type' => 'delete']);
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'disable'));


	// List all functions/reponsibilities links
   $group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$user = \APP\Auth::getUser();

		// Need to implement some more permission checks, if scmr is enabled, if you are admin or manager or senior staff

		if (
			(
				!$this->get('settings')['licensing']['isSMCR'] &&
				!\APP\Tools::getConfig('showResponsibilitiesAndCommittees')
			)
		) {
			// Else, return 403
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
			;
		}
		// noty smcr or (not admin and nor manager and not senior manager)

		$params = $request->getParsedBody();

			$fr_name = 'Function';
			if (
				isset($params["search"]["staffShortHand"]) &&
				$params["search"]["staffShortHand"] == 'SMR'
			) {
				$fr_name = 'Responsibility';
			}

		$query_id = 'smcrstafffunctionresponsibilityList';
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		$option = isset($args["option"]) ? $args["option"] : "";


		switch($args["option"]) {
			case "/download":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				$export_fields = [
					"ID" => "FunctionResponsibility.id",
					$fr_name => "FunctionResponsibility.name",
					"Job Title" => "user.designation.name",
					"Employee ID" => 'user.id',
					"First Name" => 'user.fname',
					"Last Name" => 'user.lname',
					"Email" => 'user.email',
				];
				if ($fr_name == 'Responsibility') {
					$export_fields["Status"] = 'completion_status';
				}


				$download_file_name = uniqid("Functions and Responsibilities.list.") . ".xlsx";

				\APP\Tools::generateExcelDownload(
						$data,
						$export_fields,
						$this->get('settings')["LMSTempPath"] . $download_file_name
					)
				;

				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');

			break;
		}


		if (isset($params["search"]["pagination"]) && $params["search"]["pagination"] == false) {
			unset($params["search"]["pagination"]);
			$response_data = \APP\SmartTable::searchPaginate($params, $query, false, false);
			$response_data = json_encode($response_data);
		} else {
			unset($params["search"]["pagination"]);
			$paginated_data = \APP\SmartTable::searchPaginate($params, $query, false, true);
			$response_data = $paginated_data->toJson();
		}

		return
			$response
				->withHeader('Content-Type', 'application/json')
				->write($response_data);
	})->add(\APP\Auth::getSessionCheck()); // All is needed to be logged in, specific permision checks will be done inside request

});