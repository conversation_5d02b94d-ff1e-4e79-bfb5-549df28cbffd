<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/zoom",  function ($group) use($app) {

	$group->get('/install', function (Request $request, Response $response) {

        $zoom = new \APP\Zoom(false);
        unset($_SESSION["dt"]);
        unset($_SESSION["tm"]);
        unset($_SESSION["dur"]);

        return $response->withStatus(302)->withHeader('Location', $zoom->get_install_link());

    })->add(\APP\Auth::getLoginCheck($this->get('settings')["LMSUri"] . "login"));

    $group->get('/video/{resource:[0-9]+}', function (Request $request, Response $response, $args) {
        $_SESSION["checkVideo"] = 1;
        $_SESSION["learningModuleId"] = $args["resource"];
        $zoom = new \APP\Zoom(false);

        return $response->withStatus(302)->withHeader('Location', $zoom->get_install_link("/video"));
    });

    $group->get('/video', function (Request $request, Response $response) {
        $zoom = new \APP\Zoom(false);

        $params = $request->getQueryParams();

        if (isset($params["code"])){
            if (isset($_SESSION["checkVideo"]) && isset($_SESSION["learningModuleId"])){

                $lm_id = $_SESSION["learningModuleId"];

                unset($_SESSION["checkVideo"]);
                unset($_SESSION["learningModuleId"]);

                $zoom->get_oauth_token($params["code"], "/video");

                $lm = \Models\LearningModule::find($lm_id);
                if (isset($lm->material->meeting_id)){
                    $meeting_id = $lm->material->meeting_id;
                } else {
                    if (preg_match("/\/([0-9]+)/", $lm->material->link, $matches)){
                        $meeting_id = $matches[1];
                    } else {
                        $meeting_id = false;
                    }
                }

                $recordings = json_decode($zoom->getMeetingRecordings($meeting_id));
                if (isset($recordings->recording_files)){
                    $recordings = $recordings->recording_files;
                } else {
                    $recordings = [];
                }
                $material = new stdClass();
                foreach($lm->material as $key => $value) {
                    $material->$key = $value;
                }
                $material->recordings = $recordings;
                $lm->material = $material;
                $lm->save();

                $response->getBody()->write('<script>window.name = "close"; setTimeout(function(){ window.close(); }, 1000);</script>' . count($recordings)  . " recordings found.");
                return $response;
            }
        }
    });

    $group->get('', function (Request $request, Response $response) {

        $zoom = new \APP\Zoom(false);

        $params = $request->getQueryParams();

        if (isset($params["code"])){
           if (!isset($_SESSION["dt"])) {
                $_SESSION["zoom_installed"] = 1;
                return $response->withStatus(302)->withHeader("Location", $this->get('settings')["LMSUrl"]);
            }
            $zoom->get_oauth_token($params["code"]);

            $utc_dt = \Carbon\Carbon::parse("{$_SESSION['dt']}T{$_SESSION['tm']}")->setTimezone("UTC");

            $meeting = $zoom->createMeeting(
                "me",
                "Open eLMS meeting {$_SESSION['dt']} {$_SESSION['tm']}",
                $utc_dt->toDateString(),
                $utc_dt->toTimeString(),
                $_SESSION["dur"], "A meeting booked through Open eLMS");

            $new_learning_module = new \Models\LearningModule();
            $new_learning_module->type_id = \Models\LearningModuleType
                ::where("slug", "zoom_meeting")
                ->withoutGlobalScope('type_filter')
                ->first()
                ->id
            ;

            $category = \Models\LearningModuleCategory::firstOrCreate(
                ['name' => 'Live Lessons'],
                ['status' => 1]
            );
            $new_learning_module->category_id = $category->id;
            $new_learning_module->name = (isset($_SESSION["event_name"]) ?  $_SESSION["event_name"] . ' ' : '') . "Zoom meeting #{$meeting->id}";
            $new_learning_module->is_course = 0;
            $new_learning_module->material = ["sessions" => [], "link" => $meeting->join_url];
            $new_learning_module->status = 1;
            $new_learning_module->save();


            $_SESSION["zoom_start_url"] = $meeting->start_url;
            $_SESSION["zoom_join_url"] = $meeting->join_url;
            //$_SESSION["zoom_lesson_id"] = $new_lesson->id;
            $_SESSION["zoom_resource_id"] = $new_learning_module->id;
            $_SESSION["zoom_meeting_id"] = $meeting->id;

            $response->getBody()->write('<script>window.name = "close"; setTimeout(function(){ window.close(); }, 1000);</script>Zoom meeting created.');
            return $response;
        } else {
            $_SESSION["dt"] = $params["dt"];
            $_SESSION["tm"] = $params["tm"];
            $_SESSION["dur"] = $params["dur"];
            if (isset($params["event_name"])) {
                $_SESSION["event_name"] = $params["event_name"];
            }
            return $response->withStatus(302)->withHeader('Location', $zoom->get_install_link());
        }

		return
			$response
        ;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->get('/1', function (Request $request, Response $response) {
        $return_vars = [
            "start_url",
            "join_url",
            "meeting_id",
            "resource_id",
        ];
        $data = [];
        foreach ($return_vars as $key => $return_var) {
            if (isset($_SESSION["zoom_" . $return_var])) {
                $data[$return_var] = $_SESSION["zoom_" . $return_var];
            }
        }

        $response->getBody()->write(json_encode($data));
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	$group->get('/uninstall', function (Request $request, Response $response) {
        //do nothin
        return $response;
    })->add(\APP\Auth::getSessionCheck('You do not have permission to access this page, you may have logged out or switched profile. Please log in correctly and try again.'));
});
