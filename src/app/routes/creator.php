<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;

$app->group("/creator",  function ($group) {

	// Check if token is valid, set session to allow loading jackdaw editor and disable token
	// https://openelms/creator/token/2f868fcec75f26fe309778a295b8
	$group->get('/token/{token:.+}', function (Request $request, Response $response, array $args) {
		$resource = \Models\LearningModule
			::where('jackdaw_access_token', $args['token'])
			->where('jackdaw_access_token', '>', '')
			->where('status', true)
			->whereIn( 'type_id',
				\Models\LearningModuleType
					::select('id')
					->where('slug', 'e_learning')
					->get()
			)
			->first()
		;

		if ($resource) {
			// Set session, remove token, redirecto to jackdaw editor
			$_SESSION["creator_api_access"] = true;
			$_SESSION["creator_api_access_resource_id"] = $resource->id;
			$_SESSION["creator_api_access_session_start"] = \Carbon\Carbon::now();
			$redirect_url = $this->get('settings')["LMSUrl"] . 'api/jackdaw_html5/index.html?id=' . $resource->id;

			// remove token from resource so that this link can't be used twice
			$resource->jackdaw_access_token = '';
			$resource->save();

			return $response
				->withHeader('Location', $redirect_url)
				->withStatus(302)
			;


		} else {

			unset($_SESSION["creator_api_access"]);
			unset($_SESSION["creator_api_access_resource_id"]);
			unset($_SESSION["creator_api_access_session_start"]);

			return \APP\Tools::returnCode($request, $response, 404);
		}
	});

});
