<?php

use APP\Controllers\CredlyController;

$app->group("/credly", function ($group) {
    $group->get('/badges', CredlyController::class . ':getBadges')->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'insert'));
    $group->get('/list/{id:[0-9]+}',CredlyController::class.':listBadges')->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));
});
