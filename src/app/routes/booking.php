<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/booking",  function ($group) {

	// Book training
	$group->put('/{module_id:[0-9]+}/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		if ($args["user_id"] == 0) {
			$user_id = \APP\Auth::getUserId();
		} else {
			if (\APP\Auth::isManagerOf($args["user_id"])) {
				$user_id = $args["user_id"];
			} else {
				return $response;
			}
		}

		$params = $request->getParsedBody();
		$session = $params["session"];

		$l_session = new \Models\LearningSession;
		$l_session->learning_module_id = $args["module_id"];
		$l_session->user_id = $user_id;
		$l_session->approved = \Models\User::bookingApproved($user_id, $args["module_id"]);
		$l_session->session_date = \Carbon\Carbon::parse($session["session_date_str"]);
		$l_session->trainer = $session['trainer'];
		$l_session->location = $session["location"];
		$l_session->duration = $session["duration"];
		$l_session->session_uid = $session["session_uid"];
		$l_session->completed = false;
		$l_session->save();

		unset($l_session->user);
		unset($l_session->module);

		$response->getBody()->write(json_encode($l_session));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-book-training'], 'insert'));



	// User/manager deletes training
	$group->put('/cancel/{learning_session_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$l_session = \Models\LearningSession::find($args["learning_session_id"]);

		if (
			\APP\Auth::isAdmin() ||
			$l_session->user_id == \APP\Auth::getUserId() ||
			\APP\Auth::isManagerOf($l_session->user_id) ||
			\APP\Auth::accessAllLearners()
		) {
			$l_session->delete();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-book-training'], 'disable'));

	// Manager gets list of bookings?
	$group->get('/waiting', function (Request $request, Response $response, $args) {

		$l_session_query = \Models\LearningSession
			::with("Module", "User")
		;
		if (!\APP\Auth::isAdmin()) {
			$l_session_query->join("manager_users", function ($join) {
				$join
					->on("manager_users.user_id", "=", "learning_sesssions.user_id")
					->where("manager_users.manager_id", "=", \APP\Auth::getUserId())
					->whereNull("manager_users.deleted_at")
				;
			});
		}
		$l_sessions = $l_session_query->get();

		$response->getBody()->write(json_encode($l_sessions));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	$group->get('/{module_id:[0-9]+}/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		if ($args["user_id"] == 0) {
			$user_id = \APP\Auth::getUserId();
		} else {
			if (\APP\Auth::isManagerOf($args["user_id"]) || \APP\Auth::isAdmin() || \APP\Auth::accessAllLearners()) {
				$user_id = $args["user_id"];
			} else {
				return $response;
			}
		}
		$l_module = \Models\LearningModule::find($args["module_id"]);

		$l_module->prerequisites = \APP\Learning::augmentPrerequisitesLearningResult(
				$user_id,
				$l_module->prerequisites
			);

		$l_session = \Models\LearningSession
			::where("user_id", "=", $user_id)
			->where("learning_module_id", "=", $l_module->id)
//            ->where("completed", "=", 0)
			->first();
		;

		$response
			->getBody()
			->write(json_encode([
					"module" => $l_module,
					"booking" => $l_session,
				]))
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-modules'], 'select'));
});