<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/email-queue",  function ($group) {

   $group->post('/list', function (Request $request, Response $response) {
      try{
		$params = $request->getParsedBody();
		$query = \Models\EmailQueue
			::with(['fromUser' => function ($query) {
				$query
					->select('id', 'fname', 'lname', 'email');
			}])
			->with(['template' => function ($query) {
				$query
					->select('id', 'name', 'subject');
			}])
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}


		if (isset($params["search"]["subject"])) {
			$subject = $params["search"]["subject"];
			$query = $query
				->whereHas('template', function ($query) use ($subject) {
					$query
						->where("email_templates.name", "LIKE", "%{$subject}%")
						->orWhere("email_templates.subject", "LIKE", "%{$subject}%")
					;
				});
			;
			unset($params["search"]["subject"]);
		}

		if (isset($params["search"]["from"])) {
			$from = $params["search"]["from"];
			
			// Check if searching for "system" entries
			if (strtolower(trim($from)) === 'system') {
				$query = $query->whereNull('from');
			} else {
				// Search in user data for non-system entries
				$query = $query
					->whereHas('fromUser', function ($query) use ($from) {
						$query
							->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $from . "%'")
							->orWhere("users.email", "LIKE", "%{$from}%")
						;
					});
			}
			
			unset($params["search"]["from"]);
		}

		if (isset($params["search"]["recipients"])) {
			$recipients_search = trim($params["search"]["recipients"]);
			
			if (!empty($recipients_search)) {
				// Optimized approach: Use a single efficient query with UNION
				$email_queue_table = 'email_queue';
				
				// First, find user IDs that match the search term (with limit for performance)
				$matching_user_ids = \Models\User::where(function ($userQuery) use ($recipients_search) {
					$userQuery->whereRaw("CONCAT(fname, ' ', lname) LIKE ?", ["%{$recipients_search}%"])
						->orWhere('email', 'LIKE', "%{$recipients_search}%");
				})
				->limit(20) // Reduced limit for better performance
				->pluck('id')
				->toArray();
				
				if (!empty($matching_user_ids) || !empty($recipients_search)) {
					$query = $query->where(function ($query) use ($recipients_search, $matching_user_ids, $email_queue_table) {
						// Search in direct email recipients (indexed field)
						if (!empty($recipients_search)) {
							$query->where('recipients_emails', 'LIKE', "%{$recipients_search}%");
						}
						
						// For user recipients, use more efficient approach
						if (!empty($matching_user_ids)) {
							// Use IN clause with subquery for better performance
							$user_ids_string = implode(',', array_slice($matching_user_ids, 0, 10));
							
							// Single optimized query using JSON_OVERLAPS (MySQL 8.0+) or fallback
							$query->orWhereRaw("
								CASE 
									WHEN JSON_VALID(recipients) = 1 THEN
										JSON_OVERLAPS(recipients, JSON_ARRAY($user_ids_string))
									ELSE
										recipients REGEXP CONCAT('\\\\[([0-9,]*)', CONCAT('(', REPLACE('$user_ids_string', ',', '|'), ')'), '([0-9,]*)\\\\]')
								END
							");
						}
					});
				}
			}
			
			unset($params["search"]["recipients"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		} catch(\Exception $e) {
			print_r($e->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'select'));

	//approve queue item for sending out
	$group->post('/approve/{id}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$entry = \Models\EmailQueue
			::find($args['id'])
		;
		if ($entry) {
			$entry->approved = true;
			$entry->save();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

	//disapprove queue item for sending out
	$group->post('/disapprove/{id}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$entry = \Models\EmailQueue
			::find($args['id'])
		;
		if ($entry) {
			$entry->approved = false;
			$entry->save();
		}

		return $response;
  })->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

  $group->put('/approve-all', function (Request $request, Response $response) {
		\Models\EmailQueue::where('approved',false)->update(['approved'=>true]);

		return $response;
  })->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

	$group->delete('/all', function (Request $request, Response $response) {
		\Models\EmailQueue::query()->delete();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'disable'));

	// Get detailed recipients for a specific email queue entry
	$group->get('/recipients/{id}', function (Request $request, Response $response, array $args) {
		$email_queue = \Models\EmailQueue::with(['fromUser' => function ($query) {
			$query->select('id', 'fname', 'lname', 'email');
		}])
		->with(['template' => function ($query) {
			$query->select('id', 'name', 'subject');
		}])
		->find($args['id']);

		if (!$email_queue) {
			return \APP\Tools::returnCode($request, $response, 404, 'Email queue entry not found');
		}

		// Get detailed recipient information
		$recipients_data = [
			'id' => $email_queue->id,
			'template' => $email_queue->template,
			'from_user' => $email_queue->fromUser,
			'created_at' => $email_queue->created_at,
			'processed' => $email_queue->processed,
			'approved' => $email_queue->approved,
			'user_recipients' => [],
			'email_recipients' => []
		];

		// Get user recipients with full details
		if (!empty($email_queue->recipients)) {
			$user_ids = is_array($email_queue->recipients) ? $email_queue->recipients : json_decode($email_queue->recipients, true);
			if (!empty($user_ids)) {
				$recipients_data['user_recipients'] = \Models\User::whereIn('id', $user_ids)
					->select('id', 'fname', 'lname', 'email', 'username')
					->orderBy('fname')
					->orderBy('lname')
					->get()
					->toArray();
			}
		}

		// Get direct email recipients
		if (!empty($email_queue->recipients_emails)) {
			$email_list = explode(',', $email_queue->recipients_emails);
			$recipients_data['email_recipients'] = array_map('trim', $email_list);
		}

		// Add summary counts
		$recipients_data['summary'] = [
			'total_recipients' => count($recipients_data['user_recipients']) + count($recipients_data['email_recipients']),
			'user_recipients_count' => count($recipients_data['user_recipients']),
			'email_recipients_count' => count($recipients_data['email_recipients'])
		];

		$response->getBody()->write(json_encode($recipients_data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-audit-email-queue', 'select'));

});
