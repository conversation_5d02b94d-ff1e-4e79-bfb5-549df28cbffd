<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/timings",  function ($group) {

	// Get list os all timings.
	$group->get('/', function (Request $request, Response $response, $args) {
		$timings = [];
		if (\APP\Auth::checkStructureAccess('system-setup-defaults-timings', 'select')) {
			$timings = \Models\Timing::all();
		}

		$response->getBody()->write(json_encode($timings));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	// Update passed timings
	$group->put('/', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		// loop all passed objects, find entry where key field equals passed objects key, update timings.
		foreach($data as $key => $value) {
			$timing = \Models\Timing::where("key", $key)->first();
			if ($timing) {
				$timing->timing = $value['timing'];
				$timing->start_date = NULL;
				if (isset($value['start_date'])) {
					$timing->start_date = $value['start_date'];
				}
				$timing->save();
			}
		}
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-timings', 'update'));
});