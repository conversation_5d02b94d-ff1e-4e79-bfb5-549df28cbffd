<?php

use APP\Auth;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Models\AvailableModule;
use Models\User;
use GuzzleHttp\Exception\ClientException;

$GLOBALS['openAIauth'] = "Bearer ***************************************************";

$app->group("/jackdaw",  function ($group) {

	// Run jackdaw in stand-alone page, module identificator can be provided as number or text, if number and exists in database, edit it.
	// Else If text, look for name and if exists edit it.
	$group->get('/editor/{module_id:.+}', function (Request $request, Response $response, array $args) {
		$e_learning_type_id = \Models\LearningModuleType::getId('e_learning');
		if (is_numeric($args["module_id"])) {
			$resource = \Models\LearningModule
				::where('id', $args["module_id"])
				->where('status', true)
				->where('type_id', $e_learning_type_id)
				->first()
			;
		} else {
			$resource = \Models\LearningModule
				::where('name', 'like', '%' . $args["module_id"] . '%')
				->where('status', true)
				->where('type_id', $e_learning_type_id)
				->first()
			;
		}
		$jackdaw = \APP\Jackdaw::getCode($resource->id, $this->get('settings'));


		$response->getBody()->write('<!DOCTYPE html><html style="height:100%;"><body style="height:100%; margin:0;">' . $jackdaw->player_code . '</body></html>');
		return $response->withHeader('Content-Type', 'text/html');

	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'update'));


	//////////////////////////////
	// Creator AI related API
	//////////////////////////////

	$group->post('/ai_course/{version}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		if (!isset($data['hasQuiz'])) $data['hasQuiz'] = false;

		$prompt = "Generate a learning material on the topic \"".$data['title']."\", addressing educated audience but the language should be easy to read in a style that is first person and relaxed, using short sentences where possible, that would be ".$data['llength']." paragraphs long. Provide it in json format - title of the learning  as \"title\", array of the paragraphs named \"paragraphs\" as an object, that contain paragraph title as \"title\", paragraph text as \"text\" and 8 words for animation to go along with this paragraph (describing: object, motion, direction) as an array named \"keywords\", 5 keywords from paragraph as an array named \"bgKeywords\" and one sentence describing a picture that would go well with this paragraph as \"bgDescr\"".( $data['hasQuiz'] ? ". then add an array named \"examQuestions\" of ".$data['qlength']." exam questions that would test learners knowledge of the subject as an object, that contains question text as \"text\" and an array named \"answers\" of 4 answer options, right one starts with '+' symbol." : "");

		$ch = curl_init();

		if ($args['version'] == '3')
		{
			curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
				"Authorization: ".$GLOBALS['openAIauth'],
				"Accept: application/json",
				"Content-Type: application/json"
			]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array(
				"prompt" => $prompt,
				"max_tokens" => 2000,
				"temperature" => 0.7,
				"model" => 'text-davinci-003'
			)));
		}
		else // default to gpt-4
		{
			curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
				"Authorization: ".$GLOBALS['openAIauth'],
				"Accept: application/json",
				"Content-Type: application/json"
			]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array(
				"model" => 'gpt-4',
				"messages" => array(array(
					"role" => "user",
					"content" => $prompt
				))
			)));
		}

		$res = curl_exec($ch);
		curl_close($ch);

		$res = json_decode($res);

		$res->water_animations = \Models\CreatorTags::
			select('id', 'name')
			->where('name', 'water')
			->where('type', 'animations')
			->with(['media' => function ($q) use ($args) { $q = $q->select('creator_media.id', 'creator_media.name as file_name', 'creator_media.keywords'); }])
			->get()
			[0]['media']
			;

		$res->sky_animations = \Models\CreatorTags::
			select('id', 'name')
			->where('name', 'sky')
			->where('type', 'animations')
			->with(['media' => function ($q) use ($args) { $q = $q->select('creator_media.id', 'creator_media.name as file_name', 'creator_media.keywords'); }])
			->get()
			[0]['media']
			;

		$response->getBody()->write($res);
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->post('/ai_expand/{version}', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		// $prompt = "There is a learning material named \"".$data['courseTitle']."\" that contains chapters titled: \"".implode('", "',$data['chapters'])."\".\nLets rewrite the chapters \"".$data['title']."\" text \"".$data['text']."\" by expanding it to ".$data['count']." paragraphs, each paragraph no more than 80 words in length, do not stray into the topics of other chapters. Result should be in a pure JSON format as an array of generated paragraph text.";

		$prompt = "We have a e-learning course named \"".$data['courseTitle']."\", text content in a JSON form as follows:\n".$data['chapters']."\nPlease expand the chapter \"".$data['title']."\" to additional ".$data['count']." subsections, no titles, no more than 80 words in length, addressing educated audience but the language should be easy to read in a style that is first person and relaxed, using short sentences where possible. Make sure the new content does not overlap with a content from other chapters of the course.\nReturn only expanded chapter in a JSON format as an array of generated subsection text, no objects, just pure text string.";

		$ch = curl_init();

		if ($args['version'] == '3')
		{
			curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
				"Authorization: ".$GLOBALS['openAIauth'],
				"Accept: application/json",
				"Content-Type: application/json"
			]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array(
				"prompt" => $prompt,
				"max_tokens" => 2000,
				"temperature" => 0.7,
				"model" => 'text-davinci-003'
			)));
		}
		else // default to gpt-4
		{
			curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
				"Authorization: ".$GLOBALS['openAIauth'],
				"Accept: application/json",
				"Content-Type: application/json"
			]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array(
				"model" => 'gpt-4',
				"messages" => array(array(
					"role" => "user",
					"content" => $prompt
				))
			)));
		}

		$res = curl_exec($ch);
		curl_close($ch);

		$response->getBody()->write($res);
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));

	$group->post('/ai_match_images', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		$ch = curl_init();

		curl_setopt($ch, CURLOPT_URL, 'http://openelms.com:25317/search/'.$data['type']);
		// curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:5000/search/'.$data['type']);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			"Accept: application/json",
			"Content-Type: application/json"
		]);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array(
			"terms" => $data['terms']
		)));

		$res = curl_exec($ch);
		curl_close($ch);

		$introSummarySet = array(
			"corporate_16.jpg",
			"corporate_room_empty1_left.jpg",
			"corporate_room_empty1_right.jpg",
			"corporate_room_empty4_right.jpg",
			"corporate_white_wall3.jpg",
			"corporate_white_wall4.jpg",
			"room_1.jpg",
			"room_4.jpg",
			"room_5.jpg",
			"room_10.jpg",
			"room_16.jpg",
			"studio23.jpg",
			"office_bright_modern_40.jpg"
		);

		$comp = [];
		$bgList = json_decode($res);

		array_unshift($bgList, $introSummarySet);
		array_push($bgList, $introSummarySet);

		foreach($bgList as $kset)
		{
			$media = \Models\CreatorMedia::
				select('id', 'name as file_name', 'areas')
				->whereIn('name', $kset)
				->with('tags')
				->get()
				;

			$haveData = [];
			$media_out = [];

			foreach($media as $file)
			{
				array_push($haveData, $file->file_name);
				array_push($media_out, [
					'id' => $file->id,
					'file_name' => $file->file_name,
					'tags' => $file->tags,
					'areas' => $file->areas,
					// 'keywords' => $file->keywords
				]);
			}

			foreach($kset as $file)
			{
				if (!in_array($file, $haveData))
				{
					array_push($media_out, [ 'file_name' => $file, 'tags' => [] ]);
				}
			}
			array_push($comp, $media_out);
		}

		$response->getBody()->write(json_encode($comp));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->post('/ai_generate_image', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		// return $response->write("OK");

		// get prompt text for image generation
		$prompt = "Describe, in two sentences, the picture, that would go with this text: \"".$data['text']."\" Include framing, film type, shoot context, lighting prompt. No person's face should be visible in the picture.";

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/completions');
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			"Authorization: ".$GLOBALS['openAIauth'],
			"Accept: application/json",
			"Content-Type: application/json"
		]);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array(
			"prompt" => $prompt,
			"max_tokens" => 2000,
			"temperature" => 0.7,
			"model" => 'text-davinci-003'
		)));

		$res = curl_exec($ch);
		curl_close($ch);

		// get image
		$prompt = $data['prePrompt'] . ' ' . trim(json_decode($res)->choices[0]->text);

	// $prompt = $data['prePrompt'] . ' ' . $data['prompt'];	////////////////////////

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/images/generations');
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			"Authorization: ".$GLOBALS['openAIauth'],
			"Accept: application/json",
			"Content-Type: application/json"
		]);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array(
			"prompt" => $prompt,
			"n" => 1,
			"size" => '1024x1024'
		)));

		$res = curl_exec($ch);
		curl_close($ch);

		$img = file_get_contents(json_decode($res)->data[0]->url);
		$im = imagecreatefromstring($img);
		$bgimg = imagecreatetruecolor(1070, 650);
		$bgthumb = imagecreatetruecolor(200, 121);
		imagecopyresized($bgimg, $im, 0, 0, 0, 201, 1070, 650, 1024, 622);
		imagecopyresized($bgthumb, $bgimg, 0, 0, 0, 0, 200, 121, 1070, 650);
		$imageName = 'ai-'.json_decode($res)->created.'.jpg';
		imagejpeg($bgimg, $this->get('settings')["LMSPublicPath"].'api/temp/'.$imageName);
		imagejpeg($bgthumb, $this->get('settings')["LMSPublicPath"].'api/temp/thumbnails/'.$imageName);
		imagedestroy($bgthumb);
		imagedestroy($bgimg);
		imagedestroy($im);

		$response->getBody()->write(json_encode($imageName));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));



	//////////////////////////////
	// Creator Media Library API
	//////////////////////////////

	$group->put('/library/add_tag', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		$media = \Models\CreatorMedia::
			firstOrNew([
					'name' => $data['file_name'],
					'type' => $data['type']
				])
			;
		$media->save();

		$link = new \Models\CreatorMediaTags;
		$link->creator_tag_id = $data['tag_id'];
		$link->creator_media_id = $media->id;
		$link->save();

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->put('/library/remove_tag', function (Request $request, Response $response, $args) {

        $data = $request->getParsedBody();
        AvailableModule::deleteItem($data['id'], $data['tag_id']);
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->put('/library/rename_tag', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		$tag = \Models\CreatorTags::
			where('id', $data['tag_id'])
			->first()
			;
		$tag->name = $data['new_name'];
		$tag->save();

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->put('/library/delete_tag', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		\Models\CreatorMediaTags::
			where('creator_tag_id', $data['tag_id'])
			->delete()
			;

		\Models\CreatorTags::
			where('id', $data['tag_id'])
			->delete()
			;

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->put('/library/new_tag', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
        $data['type']= $data['type']!='animations'?'background-image':'flash'; // change 'flash' to 'background-image' for consistency
        $new_tag = AvailableModule::createTag($data);

		$response->getBody()->write(json_encode($new_tag));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->put('/library/mark_area', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		$media = \Models\CreatorMedia::
			firstOrNew([
					'name' => $data['name'],
					'type' => $data['type']
				])
			;
		$media->areas = $data['areas'];
		$media->save();

		$response->getBody()->write(json_encode($media));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->put('/library/set_keywords', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		$media = \Models\CreatorMedia::
			firstOrNew([
					'name' => $data['name'],
					'type' => $data['type']
				])
			;
		$media->keywords = $data['keywords'];
		$media->save();

		$response->getBody()->write(json_encode($media));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->put('/library/get_keywords', function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		// $image_url = 'https://lms.e-learningwmb.co.uk/test/api/data/'.$data['type'].'/'.$data['name'];

		$image_url = $this->get('settings')["LMSUrl"].'api/data/'.$data['type'].'/'.($data['type'] == 'animations' ? str_replace('.swf', '.jpg', $data['name']) : $data['name']);
		$api_credentials = array(
			'username' => '<EMAIL>',
			'key' => 'W10abzUI512cIxfLi4motcL8XUJcG3'
		);

		$ch = curl_init();

		curl_setopt($ch, CURLOPT_URL, 'https://mykeyworder.com/api/v1/analyze?url='.urlencode($image_url));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($ch, CURLOPT_HEADER, FALSE);
		curl_setopt($ch, CURLOPT_USERPWD, $api_credentials['username'].':'.$api_credentials['key']);

		$res = curl_exec($ch);
		curl_close($ch);

		$json_response = json_decode($res);

		$generated_keywords = isset($json_response->keywords) ? implode(', ', $json_response->keywords) : '';

		$media = \Models\CreatorMedia::
			firstOrNew([
					'name' => $data['name'],
					'type' => $data['type']
				])
			;
		if ($generated_keywords != '') $media->keywords = ($media->keywords != "") ? $media->keywords.', '.$generated_keywords : $generated_keywords;
		$media->save();

		$response->getBody()->write(json_encode($media));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->get('/library/list_tags', function (Request $request, Response $response, $args) {

		$tags = AvailableModule::getTag('all');
		$response->getBody()->write(json_encode($tags));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->get('/library/image_keywords', function (Request $request, Response $response, $args) {

		$media = \Models\CreatorMedia::
			select('id', 'keywords', 'type')
			->where('type', 'backgrounds')
			->get()
			;

		$result = [];

		foreach($media as $image)
		{
			if (strlen($image->keywords) > 0)
			{
				array_push($result, [
					'prompt' => $image->keywords.' ->',
					'completion' => $image->id.'\n'
				]);
			}
		}

		$response->getBody()->write(json_encode($result));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->get('/library/keywords/{type}', function (Request $request, Response $response, $args) {

		if ($args['type'] == 'animations')
			$files = \APP\Jackdaw::getMediaFiles($this->get('settings')["LMSPublicPath"].'api/data/animations', array('swf'));
		else
			$files = \APP\Jackdaw::getMediaFiles($this->get('settings')["LMSPublicPath"].'api/data/backgrounds', array('png', 'jpg', 'jpeg', 'gif', 'webp', 'avif', 'apng'));

		$media = \Models\CreatorMedia::
			select('id', 'name', 'keywords', 'type')
			->where('type', $args['type'])
			->whereDoesntHave('tags', function($q) {
				$q
					->where('type', 'animations')
					->whereIn('name', ['silhouette', 'scene'])
				;
			})
			->get()
		;
		$result = [];

		foreach($media as $anim)
		{
			if (strlen($anim->keywords) > 0 && in_array($anim->name, $files))
			{
				array_push($result, [
					'name' => $anim->name,
					'keywords' => $anim->keywords
				]);
			}
		}

		$response->getBody()->write(json_encode($result));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->get('/library/animations_of/{type}', function (Request $request, Response $response, $args) {

		$media = \Models\CreatorTags::
			select('id', 'name')
			->where('name', $args['type'])
			->where('type', 'animations')
			->with(['media' => function ($q) use ($args) { $q = $q->select('creator_media.id', 'creator_media.name as file_name', 'creator_media.keywords'); }])
			->get()
			;

		$response->getBody()->write(json_encode($media));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));


	$group->get('/library/{media_type}', function (Request $request, Response $response, array $args) {
		if (
			isset($args['media_type'])
			&& (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManager() ||
				\APP\Auth::isJackdawOf($args['module_id']) ||
				\APP\Auth::isDistributor() ||
				\APP\Auth::isCd()
			)
		) {
			$data = new stdClass();
			$folder = $this->get('settings')["LMSPublicPath"].'api/data/'.$args['media_type'];

			if ($args['media_type'] == 'backgrounds')
            {
                $res = AvailableModule::list(['type' => 'background-image']);
                $url = AvailableModule::getUrl();
				$haveTags = [];
				$data->media = [];

				foreach($res['data'] as $file)
				{
					array_push($haveTags, $url.'thumbnails/'.$file['id']);
					array_push($data->media, [
						'id' => $file['id'],
						'file_name' => $url.'thumbnails/'.$file['id'],
						'tags' => $file['tags'],
						'areas' => $file['areas'],
                        'keywords' => $file['keywords'],
                        'original'=>$url.'images/'.basename($file['file_path'])
					]);
                }

				}
			else if ($args['media_type'] == 'animations')
			{
				$files = \APP\Jackdaw::getMediaFiles($folder, array('swf'));
				$media = \Models\CreatorMedia::
					select('id', 'name as file_name', 'keywords')
					->whereIn('name', $files)
					->with('tags')
					->get()
					;

				$haveTags = [];
				$data->media = [];

				foreach($media as $file)
				{
					array_push($haveTags, $file->file_name);
					array_push($data->media, [
						'id' => $file->id,
						'file_name' => $file->file_name,
						'tags' => $file->tags,
						'keywords' => $file->keywords
					]);
				}

				foreach($files as $file)
				{
					if (!in_array($file, $haveTags))
					{
						array_push($data->media, [ 'file_name' => $file, 'tags' => [] ]);
					}
				}
			}
            $type = $args['media_type']=='backgrounds'?'background-image':'flash';
            $data->tags = AvailableModule::getTag($type);
			$response->getBody()->write(json_encode($data));
			return $response->withHeader('Content-Type', 'application/json');

		}
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));



	// return jackdown cloud role types, currently hard-coded, will see how they will evolve and then later might put in database.
	$group->get('/type/list', function (Request $request, Response $response, array $args) {

		if ($this->get('settings')['licensing']['isJackdawCloud']) {
			$response->getBody()->write(json_encode($this->get('settings')['Jackdaw']['types']));

		} else {
			$response->getBody()->write('[]');
		}
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons', 'select'));

	$group->get('/update-room-files/{module_id:[0-9]*}', function (Request $request, Response $response, array $args) {
		if (
			isset($args['module_id'])
			&& (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManager() ||
				\APP\Auth::isJackdawOf($args['module_id']) ||
				\APP\Auth::isDistributor() ||
				\APP\Auth::isCd()
			)
		) {
			\APP\Jackdaw::updateRoomFiles($this->get('settings')["LMSPublicPath"], $args["module_id"]);
		}
		return $response;
	})->add(\APP\Auth::getSessionCheck());


	// gets contents of both xml files (information and quiz) and overwrites ones of the module whose id is given
	$group->post('/update-xml-files/{module_id:[0-9]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$resp = (object)'hello';
		$resp->status = "error";
		if (isset($args['module_id'])
			&& (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManager() ||
				\APP\Auth::isJackdawOf($args['module_id']) ||
				\APP\Auth::isDistributor() ||
				\APP\Auth::isCd()
			)
		) {
			if (\APP\Jackdaw::replaceXmlFiles(
					$this->get('settings')["LMSPublicPath"],
					$args["module_id"],
					$params['informationXml'],
					$params['quizXml'],
					$params['imagesToCopy'])
				)
				 	$resp->status = "success";
		}

		$response->getBody()->write(json_encode($resp));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// return jackdaw code back
	$group->get('/init/{module_id:[0-9]*}', function (Request $request, Response $response, array $args) {
		$user = \APP\Auth::getUser();

		if ($user->shadowrole) {
			$jackdaw_type = $user->shadowrole->jackdaw_type;
		} else {
			$jackdaw_type = $user->role->jackdaw_type;
		}

		if (
			$this->get('settings')['licensing']['isJackdawCloud'] &&
			!$jackdaw_type
		) {
			return \APP\Tools::returnCode($request, $response, 403, 'Jackdaw type is not specified');
		}

		// Each time Jackdaw is opened, all application/necesary files are coppied over to resources.
		\APP\Jackdaw::updateRoomFiles($this->get('settings')["LMSPublicPath"], $args["module_id"]);




		$jackdaw = \APP\Jackdaw::getCode($args["module_id"], $this->get('settings'));

		// Check if system is configured as jackdaw and user has permissions to add Jackdaw modules.
		if ($this->get('settings')['licensing']['isJackdawCloud'] && $jackdaw_type) {

			$type_config = $this->get('settings')['Jackdaw']['types'][$jackdaw_type];

			if ($type_config) {
				$learning_count = \Models\LearningModule::getCreatedCount($user->id, 1); // Get count of active learning modules, added by user and type 1(e-learning)
				if (
					($type_config['add'] - $learning_count) <= 0 &&
					$jackdaw_type != 'CMS' &&
					$jackdaw_type != 'Unlimited'
				) {
					//check if $args["module_id"] is prsent and that module is user created
					if (isset($args["module_id"])) {
						$learning = \Models\LearningModule
							::where('id', $args["module_id"])
							->where('status', true)
							->where('created_by', $user->id)
							->first()
						;
						if (!$learning) {
							return \APP\Tools::returnCode($request, $response, 403, 'Can\'t add more resources, limit reached!');
						}
					} else {
						return \APP\Tools::returnCode($request, $response, 403, 'Can\'t add more resources, limit reached!');
					}
				}
			} else {
				return \APP\Tools::returnCode($request, $response, 403, 'Jackdaw type is not specified');
			}
		}

		$response->getBody()->write(json_encode($jackdaw));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-jackdaw-instance', 'update'));

	// NEW: Download as container, if free user and more than 15 screens: return error.
	// OLD: Downloads jackdaw course as a zip file
	$group->get('/download/{module_id:[0-9]*}', function (Request $request, Response $response, array $args) {

		// if is jackdaw user and module is made by him, download it!
		// This request will create container if all is legit and respond with download container URL that will be served by JS, else respond with error message.

		if (
			isset($args['module_id'])
			&& (
				\APP\Auth::isAdmin() ||
				\APP\Auth::isManager() ||
				\APP\Auth::isJackdawOf($args['module_id']) ||
				\APP\Auth::isDistributor()
			)
		) {
			$resource = \Models\LearningModule
				::find($args['module_id'])
			;

			$token = bin2hex(random_bytes(14));
			$name = $resource->name;
			// check if token already exists

			$container = \Models\LearningModuleContainer
				::where('learning_module_id', $resource->id)
				->where('created_by', \APP\Auth::getUserId())
				->where('status', true)
				->first()
			;

			// Create new container if existing does not exists.
			if (empty($container)) {
				$container = \Models\LearningModuleContainer
					::firstOrNew(
						['token' => $token]
					)
				;
				$container->name = $name;
				$container->learning_module_id = $resource->id;
				$container->created_by = \APP\Auth::getUserId();
				$container->save();
			}


			if ($container) {
				$response->getBody()->write($this->get('settings')['LMSUri'] . 'learningdistribution/download/' . $container->id);
				return $response->withHeader('Content-Type', 'text/html');
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}


	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));

	// Run e-learning course for learners


	// /play[/{module_id}[/match/{match_id}]]
	$group->get('/play[/{module_id}[/match/{match_id}]]', function (Request $request, Response $response, array $args) {
		$e_learning_type_id = \Models\LearningModuleType::getId('e_learning');

		if (\APP\Auth::isAdmin() && isset($args['match_id'])) {
			$match = \Models\LearningModule
				::where('id', $args['match_id'])
				->where('status', true)
				->where('type_id', $e_learning_type_id)
				->first()
			;
			$match->api_match = $args["module_id"];
			$match->save();
		}


		if (is_numeric($args["module_id"])) {
			$resource = \Models\LearningModule
				::where('id', $args["module_id"])
				->where('status', true)
				->where('type_id', $e_learning_type_id)
				->first()
			;
		} else {
			$resource = \Models\LearningModule::where(function ($query) use ($args) {
					$query
						->where('name', 'like', '%' . $args["module_id"] . '%')
						->orWhere('api_match', 'like', '%' . $args["module_id"] . '%')
					;
				})
				->where('status', true)
				->where('type_id', $e_learning_type_id)
				->first()
			;
		}

		if (
			$resource &&
			isset($resource->id)
		) {
			$learning = \Models\LearningResult::where('learning_module_id', $resource->id)
				->where('user_id', \APP\Auth::getUserId())
				->where('refreshed', 0)
				->first()
			;
			if ($learning) {

				$data = \APP\Learning::launchScorm($resource->id);

				$response->getBody()->write('
				<!DOCTYPE html>
				<html style="height:100%;">
					<body style="height:100%; margin:0; font-size: 0;">
						<iframe src="' . $this->get('settings')["LMSUri"] . 'scorm/play_scorm.php?a=' . $data['scorm']['id'] . '&scoid=' . $data['scorm']['start_module_id'] . '&fname=' . $data['user']['fname'] . '&lname=' . $data['user']['lname'] . '&email=' . $data['user']['email'] . '&username=' . $data['user']['username'] . '&user_id=' . $data['user']['id'] . '" frameborder="0" style="height: 600px; width: 100%;"></iframe>
					</body>
				</html>
				');
				return $response->withHeader('Content-Type', 'text/html');
			} else {
				return \APP\Tools::returnCode($request, $response, 400, 'Unable to acess resource.');
			}
		} else {
			// No match is made
			if (\APP\Auth::isAdmin()) {
			// if admin, list all enabled resources that he can match/play
				$learning_modules = \Models\LearningModule::where('status', true)
					->where('type_id', $e_learning_type_id)
				;
				$learning_modules = $learning_modules
					->orderBy('name')
					->get()
				;

				$modules = '';
				foreach ($learning_modules as $key => $module) {
					$modules = $modules . '
						<tr>
							<td>
								' . $module->name . '
							</td>
							<td>
								[ <a href="' . $this->get('settings')["LMSUrl"] . 'jackdaw/play/' . $module->id .'"><strong>PLAY</strong></a> ]&nbsp;&nbsp;
								' . (\APP\Auth::isAdmin() ? '[ <a href="' . $this->get('settings')["LMSUrl"] . 'jackdaw/play/' . $args["module_id"] .'/match/' . $module->id . '">MATCH</a> ]' : '') . '
							</td>
						</tr>
					';
				}

			$response->getBody()->write('
					<!DOCTYPE html>
					<html style="height:100%;">
						<body style="height:100%;">
							<h1 align="center">No match has been found against "<u>' . $args["module_id"] . '</u>", please choose from all available resources below.</h1>
							' . (\APP\Auth::isAdmin() ? '<h2 align="center">If you wish to use match "<u>' . $args["module_id"] . '</u>" against specific resource, please click "[MATCH] accordingly.</h2>' : '') . '
							<table align="center">
								<thead>
									<tr>
										<th>
											Name
										</th>
										<th>
											Action
										</th>
									</tr>
								</thead>
								<tbody>
									' . $modules . '
								</tbody>
							</table>
						</body>
					</html>
				');
				return $response->withHeader('Content-Type', 'text/html');
			} else {
				// As a trainee you get back notification that no match is found, please contact your administrator
				$response->getBody()->write('
					<!DOCTYPE html>
					<html style="height:100%;">
						<body style="height:100%;">
							<h1 align="center">No match has been found against match "<u>' . $args["module_id"] . '</u>", please contact your manager.</h1>
						</body>
					</html>
				');
				return $response->withHeader('Content-Type', 'text/html');
			}
		}
	})->add(\APP\Auth::getStructureAccessCheck('trainee-learning-results', 'select'));

	// Enable/disable entry
	$group->put('/free/{status:.*}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$user = \Models\FreeJackdawModule::find($args["id"]);
		$user->status = $args['status'];
		$user->save();

		// Unassign user from managers
		\Models\ManagerUser
			::where('user_id', $user->id)
			->delete();
		;

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-users', 'disable'));

	// Get free resource entry
	$group->get('/free/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$query = \Models\FreeJackdawModule
			::find($args['id'])
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-free-jackdaw-resources', 'select'));

	// Update free resource entry
	$group->put('/free/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$query = \Models\FreeJackdawModule
			::find($args['id'])
		;
		if (isset($params['name'])) {
			$query->name = $params['name'];
			$query->save();
		}

		return
			$response
				->withHeader('Content-Type', 'application/json')
				//->write(json_encode($query))
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-free-jackdaw-resources', 'update'));

	// Add new free resource entry
	$group->post('/free/new', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$query = new \Models\FreeJackdawModule;
		if (isset($params['name'])) {
			$query->name = $params['name'];
			$query->save();
		}

		return
			$response
				->withHeader('Content-Type', 'application/json')
				//->write(json_encode($query))
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-free-jackdaw-resources', 'update'));

	// Get full list of available learning resources - installable, for adding them to list of free resources
	$group->get('/free/available', function (Request $request, Response $response, array $args) {
		$query = \Models\AvailableModule
			::pluck('name')
			->toArray()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-free-jackdaw-resources', 'select'));

	// Get list of free courses offered for jackdaw users
	$group->post('/free/list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$query = \Models\FreeJackdawModule
			::where('id', '>', 0)
		;


		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-free-jackdaw-resources', 'select'));

	$group->get('/free/list', function (Request $request, Response $response, array $args) {
		$query = \Models\FreeJackdawModule
			::where('status', true)
			->get()
		;

		foreach ($query as $key => $value) {
			if (!is_dir($this->get('settings')["AvailableModulesLocation"] . "/" . $value['name'])) {
				unset($query[$key]);
			}
		}

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('trainee-modules', 'select'));

	// Get thumbnail from courses directory!
	$group->get('/free/thumb/{course_name}', function (Request $request, Response $response, array $args) {


		// get free list

		$list = \Models\FreeJackdawModule
			::where('status', true)
			->get()
		;


		// check if requested name is in free list
		foreach ($list as $key => $course) {
			if ($course['name'] == $args['course_name']) {
				if (is_file($this->get('settings')['AvailableModulesLocation'] . '/' . $args['course_name'] . '/images/thumb.jpg')) {
					$imageStream = new OpenStream($this->get('settings')['AvailableModulesLocation'] . '/' . $args['course_name'] . '/images/thumb.jpg', 'r');
					$response = $response
						->withBody($imageStream)
						->withHeader('Content-Type', FILEINFO_MIME_TYPE)
					;
				} else {
					return \APP\Tools::returnCode($request, $response, 404);
				}
			}
		}
		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('trainee-modules', 'select'));

	// Install free course for jackdaw user
	$group->post('/free/add', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$user = \APP\Auth::getUser();
		$install_resource = false;

		// Only if isJackdawCloud is set in configuration file
		if ($this->get('settings')['licensing']['isJackdawCloud']) {
			$jackdaw_settings = \APP\Jackdaw::getSettings($this->get('settings'));

			// If jackdaw user has add property and it is not 0 or less.
			if (isset($jackdaw_settings['add']) && $jackdaw_settings['add'] <= 0) {
				return \APP\Tools::returnCode($request, $response, 401, 'Can\'t add more resources, limit reached!');
			}


			// get free list
			$list = \Models\FreeJackdawModule
				::where('status', true)
				->get()
			;

			// check if requested name is in free list
			foreach ($list as $key => $course) {
				if ($course->name == $params['name']) {
					// Install resource
					$install_resource = true;
				}
			}

			// Name matches free course list, create new resource, copy files over, assign learning to user/team and respond with ID to open in jackdaw.
			if ($install_resource) {
				$learning = new \Models\LearningModule;
				$learning->id = \APP\Course::getFreeCourseId($this->get('settings')["CourseIdStart"]);
				$learning->name = $params['name'] . ' - ' . $learning->id;
				$learning->status = 1;
				$learning->type_id = \Models\LearningModuleType::getId('e_learning');
				$learning->jackdaw = 1;

				$material = new stdClass;
				$material->min_passing_percentage = 70;
				$material->scorm_standard = 2;
				$material->course_complete_status = 0;
				$learning->material = $material;


				//$learning->material = '{"min_passing_percentage":"70","scorm_standard":"2","course_complete_status":"0"}';
				$learning->created_by = \APP\Auth::getUserId();
				$learning->jackdaw_resource = true; // Created using copying new jackdaw resource, same as by jackdaw editor.
				// if jackdaw and user is in the team, add team_id
				if (isset($jackdaw_settings['group_id'])) {
					$learning->created_by_group = $jackdaw_settings['group_id'];
				}
				$learning->save();

				// Try copying files over and assigning learning to user, else delete files and delete learning.
				try {
					$course = \APP\Course::get($learning);
					$source_location = $this->get('settings')["AvailableModulesLocation"] . "/" . $params['name'];
					$course->copyAvailableModule(
						$source_location,
						$this->get('settings')["LMSScormDataPath"]
					);

					// Assign module to users/group users
					if (isset($jackdaw_settings['group_id'])) {
						$group = \Models\Group
							::where('id', $jackdaw_settings['group_id'])
							->where('status', true)
							->with(['users' => function ($query) {
								$query
									->where('users.status', true)
								;
							}])
							->first()
						;

						foreach ($group->users as $key => $user) {
							\Models\UserLearningModule::linkResources($user->id, $learning->id, 'Install free course for jackdaw user and assign to group users');
							\APP\Learning::syncUserResults($user->id);
						}
					} else {
						// Assign module to current user
						\Models\UserLearningModule::linkResources($user->id, $learning->id, 'Install free course for jackdaw user and assign to current users');
						\APP\Learning::syncUserResults($user->id);
					}

					$response->getBody()->write(json_encode($learning->id));
					return $response;

				} catch(\APP\ScormException $e) {
					$course->deleteScormSetup();
					$learning->delete();
					return \APP\Tools::returnCode($request, $response, 500, implode("\n", ["Scorm error." . $e->getMessage()]));
				}
			}

			return
				$response
			;
		}
	})->add(\APP\Auth::getStructureAccessCheck('trainee-modules', 'select'));

	$group->map(['PUT', 'GET'], '/link2ai[/{check}]', function (Request $request, Response $response, array $args) {
		$logger = \APP\LoggerHelper::getLogger();
		$data = $request->getParsedBody();
		if (
			!( method_exists('\APP\Auth', 'roleShowOpenElmsAi') ? \APP\Auth::roleShowOpenElmsAi() : true ) &&
			!\APP\Auth::isAdmin()
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$user = \APP\Auth::getUser();
		$username = $this->get('settings')["database"]["database"];

		$dataToEncrypt = json_encode([
			'user_id' => $user->id,
			'email' => $user->email,
			'fname' => $user->fname,
			'lname' => $user->lname,
		]);

		$add_data = [];

		if (
			isset($args['check']) &&
			$args['check'] == 'check'
		) {
			$add_data['check_if_exists'] = true;

			if (
				isset($data['login']) &&
				isset($data['password'])
			) {
				$add_data['login'] = $data['login'];
				$add_data['password'] = $data['password'];
			}

		}

		if ($user->openelms_ai_link) {
			$add_data['openelms_ai_link'] = $user->openelms_ai_link;
		}

		// add check_if_exists to existing $dataToEncrypt
		$dataToEncrypt = json_encode(
				array_merge(
					json_decode($dataToEncrypt, true),
					$add_data
				)
			)
		;

		$secret = crypt((string)floor(time()*0.01), \APP\Tools::getConfig('openelmsAiLinkToken'));
		$method = 'AES-256-CBC';
		$key = hash('sha256', $secret, true);
		$iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($method));
		$encryptedData = openssl_encrypt($dataToEncrypt, $method, $key, 0, $iv);
		$encryptedData = rtrim(strtr(base64_encode($iv . $encryptedData), '+/', '-_'), '=');
		// $ivAndEncrypted = base64_encode($iv . $encryptedData);
		// $encryptedData = urlencode($ivAndEncrypted);

		$redirect_url = 'https://openelms.ai/user/auth-link-v2/'.$username.'/'.$encryptedData;

		if (strlen($redirect_url) > 2000) {
			return \APP\Tools::returnCode($request, $response, 500, 'Request failed, please contact support.');
		}

		if (
			isset($args['check']) &&
			$args['check'] == 'check'
		) {
			try {
				$client = new \GuzzleHttp\Client();
				$ai_response = $client->get($redirect_url, [
					'headers' => [
						'Accept' => 'application/json',
						'Content-Type' => 'application/json',
						'User-Agent' => 'Mozilla/5.0',
					],
				]);
				$body = $ai_response->getBody()->getContents();
				$decoded_body = json_decode($body, true);
				if (isset($decoded_body['openelms_ai_link'])) {
					$user->openelms_ai_link = $decoded_body['openelms_ai_link'];
					$user->save();
					$body = [
						'status' => 'success',
						'message' => 'Link created successfully.',
					];
					$body = json_encode($body);
				} else {
					return \APP\Tools::returnCode($request, $response, 404, $body);
				}

				$response->getBody()->write($body);
				return $response->withHeader('Content-Type', 'text/plain');
			} catch (ClientException $e) {
				$logger->error('ClientException: ' . $e->getMessage());
				return \APP\Tools::returnCode($request, $response, 404);
			} catch (\Exception $e) {
				$logger->error('Exception: ' . $e->getMessage());
				return \APP\Tools::returnCode($request, $response, 500, "General error");
			}
		}

		return $response->withHeader('Location', $redirect_url)->withStatus(302);

	})->add(\APP\Auth::getSessionCheck());

	$group->post('/upload-course', function (Request $request, Response $response, array $args)
	{
		$params = $request->getParsedBody();

		\APP\Auth::checkSession();
		if (isset($params['user_id'])) {
			$user_id = $params['user_id'];
		} else{
			$user_id = 1;
		}

		$company_id = null;
		if ($user_id > 1) {
			$user = \Models\User::find($user_id);
			if (
				$user &&
				$user->company_id
			) {
				$company_id = $user->company_id;
			}
		}
		// if (!(
		// 	isset($params['token'])
		// 	&& strlen(\APP\Tools::getConfig('openelmsAiLinkToken')) > 0
		// 	&& $params['token'] == base64_encode(crypt((string)floor(time()*0.01), \APP\Tools::getConfig('openelmsAiLinkToken')))
		// 	)
		// ) {
		// 	return \APP\Tools::returnCode($request, $response, 403);
		// }

		if (isset($_FILES['zipfile']))
		{
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSTempPath"], true);
			$scormZipFile = new \Upload\File('zipfile', $storage);
			$scormZipFileName = preg_replace('/[^a-zA-Z0-9]/', '_', $params["name"]);
			$scormZipFile->setName($scormZipFileName);
			$scormZipFileName = $scormZipFile->getNameWithExtension();
			$fileTypeValidation = new \Upload\Validation\Mimetype(['application/zip', 'application/octet-stream']);
			$fileTypeValidation->setMessage("Invalid file type. You must upload a zip archive.");
			$scormZipFile->addValidations([
				$fileTypeValidation,
				new \Upload\Validation\Size('1024M')
			]);
			$scormZipFile->upload();
		} else {
			return \APP\Tools::getConfig($request, $response, 501, 'No file');
		}

		$data = [
			"id" => 0,
			"created_by" => $user_id,
			"visible_learner" => true,
			"self_enroll_access"=>0,
			"editing_access"=>0,
			"self_enroll" => true,
			"material" => [
				"scorm_standard" => "2",
				"min_passing_percentage" => "69",
				"course_complete_status" => "1",
				"zip_file" => $scormZipFileName
			],
			"type_id" => 1,
			"track_progress" => true,
			"name" => $params['name'],
			"description" => $params['description'] ?? null,
			// "category_id" => 35,
			"type" => [
			  "id" => 1,
			  "name" => "e-learning",
			  "slug" => "e_learning",
			  "status" => 1,
			  "type" => 0,
			  "custom" => false,
			],
			"company_id" => $company_id,
      	];
		// $user = User::find($user_id);
		// 	if(\APP\Tools::getConfig('sharedClients')){
		// 		if ($user && $user->role->is_admin == 0 && $user->role->access_all_companies == 0 && $user->company_id) {
		// 		$data['company_id'] = $user->company_id;
		// 	}
		// }
		$learning = \Models\LearningModule::insertImportModule($data, $this->get('settings'), $response);

		if (isset($learning["error"])) {
			return \APP\Tools::returnCode($request, $response, 500, $learning["error"]);
		} else {
			unlink($this->get('settings')["LMSTempPath"] . $scormZipFileName);
			$response->getBody()->write((string)$learning["id"]);
			return $response;
		}
	});

});
