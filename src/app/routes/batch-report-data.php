<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/batch-report-data",  function ($group) {


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$query = \Models\BatchReportData::find($args["id"]);
		$query->status = false;
		$query->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$query = \Models\BatchReportData::find($args["id"]);
		$query->status = true;
		$query->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$query = \Models\BatchReportData::where('id', $args["id"])
			->with('BatchReport')
			->first()
		;

		$query->unread = false;
		$query->save();

		$file = $GLOBALS["CONFIG"]->LMSBatchReportData . $query->file_name;
		if (is_file($file)) {
			$file_content = file_get_contents($file);
			$query->data = json_decode(gzdecode($file_content));
		} else {
			$query->data = [];
		}


		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));

	$group->get('/read/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$query = \Models\BatchReportData::find($args["id"]);
		$query->unread = false;
		$query->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));


	$group->post('/list', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();
		$query = \Models\BatchReportData::select(
				'id',
				'batch_report_id',
				'rows',
				'run_time',
				'unread',
				'status',
				'updated_at',
				DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk")
			)
			->orderBy('created_at', 'DESC')
		;

		if (array_key_exists('batch_report_id', $params)) {
			$query = $query
				->where('batch_report_id', $params["batch_report_id"])
			;
		}

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');



	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));


	// Same as  download any list functionality, just from table data, did not wanted to maodify each route file, will use one for all downloads
	$group->post('/download', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$download_file_name = false;

		if (
			empty($params['id']) &&
			empty($params['slug'])
		) {
			return false;
		}


		$query = \Models\BatchReportData::find($params['id']);
		$file = $GLOBALS["CONFIG"]->LMSBatchReportData . $query->file_name;
		if (is_file($file)) {
			$file_content = file_get_contents($file);
			$data_json_decoded = json_decode(gzdecode($file_content));
		} else {
			$data_json_decoded = [];
		}

		$slug = $params["slug"];

		if (
			$slug === 'customreview' &&
			isset($params['table_data']['search']['review_id']) &&
			$params['table_data']['search']['review_id'] > 0
		) {
			// Custom Reviews have unique way to generate export fields
			$review = \Models\CustomReview
				::where('id', $params['table_data']['search']['review_id'])
				->first()
			;
			$custom_review_settings = \Models\CustomReview::exportFields($review, $data_json_decoded);
			$download_file_name = $custom_review_settings['download_file_name'];
			$export_fields = $custom_review_settings['export_fields'];
		} else {
			// Get download settings from database table

			if (
				$slug === 'taskassessmentreports' &&
				isset($params['table_data']['search']['additionalSearchParams'])
			) {
				$additionalSearchParams = json_decode($params['table_data']['search']['additionalSearchParams'], TRUE);
				if ($additionalSearchParams['report']) {
					$slug = $slug . $additionalSearchParams['report'];
				}
			}

			$review = \Models\CustomReview
				::where('slug', $slug)
				->first()
			;

			if (
				$review &&
				$review->download_file_name
			) {
				$download_file_name = $review->download_file_name;
				$download_file_name = uniqid($download_file_name) . ".xlsx";
			}

			$export_fields = json_decode($review->export_fields, TRUE);

		}

		if ($download_file_name) {
			\APP\Tools::generateExcelDownload(
				$data_json_decoded,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			// Set data as read!
			$query->unread = false;
			$query->save();

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			return
				$response
					->withStatus(404)
					->withHeader('Content-Type', 'text/html')
					->write('404 Not Found')
			;
		}


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-programmes-data', 'select'));
});