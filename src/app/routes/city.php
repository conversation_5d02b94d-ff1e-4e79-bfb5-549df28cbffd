<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/city",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$city = \Models\City::find($args["id"]);

		$response->getBody()->write(json_encode($city));
		return $response->withHeader('Content-Type', 'application/json');
	});

	$group->map(["PUT", "POST"], "/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$city = \Models\City::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$city->name = $data["name"];
		}

		if (isset($data["country_id"])) {
			if ($city->country_id != $data["country_id"]) {
				\Models\User
					::where("city_id", "=", $city->id)
					->update([
							"country_id" => $data["country_id"]
					])
				;
			}
			$city->country_id = $data["country_id"];
		}

		$city->save();
		

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-cities', 'update'));

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$city = \Models\City::find($args["id"]);
		$city->status = 0;
		$city->save();
		

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-cities', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$city = \Models\City::find($args["id"]);
		$city->status = 1;
		$city->save();
		

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-cities', 'disable'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		
		// Cache city data for 1 hour since it rarely changes
		$cacheKey = \APP\Cache\CacheHelper::key('cities', 'all');
		
		if (function_exists('cache')) {
			$cities = cache()->remember($cacheKey, 3600, function() {
				return \Models\City
					::where("status", true)
					->select('id', 'name', 'country_id') // Only select needed fields
					->get()
				;
			});
		} else {
			// Fallback for CLI/contexts without cache
			$cities = \Models\City
				::where("status", true)
				->select('id', 'name', 'country_id')
				->get()
			;
		}

		$response->getBody()->write(json_encode($cities));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionRegisterCheck());

	$group->get('/all/{country_id: [0-9]+}', function (Request $request, Response $response, $args) {
		// Cache country-specific city data for 1 hour
		$cacheKey = \APP\Cache\CacheHelper::key('cities', 'country', $args["country_id"]);
		
		if (function_exists('cache')) {
			$data = cache()->remember($cacheKey, 3600, function() use ($args) {
				return \Models\City
					::where("status", ">", 0)
					->where("country_id", "=", $args["country_id"])
					->select('id', 'name', 'country_id')
					->get();
			});
		} else {
			$data = \Models\City
				::where("status", ">", 0)
				->where("country_id", "=", $args["country_id"])
				->select('id', 'name', 'country_id')
				->get();
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionRegisterCheck());


	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$city = new \Models\City;

		if (isset($data["name"])) {
			$city->name = $data["name"];
		}

		if (isset($data["country_id"])) {
			$city->country_id = $data["country_id"];
		}

		$city->status = 1;

		$city->save();
		

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-cities', 'insert'));

   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}


		$query = \Models\City::with(['country' => function($query) {
				$query->where("status", ">", "0")->select('id','name');
			}])
		;

		if (isset($params["search"]) && is_array($params["search"]))
		{
			foreach($params["search"] as $field => $value)
			{
				if (is_int($value))
				{
					$query->where($field, "=", $value);
				}
				else
				{
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"]))
		{
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download")
		{
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
					"ID" => "id",
					"City Name" => "name",
					"Country Name" => "country.name",
			];


			$download_file_name = uniqid(\APP\Templates::translate('%%cities%%') . ".list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
					);

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-cities', 'select'));
});