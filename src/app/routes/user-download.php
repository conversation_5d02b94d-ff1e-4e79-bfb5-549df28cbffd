<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/user-download",  function ($group) {

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\UserDownload
			::where('user_id', \APP\Auth::getUserId())
			->find($args["id"])
		;

		if (
			!$entry ||
			!$entry->export_id
		) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$export = \Models\LogExportImport::find($entry->export_id);
		if (!$export) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$file_loc = $GLOBALS["CONFIG"]->LMSTempPath . $export->file_original;

		if (is_file($file_loc)) {
			unlink($file_loc);
		}
		\Models\LogExportImport::decrypt($export, $file_loc);

		$response->getBody()->write($export->file_original);
		return $response;

	})->add(\APP\Auth::getSessionCheck());

	$group->delete('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\UserDownload
			::where('user_id', \APP\Auth::getUserId())
			->find($args["id"])
		;

		if (
			!$entry
		) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$entry->delete();
		return $response;

	})->add(\APP\Auth::getSessionCheck());

	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\UserDownload
			::where('user_id', \APP\Auth::getUserId())
			->select(
				'id',
				'name',
				'type',
				'status',
				'rows_processed',
				'generation_seconds',
				'created_at',
				DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "  %H:%i') AS created_at_uk")
			)
		;
		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}
		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write(json_encode($p));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

});