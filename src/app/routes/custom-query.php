<?php

use APP\Auth;
use Models\ApprenticeshipStandardUser;
use Models\ScheduleLink;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\CustomQuery;
use APP\Form as FormClass;


$app->group("/custom-query",  function ($group) {

	$group->get('/list', function (Request $request, Response $response, $args) {
		$custom_query = \Models\CustomQuery::with('customQueryBinding.formField')->get();
		 $response->getBody()->write(json_encode($custom_query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));


    $group->post('/all', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\CustomQuery::where("id", ">", "0")
		->with('customQueryBinding');

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));



    $group->get('/{id}', function (Request $request, Response $response, $args) {
		$custom_query = \Models\CustomQuery::with('customQueryBinding')->find($args['id']);
		 $response->getBody()->write(json_encode($custom_query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));



	$group->delete('/{id}', function (Request $request, Response $response, $args) {
		$custom_query = \Models\CustomQuery::find($args['id']);
		if($custom_query){
			$exisyQueryUsed=\Models\CustomFieldQuery::where("type","query")->where("value",$custom_query->id)->get();
			if(count($exisyQueryUsed)>0){
				$custom_query_message=["status"=>0,"message"=>"It is used in current forms. So cannot Delete Query!!"];
			}else{
				$custom_query->delete();
				$custom_query_message=["status"=>0,"message"=>"Delete Sucessfully!"];
			}

		}
		 $response->getBody()->write(json_encode($custom_query_message));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));



	$group->post('/save', function (Request $request, Response $response, $args) {
	        $data = $request->getParsedBody();
    		$slug = \APP\Tools::safeName($data['name']);
            $customFieldQueryModel=new \Models\CustomQuery();

            $customFieldQueryModel->name=$data['name'];
            $customFieldQueryModel->slug=$slug;
            $customFieldQueryModel->value=$data['value'];
            $customFieldQueryModel->save();

                // [{form:"1",field:"5","name":"fname"}]
            if(!empty($data['variables'])){
                foreach($data['variables'] AS $variable){
                    $customFieldQueryBindingModel=new \Models\CustomQueryBinding();
                    $customFieldQueryBindingModel->custom_query_id=$customFieldQueryModel->id;
                    $customFieldQueryBindingModel->variable=$variable['name'];
                    $customFieldQueryBindingModel->type="form";
                    $customFieldQueryBindingModel->type_id=$variable['field'];
                    $customFieldQueryBindingModel->fetch_value_type=$variable['type'];
                    $customFieldQueryBindingModel->save();
                }
            }

			$query = \Models\CustomQuery::where("id",  $customFieldQueryModel->id)
		               ->with('customQueryBinding.formField')->first();
    		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

});

