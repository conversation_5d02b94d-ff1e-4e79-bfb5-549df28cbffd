<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/smcr-function-responsibility",  function ($group) {

	// Get spcific SMCR function
	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$query = \Models\SmcrFunctionResponsibility::find($args["id"]);

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	});

	// Update existing SMCR function
	$group->put("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$query = \Models\SmcrFunctionResponsibility::find($args["id"]);
		$data = $request->getParsedBody();

		$query->name = $data["name"];
		if (isset($data["description"])) {
			$query->description = $data["description"];
		}
		if (isset($data["type"])) {
			$query->type = $data["type"];
		}
		if (isset($data["smcr_staff_type_id"])) {
			$query->smcr_staff_type_id = $data["smcr_staff_type_id"];
		}
		if (isset($data["is_mandatory"])) {
			$query->is_mandatory = $data["is_mandatory"];
		}

		$query->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'update'));

	// Disable SMCR function
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$query = \Models\SmcrFunctionResponsibility::find($args["id"]);
		$query->status = 0;
		$query->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'disable'));

	// Enable SMCR function
	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$query = \Models\SmcrFunctionResponsibility::find($args["id"]);
		$query->status = 1;
		$query->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'disable'));

	// Get all list of enabled SMCR functions
	$group->get('/all', function (Request $request, Response $response) {
		$query = \Models\SmcrFunctionResponsibility
			::where("status", true)
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	});


	// Add new SMCR function
	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$query = new \Models\SmcrFunctionResponsibility;

		$query->name = $data["name"];
		if (isset($data["description"])) {
			$query->description = $data["description"];
		}
		if (isset($data["type"])) {
			$query->type = $data["type"];
		}
		if (isset($data["smcr_staff_type_id"])) {
			$query->smcr_staff_type_id = $data["smcr_staff_type_id"];
		}
		if (isset($data["is_mandatory"])) {
			$query->is_mandatory = $data["is_mandatory"];
		}

		$query->status = true;
		$query->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'insert'));

   $group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\SmcrFunctionResponsibility
			::with('stafftype')
		;

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}

			if (isset($params["search"]["manager_id"])) {
				$query = $query
					->with(['staff' => function($query) use ($params) {
						$query = $query
							->where('user_id', $params["search"]["manager_id"])
						;
					}])
				;
			}

			if (
				isset($params["search"]["status"]) &&
				isset($params["search"]["manager_id"])
			) {
				if ($params["search"]["status"] == 'Assigned') {
					$query->whereHas('staff', function ($query) use($params) {
						$query = $query
							->where('user_id', $params["search"]["manager_id"])
						;
					})
				;
				} elseif (isset($params["search"]["manager_id"])) {
					$query->whereDoesntHave('staff', function ($query) use($params) {
						$query = $query
							->where('user_id', $params["search"]["manager_id"])
						;
					})
				;
				}
				unset($params["search"]["status"]);
			}

			if (
				isset($params["search"]["manager_id"])
			) {
				unset($params["search"]["manager_id"]);
			}

			if (
				isset($params["search"]["disabled_status"])
			) {
				$params["search"]["status"] = $params["search"]["disabled_status"];
				unset($params["search"]["disabled_status"]);
			}

			foreach($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name",
				"Description" => "description",
			];


			$download_file_name = uniqid("smcr-functions.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-functions', 'select'));

});