<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/api-badgr",  function ($group) {

	// *****************************************************
	// ****** reading Badgr login credentials from DB ******
	// *****************************************************
	$group->get("/" , function (Request $request, Response $response, $args) {

		$data = \Models\Configuration::where('key', '=', 'BadgrApiKey')->get()->first();

		$loginData = explode(',', $data['value']);

		$data['username'] = $loginData[0];
		$data['password'] = base64_decode($loginData[1]);
		$data['region'] = \APP\Tools::getConfig('badgrRegion');
		$data['enabled'] = \APP\Tools::getConfig('badgesEnabled');

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());

	// **********************************************
	// ****** updating Badgr login credentials ******
	// **********************************************
	$group->put("/update/" , function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();
		$query = \Models\Configuration::where('key', '=', 'BadgrApiKey')->first();
		$query->value = $data['username'] . ',' . base64_encode($data['password']);
		$query->save();

		\APP\Tools::updateConfig('badgesEnabled', $data['enabled']);
		\APP\Tools::updateConfig('badgrRegion', $data['region']);

		refreshToken();

		$response->getBody()->write($data['username'] . ',' . base64_encode($data['password']));
		return $response;

	})->add(\APP\Auth::getSessionCheck());


	// ****************************************************
	// ****** Return Badge list if Bages are enabled ******
	// ****************************************************
	$group->get("/list/" , function (Request $request, Response $response, $args) {

		$data = \Models\Configuration::where('key', '=', 'BadgrApiKey')->get();

		$responseData = [ 'enabled'=>false ];	// by default we set Badges as disabled

		if (count($data)) {						// if there is Badges config
			$data = $data->first();
			if ($data['status'] == 1)			// and Badges are enabled
			{
				// we get badges list from badgr server
				$badgesListFromBadgrServer = json_decode(\APP\Tools::getCurlData('https://api.' . \APP\Tools::getConfig('badgrRegion') . 'badgr.io/v2/badgeclasses', ['Authorization: Bearer ' . getAccessToken()]));

				$list = [];
				if(isset($badgesListFromBadgrServer->result)){
				foreach ($badgesListFromBadgrServer->result as $value) {
					array_push($list, [
						'name'=>$value->name,
						'description'=>$value->description,
						'entityId'=>$value->entityId . "," . $value->issuer,
					]);
				}
			}

				$responseData = [
					'enabled'=>true,
					'list'=>$list
				];
			}
		}

		$response->getBody()->write(json_encode($responseData));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());


	// *******************************
	// ****** for test purposes ******
	// *******************************
	$group->get("/test/" , function (Request $request, Response $response, $args) {

		//return getAccessToken();
		//return \Models\Users::competencies()->get();

		$data = json_decode(\APP\Tools::getCurlData(
			'https://api.' . \APP\Tools::getConfig('badgrRegion') . 'badgr.io/v2/badgeclasses/YKUb_t-YRviLSWq6syTUgQ',
			[('Authorization: Bearer ' . getAccessToken()), 'Content-Type: application/json']));

		$response->getBody()->write('Data: ' . $this->get('settings')['LMSUrl']);
		return $response->withHeader('Content-Type', '*/*');

	})->add(\APP\Auth::getSessionCheck());


	// *************************************
	// ****** return Users Badge data ******
	// *************************************
	$group->get("/user/[{id:[0-9]+}]" , function (Request $request, Response $response, $args)
	{
		if (!\Models\Role::getRoleParam('lfp_show_badges')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

		if (isset($args['id']) && intval($args['id']) > 0)
		{
			$data = \Models\User
				::whereId($args['id'])
				->select('fname', 'lname', 'email', 'id')
				->with('competencies')
				->first()
				;
			$responseData = [
				'name' => $data['fname'] . " " . $data['lname'],
				'email' => $data['email'],
			];
			$list = [];

			foreach ($data['competencies'] as $item) {
				// acquired check
				if (
					$item['required_points'] <= $item['pivot']['points']
				) {

					// if there is a badge associated with a competency OR user have one already, even if competency has no badge associated at the moment
					if (
						$item['badge'] || $item['pivot']['badge']
					) {
                        /*New logic for adding competencies badges to user badges*/
                        $userBadge = \Models\UserBadge::where([['type','=','competencies'], ['type_id',$item['id']], ['user_id', $data->id]])->first();
                        if(!$userBadge){
                            $userBadge = new \Models\UserBadge;
                            $userBadge->type = "competencies";
                            $userBadge->type_id = $item['id'];
                            $userBadge->user_id = $data->id;
                            $userBadge->badge = $item['badge']?$item['badge']:$item['pivot']['badge'];
                            $userBadge->save();
                        }
					}
				}
			}


            $userBadges =  \Models\UserBadge::select(DB::raw("user_badges.badge,user_badges.type,user_badges.id,user_badges.is_claimed,learning_modules.name as learning_name,learning_modules.description as learning_description, learning_modules.id as learning_id, apprenticeship_standards.name as standard_name,apprenticeship_standards.id as standard_id, competencies.name as competency_name, competencies.id as competency_id"))
                ->leftJoin('learning_modules',function($join){
                    $join->on('learning_modules.id','user_badges.type_id')->where('user_badges.type','=','learning_modules');
                })->leftJoin('apprenticeship_standards',function($join){
                    $join->on('apprenticeship_standards.id','user_badges.type_id')->where('user_badges.type','=','apprenticeship_standards');
                })->leftJoin('competencies',function($join){
                    $join->on('competencies.id','user_badges.type_id')->where('user_badges.type','=','competencies');
                })->where('user_badges.user_id',$args['id'])->where('user_badges.is_active',1)->get();

			$responseData['list'] = $userBadges;

			$response->getBody()->write(json_encode($responseData));
			return $response->withHeader('Content-Type', 'application/json');
		}

	})->add(\APP\Auth::getSessionCheck());


	// *****************************************
	// ****** issue the Badge to the User ******
	// *****************************************
	$group->get("/claim/[{id:[0-9]+}]" , function (Request $request, Response $response, $args) {

		if (isset($args['id']) && intval($args['id']) > 0) {

            $userBadges =  \Models\UserBadge::select(DB::raw("user_badges.badge,user_badges.user_id,user_badges.type,user_badges.id,user_badges.is_claimed,learning_modules.name as learning_name,
            learning_modules.description as learning_description, learning_modules.id as learning_id, apprenticeship_standards.name as standard_name,
            apprenticeship_standards.id as standard_id, competencies.name as competency_name, competencies.description as competency_description, competencies.id as competency_id"))
                ->leftJoin('learning_modules',function($join){
                    $join->on('learning_modules.id','user_badges.type_id')->where('user_badges.type','=','learning_modules');
                })->leftJoin('apprenticeship_standards',function($join){
                    $join->on('apprenticeship_standards.id','user_badges.type_id')->where('user_badges.type','=','apprenticeship_standards');
                })->leftJoin('competencies',function($join){
                    $join->on('competencies.id','user_badges.type_id')->where('user_badges.type','=','competencies');
                })->with('user')->where('user_badges.id',$args['id'])->first();


			$badgeData = explode(',', $userBadges->badge);
            $narative = null;
            if($userBadges->type == "learning_modules"){
                $narative =$userBadges->learning_name.': '.$userBadges->learning_description;
            } if($userBadges->type == "apprenticeship_standards"){
                $narative =$userBadges->standard_name.': '.'not applicable';

            } if($userBadges->type == "competencies"){
                $narative =$userBadges->competency_name.': '. $userBadges->ompetency_description;

            }

			$data = [
				'issuer' => $badgeData[1],
				'issuerOpenBadgeId' => "https://api." . \APP\Tools::getConfig('badgrRegion') . "badgr.io/public/issuers/" . $badgeData[1],
				'recipient' => [
					'identity' => $userBadges->user->email,
					'hashed' => false,
					'type' => 'email',
					'plaintextIdentity' => $userBadges->user->email
					],
				'issuedOn' => date(DATE_ISO8601),
				'narrative' => $narative,
				'evidence' => [[
					'url' => ($this->get('settings')['LMSUrl'] == 'https://openelms/' ? 'https://this.is.valid.com' : $this->get('settings')['LMSUrl']),	//this is for development enviroment
					//'narrative' => '',
					],],
				'expires' => null,
				'extensions' => [
					'extensions:recipientProfile' => [
						'@context' => "https://openbadgespec.org/extensions/recipientProfile/context.json",
						'type' => ["Extension", "extensions:RecipientProfile"],
						'name' => $userBadges->user->fname . " " . $userBadges->user->lname,
						],
					],
			];

			$fromBadgrServer = json_decode(\APP\Tools::getCurlData(
					'https://api.' . \APP\Tools::getConfig('badgrRegion') . 'badgr.io/v2/badgeclasses/' . $badgeData[0] . '/assertions',
					[('Authorization: Bearer ' . getAccessToken()), 'Content-Type: application/json'],
					$data, true));

			if ($fromBadgrServer->status->success) {

				$badgeName = json_decode(\APP\Tools::getCurlData(
					'https://api.' . \APP\Tools::getConfig('badgrRegion') . 'badgr.io/v2/badgeclasses/' . $badgeData[0],
					[('Authorization: Bearer ' . getAccessToken()), 'Content-Type: application/json']));
				$badgeName = $badgeName->result[0]->name;
				$badgeName = (strlen($badgeName) > 64) ? substr($badgeName, 0, 61) . '...' : $badgeName;

				$returnData = $fromBadgrServer->result[0]->entityId . ',' . $fromBadgrServer->result[0]->issuer . ',' . base64_encode($badgeName);

				$query = \Models\UserBadge::where('id', $args['id'])->first();
				$query->badge = $returnData;
				$query->is_claimed = 1;
				$query->save();

			} else {

				$returnData = 'error';
				//$returnData = json_encode($fromBadgrServer);

			}

			$response->getBody()->write($returnData);
			return $response;

		}

	})->add(\APP\Auth::getSessionCheck());


	function getAccessToken() {
		$data = \Models\Configuration::where('key', '=', 'BadgrApiKey')->get()->first();
		// if DB contains token data, process it
		if ($data['description']) {
			$tokenData = explode(',', $data['description']);
			// if token is not expired, return it
			if(time() < $tokenData[1])
				return $tokenData[0];
		}
		// else get & return new token
		return refreshToken();
	}

	function refreshToken() {
		$data = \Models\Configuration::where('key', '=', 'BadgrApiKey')->get()->first();
		$loginData = explode(',', $data['value']);

		if (
			!empty($loginData[0]) &&
			!empty($loginData[1])
		) {
			$tokenFromBadgrServer = json_decode(
				\APP\Tools::getCurlData('https://api.' . \APP\Tools::getConfig('badgrRegion') . 'badgr.io/o/token',[],['username'=>$loginData[0], 'password'=>base64_decode($loginData[1])])
			);
		}


		if (
			!empty($tokenFromBadgrServer->access_token) &&
			!empty($tokenFromBadgrServer->expires_in)
		) {
			// write fresh token into DB
			\Models\Configuration::where('key', '=', 'BadgrApiKey')->update([
				'description' => $tokenFromBadgrServer->access_token . "," . (time() + $tokenFromBadgrServer->expires_in)
			]);

			return
				$tokenFromBadgrServer->access_token;
		} else {
			return false;
		}
	}

});