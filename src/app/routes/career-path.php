<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/career-path", function ($group) {

    $group->post('/list', function (Request $request, Response $response) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        $query = \Models\CareerPath::where("id", '>', 0);

        $data = \APP\SmartTable::searchPaginate($params, $query, false);

        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getSessionCheck());

    $group->put('/update-status/{status:enable|disable}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $careerPath = \Models\CareerPath::find($args['id']);
        if ($args['status'] == 'enable') {
            $careerPath->status = 1;
        } else {
            $careerPath->status = 0;
        }
        $careerPath->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-career-path', 'disable'));

    $group->post('/save', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();
        $data = [
            'name' => $params['name'],
            'status' => true
        ];
        $careerPath = \Models\CareerPath::create($data);

        $designations = $params['designations'] ?? [];
        if ($careerPath) {
            foreach ($designations as $key => $value) {
                $order = $key + 1;
                \Models\CareerPathDesignations::updateOrCreate([
                    'career_path_id' => $careerPath->id,
                    'designation_id' => $value,
                ], [
                    'career_path_id' => $careerPath->id,
                    'designation_id' => $value,
                    'listing_order' => $order
                ]);
            }
        }

        $response->getBody()->write('Successfull');
		return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-career-path', 'insert'));

    $group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {

        $data = \Models\CareerPath::with('Designations')->find($args['id']);

        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-career-path', 'select'));

    $group->put('/update/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();
        $data = [
            'name' => $params['name'],
        ];

        $careerPath = \Models\CareerPath::where('id', $args['id'])->update($data);
        $designations = $params['designations'] ?? [];

        if ($careerPath) {
            \Models\CareerPathDesignations::where('career_path_id', $args['id'])->delete();

            foreach ($designations as $key => $value) {
                $order = $key + 1;
                \Models\CareerPathDesignations::updateOrCreate([
                    'career_path_id' => $args['id'],
                    'designation_id' => $value,
                ], [
                    'career_path_id' => $args['id'],
                    'designation_id' => $value,
                    'listing_order' => $order
                ]);
            }
        }

        $response->getBody()->write('Successfull');
		return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-career-path', 'insert'));

});