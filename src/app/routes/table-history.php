<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/table-history",  function ($group) {

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\TableHistory
			::find($args["id"])
			->with('CreatedBy')
		;

		$response->getBody()->write(json_encode($entry));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-table-history', 'select'));

	$group->get('/{table_id:[0-9]+}/{table_name}', function (Request $request, Response $response, $args) {

		// If table_name is role_structure, then need to do some custom work, need to list all entries in table_history that ar joined with role_structure table and specific table_id
		if ($args['table_name'] == "role_structure") {
			$entry = \Models\TableHistory
				::where('table_name', $args['table_name'])
				->join('role_structure', function ($join) {
					$join->on('role_structure.id', '=', 'table_history.table_id');
				})
				->where('role_structure.role_id', $args['table_id'])
				->get()
			;
		} else {
			$entry = \Models\TableHistory
				::where('table_id', $args['table_id'])
				->where('table_name', $args['table_name'])
				->get()
			;
		}



		$response->getBody()->write(json_encode($entry));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-table-history', 'select'));




	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$query = \Models\TableHistory
			::where("table_id", ">", "0")
		;

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		if (
			isset($args["download"]) &&
			$args["download"] == "/download"
		) {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Table name" => "table_name",
				"Modified by ID" => "user_id",
				"Before" => "before",
				"After" => "after",
			];


			$download_file_name = uniqid("table-history.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		}

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-table-history', 'select'));
});
