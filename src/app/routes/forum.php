<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
$app->group("/forum",  function ($group) {


	$group->post("/new", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (\APP\Auth::isDemoUser()) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
			;
		}

		$forum = new \Models\Forum;
		$forum->schedule_id = isset($data['schedule_id']) ? $data['schedule_id'] : null;
		$forum->visible_learner = isset($data['visible_learner']) ? $data['visible_learner'] : false;
		$forum->allow_learner_topic = isset($data['allow_learner_topic']) ? $data['allow_learner_topic'] : false;
		$forum->allow_learner_post = isset($data['allow_learner_post']) ? $data['allow_learner_post'] : false;
		$forum->added_by = \APP\Auth::getUserId();
		$forum->status = isset($data['status']) ? $data['status'] : false;
		$forum->save();
		$response->getBody()->write(json_encode($forum));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());




	$group->put("/visibility/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (
			\APP\Auth::isDemoUser() ||
			\APP\Auth::isLearner()
		) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
			;
		}

		$forum = \Models\Forum::find($args['id']);
		$forum->visible_learner = !$forum->visible_learner;
		$forum->save();

	})->add(\APP\Auth::getSessionCheck());

	/*Delete Forum*/
	$group->delete("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$forum = \Models\Forum::find($args['id']);
		if (
			$forum &&
			(
				\APP\Auth::isAdmin() ||
				$forum->added_by == \APP\Auth::getUserId()
			)
		) {
			if($forum->posts()->exists())
				$forum->posts()->delete();
			if($forum->topics()->exists())
				$forum->topics()->delete();
			$forum->delete();
		}

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-forum ', 'lessons-and-learning-resources'], 'disable'));

	/*************TOPICS SECTION***************/
	/*Create new topics*/
	$group->post("/topic/new", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (\APP\Auth::isDemoUser()) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
				;
		}

		// Fail is user is DEMO
		if (!isset($data['forum_id']) && empty($data['forum_id'])) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('No Forum attached')
				;
		}

		$topic = new \Models\Topic;
		$topic->name = isset($data['name'])? $data['name'] : "topic";
		$topic->content = isset($data['content']) ? $data['content'] : "content";
		$topic->forum_id = $data['forum_id'];
		$topic->added_by = \APP\Auth::getUserId();
		$topic->save();
		$response->getBody()->write(json_encode($topic));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	/*Update topics*/
	$group->post('/topic/edit/{topic_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (\APP\Auth::isDemoUser()) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
				;
		}

		// Fail is user is DEMO
		if (!isset($args['topic_id']) && empty($args['topic_id'])) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('No Forum attached')
				;
		}

		$topic = \Models\Topic::find($args['topic_id']);
		$topic->name = isset($data['name'])? $data['name'] : "topic";
		$topic->content = isset($data['content']) ? $data['content'] : "content";
		$topic->status = isset($data['status']) ? $data['status'] : 1;
		$topic->forum_id = $data['forum_id'];
		$topic->added_by = \APP\Auth::getUserId();
		$topic->save();
		$response->getBody()->write(json_encode($topic));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	/*Get topics on the basis of forum*/
	$group->get('/topics/{forum_id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$topics = \Models\Topic
			::where('forum_id',$args['forum_id'])->get();
	});



	/*Delete Topic*/
	$group->delete("/topic/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$topic = \Models\Topic::find($args['id']);
		if (
			$topic &&
			(
				\APP\Auth::isAdmin() ||
				$topic->added_by == \APP\Auth::getUserId()
			)
		) {
			if($topic->posts()->exists())
				$topic->posts()->delete();
			$topic->delete();
		}

	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-forum ', 'lessons-and-learning-resources'], 'disable'));

  /*Get Topic*/
	$group->get("/topic/{topic_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$topic = \Models\Topic::where('id',$args['topic_id'])
			->with(['posts'=>function($query){
			$query
				->select('id','topic_id','added_by','created_at','updated_at','post')
				->with(['AddedBy'=>function($query){
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id',
						'image'
					)
					->with('role');
			}])->orderBy('id', 'DESC');
		}])->first();


		if (
			$topic &&
			(
				\APP\Auth::isAdmin() ||
				\APP\Auth::accessAllLearners() ||
				$topic->added_by == \APP\Auth::getUserId()
                ||$topic->AddedBy->Role->is_admin
                ||$topic->AddedBy->Role->is_manager
			)
		) {

			$response->getBody()->write(json_encode($topic));
		return $response->withHeader('Content-Type', 'application/json');
		}
	});


/*Get Forum*/
	$group->post("/{forum_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		if (!isset($args['forum_id']) && empty($args['forum_id'])) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('No Forum found!!')
				;
		}

		/*Forum Added by managers*/
		$params = $request->getParsedBody();

		// fake parameter to redo request, unset this!
		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$forum = \Models\Topic::where('forum_id',$args['forum_id'])
			   ->select('id','forum_id','added_by','created_at','updated_at','name','content')
				->with(['AddedBy' => function($query) {
					$query
						->select(
							'id',
							'fname',
							'lname',
							'role_id',
							'image'
						)
						->with('role');
					}])->with(['posts'=>function($query){
							$query
								->select('id','topic_id','added_by','created_at','updated_at','post')
								->with(['AddedBy'=>function($query){
									$query
										->select(
											'id',
											'fname',
											'lname',
											'role_id',
											'image'
										)
										->with('role');
								}]);

						}])->orderBy('id','DESC');

			$p = \APP\SmartTable::searchPaginate($params, $forum);

		if (
			$forum
		) {

			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		}
	});

	/**************TOPIC POST SECTIONS****************/
	/*Update/edit post*/
	$group->post('/post/edit/{post_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (\APP\Auth::isDemoUser()) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
				;
		}

		// Fail is user is DEMO
		if (!isset($args['post_id']) && empty($args['post_id'])) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('No Forum attached')
				;
		}

		$post = \Models\Post::find($args['post_id']);
		$post->post = isset($data['post'])? $data['post'] : "Default";
		$post->status = isset($data['status']) ? $data['status'] : 1;
		$post->added_by = \APP\Auth::getUserId();
		if($post->added_by == \APP\Auth::getUserId()||\APP\Auth::isAdmin())
		$post->save();
		else
		{
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('No Permission')
				;
		}
		$response->getBody()->write(json_encode($post));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	/*Add new Post*/
	$group->post('/post/new', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		// Fail is user is DEMO
		if (\APP\Auth::isDemoUser()) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('403 Forbidden')
				;
		}

		// Fail is user is DEMO
		if (!isset($data['topic_id']) && empty($data['topic_id'])) {
			return $response
				->withStatus(403)
				->withHeader('Content-Type', 'text/html')
				->write('No Forum attached')
				;
		}

		$post = new \Models\Post;
		$post->post = isset($data['post'])? $data['post'] : "topic";
		$post->status = isset($data['status']) ? $data['status'] : 1;
		$post->added_by = \APP\Auth::getUserId();
		$post->topic_id = $data['topic_id'];
		$post->save();
		$response->getBody()->write(json_encode($post));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	/*Delete Post*/
	$group->delete("/post/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$post = \Models\Post::find($args['id']);
		if (
			$post &&
			(
				$post->added_by == \APP\Auth::getUserId()||\APP\Auth::getSessionCheck()
			)
		) {

			$post->delete();
		}

	});

})->add(\APP\Auth::getSessionCheck());
