<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
$app->group("/calendar",  function ($group) {
	$group->get("/ical", function (Request $request, Response $response, array $args) {
		$getVar = $request->getQueryParams(); // get all GET params
		$dt = new DateTime('now'); // create new date object used for timestamp.

		return
			$response
				->withHeader('Content-Type', 'text/Calendar; charset=utf-8')
				->withHeader('Content-Disposition', 'attachment; filename="cal.ics"')
				->write('BEGIN:VCALENDAR
VERSION:2.0
PRODID:OpenLMS
CALSCALE:GREGORIAN
BEGIN:VEVENT
DTSTART:' . $getVar['time'] . '
DTEND:' . $getVar['timeEnd'] . '
UID:' . uniqid() . '
DTSTAMP:' . $dt->format('Ymd\THis\Z') . '
LOCATION:' . $getVar['location'] . '
DESCRIPTION:' . $getVar['description'] . '
SUMMARY:' . $getVar['title'] . '
END:VEVENT
END:VCALENDAR');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));
});
