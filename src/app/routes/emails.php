<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/emails",  function ($group) {

	$group->put('/{email_template_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		// Update template
		$email_template = \Models\EmailTemplate::find($args["email_template_id"]);
		$fields = [
			"subject", "body", "status", "copy_email_to_managers", "ignore_queue_approval",
		];
		\APP\Tools::setObjectFields($email_template, $fields, $params);
		//  Add sanitization for base64 images
		$email_template->body = preg_replace(
			'/<img[^>]+src=["\']data:image\/[^;]+;base64,[^"\']+["\'][^>]*>/i',
			'',
			$email_template->body
		);

		$email_template->save();

		// Update e-mail queue
		if (
			!empty($params['queue']['id']) &&
			!empty($params['queue']['frequency_pattern'])
		) {
			$email_queue = \Models\EmailQueue::find($params['queue']['id']);
			$email_queue->frequency_pattern = json_encode($params['queue']['frequency_pattern']);
			$email_queue->send_date = \Carbon\Carbon::parse($params['queue']['send_date']);
			$email_queue->save();
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-emails', 'update'));


	// To get specific template
	$group->get('/template/{email_template_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$email_template = \Models\EmailTemplate
			::where("id", $args["email_template_id"])
			->with(['Files' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->first();

		$response->getBody()->write(json_encode($email_template));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-emails', 'select'));

	// Not sure if this is uesed anywhere.
	$group->get('/template/{email_template_name}', function (Request $request, Response $response, array $args) {
		$email_template = \Models\EmailTemplate
			::where("name", "=", $args["email_template_name"])
			->with(['Files' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->first();

		$response->getBody()->write(json_encode($email_template));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-emails', 'select'));


	$group->get('/', function (Request $request, Response $response) {
		$email_templates = \Models\EmailTemplate
			::where(function ($query) {
				$query
					->where("is_temporary", false)
					->orWhereHas('queue', function ($query) {
						$query
							->whereNotNull('frequency_pattern')
							->where('processed', false)
						;
					})
				;
			})
			->where(function ($query) {
				$query
					->where('site_versions', '')
					->orWhere('site_versions', 'LIKE', '%"' . $this->get('settings')["licensing"]['version'] . '%"')
				;
			})
			->with(['Files' => function($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->with(['queue' => function($query) {
				$query
					->whereNotNull('frequency_pattern')
					->where('processed', false)
				;
			}])
			->get();

		$response->getBody()->write(json_encode($email_templates));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-emails', 'select'));

	$group->delete('/{email_template_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		// get template
		$email_template = \Models\EmailTemplate::find($args["email_template_id"]);

		//check if there is queue linked to it.
		$email_queue = \Models\EmailQueue
			::where('email_template_id', '=', $email_template->id)
			->first()
		;
		if ($email_queue->frequency_pattern) {
			$email_queue->delete();
			$email_template->delete();
		} else {
			return \APP\Tools::returnCode($request, $response, 500);
		}

		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-emails', 'disable'));

	$group->post('/email_history/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::accessAllLearners() &&
			!\APP\Auth::isManagerOf($args["user_id"]) &&
			\APP\Auth::getUserId() != $args["user_id"]
		) {
			\APP\Tools::returnCode($request, $response, 403);
		}

		if (!\Models\Role::getRoleParam('lfp_show_email_history')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

		$user_id = $args["user_id"];
		$params = $request->getParsedBody();

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}
		$query = \Models\EmailHistory::where('user_id',$user_id);
		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

});