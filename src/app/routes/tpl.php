<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/tpl/", function($group) {

	$group->get("excel/{excel_tpl:\/?.*}",  function (Request $request, Response $response, array $args) {

		$excel_tpl = $this->get('settings')["LMSTplsPath"] . "excel/" . $args["excel_tpl"];

		if (is_file($excel_tpl)) {
			$response
				->getBody()
				->write(file_get_contents($excel_tpl))
			;
			return $response
				->withHeader('Content-Type', 'application/xls')
				->withHeader('Content-Disposition', "attachment;filename='" . basename($excel_tpl))
				->withHeader('Pragma', 'public')
				->withHeader('Cache-Control', 'maxage=1')

			;
		} else {
			return \APP\Tools::returnCode($request, $response, 404);
		}
	});

	$group->get("public/{tpl:\/?.*}",  function (Request $request, Response $response, array $args) {

		$filePath = $this->get('settings')["LMSPublicPath"] . $args["tpl"];

		if (is_file($filePath)) {
			$ext = pathinfo($filePath, PATHINFO_EXTENSION);

			if ($ext == "js") {

				$content = file_get_contents($filePath); // Read the file directly
				$fileModifiedTime = filemtime($filePath);
				$etag = md5($content);

				$response = $response
					->withHeader('Content-Type', 'application/javascript; charset=utf-8')
					->withHeader('Cache-Control', 'public, max-age=31536000')
					->withHeader('Last-Modified', gmdate('D, d M Y H:i:s T', $fileModifiedTime))
					->withHeader('Etag', $etag);

				if ($request->hasHeader('If-Modified-Since') && strtotime($request->getHeaderLine('If-Modified-Since')) === $fileModifiedTime) {
					return $response->withStatus(304);
				}

				if($request->hasHeader('If-None-Match') && $request->getHeaderLine('If-None-Match') === $etag){
					return $response->withStatus(304);
				}

				$response->getBody()->write($content);

				return $response;
			} else if ($ext == "map") {

				$content = file_get_contents($filePath);
				$fileModifiedTime = filemtime($filePath);
				$etag = md5($content);

				$response = $response
					->withHeader('Content-Type', 'application/json; charset=utf-8')
					->withHeader('Cache-Control', 'public, max-age=31536000')
					->withHeader('Last-Modified', gmdate('D, d M Y H:i:s T', $fileModifiedTime))
					->withHeader('Etag', $etag);

				if ($request->hasHeader('If-Modified-Since') && strtotime($request->getHeaderLine('If-Modified-Since')) === $fileModifiedTime) {
					return $response->withStatus(304);
				}

				if($request->hasHeader('If-None-Match') && $request->getHeaderLine('If-None-Match') === $etag){
					return $response->withStatus(304);
				}

				$response->getBody()->write($content);

				return $response;
			} else {
				return \APP\Tools::returnCode($request, $response, 400);
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 404);
		}
	});


    $group->get("{tpl:\/?.*}",  function (Request $request, Response $response, array $args) {

        $filePath = $this->get('settings')["LMSTplsPath"] . $args["tpl"];

        if (is_file($filePath)) {
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            $vars = \APP\Templates::getVariables($this->get('settings'));

            if ($ext == "html") {
				$translator = \APP\Templates::getTranslator();

				try {
					$clean_page_name = str_replace('html/','', str_replace(".html", "", $args["tpl"]));
					$page = \Models\Page::where("link", "=", $clean_page_name)->firstOrFail();
					$page_name = $page->name;
				} catch (Exception $e) {
					$page_name = "";
				}

				$vars["PageName"] = $translator->replaceLabels($page_name);

				$rendered_tpl =  $this->get('view')->fetch($args["tpl"], $vars);

				$translated_tpl = $translator->processTtemplate($rendered_tpl);
				// Need to replace labels from site versioning.
				$translated_tpl = $translator->replaceVersionLabels($translated_tpl);

				$response
					->getBody()
					->write($translated_tpl)
				;

                // Check for 'v' parameter and set caching headers if present
                if ($request->getQueryParams()['v'] ?? null) {
                    $fileModifiedTime = filemtime($filePath);
                    $etag = md5($translated_tpl);

                    $response = $response
                        ->withHeader('Cache-Control', 'public, max-age=31536000') // Cache for 1 year
                        ->withHeader('Last-Modified', gmdate('D, d M Y H:i:s T', $fileModifiedTime))
                        ->withHeader('Etag', $etag);

                    // Check for conditional requests
                    if ($request->hasHeader('If-Modified-Since') && strtotime($request->getHeaderLine('If-Modified-Since')) === $fileModifiedTime) {
                        return $response->withStatus(304);
                    }

                    if ($request->hasHeader('If-None-Match') && $request->getHeaderLine('If-None-Match') === $etag) {
                        return $response->withStatus(304);
                    }
                }

				return $response;
            } else if ($ext == "js") {

                $content = $this->get('view')->fetch($args["tpl"], $vars);
                $fileModifiedTime = filemtime($filePath);
                $etag = md5($content);

                $response = $response
                    ->withHeader('Content-Type', 'application/javascript; charset=utf-8')
                    ->withHeader('Cache-Control', 'public, max-age=31536000') // Cache for 1 year
                    ->withHeader('Last-Modified', gmdate('D, d M Y H:i:s T', $fileModifiedTime))
                    ->withHeader('Etag', $etag);

                // Check for conditional requests
                if ($request->hasHeader('If-Modified-Since') && strtotime($request->getHeaderLine('If-Modified-Since')) === $fileModifiedTime) {
                    return $response->withStatus(304); // Not Modified
                }

                if($request->hasHeader('If-None-Match') && $request->getHeaderLine('If-None-Match') === $etag){
                    return $response->withStatus(304);
                }

                $response->getBody()->write($content);

                return $response;
            } else {
				return $this
					->get('view')
					->render($response, $args["tpl"], $vars)
				;
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 404);
		}
	});
});
