<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/apprenticeship-route",  function ($group) {

	$group->get('/list', function (Request $request, Response $response, array $args) {
		$query = \Models\ApprenticeshipRoute
			::with('standards')
			->get()
		;
		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('misc-permissions-apprenticeship-routes', 'select'));
});
