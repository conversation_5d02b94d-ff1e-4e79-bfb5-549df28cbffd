<?php

use APP\Controllers\WorkflowController;
use APP\Form;
use Illuminate\Support\Arr;
use Models\FormWorkflow;
use Models\FormWorkflowTemplates;
use Models\UserForm;
use Models\UserFormValue;
use Models\UserWorkflowForm;
use Models\UserFormSignoff;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Models\EventType;
use Models\EventTypeWorkflow;
use Models\Schedule;
use Models\ScheduleVisitTypeWorkflow;
use Illuminate\Database\Capsule\Manager as DB;
use Models\CustomFieldQuery;
use Slim\Psr7\Stream;

$app->group("/form-workflow", function ($group) {
    $group->post('/', function (Request $request, Response $response) {
        $body = $request->getParsedBody();

        $documentTemplate = FormWorkFlow::create([
            'name' => $body['name'],
        ]);
        foreach ($body['params'] as  $value) {
            FormWorkflowTemplates::create([
                'form_workflow_id' => $documentTemplate->id,
                'document_template_id' => $value['forms']['id']
            ]);
        }
        $response->getBody()->write(json_encode($documentTemplate));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

    $group->post('/list', function (Request $request, Response $response) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["refresh"])) {
            unset($params["search"]["refresh"]);
        }

        $query = FormWorkflow::where("id", ">", 0)->with('form_workflow_templates.document_templates');

        $data = \APP\SmartTable::searchPaginate($params, $query, false);

        $response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

    $group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $formWorkflow = FormWorkflow::find($args["id"]);
        $formWorkflow->status = 0;
        $formWorkflow->save();

        //When disable the work flow, Remove from assigned users
        Schedule::detachWorkFlowAssignedForms($args["id"]);

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

    $group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $formWorkflow  = FormWorkflow::find($args["id"]);
        $formWorkflow->status = 1;
        $formWorkflow->save();

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

    $group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $formWorkflow = FormWorkflow::with('form_workflow_templates')->find($args['id']);
        $response->getBody()->write(json_encode($formWorkflow));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getSessionCheck());


    $group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $body = $request->getParsedBody();
        $formWorkflow = FormWorkflow::find($args['id']);
        $formWorkflow->name = $body['name'];
        $formWorkflow->save();
        $bindings = [];
        foreach ($body['params'] as  $value) {
            $bindings[] = $value['forms']['id'];
            FormWorkflowTemplates::updateOrCreate(
                ['form_workflow_id' => $formWorkflow->id, 'document_template_id' => $value['forms']['id']],
                [
                    'form_workflow_id' => $formWorkflow->id,
                    'document_template_id' => $value['forms']['id']
                ]
            );
        }
        FormWorkflowTemplates::whereNotIn('document_template_id', $bindings)->where('form_workflow_id', $formWorkflow->id)->delete();
        $response->getBody()->write(json_encode($formWorkflow));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getSessionCheck());

    $group->get('/all[/{display_all}]', function (Request $request, Response $response, $args) {
        session_write_close();
        $formWorkflow = FormWorkflow::enabled();
        if(!isset($args['display_all']))
        {
            $formWorkflow = $formWorkflow->whereDoesntHave('form_workflow_templates.document_templates.document_template_bindings.form',function($query){
                $query->where('restricted_form',1);
            });
        }
        $formWorkflow = $formWorkflow->whereHas("form_workflow_templates.document_templates.document_template_bindings",function($query){
        })
        ->get(); //This will show all templates that has bindings irrespective of type (Forms/Graph)

        $response->getBody()->write(json_encode($formWorkflow));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getSessionCheck());


    // get all users from standard using pagination
    $group->post('/users/{form_workflow_id:[0-9]+}', function (Request $request, Response $response, $args) {
        $params = $request->getParsedBody();

        if (isset($params["search"]["form_workflow_id"])) {
            unset($params["search"]["form_workflow_id"]);
        }

        $query = \Models\User::selectRaw("users.*")
            ->where("users.status", ">", "0")
            ->with(['UserWorkFlow' => function ($query) use ($args) {
                $query->where("type", 'direct');
                $query->where("form_workflow_id", $args["form_workflow_id"]);
            }])
            ->select('id', 'fname', 'lname', 'email');
        if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
            $query->whereHas('UserWorkFlow', function ($query) use ($args) {
                $query->where("type", 'direct');
                $query->where("form_workflow_id", $args["form_workflow_id"]);
            });
            unset($params["search"]["show_assigned"]);
        }

        // if (\APP\Auth::isManager() && !\APP\Auth::isAdmin() && !\APP\Auth::accessAllLearners()) {
        // 	$query = $query
        // 		->whereIn("users.id", function($query) {
        // 			$query
        // 				->select("user_id")
        // 				->from("manager_users")
        // 				->where("manager_id", "=", \APP\Auth::getUserId())
        // 			;
        // 		})
        // 	;
        // }

        $p = \APP\SmartTable::searchPaginate($params, $query);

        $response->getBody()->write(json_encode($p->toJson()));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

    $group->post('/departments/{form_workflow_id:[0-9]+}', function (Request $request, Response $response, $args) {
        try {
            $params = $request->getParsedBody();

            if (isset($params["search"]["form_id"])) {
                unset($params["search"]["form_id"]);
            }

            $query = \Models\Department::where("departments.status", 1)
                ->with(["company" => function ($query) {
                    $query->select(["companies.id", "companies.name"]);
                }])
                ->with(['DepartmentFormWorkFlow' => function ($query) use ($args) {
                    $query
                        ->where("form_workflow_id", $args["form_workflow_id"]);;
                }]);

            if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
                $query->whereHas('DepartmentFormWorkFlow', function ($query) use ($args) {
                    $query
                        ->where("form_workflow_id", $args["form_workflow_id"]);;
                });
                unset($params["search"]["show_assigned"]);
            }
            $p = \APP\SmartTable::searchPaginate($params, $query);

            $response->getBody()->write(json_encode($p->toJson()));
		return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            print_r($e->getMessage());
            die;
        }
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

    $group->post('/groups/{form_workflow_id:[0-9]+}', function (Request $request, Response $response, $args) {
        try {
            $params = $request->getParsedBody();

            if (isset($params["search"]["form_id"])) {
                unset($params["search"]["form_id"]);
            }

            $query = \Models\Group::where("groups.status", 1)
                ->with(['GroupFormWorkFlow' => function ($query) use ($args) {
                    $query
                        ->where("form_workflow_id", $args["form_workflow_id"]);;
                }]);

            if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
                $query->whereHas('GroupFormWorkFlow', function ($query) use ($args) {
                    $query
                        ->where("form_workflow_id", $args["form_workflow_id"]);;
                });
                unset($params["search"]["show_assigned"]);
            }
            $p = \APP\SmartTable::searchPaginate($params, $query);

            $response->getBody()->write(json_encode($p->toJson()));
		return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            print_r($e->getMessage());
            die;
        }
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));



    $group->put('/{form_workflow_id:[0-9]+}/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
        DB::beginTransaction();
        try {
            $relations_ids=[];
            $insert_log='';
            $logData=[];
            $form_workflow_id = $args['form_workflow_id'];
            $user_id = $args['user_id'];
            $form_ids = FormWorkflow::getAssignedFormIds($form_workflow_id);

            foreach ($form_ids as $form_id) {
                $data = [
                    "user_id" => $user_id,
                    "form_id" => $form_id['form_id'],
                    "type" => "work_flow",
                    "type_id" => $form_workflow_id,
                    "assigned_method"=>'workflow_direct_assign',
                    "reference_type_id"=>NULL,
                    "reference_type"=>NULL

                ];

                $data=array_merge($data,$form_id);
                # Checkin if form_id is present (type 'graph' and 'report' doesn't have form_id)
                if($form_id['form_id']){
                    $user_form = \Models\UserForm::addUsersForm($data);
                    $user_assigned_type = \Models\UserFormAssignedType::insertAssignedTypes($data);
                    /**
                     * Add log for userform to workflow relation
                     * tag - workflow_lesson_assign
                     */
                    $data['user_form_id']=isset($user_form['id'])?$user_form['id']:null;
                    unset($data['form_id']);
                    unset($data['user_id']);
                    $relationModel=\Models\UserFormTemplateWorkflowRelations::addUserFormTemplateWorkFlow($data);
                    $relations_ids[]=$relationModel->id;
                }



            }
            $user_workflow_form = UserWorkFlowForm::firstOrCreate([
                "user_id" => $user_id,
                "form_workflow_id" => $form_workflow_id,
                "type" => "direct",
                "type_id" => NULL,
            ]);
            \Models\UserFormTemplateWorkflowRelations::whereIn("id",$relations_ids)
                ->update(["user_workflow_id"=>$user_workflow_form->id]);
            DB::commit();
            $response->getBody()->write(json_encode($user_workflow_form->toJson()));
		return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            die;
        }
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

    // Remove user from standard
    $group->delete("/{form_workflow_id:[0-9]+}/user/{user_id:[0-9]+}", function (Request $request, Response $response, $args) {
        DB::beginTransaction();
        try {
            $userFormData=[];
            $user_custom_values=[];
            $form_workflow_id = $args['form_workflow_id'];
            $user_id = $args['user_id'];
            $form_ids = FormWorkflow::getAssignedFormIds($form_workflow_id);
            if($form_ids){
                $form_ids=collect($form_ids)->pluck('form_id')->toArray();
                $userFormData = \Models\UserForm::where("user_id", $args["user_id"])->whereIn("form_id", $form_ids)->get();
                foreach ($userFormData as $user_form) {
                    $user_custom_values[]=$user_form->user_custom_form_value_id;
                    \Models\UserWorkflowForm::where("form_workflow_id", $form_workflow_id)
                        ->where("user_id", $args["user_id"])->delete();
                    \Models\UserFormTemplateWorkflowRelations::where("user_form_id",$user_form->id)->delete();
                }
                \Models\UserForm::where("user_id", $args["user_id"])->whereIn("form_id", $form_ids)->delete();
                if($user_custom_values){
                    \Models\UserCustomFormValue::whereIn("id", $user_custom_values)->delete();
                }

            }
            DB::commit();
            return $userFormData;
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
        }
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

    $group->put("/{form_workflow_id:[0-9]+}/department/{department_id:[0-9]+}", function (Request $request, Response $response, $args) {
        try {
            $data = $request->getParsedBody();

            $userAssignedForm = new \Models\DepartmentFormWorkFlow();
            $userAssignedForm->department_id = $args['department_id'];
            $userAssignedForm->form_workflow_id = $args['form_workflow_id'];
            $userAssignedForm->status = "1";
            $userAssignedForm->save();

            // Get all department users.
            $department = \Models\Department::where('id', $args["department_id"])
                ->where('status', true)
                ->with(["employees" => function ($query) {
                    $query
                        ->where('status', true);
                }])
                ->first();

            // Huge resource problem, need to optimise down the line
            if ($department) {
                foreach ($department->employees as $key => $employee) {
                    $relations_ids=[];
                    $form_ids = FormWorkflow::getAssignedFormIds($args['form_workflow_id']);

                    foreach ($form_ids as $form_id) {
                        $data = [
                            "user_id" => $employee->id,
                            "form_id" => $form_id['form_id'],
                            "type" => "work_flow",
                            "type_id" => $args['form_workflow_id'],
                            "reference_type_id"=>NULL,
                            "reference_type"=>NULL,

                        ];

                        $user_form = \Models\UserForm::addUsersForm($data);
                        $user_assigned_type = \Models\UserFormAssignedType::insertAssignedTypes($data);

                         /**
                         * Add log for userform to workflow relation
                         * tag - workflow_direct_assign
                         */
                        $insert_log=array_merge(["user_form_id"=>$user_form['id'],
                        "type"=>"workflow",
                        "type_id"=>$args['form_workflow_id'],
                        "assigned_method"=>'workflow_direct_assign'],$form_id);
                        unset($insert_log['form_id']);
                        $relationModel=\Models\UserFormTemplateWorkflowRelations::addUserFormTemplateWorkFlow($insert_log);
                        $relations_ids[]=$relationModel->id;

                    }
                    $user_workflow_form = UserWorkFlowForm::firstOrCreate([
                        "user_id" => $employee->id,
                        "form_workflow_id" => $args['form_workflow_id'],
                        "type"=>"workflow",
                        "type_id"=>$args['form_workflow_id']
                    ]);
                    \Models\UserFormTemplateWorkflowRelations::whereIn("id",$relations_ids)
                            ->update(["user_workflow_id"=>$user_workflow_form->id]);

                }
            }
        } catch (\Exception $e) {
            print_r($e->getMessage());
        }

        return
            $userAssignedForm;
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


    $group->put("/{form_workflow_id:[0-9]+}/group/{group_id:[0-9]+}", function (Request $request, Response $response, $args) {
        try {
            $data = $request->getParsedBody();

            $userAssignedForm = new \Models\GroupFormWorkFlow();
            $userAssignedForm->group_id = $args['group_id'];
            $userAssignedForm->form_workflow_id = $args['form_workflow_id'];
            $userAssignedForm->status = "1";
            $userAssignedForm->save();

            // Get all group users.
            $group = \Models\Group::where('id', $args["group_id"])
                ->where('groups.status', true)
                ->with(["employees" => function ($query) {
                    $query
                        ->where('users.status', true);
                }])
                ->first();

            // Huge resource problem, need to optimise down the line
            if ($group) {
                foreach ($group->employees as $key => $employee) {
                    $relations_ids=[];
                    $form_ids = FormWorkflow::getAssignedFormIds($args['form_workflow_id']);

                    foreach ($form_ids as $form_id) {
                        $data = [
                            "user_id" => $employee->id,
                            "form_id" => $form_id['form_id'],
                            "type" => "work_flow",
                            "type_id" => $args['form_workflow_id'],
                            "reference_type_id"=>NULL,
                            "reference_type"=>NULL,
                        ];

                        $user_form = \Models\UserForm::addUsersForm($data);
                        $user_assigned_type = \Models\UserFormAssignedType::insertAssignedTypes($data);
                         /**
                         * Add log for userform to workflow relation
                         * tag - workflow_direct_assign
                         */
                        $insert_log=array_merge(["user_form_id"=>$user_form['id'],
                        "type"=>"workflow",
                        "type_id"=>$args['form_workflow_id'],
                        "assigned_method"=>'workflow_direct_assign'],$form_id);
                        unset($insert_log['form_id']);
                        $relationModel=\Models\UserFormTemplateWorkflowRelations::addUserFormTemplateWorkFlow($insert_log);
                        $relations_ids[]=$relationModel->id;
                    }
                    $user_workflow_form = UserWorkFlowForm::firstOrCreate([
                        "user_id" => $employee->id,
                        "form_workflow_id" => $args['form_workflow_id'],
                        "type" => "workflow",
                        "type_id" => $args['form_workflow_id'],
                    ]);
                    \Models\UserFormTemplateWorkflowRelations::whereIn("id",$relations_ids)
                        ->update(["user_workflow_id"=>$user_workflow_form->id]);
                }
            }
        } catch (\Exception $e) {
            print_r($e->getMessage());
        }

        return
            $userAssignedForm;
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));


    // Remove department and users from standard
    $group->delete("/{form_workflow_id:[0-9]+}/department/{department_id:[0-9]+}", function (Request $request, Response $response, $args) {
        try {
            $data = $request->getParsedBody();
            $form_workflow_id = $args['form_workflow_id'];
            $response = \Models\DepartmentFormWorkFlow::where("form_workflow_id", $args["form_workflow_id"])->where("department_id", $args["department_id"])
                ->delete();
            $department = \Models\Department::where('id', $args["department_id"])
                ->where('status', true)
                ->with(["employees" => function ($query) {
                    $query
                        ->where('status', true);
                }])
                ->first();
            if ($department) {
                $form_ids = FormWorkflow::getAssignedFormIds($form_workflow_id);
                if($form_ids){
                    $form_ids=collect($form_ids)->pluck('form_id')->toArray();
                    foreach ($department->employees as $key => $employee) {
                        $userFormData = \Models\UserForm::where("user_id", $employee->id)->whereIn("form_id", $form_ids)->get();
                        foreach ($userFormData as $user_form) {
                            \Models\UserWorkflowForm::where("form_workflow_id", $form_workflow_id)
                                ->where("user_id", $employee->id)->delete();
                            \Models\UserForm::where("id", $user_form->id)->delete();
                            \Models\UserCustomFormValue::where("id", $user_form->user_custom_form_value_id)->delete();
                        }
                    }
                }
            }
            return $response;
        } catch (\Exception $e) {
            print_r($e->getMessage());
        }
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

    // Remove group and users from standard
    $group->delete("/{form_workflow_id:[0-9]+}/group/{group_id:[0-9]+}", function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();
        $response = \Models\GroupFormWorkFlow::where("form_workflow_id", $args["form_workflow_id"])->where("group_id", $args["group_id"])->delete();
        $group = \Models\Group::where('id', $args["group_id"])
            ->where('groups.status', true)
            ->with(["users" => function ($query) {
                $query
                    ->where('users.status', true);
            }])
            ->first();
        if ($group) {
            $form_ids = FormWorkflow::getAssignedFormIds($args["form_workflow_id"]);
            if($form_ids){
                $form_ids=collect($form_ids)->pluck('form_id')->toArray();
                foreach ($group->users as $key => $user) {
                    $userFormData = \Models\UserForm::where("user_id", $user->id)->whereIn("form_id", $form_ids)->get();
                    foreach ($userFormData as $user_form) {
                        \Models\UserWorkflowForm::where("form_workflow_id", $args["form_workflow_id"])
                            ->where("user_id", $user->id)->delete();
                        \Models\UserForm::where("id", $user_form->id)->delete();
                        \Models\UserCustomFormValue::where("id", $user_form->user_custom_form_value_id)->delete();
                    }
                }
            }
        }
        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


    $group->post('/assigned_workflow/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
        try {
            if (!\Models\Role::getRoleParam('lfp_show_assigned_workflow')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

            if (\APP\Auth::isLearner()) {
                $args["user_id"] = \APP\Auth::getUserId();
            }

            $user_id = $args["user_id"];
            $params = $request->getParsedBody();

            if (isset($params["search"]["refresh"])) {
             unset($params["search"]["refresh"]);
            }
            $query = UserWorkflowForm
                ::with('lesson','programme','schedule','UserFormTemplateWorkflowRelations.user_forms')
                ->with(['form_workflow' => function ($query) use ($user_id) {
                    $query
                        ->with(['form_workflow_templates' => function ($query) use ($user_id) {
                            $query
                                ->with('document_templates.document_template_bindings.customreportfield')
                                ->with(['document_templates.document_template_bindings.form' => function ($query) use ($user_id) {
                                    $query
                                        ->with(['UserForms' => function ($query) use ($user_id) {
                                            $query
                                                ->where('user_id', $user_id)
                                            ;
                                        }])
                                    ;
                                }])
                            ;
                        }])
                    ;
                }])
                // if there is any forms disabled for the user in workflow then dont return the workflow
                ->whereDoesntHave('form_workflow.form_workflow_templates.document_templates.document_template_bindings.form', function ($query) use ($user_id) {
                    $query->whereHas('UserForms', function ($query) use ($user_id) {
                        $query
                            ->where('user_id', $user_id)
                            ->whereNotNull('deleted_at')
                        ;
                    });
                })
                ->select(
                    "*",
                    "user_workflow_form.user_id as user_id",
                    "user_workflow_form.id AS id",
                    "form_workflow.name as name",
                    "user_workflow_form.created_at"
                )
                ->join('form_workflow', function ($join) {
                    $join
                        ->on('form_workflow.id', 'user_workflow_form.form_workflow_id')
                        ->where("form_workflow.status", "=", 1);
                })
                ->join('users', function ($join) {
                    $join
                        ->on("users.id", "=", "user_workflow_form.user_id")
                        ->where("users.status", "=", 1);
                })
                ->where("user_workflow_form.user_id", $user_id)
            ;

            if ( \APP\Auth::isAdmin() && $user_id == \APP\Auth::getUserId()) {
                $query
                    ->whereIn("user_workflow_form.user_id", function ($query) use ($user_id) {
                        $query
                            ->select("user_id")
                            ->from("manager_users")
                            ->where("manager_id", "=", $user_id)
                            ->whereNull('manager_users.deleted_at');
                    })
                ;
            }

            if(\APP\Auth::isManager() && !\APP\Auth::isAdmin()){
                $current_user = \APP\Auth::getUserId();
                $query->where(function($query)use($current_user,$user_id){
                    $query->where(function($query)use($current_user){
                        $query->where('user_workflow_form.type','schedule')
                            ->whereExists(function($query)use($current_user){
                                $query->from('schedules')->whereColumn('schedules.id','user_workflow_form.type_id')
                                ->join('schedule_links','schedule_links.schedule_id','schedules.id')
                                ->join('forms','forms.id','schedule_links.link_id')
                                ->where('schedule_links.type','forms')
                                ->where(function($query)use($current_user){
                                    $query->where(function($query)use($current_user){
                                        $query->where('forms.restricted_form',1);
                                        $query->whereExists(function($query)use($current_user){
                                            $query->from('schedule_links as manager_links')
                                            ->whereColumn('manager_links.schedule_id','schedules.id')
                                            ->where('manager_links.type',"managers")
                                            ->where('manager_links.link_id',$current_user);
                                        });
                                })->orWhere(function($query){
                                    $query->where('forms.restricted_form',0)
                                          ->whereNotExists(function($query){
                                            $query->from('schedule_links as form_schedule_links')
                                            ->whereColumn('form_schedule_links.schedule_id','schedule_links.schedule_id')
                                            ->where('form_schedule_links.type','forms')
                                            ->join('forms as schedule_forms','schedule_forms.id','form_schedule_links.link_id')
                                            ->where('schedule_forms.restricted_form',1);
                                    });
                                });
                                    });
                            });
                    })->orWhere(function($query)use($current_user){
                        $query->where('user_workflow_form.type','!=','schedule')
                        ->whereIn("user_workflow_form.user_id", function ($query) use ($current_user) {
                            $query
                                ->select("user_id")
                                ->from("manager_users")
                                ->where("manager_id", "=", $current_user)
                                ->whereNull('manager_users.deleted_at');
                        });
                    });
                });
            }
            $p = \APP\SmartTable::searchPaginate($params, $query);
            $response->getBody()->write(json_encode($p));
		    return $response->withHeader('Content-Type', 'application/json');

        } catch (\Exception $e) {
            return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
        }
    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

    $group->get('/forms/{id:[0-9]+}/{user_id:[0-9]+}/{type:lesson|programme|schedule|direct}/[{type_id:[0-9]+}]', function (Request $request, Response $response, $args) {

            if (\APP\Auth::isLearner()) {
                $args["user_id"] = \APP\Auth::getUserId();
            }

            $formWorkflow = FormWorkflow::with(['form_workflow_templates.document_templates.document_template_bindings' => function ($query) use ($args) {
            $query->with(['form.UserForms' => function ($query) use ($args) {
                if(isset($args['type_id']))
                {

                  $query->where('type',$args['type'])->where('type_id',$args['type_id']);

                }else{
                  $query->where('type','=',"work_flow");
                }
                $query->where('user_id', $args['user_id'])->with('FormValue')->with('UserFormSignoff.User');

                $query->with(['scheduleLinkManagers' => function($query) {
                    $query->whereNull('deleted_at');
                }]);

            }]);
            $query->with('form.FormSignOffRoles');
            $query->with('form_field.Field.FieldOption');
        }])->find($args['id']);
        $formWorkflow = $formWorkflow->toArray();
        foreach ($formWorkflow['form_workflow_templates'] as $key => &$value) {
            foreach ($value['document_templates']['document_template_bindings'] as $bind_key => &$document_template_binding) {
                $form = $document_template_binding['form'];
if(isset($document_template_binding['form_field']) && isset($document_template_binding['form_field']['field'])){
                    if($document_template_binding['form_field']['field']['type'] =="selectbox" && isset($document_template_binding['form_field']['field']['model'])){
                        $model = $document_template_binding['form_field']['field']['model'];
                        $options=$model::where('status',1)->get();
                        $document_template_binding['form_field']['field']['field_option']=$options->toArray();
                    }
                }
                if (\APP\Auth::isManager()) {
                    if (!empty ($document_template_binding['form']) && $document_template_binding['form']['user_forms']) {
                        $userForm = Arr::first($document_template_binding['form']['user_forms']);
                        $isRestrictedForm = $form['restricted_form'] == 1 &&
                            data_get($args, 'type') == 'schedule' &&
                            !empty($form['user_forms']) &&
                            $document_template_binding['type'] == "is_form" &&
                            !empty($userForm) &&
                            !empty($userForm['form_value']) &&
                            !empty($userForm['form_value']['values']);

                        // check if restricted form fields in the template by checking into the event manager is not assigned
                        $isRestrictedForm = $isRestrictedForm && collect($userForm['schedule_link_managers'])->where('link_id', '=', \APP\Auth::getUserId())->isEmpty();

                        if($isRestrictedForm) {
                            $formFieldSlug = data_get($document_template_binding['form_field'], 'field.slug');
                            if(array_key_exists($formFieldSlug, $userForm['form_value']['values']) && !empty($formFieldSlug)) {
                                if(isset($document_template_binding['form']['user_forms'][0]['form_value']['values'][$formFieldSlug])) {
                                    $document_template_binding['form']['user_forms'][0]['form_value']['values'][$formFieldSlug]  = 'Data Restricted';
                                }
                            }
                        }
                    }
                }
                if (!empty($form) && !empty($form['user_forms'])) {
                    if (count($form['user_forms']) && $document_template_binding['type'] == "is_form" && $form['is_derived_form']) {
                        $user_form = $form['user_forms'][0];
                        $form_field = $document_template_binding['form_field'];
                        $customFieldQuery = CustomFieldQuery::where('form_field_id', $document_template_binding['form_field_id'])->first();
                        if ($customFieldQuery) {
                            $userFormValue = UserFormValue::where('form_field_id', $form_field['id'])->where('user_form_id', $user_form['id'])->first();
                            $valueData = $userFormValue->value ?? '';
                            if ($valueData) {
                                // dd($formWorkflow['form_workflow_templates'][$key]['document_templates']['document_template_bindings'][$bind_key]['form']['user_forms'][0]);
                                $formWorkflow['form_workflow_templates'][$key]['document_templates']['document_template_bindings'][$bind_key]['form']['user_forms'][0]['form_value']['values'][$form_field['field']['slug']] = $valueData;
                            }
                        }
                    }
                }
            }
        }
        $response->getBody()->write(json_encode($formWorkflow));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

    $group->get('/signature/{id:[0-9]+}', function (Request $request, Response $response, $args) {
        $signOffForm = UserFormSignoff::find($args['id']);
        if ($signOffForm && is_file($signOffForm->e_signature)) {
            // Open the image file stream
            $imageStream = new Stream(fopen($signOffForm->e_signature, 'rb'));

            // Create response with image content
            return $response
                ->withBody($imageStream)
                ->withHeader('Content-Type', 'image/png')
                ->withHeader('Content-Disposition', 'inline; filename="' . basename($signOffForm->e_signature) . '"');
        } else {
            // Return 404 response if the signature or file is not found
            $response->getBody()->write(json_encode(['message' => 'Signature not found']));
            return $response
                ->withHeader('Content-Type', 'application/json')
                ->withStatus(404);
        }
    });

    $group->post('/download/pdf', function (Request $request, Response $response) {
        $body = $request->getParsedBody();
        $pdf_file = $this->get('settings')['LMSPdfGenPath'] . "pdf.js";
        $temp_pdf_file =$this->get('settings')['LMSPdfGenPath'].uniqid();
        file_put_contents($temp_pdf_file,$body['query']);
        $env = "XDG_CONFIG_HOME=/tmp/.chromium XDG_CACHE_HOME=/tmp/.chromium";
        $cmd = $env." node " . $pdf_file . " " . $temp_pdf_file;
        $output = shell_exec($cmd);
        if ($output) {
            $response->getBody()->write($output);
		return $response;

        } else {
            return $response->withStatus(404);
        }
    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

    $group->get('/download/pdf/{uuid}', function (Request $request, Response $response, $args) {
        if (isset($args['uuid']) && is_file($this->get('settings')['LMSPdfGenPath'] . $args['uuid'])) {
            $pdf_file = $this->get('settings')['LMSPdfGenPath'] . $args['uuid'];
            $pdfStream = new OpenStream($pdf_file, 'r');
            $response = $response->withBody($pdfStream)->withHeader('Content-Type', 'application/pdf');
            return $response;
        } else {
            return $response->withStatus("404", "Not Found");
        }
    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

    $group->get('/events/{id:[0-9]+}/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {

            if (\APP\Auth::isLearner()) {
                $args["user_id"] = \APP\Auth::getUserId();
            }

            $event_type_id=EventTypeWorkflow::where('form_workflow_id',$args['id'])->pluck('event_type_id');
            $eventTypeSlug = EventType::whereIn('id',$event_type_id)->where('status',1)->pluck('slug');
            $schedule_visit_type=ScheduleVisitTypeWorkflow::where('form_workflow_id',$args['id'])->pluck('schedule_visit_type_id');
            $schedules = Schedule::whereHas('Users',function($query) use($args){
                $query->where('link_id',$args['user_id']);
            })->where(function($query)use($eventTypeSlug,$schedule_visit_type){
                  $query->whereIn('type',$eventTypeSlug);
                  $query->orWhereIn('visit_type_id',$schedule_visit_type);
            })->where('status',1)->orderBy('name')->get();
            $response->getBody()->write(json_encode($schedules));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

    // Update FormWorkflow property
    $group->put("/{id:[0-9]+}/property/{property:[\/a-z_-]*}", function (Request $request, Response $response, array $args) {
        $data = $request->getParsedBody();

        $entry = \Models\FormWorkflow::find($args['id']);
        if (isset($entry[$args['property']])) {
            $entry[$args['property']] = $data['value'];
        }
        $entry->save();


        return
            $response
        ;
    })->add(\APP\Auth::getStructureAccessCheck('library-learning-form-workflow-data', 'update'));

    $group->get("/export/json/{id:[0-9]+}", WorkflowController::class . ':export')
        ->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));
    $group->post("/import", WorkflowController::class . ':import')
        ->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'update'));

});
