<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/default-label",  function ($group) {

	$group->get("/{slug}" , function (Request $request, Response $response, $args) {
		$entry = \Models\DefaultLabel
			::where('slug', $args['slug'])
			->first()
		;

		$default_labels = $GLOBALS["CONFIG"]->licensing['labels'];


		$return_data = new \stdClass;
		$return_data->slug = $args['slug'];
		$return_data->id = $args['slug'];
		$return_data->default = $default_labels[$args['slug']];
		$return_data->overwrite = '';
		if ($entry) {
			$return_data->overwrite = $entry->overwrite;
		}

		$response->getBody()->write(json_encode($return_data));
		return $response->withHeader('Content-Type', 'application/json');
	});

	$group->put("/{slug}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$entry = \Models\DefaultLabel
			::where('slug', $args['slug'])
			->first()
		;
		if (!$entry) {
			$entry = new \Models\DefaultLabel;
			$entry->slug = $args['slug'];
		}
		$entry->overwrite = isset($data['overwrite']) ? $data['overwrite'] : '';

		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-default-labels', 'update'));


	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$group = \Models\DefaultLabel::find($args["id"]);
		$group->status = false;
		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-default-labels', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$group = \Models\DefaultLabel::find($args["id"]);
		$group->status = true;
		$group->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-default-labels', 'disable'));


	// Returns all entries from active site version licensing labels
	$group->post('/all', function (Request $request, Response $response) {

		$params = $request->getParsedBody();

		$default_labels = $GLOBALS["CONFIG"]->licensing['labels'];
		$response_data = [];

		// Build data for response_data
		foreach ($default_labels as $key => $default_label) {
			$response_entry = new \stdClass;
			$response_entry->slug = $key;
			$response_entry->default = $default_label;
			$default_label_db = \Models\DefaultLabel::where('slug', $key)->first();
			$response_entry->overwrite = '';
			if ($default_label_db) {
				$response_entry->overwrite = $default_label_db->overwrite;
			}
			$response_data[] = $response_entry;
		}

		$response->getBody()->write(json_encode($response_data));
		return $response->withHeader('Content-Type', 'application/json');
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-default-labels', 'select'));
});