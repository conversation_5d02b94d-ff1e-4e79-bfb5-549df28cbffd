<?php

use Models\Coupon;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use APP\Controllers\CouponController;

$app->group("/coupon", function ($group) {

    $group->post('/list', CouponController::class . ':list')->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

    $group->get('/{id:[0-9]+}', CouponController::class . ':get')->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

    $group->post('/save', CouponController::class . ':create')->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->put('/update/{id:[0-9]+}', CouponController::class . ':update')->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->put('/update-status/{status:enable|disable}/{id:[0-9]+}', CouponController::class . ':updateStatus')->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

    $group->post('/apprenticeship_standard/list', CouponController::class . ':listStandards')->add(\APP\Auth::getStructureAccessCheck(['apprenticeship_standards-and-other-programmes', 'trainee-standards'], 'select'));
    $group->put('/apprenticeship_standard/{id:[0-9]+}', CouponController::class . ':addProgramme');//->add(\APP\Auth::getStructureAccessCheck('library-apprenticeship_standards-resources-and-lessons-users', 'insert'));
    $group->put('/apprenticeship_standard/delete/{id:[0-9]+}', CouponController::class . ':deleteProgramme');//->add(\APP\Auth::getStructureAccessCheck('library-apprenticeship_standards-resources-and-lessons-users', 'disable'));

    $group->post('/learning_resource/list', CouponController::class . ':listLearningResources')->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));
    $group->put('/learning_resource/{id:[0-9]+}', CouponController::class . ':addLearningResource')->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));
    $group->put('/learning_resource/delete/{id:[0-9]+}', CouponController::class . ':deleteLearningResource')->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));

    $group->post('/learning_activity/list', CouponController::class . ':listLearningActivities')->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));
    $group->put('/learning_activity/{id:[0-9]+}', CouponController::class . ':addLearningActivity')->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));
    $group->put('/learning_activity/delete/{id:[0-9]+}', CouponController::class . ':deleteLearningActivity')->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));

    $group->post('/schedule/list', CouponController::class . ':listEvents')->add(\APP\Auth::getStructureAccessCheck(['library-learning-resources-and-lessons', 'trainee-modules'], 'select'));
    $group->put('/schedule/{id:[0-9]+}', CouponController::class . ':addEvent')->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'insert'));
    $group->put('/schedule/delete/{id:[0-9]+}', CouponController::class . ':deleteEvent')->add(\APP\Auth::getStructureAccessCheck('library-learning-resources-and-lessons-users', 'disable'));

    $group->post('/info', CouponController::class . ':getInfo')->add(\APP\Auth::getSessionCheck());

    $group->post('/apply', CouponController::class . ':apply')->add(\APP\Auth::getSessionCheck());

});
