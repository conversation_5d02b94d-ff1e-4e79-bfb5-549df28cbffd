<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/resource-assignment",  function ($group) {

	$group->get("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = \Models\LearningResult
			::where('id', $args['id'])
			->with('module')
			->with('user')
			->first()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-resource-assignment'], 'select'));


	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\UserLearningModule
			::where('user_learning_modules.id', '>', 0)
		;
		$master_table = 'user_learning_modules';
		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}

			if (
				isset($params["search"]["archive"]) &&
				$params["search"]["archive"]
			) {
				unset($params["search"]["archive"]);
				$query = \Models\UserLearningModuleArchive
					::where('user_learning_module_archives.id', '>', 0)
				;
				$master_table = 'user_learning_module_archives';
			}

		}

		$query = $query
			->select([
				$master_table . '.id',
				$master_table . '.user_id',
				$master_table . '.learning_module_id',
				$master_table . '.comment_link',
				$master_table . '.url_link',
				$master_table . '.comment_unlink',
				$master_table . '.url_unlink',
				$master_table . '.deleted_by',
				$master_table . '.created_by',
				$master_table . '.created_at',
				$master_table . '.deleted_at',
				#'users.id as user_id',
				#'users.username',
				#'users.fname',
				#'users.lname',
				//'learning_modules.name as module_name',
				//'learning_modules.type_id',
				//'learning_module_types.name as type_name',
				//'learning_modules.category_id',
				//'learning_module_categories.name as category_name',
				#DB::raw("CONCAT(users.fname, ' ', users.lname) as learner_name"),
				//DB::raw("CONCAT(created_by.fname, ' ', created_by.lname) as created_by_name"),
				//DB::raw("CONCAT(deleted_by.fname, ' ', deleted_by.lname) as deleted_by_name"),
				//DB::raw("DATE_FORMAT(" . $master_table . ".created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS created_at_uk"),
				//DB::raw("DATE_FORMAT(" . $master_table . ".deleted_at,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS deleted_at_uk"),
			])
			->with(['user' => function ($query) use ($args) {
				$query
					->select(
						[
							'id',
							'fname',
							'lname',
							'username',
							'usercode',
						]
					);
			}])
			->with(['createdby' => function ($query) use ($args) {
				$query
					->select(
						[
							'id',
							'fname',
							'lname',
						]
					)
				;
			}])
			->with(['deletedby' => function ($query) use ($args) {
				$query
					->select(
						[
							'id',
							'fname',
							'lname',
						]
					)
				;
			}])
			->with(['module' => function ($query) use ($args) {
				$query
					->select(
						[
							'id',
							'name',
							'category_id',
							'type_id',
							'is_course',
						]
					)
					->with(['type' => function ($query) use ($args) {
						$query
							->select(
								[
									'id',
									'name',
								]
							)
						;
					}])
					->with(['category' => function ($query) use ($args) {
						$query
							->select(
								[
									'id',
									'name',
								]
							)
						;
					}])
				;
			}])

			->withTrashed()
		;

		if (!empty($params["search"]["username"])) {
			$query = $query
				->whereHas('user', function ($query) use ($params) {
					$query
						->where('username', $params["search"]["username"])
					;
				})
			;
			unset($params["search"]["username"]);
		}

		if (!empty($params["search"]["usercode"])) {
			$query = $query
				->whereHas('user', function ($query) use ($params) {
					$query
						->whereRaw("usercode LIKE '%" . $params["search"]["usercode"] . "%'")
					;
				})
			;
			unset($params["search"]["usercode"]);
		}

		if (!empty($params["search"]["fname_lname"])) {
			$query = $query
				->whereHas('user', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["fname_lname"] . "%'")
					;
				})
			;
			unset($params["search"]["fname_lname"]);
		}

		if (!empty($params["search"]["learning_module_name"])) {
			$query = $query
				->whereHas('module', function ($query) use ($params) {
					$query
						->whereRaw("name LIKE '%" . $params["search"]["learning_module_name"] . "%'")
					;
				})
			;
			unset($params["search"]["learning_module_name"]);
		}

		if (!empty($params["search"]["learning_module_category_id"])) {
			$query = $query
				->whereHas('module', function ($query) use ($params) {
					$query
						->where('category_id', $params["search"]["learning_module_category_id"])
					;
				})
			;
			unset($params["search"]["learning_module_category_id"]);
		}

		if (isset($params["search"]["learning_module_type_id"])) {
			$query = $query
				->whereHas('module', function ($query) use ($params) {
					if ($params["search"]["learning_module_type_id"] == 0) {
						$query
							->where('is_course', 1)
						;
					} else {
						$query
							->where('type_id', $params["search"]["learning_module_type_id"])
						;
					}

				})
			;
			unset($params["search"]["learning_module_type_id"]);
		}

		if (!empty($params["search"]["created_by_fname_lname"])) {
			$query = $query
				->whereHas('createdby', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["created_by_fname_lname"] . "%'")
					;
				})
			;
			unset($params["search"]["created_by_fname_lname"]);
		}

		if (!empty($params["search"]["deleted_by_fname_lname"])) {
			$query = $query
				->whereHas('deletedby', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["deleted_by_fname_lname"] . "%'")
					;
				})
			;
			unset($params["search"]["deleted_by_fname_lname"]);
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-resource-assignment', 'select'));

});