<?php

use APP\Auth;
use Models\ApprenticeshipStandardUser;
use Models\ScheduleLink;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Form;
use APP\Form as FormClass;
use Models\FormField;
use Models\FormFieldAuthorPermission;
use Models\FormSignoffRole;
use Models\UserCustomFormValue;
use Models\UserFormSignoff;
use Upload\File;
use Upload\Storage\FileSystem;
use APP\Controllers\FormController;

$app->group("/form-logs",  function ($group) {
    $group->post('/list', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
        if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}
        $query = \Models\FormLog::with(['User', 'Form', 'EditedByUser'])
                                ->join('users as u', 'u.id', 'form_logs.user_id')
                                ->join('users as e', 'e.id', 'form_logs.edited_by_user')
                                ->join('forms as f', 'f.id', 'form_logs.form_id')
                                ->select('form_logs.*');



        $p = \APP\SmartTable::searchPaginate($params, $query);
        $response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck('system-setup-form-logs', 'select'));

    $group->get('/user-form-details/{log_id:[0-9]+}/{form_id:[0-9]+}', function (Request $request, Response $response, array $args) {
        // $user_form_values = \Models\UserFormValue::where('user_form_id', $args['user_form_id'])->get();
        $data = [];
        $data = collect($data);
        $formLog = \Models\FormLog::find($args['log_id']);
        $data['log'] = $formLog;
        if ($formLog->form_field_file){
            if(file_exists($this->get('settings')["LMSFormLogsData"] . $formLog->form_field_file)){
                $data['form'] = json_decode(file_get_contents($this->get('settings')["LMSFormLogsData"] . $formLog->form_field_file));
            }
        }

        $response->getBody()->write($data->toJson());
		return $response->withHeader('Content-Type', 'application/json');

        // return $user_form_values;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-form-logs', 'select'));

    $group->get('/download/{log_id:[0-9]+}', function (Request $request, Response $response, array $args) {
        $entry = \Models\FormLog::find($args['log_id']);

        if (
            !$entry ||
            !is_file($this->get('settings')["LMSFormLogsData"] . $entry->form_field_file)
        ) {
            return \APP\Tools::returnCode($request, $response, 404);
        }

        $fileStream = new OpenStream($this->get('settings')["LMSFormLogsData"] . $entry->form_field_file, 'r');
        $finfo = finfo_open(FILEINFO_MIME_TYPE);

        $response = $response
            //->withHeader('Content-Transfer-Encoding', 'Binary')
            ->withHeader('Content-Disposition','attachment;filename="' . $entry->form_field_file . '"')
            //->withHeader('Expires', '0')
            //->withHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            //->withHeader('Pragma', 'public')
            ->withHeader('Content-Length', filesize($filename))
            //->withHeader('Content-Type', FILEINFO_MIME_TYPE)

            //->withHeader('Content-Type', 'application/force-download')
            //->withHeader('Content-Type', 'application/octet-stream')
            //->withHeader('Content-Type', 'application/download')
            //->withHeader('Content-Description', 'File Transfer')
            ->withHeader('Content-Type', finfo_file($finfo, $filename))
            ->withBody($fileStream)
        ;

        return $response;
    })->add(\APP\Auth::getStructureAccessCheck('system-setup-form-logs', 'select'));
});