<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;

// Redirect container to distribution, due to name change
$app->group("/learningcontainer",  function ($group) {
	$group->get('/{token}[/{scorm_data:.*}]', function (Request $request, Response $response, array $args) {
		return $response
			->withHeader('Location', $this->get('settings')['LMSUrl'] . 'learningdistribution/' . $args['token'] . '/' . $args['scorm_data'])
			->withStatus(302)
		;
	});
});

/*
	In this case distributor has access to most of functionality here too.
	Right now I more or less hard-coded permission logic for distributor OR anyone with structure access rights.
	TODO: figure out more robost permission system for edge case like this!
*/

$app->group("/learningdistribution",  function ($group) {

	//resets all container tokens, only for setting up new site, dev mode enabled
	$group->get('/reset', function (Request $request, Response $response, $args) {
		\Models\LearningModuleContainer::resetToken();
	})->add(\APP\Auth::getDevelopmentModeCheck());

	// Gets info for specific container, in admin interface
	$group->get('/{container_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if (
			\APP\Auth::checkStructureAccess(['system-setup-learning-distribution'], 'select')
			||
			\APP\Auth::isDistributor()
		) {

			$container = \Models\LearningModuleContainer
				::where('id', $args["container_id"])
				->with(['module' => function($query) {
					$query->select('id','name', 'type_id');
				}])
				->first()
			;

			$response->getBody()->write(json_encode($container));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	$group->put('/disable/{container_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if (
			\APP\Auth::checkStructureAccess(['system-setup-learning-distribution'], 'disable')
			||
			\APP\Auth::isDistributor()
		) {

			$user = \Models\LearningModuleContainer::find($args["container_id"]);
			$user->status = 0;
			$user->save();

			return $response;

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	$group->put('/enable/{container_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if (
			\APP\Auth::checkStructureAccess(['system-setup-learning-distribution'], 'disable')
			||
			\APP\Auth::isDistributor()
		) {

			$user = \Models\LearningModuleContainer::find($args["container_id"]);
			$user->status = 1;
			$user->save();

			return $response;

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	// List all distributions
	$group->post('/list', function (Request $request, Response $response, array $args) {

		// Jackdaw distributor needs to have this permission also, regardless of structure roles.
		if (
			\APP\Auth::checkStructureAccess(['system-setup-learning-distribution'], 'select')
			||
			\APP\Auth::isDistributor()
		) {
			$params = $request->getParsedBody();

			$query = \Models\LearningModuleContainer
				::select(
					'learning_module_containers.id',
					'learning_module_containers.learning_module_id',
					'learning_module_containers.name',
					'learning_module_containers.token',
					'learning_module_containers.access_count',
					'learning_module_containers.status',
					'learning_module_containers.created_by'
				)
				->with(['module' => function($query) {
					$query->select('id', 'name');
				}])
				->with(['createdby' => function($query) {
					$query->select('id', 'fname', 'lname');
				}])
				->join("learning_modules", function ($join) {
					$join
						->on("learning_modules.id", "=", "learning_module_containers.learning_module_id")
					;
				})
			;


			$p = \APP\SmartTable::searchPaginate($params, $query);
			$response->getBody()->write($p->toJson());
			return $response->withHeader('Content-Type', 'application/json');

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	// Create new container from learning resource
	$group->post('/new', function (Request $request, Response $response, array $args) {

		if (
			\APP\Auth::checkStructureAccess(['system-setup-learning-distribution'], 'insert')
			||
			\APP\Auth::isDistributor()
		) {

			$params = $request->getParsedBody();

			//Allow creating containers only if Jackdaw is enabled in config file
			if ($this->get('settings')['licensing']['isJackdawCloud'] && isset($params['learning_module_id'])) {
				$module = \Models\LearningModule
					::find($params['learning_module_id'])
				;

				// If given module type is e-learning, proceed
				if ($module && $module->type_id == 1) {
					$token = isset($params['token']) ? $params['token'] : bin2hex(random_bytes(14));
					$name = isset($params['name']) ? $params['name'] : $module->name;
					// check if token already exists

					$container = \Models\LearningModuleContainer
						::firstOrNew(
							['token' => $token]
						)
					;
					$container->name = $name;
					$container->learning_module_id = $module->id;
					$container->created_by = \APP\Auth::getUserId();

					if ($container->exists) {
						return \APP\Tools::returnCode($request, $response, 403);
					} else {
						$container->save();
					}

				} else {
					return \APP\Tools::returnCode($request, $response, 403);
				}
			} else {
				return \APP\Tools::returnCode($request, $response, 403);
			}

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	// Update existing container record
	$group->put('/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		if (
			\APP\Auth::checkStructureAccess(['system-setup-learning-distribution'], 'update')
			||
			\APP\Auth::isDistributor()
		) {

			$data = $request->getParsedBody();
			$container = \Models\LearningModuleContainer::find($args["id"]);

			$container->name = $data["name"];
			$container->save();

			return $response;

		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());

	// Serve prepared ZIP file/container.
	$group->get('/download/{container_id:[0-9]+}', function (Request $request, Response $response, $args) {

		$container = \Models\LearningModuleContainer
			::where('id', $args["container_id"])
			->with(['module' => function($query) {
				$query->select('id','name', 'description', 'type_id');
			}])
			->first()
		;

		if (
			$container && (
				\APP\Auth::checkStructureAccess(['system-setup-learning-distribution'], 'select')
				||
				\APP\Auth::isDistributor()
				||
				\APP\Auth::isJackdawOf($container->module->id)
			)
		) {

			if ($container) {
				// Copy "container_template" to "container", modify some variables, zip it up, serve and remove temporaty directory from "container".

				$from = $this->get('settings')['LMSPrivatePath'] . "container_template";
				$to = $this->get('settings')['LMSPrivatePath'] . "containers/temp";


				if (is_dir($to)) {
					// remove temp directory, if it was left behind from some other job.
					\APP\Tools::delDirTree($to);
				}

				mkdir($to, 0775);

				if (is_dir($from) && is_dir($to)) {
					\APP\Tools::recurseCopy($from, $to);

					// Make images directory and include thumb/promo if exists
					mkdir ($to . '/images', 0775);
					$promo =  $this->get('settings')["LMSScormDataPath"] . "/" . $container->module->id . "/moddata/scorm/1/images/promo.jpg";
					if (is_file($promo)) {
						copy($promo, $to . '/images/' . 'promo.jpg');
					}
					$thumb =  $this->get('settings')["LMSScormDataPath"] . "/" . $container->module->id . "/moddata/scorm/1/images/thumb.jpg";
					if (is_file($thumb)) {
						copy($thumb, $to . '/images/' . 'thumb.jpg');
					}

					//open training.htm file, change %%SCORM_TOKEN%% and %%CONTAINER_URL%%
					$training = file_get_contents($to . "/training.htm");
					$training = str_replace("%%SCORM_TOKEN%%", $container->token, $training);
					$training = str_replace("%%CONTAINER_URL%%", $this->get('settings')['LMSUrl'], $training);
					file_put_contents($to . "/training.htm", $training);

					$imsmanifest = file_get_contents($to . "/imsmanifest.xml");
					$imsmanifest = str_replace("%%LEARNING_NAME%%", $container->module->name, $imsmanifest);
					$imsmanifest = str_replace("%%LEARNING_NAME_SAFE%%", \APP\Tools::safeName($container->module->name), $imsmanifest);
					$imsmanifest = str_replace("%%LEARNING_DESCRIPTION%%", $container->module->description, $imsmanifest);
					file_put_contents($to . "/imsmanifest.xml", $imsmanifest);

					$zip = new ZipArchive();
					$filename = $this->get('settings')["LMSPrivatePath"] . "containers/" . \APP\Tools::safeName(($container->name ? $container->name : $container->module->name), "_") . ".zip";
					if ($zip->open($filename, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
						exit("cannot create <$filename>\n");
					}

					$files = new \RecursiveIteratorIterator(
						new \RecursiveDirectoryIterator($to),
						\RecursiveIteratorIterator::LEAVES_ONLY
					);

					foreach ($files as $name => $file) {
						// Skip directories (they would be added automatically)
						if (!$file->isDir()) {
							// Get real and relative path for current file
							$filePath = $file->getRealPath();
							$relativePath = substr($filePath, strlen($to) + 1);
							// Add current file to archive
							$zip->addFile($filePath, $relativePath);
						}
					}

					$zip->close();

					\APP\Tools::delDirTree($to);

					// Prepare the response to download the file
					$response = $response->withHeader('Content-Type', 'application/zip');
					$response = $response->withHeader('Content-Disposition', 'attachment; filename="' . basename($filename) . '"');
					$response->getBody()->write(file_get_contents($filename));

					// Delete the file after sending
					register_shutdown_function(function() use ($filename) {
						unlink($filename);
					});

					return $response;

				}
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403);
		}

	})->add(\APP\Auth::getSessionCheck());


	// If API is enabled, play SCORM if token is valid!
	$group->get('/{token}[/{scorm_data:.*}]', function (Request $request, Response $response, array $args) {
		if (\APP\Tools::getConfig('allowSCORMContainerPlayer')) {
			if (\Models\LearningModuleContainer::isTokenValid($args['token'])) {

				// Log out normal user!
				\APP\Auth::logout();

				// Set some settings in session to be passed into play_scorm, like this is only for playing container, don't use/retrieve anything from user.

				$container = \Models\LearningModuleContainer
					::where('token', $args['token'])
					->with(['module' => function($query) {
						$query->select('id','name', 'type_id');
					}])
					->with(['createdby' => function($query) {
						$query
							->select('id','fname', 'role_id')
							->with('role')
						;
					}])
					->first()
				;
				$container->access_count = $container->access_count + 1;
				$container->save();

				// Check if container was created by free user, if so, check how many screens there is. If more than 15 screens, return an error.
				// "The Free Version of only allows the downloading of courses with 15 screens or fewer. Contact Open eLMS or visit the website for details on pricing else reduce the size of the e-learning."

				if (
					$container->createdby->role->jackdaw_type &&
					$container->createdby->role->jackdaw_type == 'Free'
				) {
					// check if resource has more than 15 screens, if they do, respond with error
					$xml = simplexml_load_file($this->get('settings')["LMSScormDataPath"] . $container->learning_module_id . "/moddata/scorm/1/xml/information.xml");
					$screens = 0;
					foreach ($xml as $key => $screen) {
						if ($key == 'screen') {
							$screens++;
						}
					}

					if ($screens > 15) {
						return \APP\Tools::returnCode($request, $response, 403, 'The Free Version only allows the downloading of courses with 15 screens or fewer. Contact Open eLMS or visit the website for details on pricing else reduce the size of the e-learning.');
					}
				}

				// Set token into session, for checking it later
				\APP\Auth::setScormPlayer($args['token'], $args['scorm_data']);

				// Set learner's SCORM scorm_data in session!

				$session_id_url = '?is_container=1&session_id=' . session_id();

				$response->getBody()->write('
					<!DOCTYPE html>
					<html style="height:100%;">
						<body style="height:100%; margin:0; font-size: 0;">
							<iframe src="' . $this->get('settings')["LMSUri"] . 'scorm/play_scorm.php' . $session_id_url . '" frameborder="0" style="height: 600px; width: 100%;"></iframe>
						</body>
					</html>
				');
				return $response;
			} else {
				return \APP\Tools::returnCode($request, $response, 403, 'The course is not running because the e-learning has been incorrectly configured at source. Contact your system administrator to rectify the problem..');
			}
		} else {
			return \APP\Tools::returnCode($request, $response, 403, 'The course is not running because the e-learning has been incorrectly configured at source. Contact your system administrator to rectify the problem.');
		}
	});
});
