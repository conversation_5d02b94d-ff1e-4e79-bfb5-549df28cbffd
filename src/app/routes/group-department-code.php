<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/group-department-code",  function ($group) {

	$group->get("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$entry = \Models\LearningModuleGroupDepartmentCode::find($args["id"]);

		$response
			->getBody()
			->write(json_encode($entry))
		;
		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-delivery-provider-type', 'select'));


	$group->post("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$entry = \Models\LearningModuleGroupDepartmentCode::find($args["id"]);
		$data = $request->getParsedBody();

		if (isset($data["name"])) {
			$entry->name = $data["name"];
		}
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-delivery-provider-type', 'update'));

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\LearningModuleGroupDepartmentCode::find($args["id"]);
		$entry->status = 0;
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-delivery-provider-type', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\LearningModuleGroupDepartmentCode::find($args["id"]);
		$entry->status = 1;
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-delivery-provider-type', 'disable'));

	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$query = [];
		if (\APP\Auth::isAdminInterface()) {
			$query = \Models\LearningModuleGroupDepartmentCode
				::where("status", true)
				->get()
			;
		}

		$response
			->getBody()
			->write(json_encode($query))
		;
		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-delivery-provider-type', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$entry = new \Models\LearningModuleGroupDepartmentCode;

		if (isset($data["name"])) {
			$entry->name = $data["name"];
		}
		if (\APP\Auth::getUserId()) {
			$entry->created_by = \APP\Auth::getUserId();
		}
		$entry->status = 1;
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-delivery-provider-type', 'insert'));

	// Smart table list
	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();

		$query = \Models\LearningModuleGroupDepartmentCode
			::where('id', '>', 0);


		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\Schedule::countAndConditions($query, $params);

		if (
			isset($args["download"]) &&
			$args["download"] == "/download"
		) {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields = [
				"ID" => "id",
				"Name" => "name",
			];


			$download_file_name = uniqid("delivery-provider-type.list.") . ".xlsx";

			\APP\Tools::generateExcelDownload(
				$data,
				$export_fields,
				$this->get('settings')["LMSTempPath"] . $download_file_name
			);

			$response
				->getBody()
				->write(json_encode($download_file_name))
			;

			return
				$response
					->withHeader('Content-Type', 'application/json')
			;
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query);
		}

		$response
			->getBody()
			->write($p->toJson())
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-delivery-provider-type', 'select'));
});