<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
$app->group("/api-setup",  function ($group) use ($app) {

	// Salesforce
	$group->group("/salesforce", function($group) {

		// Save SOAP credentials
		$group->post('/save-soap', function (Request $request, Response $response, array $args) {
			$data = $request->getParsedBody();

			// amalgamate eveything in "salesforceSoapCredentials", will need to explore proper encryption there, simple is better than none! better is better than simple! best is better than better! lost it here
			// Upload WSDL file
			// Test connection
			// Respond with something!

			if (isset($_FILES['salesforceWsdlFile'])) {
				$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSPrivatePath"] . 'wsdl/', true);
				$bandingIntro = new \Upload\File('salesforceWsdlFile', $storage);
				$bandingIntro->setName('enterprise.wsdl');

				$bandingIntroValidation = new \Upload\Validation\Mimetype(['application/xml']);
				$bandingIntroValidation->setMessage("Invalid file type: '" . $bandingIntro->getMimetype() . "'!");
				$bandingIntro->addValidations([
					$bandingIntroValidation,
					new \Upload\Validation\Size('14M')
				]);
				try {
					$bandingIntro->upload();
				} catch (\Upload\Exception\UploadException $e) {
					$errors = $bandingIntro->getErrors();
					return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
				}
			}

			// Maybe some environment value as salt?
			// Will have to sink about this in future, strongly!
			$salesforce_req_credentials = [
				'salesforceUsername',
				'salesforcePassword',
				'salesforceSecurityToken'
			];

			$salesforceSoapCredentials = unserialize(base64_decode(\APP\Tools::getConfig('salesforceSoapCredentials')));

			foreach ($salesforce_req_credentials as $key => $salesforce_req_credential) {
				if (
					isset($data[$salesforce_req_credential]) &&
					$data[$salesforce_req_credential]
				) {
					$salesforceSoapCredentials[$salesforce_req_credential] = $data[$salesforce_req_credential];
				}
			}
			$salesforceSoapCredentials = base64_encode(serialize($salesforceSoapCredentials));

			\APP\Tools::updateConfig('salesforceSoapCredentials', $salesforceSoapCredentials);

			return $response;

		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-salesforce', 'insert'));

		// Test phase
		$group->get('/test-soap', function (Request $request, Response $response, array $args) {

			$client = \APP\Api::salesforceSoapLogin();
			if ($client) {
				$client = \APP\Api::salesforceSoapLogin();

				$results = $client->query("
					select Label
						from PermissionSet
				");
				$permission_labels = '';
				foreach ($results as $permission) {
					$permission_labels = $permission_labels . "<li>" . $permission->Label . "</li>";
				}

				$response = $response
					->getBody()
					->write("
						User <strong>" . \APP\Api::salesforceSoapLoginUsername() . "</strong> credentials are valid and have access to:<br>
						<ul>
						" . $permission_labels . "
						</ul>
					")
				;
			}

			return $response;

		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-salesforce', 'select'));

		// Remove saved credentials!
		$group->delete('/soap', function (Request $request, Response $response, array $args) {
			\APP\Tools::updateConfig('salesforceSoapCredentials', '');
			if (is_file($this->get('settings')['LMSPrivatePath'] . 'wsdl/enterprise.wsdl.xml')) {
				unlink($this->get('settings')['LMSPrivatePath'] . 'wsdl/enterprise.wsdl.xml');
			}

			return $response;

		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-salesforce', 'disable'));

	});

	// Go1 integration
	$group->group("/go1", function($group) {

		// updates go1clientID
		$group->put('', function (Request $request, Response $response, array $args) {
			$params = $request->getParsedBody();

			$entry = \Models\Configuration
				::where('key', 'go1clientID')
				->first()
			;
			$entry->value = $params['go1clientID'];
			$entry->save();

			return $response;
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'update'));

		// Get client id, authentication status, something else eventually.
		$group->get('', function (Request $request, Response $response, array $args) {

			$data = [
				'go1clientID' => \APP\Tools::getConfig('go1clientID'),
				'authenticated' => \APP\Api::go1CheckCredentials(true),
				'go1InstallResourcesCron' => \APP\Tools::getConfig('go1InstallResourcesCron'),
			];

			$response->getBody()->write(json_encode($data));
			return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'select'));

		// When user hits authenticate, save secret and client ID, set state in session, used for authentication process
		$group->post('/pre-authenticate', function (Request $request, Response $response, array $args) {
			$params = $request->getParsedBody();

			$_SESSION["go_1_state"] = $params['state'];

			\APP\Tools::updateConfig('go1clientSecret', $params['secret']);
			\APP\Tools::updateConfig('go1clientID', $params['client_id']);

			return
				$response
			;
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'select'));

		// while authenticating, go1 redirects back here with access code and state
		$group->get('/authenticate', function (Request $request, Response $response, array $args) {
			$params = $request->getQueryParams();
			if (
				isset($params['code']) &&
				isset($params['state']) &&
				isset($_SESSION["go_1_state"]) &&
				$params['state'] == $_SESSION["go_1_state"]
			) {

				// Next step"! ->  Exchange an Auth token using an Authorization code
				$auth_token = \APP\Tools::getCurlData(
					'https://auth.go1.com/oauth/token',
					['Content-Type: application/x-www-form-urlencoded'],
					[
						'client_id' => \APP\Tools::getConfig('go1clientID'),
						'client_secret' => \APP\Tools::getConfig('go1clientSecret'),
						'grant_type' => 'authorization_code',
						'code' => $params['code'],
						'redirect_uri' => $this->get('settings')["LMSUrl"] . 'api-setup/go1/authenticate',
					]
				);
				$auth_token = json_decode($auth_token);


				// Update configuration values and close popup!
				//token_type
				\APP\Tools::updateConfig('go1AccessTokenType', $auth_token->token_type);

				//expires_in
				\APP\Tools::updateConfig('go1AccessTokenExpiresIn', $auth_token->expires_in);

				//access_token
				\APP\Tools::updateConfig('go1AccessToken', $auth_token->access_token);

				//refresh_token
				\APP\Tools::updateConfig('go1RefreshToken', $auth_token->refresh_token);

				//remove session variables
				unset($_SESSION["go_1_state"]);


				// respond with success and close window
				// go1-auth-success.html

				$vars = [
					"LMSUri" => $this->get('settings')["LMSUri"],
				];
				return $this->get('view')->render($response, 'html/go1-auth-success.html', $vars);
			} else {
				return \APP\Tools::returnCode($request, $response, 401, 'Missing code or mismatch state!');
			}

			return
				$response
			;
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'select'));

		$group->put('/learning-list', function (Request $request, Response $response, array $args) {
			$params = $request->getParsedBody();
			// check if go1 is authenticated, authenticate again if needed and retrieve list of learning material.
			\APP\Api::go1CheckCredentials();

			$parameters = [
				'limit' => 50,
				'offset' => 0
			];

			if (
				isset($params['keyword']) &&
				$params['keyword'] > ''
			) {
				$parameters['keyword'] = $params['keyword'];
			}

			if (
				isset($params['limit']) &&
				$params['limit'] > ''
			) {
				$parameters['limit'] = $params['limit'];
			}

			if (
				isset($params['offset']) &&
				$params['offset'] > ''
			) {
				$parameters['offset'] = $params['offset'];
			}

			// Get learning objects!
			$learning_objects = \APP\Tools::getCurlData(
				'https://api.go1.com/v2/learning-objects?' . http_build_query($parameters),
				['Authorization: ' . \APP\Tools::getConfig('go1AccessTokenType') . ' ' . \APP\Tools::getConfig('go1AccessToken')]
			);

			$learning_objects = json_decode($learning_objects);

			// Loop objects and check if they are already installed
			foreach ($learning_objects->hits as $key => $learning_object) {
				if (
					isset($learning_object->id) &&
					!empty($learning_object->id)
				) {
					$learning_object->installed = (\Models\LearningModule::where('go1_id', $learning_object->id)->exists());
				}
			}

			$response->getBody()->write(json_encode($learning_objects));
			return $response->withHeader('Content-Type', 'application/json');

		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'select'));

		$group->get('/install-cron-toggle', function (Request $request, Response $response, array $args) {
			\APP\Tools::updateConfig('go1InstallResourcesCron', !\APP\Tools::getConfig('go1InstallResourcesCron'));

			return
				$response
			;
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'update'));

		// Delete all entries related to GO1
		$group->get('/de-authenticate', function (Request $request, Response $response, array $args) {

			$auth_token = \APP\Tools::getCurlData(
				'https://auth.go1.com/oauth/revoke',
				['Content-Type: application/x-www-form-urlencoded'],
				[
					'client_id' => \APP\Tools::getConfig('go1clientID'),
					'client_secret' => \APP\Tools::getConfig('go1clientSecret'),
					'token' => \APP\Tools::getConfig('go1AccessToken'),
				]
			);

			\APP\Tools::updateConfig('go1clientSecret', '');
			\APP\Tools::updateConfig('go1clientID', '');
			\APP\Tools::updateConfig('go1AccessTokenType', '');
			\APP\Tools::updateConfig('go1AccessTokenExpiresIn', 0);
			\APP\Tools::updateConfig('go1AccessToken', '');
			\APP\Tools::updateConfig('go1RefreshToken', '');

			return
				$response
			;
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'disable'));

		$group->get('/retrieve-resource/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
			\APP\Api::go1CheckCredentials();

			// Retrieve details about resource
			$resource = \APP\Tools::getCurlData(
				'https://api.go1.com/v2/learning-objects/' . $args['id'],
				['Authorization: ' . \APP\Tools::getConfig('go1AccessTokenType') . ' ' . \APP\Tools::getConfig('go1AccessToken')]
			);

			$response->getBody()->write($resource);
			return $response->withHeader('Content-Type', 'application/json');
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'update'));

		$group->post('/update-resource/', function (Request $request, Response $response, array $args) {
			$params = $request->getParsedBody();

			// Check if resource is installed, if not, fail
			$learning = \Models\LearningModule::where('go1_id', $params['id'])->first();
			if (!$learning) {
				return \APP\Tools::returnCode($request, $response, 409, 'Resource is not installed!');
			}

			\APP\Api::go1CheckCredentials();

			// Download scorm container
			$scorm_zip_content = \APP\Tools::getCurlData(
				//
				'https://api.go1.com/v2/learning-objects/' . $params['id'] . '/scorm',
				['Authorization: ' . \APP\Tools::getConfig('go1AccessTokenType') . ' ' . \APP\Tools::getConfig('go1AccessToken')]
			);

			// Put zip file in temp directopry
			$scorm_file_name = $this->get('settings')['LMSTempPath'] . 'go1_scorm_' . $params['id'] . '.zip';
			if (is_file($scorm_file_name)) {
				unlink($scorm_file_name);
			}
			file_put_contents(
				$scorm_file_name,
				$scorm_zip_content
			);

			// Install resource
			$learning->updated_by = \APP\Auth::getUserId();
			$learning->description = $params['description'];

			// Add keywords/tags
			if (
				isset($params['tags']) &&
				is_array($params['tags'])
			) {
				$learning->keywords = $comma_separated = implode(", ", $params['tags']);
			}

			// Download and put image in LMSThumbPath folder
			if (
				isset($params['image']) &&
				!empty($params['image'])
			) {
				$image_path_info = pathinfo(parse_url($params['image'], PHP_URL_PATH));
				$thumb_path = $this->get('settings')["LMSThumbPath"];
				$thumbnail = $learning->id . '_thumb.' . $image_path_info['extension'];
				file_put_contents($thumb_path . $thumbnail, fopen($params['image'], 'r'));
				$learning->thumbnail = $thumbnail;

				$promo_path = $this->get('settings')["LMSPromoPath"];
				$promo_image = $learning->id . '_promo.' . $image_path_info['extension'];
				file_put_contents($promo_path . $promo_image, fopen($params['image'], 'r'));
				$learning->promo_image = $promo_image;
			}

			$learning->save();

			// Process SCORM file
			$course = \APP\Course::get($learning);

			try {
				$course->deleteScormSetup();
				$course->setupScorm(
					$scorm_file_name,
					$this->get('settings')["LMSScormDataPath"]
				);
				unlink($scorm_file_name);
			} catch(\APP\ScormException $e) {
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", ["Scorm error." . $e->getMessage()]));
			}


			$response->getBody()->write(json_encode($learning));
			return $response->withHeader('Content-Type', 'application/json');
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'insert'));

		$group->post('/install-resource/', function (Request $request, Response $response, array $args) {
			$params = $request->getParsedBody();

			// Check if resource is already installed, if so, fail
			$resouce_check = \Models\LearningModule::where('go1_id', $params['id'])->first();
			if ($resouce_check) {
				return \APP\Tools::returnCode($request, $response, 409, 'Resource already installed!');
			}

			\APP\Api::go1CheckCredentials();

			// Download scorm container
			$scorm_zip_content = \APP\Tools::getCurlData(
				//
				'https://api.go1.com/v2/learning-objects/' . $params['id'] . '/scorm',
				['Authorization: ' . \APP\Tools::getConfig('go1AccessTokenType') . ' ' . \APP\Tools::getConfig('go1AccessToken')]
			);

			// Put zip file in temp directopry
			$scorm_file_name = $this->get('settings')['LMSTempPath'] . 'go1_scorm_' . $params['id'] . '.zip';
			file_put_contents(
				$scorm_file_name,
				$scorm_zip_content
			);

			// Install resource
			$learning = new \Models\LearningModule;
			$learning->name = $params['title'];
			$learning->go1_id = $params['id'];
			$learning->status = 1;
			$learning->created_by = \APP\Auth::getUserId();
			$learning->self_enroll = 0;

			$learning->description = $params['description'];

			$learning->type_id = \Models\LearningModuleType::getId('e_learning');
			//$learning->dasdasda = 123;
			//$learning->dasdasda = 123;

			$material = new \stdClass();
			$material->min_passing_percentage = "70";
			$material->scorm_standard = "2";
			$material->course_complete_status = "1";
			$learning->material = $material;

			//$learning->thumbnail

			// Check into attributes->topics, take first one and use it as category!
			if (isset($params['attributes']['topics'][0]['value'])) {
				$category_name = $params['attributes']['topics'][0]['value'];
				$category = \Models\LearningModuleCategory::firstOrCreate(
					[
						'name' => $category_name
					],
					[
						'status' => true
					]
				);
				$learning->category_id = $category->id;
			}
			// Add keywords/tags
			if (
				isset($params['tags']) &&
				is_array($params['tags'])
			) {
				$learning->keywords = $comma_separated = implode(", ", $params['tags']);
			}

			$learning->save();

			// Download and put image in LMSThumbPath folder
			if (
				isset($params['image']) &&
				!empty($params['image'])
			) {
				$image_path_info = pathinfo(parse_url($params['image'], PHP_URL_PATH));

				$thumb_path = $this->get('settings')["LMSThumbPath"];
				$thumbnail = $learning->id . '_thumb.' . $image_path_info['extension'];
				file_put_contents($thumb_path . $thumbnail, fopen($params['image'], 'r'));
				$learning->thumbnail = $thumbnail;

				$promo_path = $this->get('settings')["LMSPromoPath"];
				$promo_image = $learning->id . '_promo.' . $image_path_info['extension'];
				file_put_contents($promo_path . $promo_image, fopen($params['image'], 'r'));
				$learning->promo_image = $promo_image;

				$learning->save();
			}

			// Process SCORM file
			$course = \APP\Course::get($learning);

			try {
				$course->deleteScormSetup();
				$course->setupScorm(
					$scorm_file_name,
					$this->get('settings')["LMSScormDataPath"]
				);
				unlink($scorm_file_name);
			} catch(\APP\ScormException $e) {
				$course->deleteScormSetup();
				$learning->delete();
				unlink($thumb_path . $thumbnail);
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", ["Scorm error." . $e->getMessage()]));
			}

			$response->getBody()->write(json_encode($learning));
			return $response->withHeader('Content-Type', 'application/json');
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'insert'));

		$group->get('/enrollments', function (Request $request, Response $response, array $args) {
			\APP\Api::go1CheckCredentials();

			$enrollments = \APP\Tools::getCurlData(
				'https://api.go1.com/v2/enrollments',
				['Authorization: ' . \APP\Tools::getConfig('go1AccessTokenType') . ' ' . \APP\Tools::getConfig('go1AccessToken')]
			);

			$response->getBody()->write($enrollments);
			return $response->withHeader('Content-Type', 'application/json');
		})->add(\APP\Auth::getStructureAccessCheck('system-setup-api-go1', 'select'));

	});
});
