<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/quizanalysis",  function ($group)
{
    $group->get("/get-quiz/{id:[0-9]+}", function (Request $request, Response $response, array $args)
	{
        // $data = $request->getParsedBody();
        $module = Models\LearningModule::
            select('id', 'name', DB::raw('jackdaw OR jackdaw_resource as creator_resource'))
            // ->where('status', 1)
            ->where('id', $args['id'])
            ->first()
        ;

        $quizXML = null;
        if ($module && $module->creator_resource)
        {
            $quizXML = APP\Jackdaw::getQuizXML($args['id'], $this->get('settings')["LMSPublicPath"]);
        }
        if ($quizXML == null) return $response->withStatus(404)->withHeader('Content-Type', 'application/json')->write('{"error":"Quiz files not found", "name":"'.$module->name.'"}');

        if (isset($quizXML->Quiz) && isset($quizXML->Quiz->Question))
            foreach ($quizXML->Quiz->Question as $key => $Question)
                if (isset($Question->Answers) && isset($Question->Answers->Answer))
                    foreach ($Question->Answers->Answer as $key => $Answer)
                        $Answer->crc32 = crc32($Answer->Text);

        $subQuery = Models\Scorm\Track::
            select('userid', 'scormid', 'element', 'timemodified', DB::raw('MAX(attempt) as max_attempt'))
            ->whereIn('element', ['cmi.core.lesson_status', 'cmi.success_status', 'cmi.suspend_data', 'cmi.core.total_time', 'cmi.core.score.raw', 'cmi.core.score.max', 'cmi.core.score.min', 'x.start.time'])
            ->where('scormid', $args['id'])
            ->where('userid', '!=', 0)
            ->groupBy('userid', 'scormid', 'element')
        ;

        $tracks = Models\Scorm\Track::
            select('scorm_scorm_scoes_track.id', 'scorm_scorm_scoes_track.userid', 'scorm_scorm_scoes_track.scormid', 'scorm_scorm_scoes_track.attempt', 'scorm_scorm_scoes_track.element', 'scorm_scorm_scoes_track.value', 'scorm_scorm_scoes_track.timemodified')
            ->with('User')
            ->joinSub($subQuery, 'max_attempts', function($join) {
                $join->on('scorm_scorm_scoes_track.scormid', '=', 'max_attempts.scormid')
                    ->on('scorm_scorm_scoes_track.userid', '=', 'max_attempts.userid')
                    ->on('scorm_scorm_scoes_track.element', '=', 'max_attempts.element')
                    ->on('scorm_scorm_scoes_track.attempt', '=', 'max_attempts.max_attempt');
            })
            ->get()
        ;

        $userData = [];

        foreach ($tracks as $key => $track)
        {
            if (!isset($userData[$track->userid])) $userData[$track->userid] = [
                'user_id' => $track->userid,
                'user_name' => $track->user->fname." ".$track->user->lname,
                'user_image' => $track->user->image,
                'status' => 'incomplete',
                'score_min' => 0,
                'score_max' => 100,
                'score_raw' => 0,
                'attempt' => $track->attempt,
                'start_time' => $track->timemodified,
                'total_time' => 0,
                'suspend_data' => null,
            ];
            switch ($track->element)
            {
                case 'cmi.success_status':
                case 'cmi.core.lesson_status':
                    $userData[$track->userid]['status'] = $track->value;
                    break;
                case 'cmi.suspend_data':
                    $object = json_decode(stripslashes($track->value));
                    if (isset($object->cmi->interactions))
                    {
                        $prevLatency = 0;
                        foreach ($object->cmi->interactions as $key => $int)
                        {
                            $int->student_response = urldecode($int->student_response);
                            preg_match('/\[(.*?)\]/', $int->student_response, $matches);
                            if (count($matches) > 1)
                            {
                                $parsedDate = date_parse_from_format('H:i d/m/Y', $matches[1]);
                                $int->time = mktime($parsedDate['hour'], $parsedDate['minute'], $parsedDate['second'], $parsedDate['month'], $parsedDate['day'], $parsedDate['year']);
                            }
                            $int->student_response = preg_replace('/^\[\d+:\d+ \d+\/\d+\/\d{4}\]/', '', $int->student_response);
                            preg_match('/(?<=@COMMENT:).*/', $int->student_response, $comment);
                            if (isset($comment[0]))
                            {
                                $int->comment = $comment[0];
                                preg_match('/^.*?(?=@COMMENT|$)/', $int->student_response, $resonse_text);
                                $int->student_response = $resonse_text[0];
                            }

                            if ((int)explode(',', $int->student_response)[0] > 0)
                            {
                                $int->student_response = explode(',', $int->student_response);
                                $int->student_response = array_map(function($value) { return intval($value); }, $int->student_response);
                            }
                            else if ($int->student_response == 't') $int->student_response = 'True';
                            else if ($int->student_response == 'f') $int->student_response = 'False';

                            if (isset($int->latency))
                            {
                                list($hours, $minutes, $seconds) = explode(":", $int->latency);
                                list($seconds, $milliseconds) = explode(".", $seconds);
                                $originalLatency = (intval($hours) * 3600) + (intval($minutes) * 60) + intval($seconds) + (intval($milliseconds) * .1);
                                $newLatency = $originalLatency - $prevLatency;
                                $prevLatency = $originalLatency;
                                $int->latency = (float)number_format((float)$newLatency, 2, '.', '');
                            }
                        }
                    }
                    $userData[$track->userid]['suspend_data'] = $object;
                    break;
                case 'cmi.core.score.min':
                    $userData[$track->userid]['score_min'] = (int)$track->value;
                    break;
                case 'cmi.core.score.max':
                    $userData[$track->userid]['score_max'] = (int)$track->value;
                    break;
                case 'cmi.core.score.raw':
                    $userData[$track->userid]['score_raw'] = (int)$track->value;
                    break;
                case 'cmi.core.total_time':
                    $userData[$track->userid]['total_time'] = $track->value;
                    break;
            }
            if ($userData[$track->userid]['start_time'] > $track->timemodified) $userData[$track->userid]['start_time'] = $track->timemodified;
        }

        $getScoresFromDB = [];
        foreach ($userData as $key => $user) if( $user['score_raw'] == 0 && $user['status'] == 'passed') array_push($getScoresFromDB, $key);

        $scores = Models\LearningResult::
            select('user_id', 'learning_module_id', 'score')
            ->groupBy('user_id', 'learning_module_id')
            ->whereIn('user_id', $getScoresFromDB)
            ->where('learning_module_id', $args['id'])
            ->get()
        ;

        foreach ($scores as $key => $score) $userData[$score->user_id]['score_raw'] = (int)$score->score;

        $result = [
            'id' => $args['id'],
            'name' => $module->name,
            'quizData' => isset($quizXML->Quiz) ? $quizXML->Quiz : null,
            'userData' => array_values($userData)
        ];

		$response->getBody()->write(json_encode($result));
		return $response->withHeader('Content-Type', 'application/json');

        // return $response->withHeader('Content-Type', 'application/json')->write(json_encode($getScoresFromDB));
        // return $response->withHeader('Content-Type', 'application/json')->write($tracks->toJson());

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get("/list-resources", function (Request $request, Response $response, array $args)
	{
        $subQuery = Models\Scorm\Track::
            select('userid', 'scormid', 'element', 'timemodified', DB::raw('MAX(attempt) as max_attempt'))
            ->whereIn('element', ['cmi.core.lesson_status', 'cmi.success_status'])
            ->groupBy('userid', 'scormid', 'element')
        ;

        $tracks = Models\Scorm\Track::
            select('scorm_scorm_scoes_track.id', 'scorm_scorm_scoes_track.userid', 'scorm_scorm_scoes_track.scormid', 'scorm_scorm_scoes_track.attempt', 'scorm_scorm_scoes_track.element', 'scorm_scorm_scoes_track.value', 'scorm_scorm_scoes_track.timemodified')
            ->joinSub($subQuery, 'max_attempts', function($join) {
                $join->on('scorm_scorm_scoes_track.scormid', '=', 'max_attempts.scormid')
                    ->on('scorm_scorm_scoes_track.userid', '=', 'max_attempts.userid')
                    ->on('scorm_scorm_scoes_track.element', '=', 'max_attempts.element')
                    ->on('scorm_scorm_scoes_track.attempt', '=', 'max_attempts.max_attempt');
            })
        ;

        $uniqueScormIds = $tracks->pluck('scormid')->unique();

        $tracks = $tracks->get();

        $scormList = Models\LearningModule::
            select('learning_modules.id',
                    'learning_modules.name',
                    'learning_modules.thumbnail',
                    DB::raw('learning_modules.jackdaw OR learning_modules.jackdaw_resource as creator_resource'),
                    'learning_module_categories.id as category_id',
                    'learning_module_categories.name as category_name',
                    'learning_module_types.id as type_id',
                    'learning_module_types.name as type_name',
                    'learning_module_types.slug as type_slug'
                )
            ->where('learning_modules.status', 1)    // hard to tell, should we filter out disabled learning resources or show analytics on all avaiable results
            ->where('is_skillscan', false)
            ->where('is_survey', false)
            ->leftJoin('learning_module_types', 'learning_module_types.id', '=', 'learning_modules.type_id')
            ->leftJoin('learning_module_categories', 'learning_module_categories.id', '=', 'learning_modules.category_id')
            ->whereIn('learning_modules.id', $uniqueScormIds)
            ->orderBy('name', 'asc')
            ->get()
        ;

        $scormData = [];

        foreach ($tracks as $key => $track)
        {
            if (!isset($scormData[$track->scormid])) $scormData[$track->scormid] = [];
            if (!isset($scormData[$track->scormid]['attempts'])) $scormData[$track->scormid]['attempts'] = 0;
            if (!isset($scormData[$track->scormid]['standard'])) $scormData[$track->scormid]['standard'] = '1.2';
            if (!isset($scormData[$track->scormid]['passed'])) $scormData[$track->scormid]['passed'] = 0;
            if (!isset($scormData[$track->scormid]['failed'])) $scormData[$track->scormid]['failed'] = 0;
            if (!isset($scormData[$track->scormid]['total'])) $scormData[$track->scormid]['total'] = 0;
            if (!isset($scormData[$track->scormid]['timecode'])) $scormData[$track->scormid]['timecode'] = 0;
            $scormData[$track->scormid]['attempts'] += $track->attempt;
            $scormData[$track->scormid]['standard'] = $track->element == 'cmi.core.lesson_status' ? '1.2' : '2004';
            $scormData[$track->scormid]['passed'] += $track->value == 'passed' ? 1 : 0;
            $scormData[$track->scormid]['failed'] += $track->value == 'failed' ? 1 : 0;
            $scormData[$track->scormid]['total'] += 1;
            if ($scormData[$track->scormid]['timecode'] < $track->timemodified) $scormData[$track->scormid]['timecode'] = $track->timemodified;
        }

        foreach ($scormList as $key => $resource)
        {
            $resource->attempts = $scormData[$resource->id]['attempts'];
            $resource->standard = $scormData[$resource->id]['standard'];
            $resource->passed = $scormData[$resource->id]['passed'];
            $resource->failed = $scormData[$resource->id]['failed'];
            $resource->total = $scormData[$resource->id]['total'];
            $resource->timecode = $scormData[$resource->id]['timecode'];
        }

		$response->getBody()->write($scormList->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));
});
