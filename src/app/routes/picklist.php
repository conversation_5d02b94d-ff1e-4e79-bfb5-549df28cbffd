<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/picklist",  function ($group) {

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\Picklist::find($args["id"]);

		$response->getBody()->write(json_encode($entry));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	$group->get('/{type}/all', function (Request $request, Response $response, $args) {
		session_write_close();
		$data = \Models\Picklist
			::where("type", $args['type'])
			->where('status', true)
		;

		if (!\APP\Auth::checkSession()) {
			$data = $data
				->where('public', true)
			;
		}

		$data = $data
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

	});

});