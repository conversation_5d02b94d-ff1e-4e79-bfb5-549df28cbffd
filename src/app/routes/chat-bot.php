<?php

use APP\Controllers\ChatBotController;
$app->group("/chat-bot", function ($group){
  $group->post('/get-response', ChatBotController::class.':getChatResponse')->add(\APP\Auth::getSessionCheck());
  $group->post('/stream-response', ChatBotController::class.':getChatResponse')->add(\APP\Auth::getSessionCheck());
  $group->post('/add-data', ChatBotController::class.':addDataToGoogleCloud')->add(\APP\Auth::getSessionCheck());
  $group->post('/delete-data', ChatBotController::class . ':deleteModuleFromGoogleCloud')->add(\APP\Auth::getSessionCheck());
});
