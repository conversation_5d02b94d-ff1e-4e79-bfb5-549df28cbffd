<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/trainingimpactreports",  function ($group) {

	$group->post("/print", function (Request $request, Response $response) {

		$params = $request->getParsedBody();

		return $this->get('view')->render($response, 'html\printtrainingimpactreport.html', [
			"LMSUrl" => $this->get('settings')["LMSUrl"],
			"LMSUri" => $this->get('settings')["LMSUri"],
			"LMSAppUri" => $this->get('settings')["LMSAppUri"],
			"LMSTplsUri" => $this->get('settings')["LMSTplsUri"],
			"LMSTplsUriHTML" => $this->get('settings')["LMSTplsUriHTML"],
			"SearchData" => json_encode($params["searchData"], JSON_FORCE_OBJECT),
			"SortData" => json_encode($params["sortData"], JSON_FORCE_OBJECT),
		]);

	})->add(\APP\Auth::getStructureAccessCheck('review-learning-resources-data', 'select'));

	$group->post("/print/aggregate/{field:[a-zA-Z]+}", function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();


		$user_fields = [
			"company" => "company_id",
			"department" => "department_id",
			"country" => "country_id",
			"city" => "city_id",
			"location" => "location_id",
			"designation" => "designation_id",
			"group",
			"competency" => "competency_id",
			"learningmodule" => "learning_module_id",
		];

		$data = [];
		$data["all"] = [
			"result_before" => 0,
			"result_after" => 0,
			"label" => "All",
		];

		if (in_array($args["field"], array_keys($user_fields))) {
			$additional_group = false;

			if (in_array($args["field"], ["competency", "learningmodule"])) {
				$additional_group = $args["field"];
			}

			$additional_search_params = ["display_as" => 0];
			$query_id = 'trainingimpactreportsList';
			$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
			$query = $QueryBuilder::generate($params, $args, $additional_search_params, $additional_group);
			$query = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$rows = $query->get();

			foreach($rows as $row) {
				$groups = [];
				$group_objects[] = [];

				if ($args["field"] != "group") {
					$user_field_ids = [intval($row->$user_fields[$args["field"]])];
				} else {
					$groups = $row->groups;
					foreach($groups as $group) {
						$user_field_ids[] = intval($group->id);
						$group_objects[intval($group->id)	] = $group;
					}
				} foreach($user_field_ids as $user_field_id) {
					if (!isset($data[$user_field_id])) {
						$data[$user_field_id] = [
								"result_before" => 0,
								"result_after" => 0,
						];
						$user_field_object = null;


						switch($args["field"]) {
							case "company":
								$user_field_object = $row->company;
								break;
							case "department":
								$user_field_object = $row->department;
								break;
							case "country":
								$user_field_object = $row->country;
								break;
							case "city":
								$user_field_object = $row->city;
								break;
							case "location":
								$user_field_object = $row->location;
								break;
							case "designation":
								$user_field_object = $row->designation;
								break;
							case "group":
								$user_field_object = $group_objects[$user_field_id];
								break;
							case "competency":
								$user_field_object = (object) ["name" => $row->competency_name];
								break;
							case "learningmodule":
								$user_field_object = (object) ["name" => $row->learning_module_name];
								break;
						}

					}

					if (!isset($data[$user_field_id]["label"])) {
						$data[$user_field_id]["label"] = $user_field_object == null ? "Not Set" : $user_field_object->name;
					}
					$data[$user_field_id]["result_before"] += $row->result_before;
					$data[$user_field_id]["result_after"] += $row->result_after;
					$data["all"]["result_before"] += $row->result_before;
					$data["all"]["result_after"] += $row->result_after;
				}
			}
		}

		$filtered_data = [];
		foreach($data as $id => $record) {
			if (
					($record["result_before"] != 0)
					||
					($record["result_after"] != 0)
			) {
				$filtered_data[$id] = $record;
			}
		}

		$response->getBody()->write(json_encode($filtered_data));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('review-learning-resources-data', 'select'));


	$group->post('/list{option:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$additional_search_params = ["display_as" => 0];
		$query_id = 'trainingimpactreportsList';
		$params = $request->getParsedBody();
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args, $additional_search_params);

		switch($args["option"]) {
			case "/download":
				$query
					->selectRaw("CONCAT(users.fname, ' ', users.lname) as trainee_name")
				;
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				if ($additional_search_params["display_as"] == 0) {
					$results_name = "CPD points";
				} 	else {
					$results_name = "Competency passes";
				}

				$export_fields = [
					"ID" => "id",
					"Employee Name" => "trainee_name",
					"Company Name" => "company.name",
					"Department Name" => "department.name",
					"$results_name at training start" => "result_before",
					"$results_name at training end" => "result_after",
					"Difference" => "difference",
				];


				$download_file_name = uniqid("training_impact.report.") . ".xlsx";

				\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				);

				$response->getBody()->write(json_encode($download_file_name));
				return $response->withHeader('Content-Type', 'application/json');
				break;
			case "/email":
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				return \APP\Email::getRecipients (
					$response, // response object used to return modified response
					$data, // results from query
					$params, // filtered/modified parameters
					$request->getParsedBody(), // original parameters
					$this->get('settings')["email"], // email settings from config file
					$query_id, // ID that will be used to generate query
					'training impact reports tpl', // Template name
					['id'], // where to look for user's ID in this query
					$args
				);
				break;
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);



		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('review-learning-resources-data', 'select'));

});