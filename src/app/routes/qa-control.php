<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\LearningResult;
use Models\QualityControl;

$app->group("/qa_control", function ($group) {
    $group->post("/get_qa", function (Request $request, Response $response, array $args) {
        $data = $request->getParsedBody();
        $type = $data['type'];
        $type_id = $data['type_id'];
        $user_id = $data['user_id'];
        $qa = \Models\QualityControl
            ::where([['type','=',$type],['type_id','=',$type_id],['user_id','=',$user_id]])
            ->with(['qalogs'=>function($query) use ($type) {
                $query
                    ->select('*',DB::raw("DATE_FORMAT(created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"))
                    ->with(["qaUser" => function ($query) {
                        $query->select('id', 'fname', 'lname');
                    }])
                    ->where('quality_controls.type','=','feedback')
                    ->orWhere('quality_controls.type','=',$type)
                    ->orderBy('created_at','desc')
                    ->get()
                ;
            }])
            ->first()
        ;

        if($qa){  $response
            ->withHeader('Content-Type', 'application/json')
            ->write(json_encode($qa));
        }else{
            $response;
        }

    })->add(\APP\Auth::getSessionCheck());

    $group->post("/get_qa_for_manager", function (Request $request, Response $response, array $args) {
        $data = $request->getParsedBody();
        $type = $data['type'];
        $type_id = $data['type_id'];
        $user_id = $data['user_id'];
        $qa = \Models\QualityControl
            ::where([['type_id','=',$type_id],['user_id','=',$user_id]])
            ->whereIn('type',['feedback', $type])
            ->with(['qalogs'=>function($query) use ($type) {
                $query
                    ->select('*',DB::raw("DATE_FORMAT(created_at,'%d/%m/%Y') AS created_at_uk"))
                    ->with(["qaUser" => function ($query) {
                        $query->select('id', 'fname', 'lname');
                    }])
                    ->where('quality_controls.type','=','feedback')
                    ->orWhere('quality_controls.type','=',$type)
                    ->orderBy('created_at','desc')
                    ->get()
                ;
            }])
            ->first()
        ;
        if($qa){
            $response->getBody()->write(json_encode($qa));
            return $response->withHeader('Content-Type', 'application/json');
        }else{
            $response->getBody()->write(json_encode(["message" => "No data found"]));
            return $response->withHeader('Content-Type', 'application/json');
        }

    })->add(\APP\Auth::getSessionCheck());

    $group->delete("/{id:[0-9]+}" , function (Request $request, Response $response, $args) {
        \Models\QualityControl
                ::where('id', '=', $args['id'])
                ->delete()
            ;
    })->add(\APP\Auth::getSessionCheck());

    $group->post("/save", function (Request $request, Response $response, array $args) {
        $data = $request->getParsedBody();
        $type = $data['type'];
        // $type_id = $args['type_id'];
        $user_id = $data['user_id'];
        /*todo: No other conditional check is performed*/

        $quality_control_ex = \Models\QualityControl::where(
            [
                [ 'type',$data['type']],
                [ 'type_id', $data['type_id']],
                [ 'user_id', $data['user_id']],
                [ 'is_new', true],
            ]
        )->first();
        if($quality_control_ex){
            $quality_control_ex->is_new = false;
            $quality_control_ex->save();
        }


        $quality_control = new \Models\QualityControl;
        $quality_control->type = $data['type'];
        $quality_control->type_id = $data['type_id'];
        $quality_control->user_id = $data['user_id'];
        $quality_control->qa = $data['qa'];
        $quality_control->qa_favorite = isset($data['qa_favorite'])?$data['qa_favorite']:false;
        $quality_control->qa_user_id = \APP\Auth::getUserId();
        $quality_control->is_new = true;
        $quality_control->judgement_reason = $data['judgement_reason'];
        $quality_control->save();





        $qa_type_obj =  $quality_control->qualityControlReportView;

        // if rejected, set status to "In progress"
                if ($quality_control->qa == 'Rejected') {
                    $standard_user = \Models\ApprenticeshipStandardUser::
                    where([['user_id', $qa_type_obj->user_id],['standard_id', $qa_type_obj->standard_id]])->first();

                   if($standard_user){
                    $standard_user->completion_status = "in progress";
                    $standard_user->save();
                   }
                    // Send e-mail to learners managers that this was rejected!
                    // "Work rejected by QA"
                    //  if($quality_control){
                        $template = \Models\EmailTemplate
                            ::where('name', 'Actions required following Quality Review')
                            ->where('status', true)
                            ->first();
                        if (
                            $template &&
                            $template->id
                        ) {

                            // Get all managers that needs notification
                            $manager_ids = [];
                            foreach ($quality_control->user->managers as $key => $manager) {
                                if (
                                    !$manager->role->email_disable_manager_notifications &&
                                    $manager->status
                                ) {
                                    $manager_ids[] = $manager->id;
                                }
                            }

                            if (
                                count($manager_ids) > 0
                            ) {
                                $email_queue = new \Models\EmailQueue;
                                $email_queue->email_template_id = $template->id;
                                $email_queue->recipients = $manager_ids;
                                $email_queue->from = \APP\Auth::getUserId();
                                $email_queue->custom_variables = json_encode([
                                    'REJECTED_WORK' => $quality_control->qualityControlReportView->type_name,
                                    'REJECTED_LEARNER' => $quality_control->user->fname . ' ' . $quality_control->user->lname,
                                    'REJECTED_WORK_ID' => $quality_control->qualityControlReportView->type_id,
                                ]);
                                $email_queue->save();
                            }
                        }
                    // }
                }

//        $quality_controlObj =  \Models\QualityControl::where('id','=',$quality_control->id)->get();
        $quality_controlObj =  \Models\QualityControl::find($quality_control->id);
        $response
            ->withHeader('Content-Type', 'application/json')
            ->write(json_encode($quality_controlObj));
    })->add(\APP\Auth::getSessionCheck());

    $group->post("/savefeedback", function (Request $request, Response $response, array $args) {
        $data = $request->getParsedBody();
        // $type = $data['type'];
        // $type_id = $args['type_id'];
        // $user_id = $data['user_id'];



        $quality_control = new \Models\QualityControl;
        $quality_control->type = $data['type'];
        $quality_control->type_id = $data['type_id'];
        $quality_control->user_id = $data['user_id'];
        $quality_control->qa = 'Rejected';
        $quality_control->qa_favorite = isset($data['qa_favorite'])?$data['qa_favorite']:false;
        $quality_control->qa_user_id = \APP\Auth::getUserId();
        $quality_control->is_new = true;
        $quality_control->judgement_reason = $data['judgement_feedback'];
        $quality_control->updated_at = \Carbon\Carbon::now();
        $quality_control->save();

        $quality_controlObj =  \Models\QualityControl::
        with(["qaUser" => function ($query) {
            $query->select('id', 'fname', 'lname');
        }])
        ->find($quality_control->id);

        $qa = \Models\QualityControl::
        with(["qaUser" => function ($query) {
            $query->select('id', 'fname', 'lname');
        }])
        ->select('*', DB::raw("DATE_FORMAT(quality_controls.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS qa_created_at"))
        ->where([['type','=', 'feedback'],['type_id','=',$data['type_id']]])->get();

        $template = \Models\EmailTemplate
        ::where('name', 'QA judgement has been responded')
        ->where('status', true)
        ->first();
        if (
            $template &&
            $template->id
        ) {

            // // Get all managers that needs notification
            // $manager_ids = [];
            // foreach ($quality_control->user->managers as $key => $manager) {
            //     if (
            //         !$manager->role->email_disable_manager_notifications &&
            //         $manager->status
            //     ) {
            //         $manager_ids[] = $manager->id;
            //     }
            // }
            // Get the user corresponds to this feedback and get notified
            $user = $quality_control->User?->id ? [$quality_control->User->id] : [];
            $typeTitle = !empty($data['typeTitle']) 
                        ? $data['typeTitle'] 
                        : (LearningResult::find($data['type_id'])?->module?->name ?? '');

            if (
                count($user) > 0
            ) {
                $email_queue = new \Models\EmailQueue;
                $email_queue->email_template_id = $template->id;
                $email_queue->recipients = $user;
                $email_queue->from = \APP\Auth::getUserId();
                $email_queue->custom_variables = json_encode([
                    'TASK_TYPE' => $typeTitle,
                    'FEEDBACK' => $quality_controlObj->judgement_reason . '(' . $quality_controlObj->qaUser->fname . ' ' . $quality_controlObj->qaUser->lname.', '.$quality_controlObj->updated_at,
                ]);
                $email_queue->save();
            }
        }
        $response->getBody()->write(json_encode($qa));
        return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getSessionCheck());

})->add(\APP\Auth::getSessionCheck());
