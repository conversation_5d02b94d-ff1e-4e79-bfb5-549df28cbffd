<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/learning-result",  function ($group) {

	$group->delete("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = \Models\LearningResult::find($args['id']);
		if (
			$data &&
			\APP\Auth::isAdmin()
		) {
			// Save person who initiated deletion
			$data->deleted_by = \APP\Auth::getUserId();
			$data->save();

			$data->delete();
			$response->getBody()->write(json_encode(['status' => true]));
			return $response->withHeader('Content-Type', 'application/json');
		}
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-results'], 'disable'));

	$group->patch("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = \Models\LearningResult::onlyTrashed()->find($args['id']);
		if (
			$data &&
			\APP\Auth::isAdmin()
		) {
			// Save person who initiated deletion
			$data->restored_by = \APP\Auth::getUserId();
			$data->save();

			$data->restore();
		}
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-results'], 'disable'));

	$group->get("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = \Models\LearningResult
			::where('id', $args['id'])
			->with('module')
			->with('user')
			->first()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-results'], 'select'));


	$group->post("/{id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$result = \Models\LearningResult::find($args['id']);
		$result->completion_status = isset($data['completion_status']) ? $data['completion_status'] : null;
		$result->passing_status = isset($data['passing_status']) ? $data['passing_status'] : null;
		$result->grade = isset($data['grade']) ? $data['grade'] : null;
		$result->score = isset($data['score']) ? $data['score'] : null;
		$result->completed_at = isset($data['completed_at']) ? $data['completed_at'] : null;
		$result->refreshed = isset($data['refreshed']) ? true : false;
		$result->save();

		return
			$response
				//->withHeader('Content-Type', 'application/json')
				//->write(json_encode($data))
		;
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-results'], 'update'));

	$group->post("/new", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$result = new \Models\LearningResult;
		$result->user_id = $data['user_id'];
		$result->learning_module_id = $data['learning_module_id'];
		$result->completion_status = isset($data['completion_status']) ? $data['completion_status'] : null;
		$result->passing_status = isset($data['passing_status']) ? $data['passing_status'] : null;
		$result->grade = isset($data['grade']) ? $data['grade'] : null;
		$result->score = isset($data['score']) ? $data['score'] : null;
		$result->completed_at = isset($data['completed_at']) ? $data['completed_at'] : null;
		$result->refresh_reason = isset($data['refresh_reason']) ? $data['refresh_reason'] : null;
		$result->refreshed = isset($data['refreshed']) ? true : false;
		if (\APP\Auth::getUserId()) {
			$result->created_by = \APP\Auth::getUserId();
		}
		$result->save();

		$response->getBody()->write((string)$result->id);
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-learning-results'], 'update'));


	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$showDeleted = 0;
		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			if(isset($params['search']['showDeleted']))
			{
				$showDeleted = $params['search']['showDeleted'];
				unset($params['search']['showDeleted']);
			}
		}

		$query = \Models\LearningResult
			::select([
				'learning_results.id',
				'learning_results.user_id',
				'learning_results.learning_module_id',
				'learning_results.completion_status',
				'learning_results.passing_status',
				'learning_results.refreshed',
				'learning_results.completed_at',
				'learning_results.created_at',
				'learning_results.deleted_by',
				'learning_results.delete_reason',
				'learning_results.deleted_at',
				'users.id as user_id',
				'users.username',
				'users.usercode',
				'users.fname',
				'users.lname',
				'users.username',
				'learning_modules.name as module_name',
				'learning_modules.type_id',
				'learning_module_types.name as type_name',
				'learning_modules.category_id',
				'learning_module_categories.name as category_name',
				DB::raw("CONCAT(users.fname, ' ', users.lname) as learner_name"),
				DB::raw("DATE_FORMAT(learning_results.completed_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS completed_at_uk"),
				DB::raw("DATE_FORMAT(learning_results.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"),
				DB::raw("DATE_FORMAT(learning_results.deleted_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS deleted_at_uk"),
			])
			->leftJoin("users", function($join) {
				$join
					->on("learning_results.user_id", "users.id")
				;
			})
			->leftJoin("learning_modules", function($join) {
				$join
					->on("learning_results.learning_module_id", "learning_modules.id")
				;
			})
			->leftJoin("learning_module_types", function($join) {
				$join
					->on("learning_module_types.id", "learning_modules.type_id")
				;
			})
			->leftJoin("learning_module_categories", function($join) {
				$join
					->on("learning_module_categories.id", "learning_modules.category_id")
				;
			})
		;

		// Completed at filter for learning results
		if (
			isset($params["search"]["completed_at"]) &&
			$params["search"]["completed_at"]
		) {
			$params["search"]["completed_at"] = json_decode($params["search"]["completed_at"], true);
			if (
				isset($params["search"]["completed_at"]['period_from']) &&
				$params["search"]["completed_at"]['period_from']
			) {
				$completed_at_from = \Carbon\Carbon::parse($params["search"]["completed_at"]['period_from'])->startOfDay();
				$query
					->where("learning_results.completed_at", ">=", $completed_at_from)
				;
			}
			if (
				isset($params["search"]["completed_at"]['period_to']) &&
				$params["search"]["completed_at"]['period_to']
			) {
				$completed_at_to = \Carbon\Carbon::parse($params["search"]["completed_at"]['period_to'])->endOfDay();
				$query
					->where("learning_results.completed_at", "<=", $completed_at_to)
				;
			}
			unset($params["search"]["completed_at"]);
		}
		if ($showDeleted == 1) {
			$query->onlyTrashed();
		}

		if (isset($args["download"]) && $args["download"] == "/download") {
			$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

			$export_fields["%%user%% ID"] = "user_id";
			$export_fields["Username"] = "username";
			if (\APP\Tools::getConfig('showEmployeeIdInReports')) {
				$export_fields["Employee ID"] = "usercode";
			}
			$export_fields["%%user%% name"] = "learner_name";
			$export_fields["Resource ID"] = "learning_module_id";
			$export_fields["Resource"] = "module_name";
			$export_fields["Category"] = "category_name";
			$export_fields["Type"] = "type_name";
			$export_fields["Completion status"] = "completion_status";
			$export_fields["Audit item"] = "refreshed";
			$export_fields["Completed at"] = "completed_at_uk";
			$export_fields["Created at"] = "created_at_uk";

			$download_file_name = uniqid("learning-results.list.") . ".xlsx";


			\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				)
			;

			$response->getBody()->write(json_encode($download_file_name));
			return $response->withHeader('Content-Type', 'application/json');
		} else {
			$p = \APP\SmartTable::searchPaginate($params, $query, false, true, true);
		}



		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-learning-results', 'select'));

	$group->post('/archive/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		$showDeleted = 0;
		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			if(isset($params['search']['showDeleted']))
			{
				$showDeleted = $params['search']['showDeleted'];
				unset($params['search']['showDeleted']);
			}
		}

		$query = \Models\LearningResultArchive
			::select([
				'learning_result_archives.*',
				'users.id as user_id',
				'users.username',
				'users.fname',
				'users.lname',
				'users.username',
				DB::raw("CONCAT(users.fname, ' ', users.lname) as learner_name"),
				DB::raw("DATE_FORMAT(learning_result_archives.completed_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS completed_at_uk"),
			])
			->join("users", function($join) {
				$join
					->on("learning_result_archives.user_id", "users.id")
					->where('users.status', true)
				;
			})
		;

		if (\APP\Auth::isLearner()) {
			$query = $query
				->where('user_id', \APP\Auth::getUserId())
			;
		}

		// Completed at filter for learning results
		if (
			isset($params["search"]["completed_at"]) &&
			$params["search"]["completed_at"]
		) {
			$params["search"]["completed_at"] = json_decode($params["search"]["completed_at"], true);
			if (
				isset($params["search"]["completed_at"]['period_from']) &&
				$params["search"]["completed_at"]['period_from']
			) {
				$completed_at_from = \Carbon\Carbon::parse($params["search"]["completed_at"]['period_from'])->startOfDay();
				$query
					->where("learning_results.completed_at", ">=", $completed_at_from)
				;
			}
			if (
				isset($params["search"]["completed_at"]['period_to']) &&
				$params["search"]["completed_at"]['period_to']
			) {
				$completed_at_to = \Carbon\Carbon::parse($params["search"]["completed_at"]['period_to'])->endOfDay();
				$query
					->where("learning_results.completed_at", "<=", $completed_at_to)
				;
			}
			unset($params["search"]["completed_at"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['trainee-learning-results', 'system-setup-learning-results'], 'select'));


});
