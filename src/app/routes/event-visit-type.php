<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\ScheduleVisitTypeWorkflow;

$app->group("/event-visit-type",  function ($group) {

	$group->put('/{status:disable|enable}/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\ScheduleVisitType::find($args["id"]);
		$entry->status = 0;
		if ($args['status'] == 'enable') {
			$entry->status = 1;
		}
		$entry->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-visit-types', 'disable'));

	$group->get('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$entry = \Models\ScheduleVisitType::with('FormWorkFlow')->find($args["id"]);

		$response->getBody()->write(json_encode($entry));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-visit-types', 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$entry = new \Models\ScheduleVisitType;
		$fields = [
			"name"
		];
		$entry->slug = \APP\Tools::safeName($data['name']) . '_' . \APP\Tools::unsecureRandom();
		\APP\Tools::setObjectFields($entry, $fields, $data, false, true);
		$entry->save();
		foreach ($data['form_workflows'] as $value) {
			ScheduleVisitTypeWorkflow::create(['schedule_visit_type_id'=>$entry->id,'form_workflow_id'=>$value['id']]);
		 }
		$response->getBody()->write(json_encode($entry->id));
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-visit-types', 'insert'));

	// Update entry
	$group->post('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$entry = \Models\ScheduleVisitType::find($args["id"]);

		$fields = [
			"name",
		];

		// Slug can be changed only to non-system critical event types
		if (!$entry->system) {
			$fields[] = "slug";
		}

		if(isset($entry->is_enable_days_since_last_review)){
			$fields[]="is_enable_days_since_last_review";
		}else{
			$data["is_enable_days_since_last_review"]=false;
		}
		\APP\Tools::setObjectFields($entry, $fields, $data, false, true);


		$entry->save();
		$form_workflows=[];
		foreach ($data['form_workflows'] as $value) {
			ScheduleVisitTypeWorkflow::create(['schedule_visit_type_id'=>$entry->id,'form_workflow_id'=>$value['id']]);
			$form_workflows[]=$value['id'];
		 }
		 ScheduleVisitTypeWorkflow::where('schedule_visit_type_id',$entry->id)->whereNotIn('form_workflow_id',$form_workflows)->delete();
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-visit-types', 'update'));


	$group->get('/all', function (Request $request, Response $response, $args) {
		session_write_close();
		$data = \Models\ScheduleVisitType
			::where("status", true)
		;

		$data = $data
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionRegisterCheck());

	$group->post('/list', function (Request $request, Response $response) {
		$params = $request->getParsedBody();


		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$query = \Models\ScheduleVisitType
			::where("schedule_visit_types.id", ">", "0")
			->select(
				'schedule_visit_types.*'
			);
		$p = \APP\SmartTable::searchPaginate($params, $query);
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-visit-types', 'select'));
});