<?php

use APP\Auth;
use Models\ApprenticeshipStandardUser;
use Models\ScheduleLink;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\Form;
use APP\Form as FormClass;
use Models\FormField;
use Models\FormFieldAuthorPermission;
use Models\FormSignoffRole;
use Models\UserCustomFormValue;
use Models\UserFormSignoff;
use Upload\File;
use Upload\Storage\FileSystem;
use APP\Controllers\FormController;
use APP\Tools;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Log;
use Models\CustomFieldQuery;
use Models\CustomFieldQueryBinding;
use Models\Role;
use Models\TableHistory;
use Models\UserFormValue;
use Models\UserForm;
use Slim\Http\Request as HttpRequest;
use Slim\Http\Response as HttpResponse;

$app->group("/form-types",  function ($group) {
	// $group->post('/new',[new FormController(),'create']); //TODO:Implementing the remaining section similar to this.

	$group->post('/check-template-field-association',[new FormController(),'checkTemplateFieldAssociation']);

	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {

		$form_type = \Models\Form::find($args["id"]);
		$form_type->status = 0;
		$form_type->save();

		$user_form_list=\Models\UserForm::where("form_id",$args["id"])->get();
		if($user_form_list){
			foreach($user_form_list AS $user_form_list_val){
				\Models\UserForm::deleteUserForms($user_form_list_val->id);
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$form_type = \Models\Form::find($args["id"]);
		$form_type->status = 1;
		$form_type->save();

			/** Softdelete Users */
		$user_form_list=\Models\UserForm::where("form_id",$args["id"])->withTrashed()->get();
		if($user_form_list){
			foreach($user_form_list AS $user_form_list_val){
				\Models\UserForm::deleteUserForms($user_form_list_val->id,false);
			}
		}

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

	$group->get('/options/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		 $field= \Models\Field::where('id',$args['id'])->first();
		 if($field->model){
		 $model=$field->model;
		 $options=$model::where('status',1)->get();
		 }else{
		 $options=\Models\FieldOption::where("field_id",$field->id)->get();
		 }
		$response->getBody()->write(json_encode($options));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	$group->get('/ilr',function (Request $request,Response $response,$args){
		$ilr= $this->get('settings')['ilr_fields'];
		$response->getBody()->write(json_encode($ilr));
		return $response->withHeader('Content-Type', 'application/json');
	});

	$group->post('/save/{user_form_id:[0-9]+}',function (Request $request,Response $response,$args){
		try{
		$data = $request->getParsedBody();
		$signature=null;
		if(array_key_exists('signature',$data))
		{
			$signature=$data['signature'];
			unset($data['signature']);
		}
		$user_form = \Models\UserForm
			::where("id",$args['user_form_id'])
			->with('Form')
			->with('FormValue')
			->first()
		;
		if (!$user_form) {
			return \APP\Tools::returnCode($request, $response, '404');
		}
		$user_form->snooze_target_date = isset($data['snooze_target_date'])?$data['snooze_target_date']:NULL;
		$form = Form::where('id',$user_form->form_id)->first();
		$valueExist = FormClass::isCommonForm($form,$user_form);
			if($user_form->FormValue && $user_form->FormValue->values){
				$path = $this->get('settings')["LMSPrivatePath"] . "form_files/";
				UserForm::unlinkFiles($data,$user_form->FormValue->values,$path);
			}

		if($valueExist)
		{
			$user_form->user_custom_form_value_id = $valueExist->user_custom_form_value_id;
			$user_form->save();
			FormClass::updateUserForm($form,$user_form);
		}
		//Here chekc the user form  and userform value and update or save
		if(isset($user_form->user_custom_form_value_id) && $user_form->user_custom_form_value_id!=null){
			// return 'inside if';
			$user_custom_from_value=UserCustomFormValue::where('id',$user_form->user_custom_form_value_id)->first();
			# For issue SCOR-3333 $user_custom_from_value is checked before modifying and saving
			if($user_custom_from_value){
				$user_custom_from_value->values=$data;
				$user_custom_from_value->save();
			}
			# End of fix
			// print_r($user_form->user_custom_form_value_id);die();
		}else{
			// return 'inside else';
			$user_custom_from_value=new UserCustomFormValue();
			$user_custom_from_value->values= '{}';
			$user_custom_from_value->save();
			$user_form->user_custom_form_value_id=$user_custom_from_value->id;
			FormClass::updateUserForm($form,$user_form);

			$user_form->save();
			$user_custom_from_value->values=$data;
			$user_custom_from_value->save();
		}
			$user = Auth::getUser();

		// if(!$user_form->form->has_sign_off)
			// {
		if($form->has_sign_off==0)
			{
				$target_date = Carbon::parse($user_form->snooze_target_date);
				$now = Carbon::now();
			if(isset($data['is_form_complete']) &&  $data['is_form_complete']==true && ($target_date->startOfDay()->lte($now->startOfDay()))){
					// $user=\Models\User::find($args['user_id']);
					if($form->has_sign_off){
						$checkFormCompleteionSatus=\Models\UserForm::checkSignOffStatus($args['user_form_id']);
						if($checkFormCompleteionSatus==true){
						 $user_form->user_form_status="Completed";
                        }
                    }else {
                        $valid = UserForm::checkRequiredFieldCompleted($args['user_form_id'],$data);
                        if($valid){
                                $user_form->user_form_status="Completed";
                        }
                    }

				$data_arr = [
					'user_id' => $user->id,
					'user_form_id' => $user_form->id,
					'signoff_status' => 0,
					'status' => $user_form->user_form_status
					];
			}


		}else{
				 if ((isset($data["has_sign_off"]) || isset($data['is_form_complete'])) && $user_form->form->has_sign_off==true && $form->has_sign_off==1) {
					$user = Auth::getUser();
					if((isset($data["has_sign_off"]) && $data["has_sign_off"]=="true") || (isset($data['is_form_complete']) && $data['is_form_complete'] == true)){
						// $user_custom_from_value->sign_off_trainee=1;
						$user_form->learner_completion_status=true;
						// $user_custom_from_value->sign_off_trainee_at =  \Carbon\Carbon::now();
						// $user_custom_from_value->save();
						if(\Models\Form::checkIsLearnerOnlyForm($user_form->form_id)){
							$user_form->user_form_status="Completed";
						}else{
							$user_form->user_form_status="Awaiting Sign-off";
						}
                        if(\APP\Auth::isLearner() && !$form->has_sign_off_order)
						{
							// \Models\UserForm::sendSignOffMailToManagers($user_form);
						}
						$user_form_signoff_data=[
						'signoff_status'=>true,
						'signoff_at'=>\Carbon\Carbon::now(),
						'status' => 'signed_off'
						];
						if($signature)
						{
							$path=$this->get('settings')['LMSESignatureImagesPath'];
							$img = str_replace('data:image/png;base64,', '', $signature);
							$img = str_replace(' ', '+', $img);
							$data_img = base64_decode($img);
							$file = $path . uniqid() . '.png';
							$success = file_put_contents($file, $data_img);
							$user_form_signoff_data['e_signature']=$file;
						}


						if($user_form->Form && $user_form->Form->is_common_form==true){
							$user_form_signoff_data['form_status']=$user_form->user_form_status;
							$user_form_signoff_data['signoff_by']=$user->id;
							\Models\UserForm::updateCommonSignOff($user_form->user_custom_form_value_id, $data['role_id'], $user_form_signoff_data);
						}else{
							if (isset($data['has_sign_off']) && $data['has_sign_off'] == true) {
								UserFormSignoff::updateOrCreate([
									'user_id' => $user->id, 'user_form_id' => $user_form->id,
									'signoff_role' => $data['role_id']
								], $user_form_signoff_data);
							}
						}
						if(\Models\UserForm::checkSignOffStatus($user_form->id))
						{
								$user_form->user_form_status="Completed";
						}
						// ----------------- saving in form_logs -------------
						$user=\APP\Auth::getUser();
						$data_arr = [
							'user_id' => $user ? $user->id : 0,
							'username' =>  $user ? $user->username : '',
							'learner_name' => $user ? $user->fname. ' '. $user->lname : '',
							'form_id' => $user_form->form_id,
							'user_form_id' => $user_form->id,
							'form_field' => json_encode($data) ?? '',
							'edited_by_user' => $user ? $user->id : 0,
							'date_time' => date("Y-m-d h:i:s", time()),
							'notes' => 'Form signed off'
						];

						\Models\FormLog::addEntry($data_arr);
						// ---------------------------------------------------
					}
				}

			// else{
			// 	$checkFilledField=\Models\UserForm::checkAssignedFormFieldCompleted($args['user_form_id']);
			// 	if($checkFilledField==true && $user_form->user_form_status='In Progress'){
			// 		$user_form->user_form_status='Completed';
			// 	}
			// }
			// if($user_form->user_form_status=="Not Started"){
			// 	$user_form->user_form_status='In Progress';
			// }

      }
      if($form->has_sign_off){
        if(UserForm::checkRequiredFieldCompleted($args['user_form_id'],$data) && \Models\UserForm::checkSignOffStatus($user_form->id) ){
          $user_form->user_form_status="Completed";
        }
      }
			$user = $user_form->user;
			\APP\Form::saveToMaster($user_form->form,$data,$user);

		$user_form->save();

		$assignments = \Models\Assignment::getUserEntries();
		$response
			->getBody()
			->write(json_encode(['assignments'=>$assignments]))
		;
		return  $response
		->withHeader('Content-Type', 'application/json')
		;

	}catch(\Exception $e){
		$response->getBody()->write(json_encode(['error'=>$e->getMessage()]));
		return  $response->withHeader('Content-Type', 'application/json');
	}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources','trainee-standards'], 'select'));

	$group->get('/user-custom-form-value/{user_form_id:[0-9]+}',function (Request $request,Response $response,$args){
		$data = $request->getParsedBody();
		$user_custom_from_value=[];
		$user_form_current=[];
		$user_form=\Models\UserForm::find($args['user_form_id']);

	if($user_form){
		$form = Form::where('id',$user_form->form_id)->first();
			//Schedule
			// if($user_form->type=='schedule' && $user_form->user_custom_form_value_id==NULL){
			// 	$user_form_current=\Models\UserForm::where("user_id",$user_form['user_id'])
			// 	            ->where('id',"!=",$user_form->id)
			// 				->where("form_id",$user_form['form_id'])
			// 				->where('user_custom_form_value_id','!=',NULL)
			// 				->where("type","schedule")
			// 				->latest()
			// 				->first();


			// }
		$user_custom_from_value=UserCustomFormValue::where('id',$user_form->user_custom_form_value_id)->first();
		if($user_custom_from_value && $form->is_derived_form)
		{
		$user_custom_from_value = $user_custom_from_value->toArray();
		foreach ($user_custom_from_value['values'] as $key => $value) {
			$formField = FormField::where('form_id',$form->id)->whereHas('Field',function($query)use($key){
				$query->where('slug',$key);
			})->first() ;
			if($formField)
			{
			$custom_field_query = CustomFieldQuery::where('form_field_id',$formField->id)->first();
			if($custom_field_query)
			{
				$valueNew = UserFormValue::where('form_field_id',$formField->id)->where('user_form_id',$user_form->id)->first()->value;
				$user_custom_from_value['values'][$key]=$valueNew;
			}
			}
		}
		}
			// if($user_form_current){
			//   $user_custom_curent_value=UserCustomFormValue::where('id',$user_form_current->user_custom_form_value_id)->first();
			//   $user_custom_from_value["values"]=$user_custom_curent_value?$user_custom_curent_value['values']:NULL;
			// }
		}
		$response->getBody()->write(json_encode($user_custom_from_value));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));


	$group->get('/assignedlearnerforms',function (Request $request,Response $response,$args){
		$data = $request->getParsedBody();
		$user_form=\Models\Form::where("has_learner_to_assign","1")
		->where('status', 1)
		->where('display_in_list',1)
//        ->whereNull('parent_id')->select('name', 'id')
		->get();
		$response->getBody()->write(json_encode($user_form));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	$group->post('/signoff/{user_form_id:[0-9]+}',function (Request $request,Response $response,$args){
		try{
		$data = $request->getParsedBody();
		$signature=null;
		if(array_key_exists('signature',$data))
		{
		$signature=$data['signature'];
		unset($data['signature']);
		}

		//return $args['user_id'];
		 $user_form=\Models\UserForm::where("id",$args['user_form_id'])
		 ->with('Form')
		// ->where("form_id",$args['form_id'])
		->first();
		$user=\APP\Auth::getUser();


		//Here chekc the user form  and userform value and update or save
		if($user_form->user_custom_form_value_id!=null){
			$user_custom_from_value=UserCustomFormValue::where('id',$user_form->user_custom_form_value_id)->first();
			$user_custom_from_value->values=$data;
			// $user_custom_from_value->save();
		}else{
		$user_custom_from_value=new UserCustomFormValue();
			$user_custom_from_value->values=$data;
			//Check Admin or Manager
			$user_custom_from_value->save();
			$user_form->user_custom_form_value_id=$user_custom_from_value->id;
			$user_form->save();
		}

		if(!\APP\Auth::isLearner()){
			$user_custom_from_value->sign_off_manager=1;
			$user_custom_from_value->sign_off_manager_by =  \APP\Auth::getUserId();
			$user_custom_from_value->sign_off_manager_at =  \Carbon\Carbon::now();
			$user_custom_from_value->save();
			$user_form->manager_completion_status=true;
			$user_form->completion_status=true;
			$user_form->save();
			$user_form_signoff_data=[
				'signoff_status'=>true,
				'signoff_at'=>\Carbon\Carbon::now()
				];
				if($signature)
				{
				$path=$this->get('settings')['LMSESignatureImagesPath'];
				$img = str_replace('data:image/png;base64,', '', $signature);
				$img = str_replace(' ', '+', $img);
				$data_image = base64_decode($img);
				$file = $path . uniqid() . '.png';
				$success = file_put_contents($file, $data_image);
				$user_form_signoff_data['e_signature']=$file;

				}


				UserFormSignoff::updateOrCreate(['user_id'=>$user->id,'user_form_id'=>$user_form->id,'signoff_role'=>$data['role_id']],$user_form_signoff_data);
				$checkFormCompleteionSatus=\Models\UserForm::checkSignOffStatus($args['user_form_id']);

				if($checkFormCompleteionSatus==true){
					$user_form->user_form_status="Completed";
					$user_form->save();
				}


				if($user_form->Form && $user_form->Form->is_common_form==true){
					$user_form_signoff_data['form_status']=$user_form->user_form_status;
					$user_form_signoff_data['signoff_by']=$user->id;
					\Models\UserForm::updateCommonSignOff($user_form->user_custom_form_value_id, $data['role_id'], $user_form_signoff_data);
				 }
				// Send out "Form Sign off by manager" to learner
				// $template = \Models\EmailTemplate
				// ::where('name', 'Form Signed Off by %%manager%%')
				// ->where('status', true)
				// ->first();
				// if (
				// $template &&
				// $template->id
				// ) {
				// 	$learner_details=\Models\User::find($user_form['user_id']);
				// 	$email_queue = new \Models\EmailQueue;
				// 	$email_queue->email_template_id = $template->id;
				// 	$email_queue->recipients = [intval($user_form["user_id"])];
				// 	$email_queue->from = \APP\Auth::getUserId();
				// 	$email_queue->custom_variables = json_encode([
				// 		'USER_FNAME' => $learner_details->fname,
				// 		'USER_LNAME'=>$learner_details->lname,
				// 		'FORM_NAME'=>$user_form->form->name,
				// 		'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
				// 		'REGARDS'=>$GLOBALS["CONFIG"]->Regards,
				// 	]);
				// 	$email_queue->save();
				// }

		}

		if(\APP\Auth::isLearner()){
			$user_custom_from_value->sign_off_trainee=1;
			$user_custom_from_value->sign_off_trainee_at =  \Carbon\Carbon::now();
			$user_custom_from_value->save();
			$user_form->learner_completion_status=true;
			$user_form->user_form_status="Awaiting Sign-off";
			$user_form->save();

            $form = Form::where('id', $user_form->form_id)->first();
            if($form && !$form->has_sign_off_order){
                \Models\UserForm::sendSignOffMailToManagers($user_form);
            }

		}else{
			$user=\Models\User::find($user_form['user_id']);
		}

		if($data['has_sign_off'] || $signature){
			// ----------------- saving in form_logs -------------
			$learner = \Models\User::find($user_form->user_id);
			$data_arr = [
				'user_id' => $learner->id,
				'username' => $learner ? $learner->username : '',
				'learner_name' => $learner ? $learner->fname. ' '. $learner->lname : '',
				'form_id' => $user_form->form_id,
				'user_form_id' => $user_form->id,
				'form_field' => json_encode($data),
				'edited_by_user' => \APP\Auth::getUserId() ?? 0,
				'date_time' => date("Y-m-d h:i:s", time()),
				'notes' => 'Form signed off'
			];
			\Models\FormLog::addEntry($data_arr);

			// ---------------------------------------------------
		}


	if(\APP\Auth::isAdmin() || \APP\Auth::isManager() || \APP\Auth::accessAllLearners()){
			\APP\Form::saveToMaster($user_form->form,$data,$user);
		}
		$response
		->getBody()
		->write("done");
		return $response;
	}catch(\Exception $e){
		print_r($e->getMessage());
	}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources','trainee-standards'], 'select'));



	$group->get('/{form_id:[0-9]+}/[{user_form_id}]', function (Request $request, Response $response, $args) {
		try{
			// if(!array_key_exists('user_id',$args)) {
			// }

			$userform=[];

			if(isset($args["user_form_id"]) &&  $args["user_form_id"]!='NaN'){
				\Models\FormField::$user_form_id=$args['user_form_id'];
			}

			$form_type = \Models\Form::with(['FormField' => function ($query) {
				$query->orderBy("position","ASC");
				$query->with([
					'CustomFieldQuery.customFieldQueryBinding.formField.form:id,name',
					'CustomFieldQuery.customFieldQueryBinding.formField.Field:id,name'
				]);

				$query->with(['Field'=>function($query){
					$query->with('FieldOption');
					$query->with(['CustomFieldValues'=>function($query){
					$query->where('user_id',\APP\Auth::getUserId())
							->orWhere('user_id',0);
					}]);
				 }])->with('FormFieldAuthorPermission.role')->with('CustomFieldQuery');
			}]);

			if(isset($args["user_form_id"]) &&  $args["user_form_id"]!='NaN'){
					$userform=\Models\UserForm::find($args['user_form_id']);


				$form_type->with(['UserForms' => function ($query) use ($args) {
					$query->where("id", $args['user_form_id'])->with("UserFormSignoff");
					}]);
					$user_id=$userform->user_id;
				// $userForms= \Models\UserForm::where(['user_id'=>$userform->user_id,'form_id'=>$userform->form_id])->first();
				if(!empty($userform) && $userform->user_form_status=="Not Started"){
					$userform->user_form_status="In Progress";
					$userform->save();
				}

			}
				$form_type = $form_type->with('FormSignOffRoles.role')
				->find($args['form_id'])->toArray();

				$form_fields = collect($form_type['form_field']);

				$ilr = $this->get('settings')['ilr_fields'];

				$form_type['form_field'] = $form_fields->map(function ($form_field) use ($ilr,$args,$userform) {

					if (isset($form_field['custom_field_query']['type']) && (
						$form_field['custom_field_query']['type'] == 'query'
						|| $form_field['custom_field_query']['type'] == 'form_field'
						|| $form_field['custom_field_query']['type'] == 'javascript'
						|| $form_field['custom_field_query']['type'] == 'previous_filled_value'
					) && $userform) {

						$form_field_id = isset($form_field['custom_field_query']['form_field_id']) ? $form_field['custom_field_query']['form_field_id'] : null;

						if ($form_field_id) {
							$user_form_value = UserFormValue::where('form_field_id', $form_field_id)
								->where('user_form_id', $args['user_form_id'])->first();

							if ($user_form_value) {
								$form_field['custom_field_query']['value'] = $user_form_value->value;
							} else {
								$form_field['custom_field_query']['value'] = FormClass::fetchFromSqlDefaultValue(
									$userform->user_id,
									$form_field['custom_field_query']['id'] ?? null,
									$form_field['custom_field_query']['form_field_id'] ?? null,
									$args['user_form_id']
								);
							}
						}
					}


					if ($form_field['field']) {
						return $form_field;
					} else {
						$form_field['field'] = $ilr[$form_field['ilr_slug']];
						$form_field['field']['field_category_id'] = 0;
						return $form_field;
					}
				});

			if(!empty($userform) && !empty($userform->user)) {
				$form_type['user_name'] = $userform->user->fname .' '. $userform->user->lname;
			}

			//Change status to in pRogresss tedfrom Not Started
			// if(\APP\Auth::isLearner() ){


			// }elseif(array_key_exists('user_id',$args)){
			// 	$form_type->with(['UserForms' => function ($query) use ($args) {
			// 		$query->where("user_id", $args['user_id']);
			// 	 }]);
			// }

			//}
			// else
			// {
			//   $userForms= \Models\UserForm::where(['user_id'=>$args['user_id'],'form_id'=>$args['id']])->first();
			//   $userFormStructure= \Models\UserFormStructure::where('id',$userForms->form_user_structure_id)->first();
			//  if($userFormStructure)
			//  {
			//      $form_type=$userFormStructure->structure;
			//  }
			// }
		$response->getBody()->write(json_encode($form_type));
		return $response->withHeader('Content-Type', 'application/json');
	}catch(\Exception $e){
		print_r($e->getMessage());
	}
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));


	/**
	 * Get All Forms
	 */
	$group->get('/all', function (Request $request, Response $response) {
		session_write_close();
		$form_type = \Models\Form
			::query()
			->enabled()
            ->nonArchived()
			->select('id', 'name')
			->where('display_in_list',1)
		;

		if (\APP\Auth::isLearner()) {
			$form_type = $form_type
				->whereHas('UserForms', function ($query) {
					$query
						->where('user_id', \APP\Auth::getUserId())
					;
				})
			;
		}

		$form_type = $form_type
			->get()
		;

		$response->getBody()->write(json_encode($form_type));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	$group->post('/new', function (Request $request, Response $response) {
		try{
		$data = $request->getParsedBody();

		$slug = \APP\Tools::safeName($data['name']);

		// check if such slug exists already.
		$entry = \Models\Form
			::where('slug', $slug)
			->first()
		;
		if ($entry) {
			return \APP\Tools::returnCode($request, $response, 500, 'This Name already exists, please choose a different name.');
		}
		$entry = new \Models\Form;
		$entry->slug = $slug;
        $entry->name = $data['name'];
        $entry->is_archived = isset($data['is_archived'])?$data['is_archived']:false;
		$entry->has_learner_to_assign=isset($data['has_learner_to_assign']) && $data['has_learner_to_assign']=="true"?"1":"0";
		$entry->has_sign_off = isset($data['has_sign_off']) && $data['has_sign_off']=="true"?"1":"0";
          $entry->has_sign_off_order = isset($data['has_sign_off_order']) && $data['has_sign_off_order']=="true"?"1":"0";
	$entry->is_common_form = isset($data['is_common_form']) && $data['is_common_form']=="true"?"1":"0";
			$entry->is_derived_form = isset($data['is_derived_form']) && $data['is_derived_form']=="true"?"1":"0";
			$entry->restricted_form = isset($data['restricted_form']) && $data['restricted_form']=="true"?1:0;
		$entry->status = 1;
		$entry->system_form=isset($data['system_form']) && $data['system_form']=="true"?"1":"0";
		$entry->slug = \APP\Tools::safeName($data['name']);
			$entry->enable_snooze_signoff_form = isset($data['enable_snooze_signoff_form'])&&$data['enable_snooze_signoff_form']=="true"?true:false;
		$entry->learning_module_feedback_form=isset($data['learning_module_feedback_form']) && $data['learning_module_feedback_form']=="true"?"1":"0";
		$entry->assign_to_user_attended_an_event =  isset($data['assign_to_user_attended_an_event'])&&$data['assign_to_user_attended_an_event']=="true"?true:false;
		if (isset($_FILES['attached_word_form'])) {
			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSFilePath"]);
			$attached_word_file = new \Upload\File('attached_word_form', $storage);
			$attached_word_file->setName(\APP\Tools::safeName($_FILES['attached_word_form']['name']) . '_' . uniqid());
			$fileSizeValidation = new \Upload\Validation\Size('800M');
			$fileTypeValidation = new \Upload\Validation\Mimetype(\APP\Tools::documentMime());
			$fileTypeValidation->setMessage("Invalid file type: '" . $attached_word_file->getMimetype() . "'!");
			$attached_word_file->addValidations([
				$fileTypeValidation,
				$fileSizeValidation
			]);
			try {
				$attached_word_file->upload();
				$entry->word_file = $attached_word_file->getNameWithExtension();
			} catch (\Upload\Exception\UploadException $e) {
				$errors = $attached_word_file->getErrors();
				return \APP\Tools::returnCode($request, $response, 500, implode("\n", $errors));
			}
		}
		$entry->save();

		if(isset($data['has_sign_off']) && $data['has_sign_off']=="true" && !empty($data['signoff_roles_append'])){
			$not_edit_signoff_roles=[];
			$roles=explode(',',$data['signoff_roles_append']);
			if(isset($data['edit_after_sign_off_permission_append'])){
				$not_edit_signoff_roles=explode(',',$data['edit_after_sign_off_permission_append']);
			}
			foreach($roles AS $index => $role){
				$signOffForm=new \Models\FormSignoffRole;
				$signOffForm->form_id=$entry->id;
				$signOffForm->role_id=$role;
				if(!empty($not_edit_signoff_roles) && in_array($role,$not_edit_signoff_roles)){
					$signOffForm->edit_after_sign_off_permission=false;
				}
                if (isset($data['has_sign_off_order']) && $data['has_sign_off_order']){
                    $signOffForm->order = $index;
                }
				$signOffForm->save();
			}
		}

		$response
			->getBody()
			->write(strval($entry->id))
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')

		;
		}catch(\Exception $e){
		print_r($e->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	$group->put('/{id}',function (Request $request,Response $response,$args){
		try {

			$insertArray=[];
			$not_edit_signoff_roles=[];

			$formType = \Models\Form::where('id', $args['id'])->first();
			$body = $request->getParsedBody();
            if(Form::hasMandatoryButNotEditableField($body)){
				return \APP\Tools::returnCode($request, $response, 400, "Mandatory fields cannot be made non-editable");
            }
			// $form_fields=\Models\FormField::where('form_id',$formType->id)->get();
			// foreach($form_fields AS $form_field_value){
			// 	\Models\FormFieldAuthorPermission::where('form_field_id',$form_field_value->id)->delete();
			// }
			// $form_fields=\Models\FormField::where('form_id',$formType->id)->delete();



			if(isset($body['formName'])){
                $formType->name=$body['formName'];
                $formType->is_archived = isset($body['is_archived']) ? $body['is_archived']:false;
            $formType->is_common_form = isset($body['is_common_form']) && $body['is_common_form']=="true"?"1":"0";
            $formType->is_derived_form = isset($body['is_derived_form']) && $body['is_derived_form']=="true"?"1":"0";
				$formType->has_learner_to_assign=isset($body['has_learner_to_assign']) && $body['has_learner_to_assign'];
                $formType->has_sign_off_order = isset($body['has_sign_off_order']) && $body['has_sign_off_order']=="true"?"1":"0";
				$formType->has_sign_off = isset($body['has_sign_off']) && $body['has_sign_off']=="true"?"1":"0";
				$formType->restricted_form = isset($body['restricted_form']) && $body['restricted_form']=="true"?"1":"0";
				//FOR ISSUE 3289: Added the lines and 'system_form' is saved into the db on every save request
				$formType->system_form = $body['is_system_form'];
				// END OF FIX
				$formType->enable_snooze_signoff_form = isset($body['enable_snooze_signoff_form'])&&$body['enable_snooze_signoff_form']?true:false;
				$formType->assign_to_user_attended_an_event = isset($body['assign_to_user_attended_an_event'])&&$body['assign_to_user_attended_an_event']=="true" ? true:false;
				$formType->learning_module_feedback_form=isset($body['learning_module_feedback_form']) && $body['learning_module_feedback_form']=="true"?"1":"0";

				$formType->save();
				if(isset($body['has_sign_off']) && $body['has_sign_off']=="true" && !empty($body['signoff_roles'])){
				if(isset($body['edit_after_sign_off_permission_append'])){
					$not_edit_signoff_roles=$body['edit_after_sign_off_permission_append'];
				}
					foreach($body['signoff_roles'] AS $index => $role){
						$signOffForm=\Models\FormSignoffRole::firstOrCreate(['form_id'=>$formType->id,'role_id'=>$role]);
						if(!empty($not_edit_signoff_roles) && in_array($role,$not_edit_signoff_roles)){
							$signOffForm->edit_after_sign_off_permission=false;
						}else{
							$signOffForm->edit_after_sign_off_permission=true;
						}
                        if (isset($body['has_sign_off_order']) && $body['has_sign_off_order']){
                            $signOffForm->order = $index;
                        }
						$signOffForm->save();
				    }
                    $form_signoffs = FormSignoffRole::where('form_id',$formType->id)->whereNotIn('role_id',$body['signoff_roles'])->get();
                    foreach($form_signoffs as $form_signoff){
                        $form_signoff->delete();
                    }
				}else{
					FormSignoffRole::where('form_id',$formType->id)->delete();
				}
			}
			$ilr = $this->get('settings')['ilr_fields'];
			foreach ($body['parameterList'] as $key=>$value) {


				$type = \Models\Field::where('slug', $value['type'])->where('field_category_id',$value['category'])->first();
			if($type || array_key_exists($value['type'],$ilr)){
					$existFormField=\Models\FormField::where("form_id",$formType->id)
			->where(function($query) use($value,$type){
				if($type)
				{
				$query->where("field_id",$type->id);
				}else
				{
				$query->where('ilr_slug',$value['type']);
				}
			})->where("is_assigned",$value['is_assigned'])->first();
					if(!$existFormField){
						$formTypeParameter = new \Models\FormField();
						$formTypeParameter->form_id = $formType->id;
						$formTypeParameter->field_id = $type?$type->id:0;
			$formTypeParameter->ilr_slug=$type?null:$value['type'];
						$formTypeParameter->is_mandatory = $value['mandatory'];
						$formTypeParameter->is_read_write = isset($value['is_read_write'])?$value['is_read_write']:false;
						$formTypeParameter->is_assigned = $value['is_assigned'];
						$formTypeParameter->position = $key;
						$formTypeParameter->permission = $value['permission'];

						$formTypeParameter->has_default_value =  isset($value['has_default_value'])?$value['has_default_value']:false;;
			$formTypeParameter->alternative_default_value = isset($value['alternative_default_value'])?$value['alternative_default_value']:NULL;
						$formTypeParameter->save();
						$formParaId=$formTypeParameter->id;
					}else{
			$existFormField->position = $key;
			$existFormField->permission = $value['permission'];
			$existFormField->is_mandatory = $value['mandatory'];
						$existFormField->is_read_write = isset($value['is_read_write'])?$value['is_read_write']:false;
						$existFormField->is_assigned = $value['is_assigned'];
			$existFormField->has_default_value =  isset($value['has_default_value'])?$value['has_default_value']:false;
			$existFormField->alternative_default_value = isset($value['alternative_default_value'])?$value['alternative_default_value']:NULL;

						$existFormField->save();
						$formParaId=$existFormField->id;
					}
						//Add Custom field functionality
						if($type &&  $type->custom=='1' && isset($value['query']) && !empty($value['query_value_type'])){
							$data=["form_field_id"=>$formParaId,
								"value"=>isset($value['query'])?$value['query']:"",
								"type"=>isset($value['query_value_type'])?$value['query_value_type']:"",
								"variable_type"=>"form",
								"variables"=>isset($value['variables'])?$value['variables']:"",
								"form_type_id" =>$formType->id
							];
							$insertCustomField = FormClass::setDefaultValueFormField($data);
				}
				else{
				$customFieldQuery =CustomFieldQuery::where('form_field_id',$formParaId)->pluck("id");
				if($customFieldQuery)
				{
					CustomFieldQueryBinding::whereIn('custom_field_query_id',$customFieldQuery)->delete();
					CustomFieldQuery::where('form_field_id',$formParaId)->delete();
					UserFormValue::where('form_field_id',$formParaId)->delete();
				}
			}

						if(isset($value['author_permission']) && !empty($value['author_permission'])){
				$insertPermissionArray =[];
							foreach($value['author_permission'] AS $auth_permission){
								$existAuthPermission=\Models\FormFieldAuthorPermission::where("role_id",$auth_permission['role_id'])
								->where("form_field_id",$formParaId)->where("permission",$auth_permission['permission'])
								->firstOrCreate(
									[
									'form_field_id'=>$formParaId,
									'role_id'=>$auth_permission['role_id'],
									'permission'=>$auth_permission['permission']
									]
								);
								$insertPermissionArray[]=$existAuthPermission->id;

				}
				FormFieldAuthorPermission::whereNotIn('id',$insertPermissionArray)->where('form_field_id',$formParaId)->delete();

						}


						$insertArray[]=$formParaId;

					}



			}
			//deleteas
			$form_field_del=\Models\FormField::whereNotIn('id',$insertArray)->where('form_id',$args['id'])->get();
			foreach($form_field_del AS $form_field_value){
					\Models\FormFieldAuthorPermission::where('form_field_id',$form_field_value->id)->delete();
			}
			$form_fields=\Models\FormField::whereNotIn('id',$insertArray)->where('form_id',$args['id'])->delete();
			$response->getBody()
				->write('true')
			;
			return $response;
		}catch (Exception $exception)
		{
			return \APP\Tools::returnCode($request, $response, 500, $exception->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));




	$group->post('/sign-off-list{download:[\/a-z]*}', function (Request $request, Response $response,$args) {
		try {
			$params=$request->getParsedBody();
			unset($params['search']['action']);
			unset($params['search']['refresh']);

			if(isset($params['search']['enabled']) && $params['search']['enabled'] == 0){
				$params['show_disabled'] = 1;
			}
			unset($params['search']['enabled']);

			$user = \Models\User::findOrFail(\APP\Auth::getUserId());

			$role_id= $user->shadow_role_id==null?$user->role_id:$user->shadow_role_id;

      $isAccessAllLearners = Role::find($role_id)->access_all_learners;
      $matchedUserFormIds = [];
			if($isAccessAllLearners){
				$userFormIds = DB::table('forms')
							->select('user_forms.id', 'forms.has_sign_off_order')
							->join('form_signoff_roles', 'form_signoff_roles.form_id', '=', 'forms.id')
							->join('user_forms', 'user_forms.form_id', '=', 'forms.id')
							->where('form_signoff_roles.role_id', $role_id)
							->get();
				$userFromIdsWithNoOrder = $userFormIds->filter(function($item){ return $item->has_sign_off_order == 0; });
				$userFormIdsWithOrder 	= $userFormIds->filter(function($item){ return $item->has_sign_off_order == 1; });
				if(count($userFormIdsWithOrder) > 0) {
					$userFormIdsWithOrder_aa = DB::table('user_forms')
							->select('user_forms.id as user_form_id', 'form_signoff_roles.role_id', 'form_signoff_roles.order')
							->join('form_signoff_roles', 'form_signoff_roles.form_id', '=', 'user_forms.form_id')
							->whereIn('user_forms.id', $userFormIdsWithOrder->pluck('id'))
							->get();
					if (count($userFormIdsWithOrder_aa) > 0) {
						foreach ($userFormIdsWithOrder_aa as $key => $value) {
							if (!isset($newFormat[$value->user_form_id])) {
								$newFormat[$value->user_form_id] = [
									"user_form_id" => $value->user_form_id,
									"roles" => []
								];
							}

							$newFormat[$value->user_form_id]['roles'][] = [
								"role_id" => $value->role_id,
								"order" => $value->order
							];
						}
					}
					if (count($newFormat) > 0) {

						foreach ($newFormat as $key => $userForm) {
							$userFormId = $userForm['user_form_id'];
							$roles = $userForm['roles'];
							foreach ($roles as $key => $role) {
								if ($role_id == $role['role_id']) {
									$currentOrder = $role['order'];
									// If the current order is 0, directly add the user_form_id
									if ($currentOrder == 0) {
										$matchedUserFormIds[] = $userFormId;
										break;
									}
									 else {
										$previousRole = collect($roles)->firstWhere('order', $currentOrder - 1);
										$previousRoleId = $previousRole['role_id'];
										if (UserFormSignoff::where(['user_form_id' => $userFormId, 'signoff_role' => $previousRoleId])->exists()) {
											$matchedUserFormIds[] = $userFormId;
											break;
										}
									}
								}
							}
						}
					}
				}
				$matchedUserFormIds = array_merge($userFromIdsWithNoOrder->pluck('id')->toArray(), $matchedUserFormIds);
			}
			// fix for duplicate columns while selecting with tablename.* we can remove unused columns from array in future for less code
			// Forms table columns
			$formsColumns = [
				"forms.name",
				"forms.slug",
				"forms.has_sign_off",
				"forms.system_form",
				"forms.is_common_form",
				"forms.restricted_form",
				"forms.has_learner_to_assign",
				"forms.status AS forms_status",
				"forms.is_archived",
				"forms.enable_snooze_signoff_form",
				"forms.word_file",
				"forms.is_derived_form",
				"forms.parent_id",
				"forms.display_in_list",
				"forms.has_sign_off_order"
			];

			// Users table columns
			$usersColumns = [
				"users.username",
				"users.usercode",
				"users.altusercode",
				"users.fname",
				"users.lname",
				"users.image",
				"users.e_signature",
				"users.phone",
				"users.school",
				"users.designation_id",
				"users.designation_assigned",
				"users.country_id",
				"users.company_id",
				"users.department_id",
				"users.location_id",
				"users.city_id",
				"users.role_id",
				"users.shadow_role_id",
				"users.description",
				"users.last_login_dt",
				"users.previous_last_login_dt",
				"users.expiration_dt",
				"users.registration_dt",
				"users.status AS users_status",
				//"users.has_visited_learner_interface", - there is no column named as has_visited_learner_interface in users table
				"users.ukg_employee_number",
				"users.startup_instructions_sent_at",
				"users.startup_instructions_sent",
				"users.days_since_last_review",
				"users.approval_status",
				"users.password_force_reset",
				"users.reset_password",
				"users.password_changed_at",
				"users.password_attempts",
				"users.self_attested",
				"users.self_attested_reminder_sent",
				"users.certified",
				"users.created_by",
				"users.creation_notes",
				"users.accessible_ui",
				"users.updated_by",
				"users.exclude_from_reports",
				"users.exclude_from_ilr_export",
				"users.exclude_from_emails",
				"users.staff_type_id",
				"users.staff_type_assigned",
				"users.report_to",
				"users.discount_percentage",
				"users.staff_type_sign_off_approval",
				"users.manager_percentage",
				"users.manager_percentage_behind",
				"users.sort_resource_order",
				"users.emergency_name",
				"users.emergency_relationship",
				"users.emergency_contact_numbers",
				"users.visa_length",
				"users.visa_number",
				"users.visa_date",
				"users.total_resources",
				"users.total_resources_smcr",
				"users.not_started_resources",
				"users.not_started_resources_smcr",
				"users.in_progress_resources",
				"users.in_progress_resources_smcr",
				"users.completed_resources",
				"users.completed_resources_smcr",
				"users.time_spent",
				"users.time_spent_smcr",
				"users.time_spent_off_the_job_training",
				"users.completed",
				"users.completed_smcr",
				"users.week_hours",
				"users.learning_status",
				"users.last_completion_date",
				"users.next_completion_date",
				"users.last_contact_date",
				"users.UKPRN",
				"users.LearnRefNumber",
				"users.PrevLearnRefNumber",
				"users.ULN",
				"users.CampId",
				"users.OTJHours",
				"users.DateOfBirth",
				"users.Ethnicity",
				"users.Sex",
				"users.LLDDHealthProb",
				"users.NINumber",
				"users.PriorAttainLegacy",
				"users.PriorAttain",
				"users.Accom",
				"users.ALSCost",
				"users.PlanLearnHours",
				"users.PlanEEPHours",
				"users.MathGrade",
				"users.EngGrade",
				"users.PostcodePrior",
				"users.Postcode",
				"users.AddLine1",
				"users.AddLine2",
				"users.AddLine3",
				"users.AddLine4",
				"users.email",
				"users.skype_id",
				"users.zoom_id",
				"users.teams_id",
				//"users.signup_status", - there is no column named as signup_status in users table
				"users.account_type_id",
				"users.position_ref",
				"users.watch",
				"users.watch_id",
				"users.email2",
				"users.dynamic_fields",
				"users.enabled_google_2FA",
				"users.google_2FA_secret",
				"users.ContactPreference",
				"users.LLDDandHealthProblem",
				"users.LearnerFAM",
				"users.ProviderSpecLearnerMonitoring",
				"users.LearnerEmploymentStatus",
				"users.LearnerHE",
				"users.LearningDelivery",
				"users.LearnerDestinationandProgression",
				//"users.DelLocPostCode", - there is no column named as DelLocPostCode in users table
				"users.PrevUKPRN",
				"users.PMUKPRN",
			];

			$columns = array_merge(
				[
					"user_forms.id AS user_form_id",
					"user_forms.*",
					"user_form_signoff.created_at AS submit_date",
					"user_form_signoff.signoff_at AS signoff_date",
					"user_form_signoff.status AS submit_status",
					"user_forms.user_form_status AS user_current_status",
					"departments.name AS department_name"
				],
				$formsColumns,
				$usersColumns
			);

			// $user
			$query = \Models\UserForm::groupby('user_forms.id')
			->select($columns)
			->with('FormValue')
			->with(['Form'=>function($query){
				$query->with('FormSignOffRoles.role');
			}])
			->leftjoin('forms','forms.id','user_forms.form_id')
			->leftjoin('users','users.id','user_forms.user_id')
			->leftjoin('user_form_signoff', function($join){
				$join->on('user_form_signoff.user_id', '=', 'users.id')
				->on('user_form_signoff.user_form_id', '=', 'user_forms.id');
			})
			->leftjoin('departments', 'departments.id', '=', 'users.department_id')
			->where(function($query){
				$query->where(function($query){
						 $query->where(function($query){
							$query->where("user_forms.user_form_status", "Not Started")
									->orWhere("user_forms.user_form_status", "In Progress")
									->orWhere("user_forms.user_form_status", "Awaiting Sign-off")
									->orWhere("user_forms.user_form_status", "Completed");
							});
				})
				->orWhere(function($query){
					$query->where('has_sign_off', 0)
							->where("user_forms.user_form_status", "Completed");
				});
			})
			->with('UserWorkflowForm.form_workflow.form_workflow_templates.document_templates.document_template_bindings')
			->with(['User.ManagersTiny' => function ($query) {
				$query->validuser(); // Apply the validuser scope
			}, 'scheduleLinkManagers' => function ($query) {
				$query->whereNull('deleted_by');
			}]);

			if(isset($params['search']['role_id'])){

				$query = $query->whereHas("Form.FormSignOffRoles", function ($query) use ($params) {
							$query = $query
								->where('role_id', $params['search']['role_id']);
						})
						->WhereDoesntHave("UserFormSignOff", function ($query) use ($params) {
							$query = $query
								->where('signoff_role', '=', $params['search']['role_id']);
							})
						->where('user_forms.user_form_status', '!=', 'Completed');
				unset($params['search']['role_id']);
			}


			if (
				!\APP\Auth::isAdmin()
			) {
				$query = $query
					->where(function($query) use ($user, $role_id, $isAccessAllLearners, $matchedUserFormIds) {


						// Logic applied to all non admin roles.
						$query = $query
							->where(function($query) use ($user, $role_id, $isAccessAllLearners) {
								$query = $query
									->where(function($query) use ($user, $role_id, $isAccessAllLearners) {

                                        $query->where(function ($subquery) {
                                            $subquery->whereExists(function ($existsQuery) {
                                                $existsQuery->select(DB::raw(1))
                                                    ->from('schedule_links')
                  																	->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
									                                  ->where('user_forms.type','schedule')
                                                    ->where('link_id', \APP\Auth::getUserId())
                                                    ->whereNull('deleted_by')
                                                    ->where('schedule_links.type', 'managers')
                                                    ->whereNull('schedule_links.deleted_at');
                                            })

                                            ->orWhere(function ($innerSubquery) {
                                                $innerSubquery->whereExists(function ($nestedSubquery) {
                                                    $nestedSubquery->from('schedules')
                                                        ->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
                                                        ->whereColumn('user_forms.type_id', '=', 'schedules.id')
                                                        ->where('user_forms.type', 'schedule')
                                                        ->where('forms.restricted_form', 1);
                                                });
                                            });
                                        })
										->orWhere(function ($subquery) {
											$subquery->whereNotExists(function ($notExistsQuery) {
												$notExistsQuery->select(DB::raw(1))
																					->from('schedule_links')
																										->where('user_forms.type','schedule')

													->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
													->where('schedule_links.type', 'managers');
											})
												->whereIn('user_forms.user_id', function ($whereInQuery) {
													$whereInQuery->select('user_id')
														->from('manager_users')
														->where('manager_id', \APP\Auth::getUserId())
														->whereNull('manager_users.deleted_at');
												});
										})

										->orWhere(function ($innerSubquery) {
											$innerSubquery->whereExists(function ($nestedSubquery) {
												$nestedSubquery->from('schedules')
													->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
													->whereColumn('user_forms.type_id', '=', 'schedules.id')
													->where('user_forms.type', 'schedule')
													->where('forms.restricted_form', 0);
											})
												->whereIn('user_forms.user_id', function ($query) {
													$query->select('user_id')
														->from('manager_users')
														->where('manager_id', \APP\Auth::getUserId())
														->whereNull('manager_users.deleted_at');
												});
										})
										;
									})
									// This will show only sign off form if current role is in "Management Roles To Sign-off Form"
									//FOR ISSUE 3260: This is commented because of it needs estio but in wrap ,not shows with out sign off forms

									// ->whereHas("Form.FormSignOffRoles", function ($query) use ($user) {
									// 	$query = $query
									// 		->where('role_id', \APP\Auth::roleId())
									// 	;
									// })
									 //END OF FIX
								;

								if(!in_array(Auth::roleId(),Tools::getConfig('TrackFormSignOffRoles'))){
								//need to identify manager approval or not
								$query = $query
									->where(function($query) use ($user,$role_id) {
										$query
											->where(function($query) use ($user,$role_id) {
												$query
													->WhereDoesntHave("UserFormSignOff", function ($query) use ($user,$role_id) {
														$query = $query
															->where('signoff_role', '=', $role_id)
															->where('user_id', '=', $user->id)
														;
													})
													->where("user_forms.user_form_status", "Awaiting Sign-off")
												;
											})
											->orWhere("user_forms.user_form_status", "!=", "Awaiting Sign-off")
										;
									})
								;
								}

							})
						;
						// If manager, then also show forms assigned to him
						if (\APP\Auth::isManager()) {
							$query = $query
								->orWhere('user_forms.user_id', \APP\Auth::getUserId())
							;
						}

						if ($isAccessAllLearners) {
							$query = $query->orWhereIn('user_forms.id',$matchedUserFormIds);
						}
					})

				;

			}




			if(isset($params['search']['additionalSearchParams']) &&  isset($params['search']['additionalSearchParams']['user_forms__snooze_target_date_from']) && isset($params['search']['additionalSearchParams']['user_forms__snooze_target_date_from']))
			{
				$from = Carbon::parse($params['search']['additionalSearchParams']['user_forms__snooze_target_date_from'])->format("Y-m-d");
				$to = Carbon::parse($params['search']['additionalSearchParams']['user_forms__snooze_target_date_to'])->format("Y-m-d");
				$query->whereBetween('user_forms.snooze_target_date', [$from, $to]);
			}
			unset($params['search']['additionalSearchParams']);

			if (isset($args["download"]) && $args["download"] == "/download") {
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);
				$data = collect($data);


				$data = $data->map(function($item){
						$item->managers = $item->User->ManagersTiny;
					# Setting submission date on condition
					if($item->submit_status == 'completed'){
						$submission_date = new DateTime($item->submit_date);
						$submission_date = $submission_date->format('Y-m-d');
						$item->submission_date = $submission_date;
					}else{
						$item->submission_date = $item->signoff_date;
					}

					# Setting roles date on condition
//					$roles = [];
//					if($item->Form->FormSignOffRoles){
//						foreach ($item->Form->FormSignOffRoles as $role) {
//							if(!(($role->Role && $item->signoff_role && $role->Role->id == $item->signoff_role) ||
//								((($item->FormValue && $item->FormValue->sign_off_trainee && $item->FormValue->sign_off_trainee == 1) ||
//								 ($item->FormValue && $item->FormValue->Values && $role->Role && $item->FormValue->Values->role_id == $role->Role->id)))
//							)){
//								$roles[] = $role->Role->name;
//							}
//						}
//					}
					$item->roles = $item->pending_roles;
					return $item;
				});
				$export_fields = [
					'User Form ID'=>'user_form_id',
					"Form ID" => "form.id",
					"Form" => "form.name",
					"Name" => "fname,lname",
					'Username' => 'username',
					"Submission Date" => "submission_date",
					'Department' => 'department_name',
					"Status"=>"user_current_status",
					"%%managers%%"=>"managers",
					"Role"=>"roles",
				];


				$download_file_name = uniqid("signoffforms.list.") . ".xlsx";

				\APP\Tools::generateExcelDownload(
						$data,
						$export_fields,
						$this->get('settings')["LMSTempPath"] . $download_file_name
					)
				;

				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
			}elseif(isset($args["download"]) && $args["download"] == "/print"){
				$params['nPage'] = "all";
				$p = \APP\SmartTable::searchPaginate($params, $query);
			}else{
				$p = \APP\SmartTable::searchPaginate($params, $query);
			}

			$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		} catch (Exception $exception) {
			return \APP\Tools::returnCode($request, $response, '500', json_encode($exception->getMessage()));
		}

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));


	$group->post('/parameter/add', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		$last_parameter_position = \Models\FormField
		::where('learning_module_types_id', $data['type_id'])
		->orderBy('position', 'DESC')
		->first()
		;

		$entry = new \Models\FormField;
		$entry->learning_module_types_id = $data['type_id'];
		$entry->parametername = $data['parametername'];
		$entry->parametertype = $data['parametertype'];
		$entry->mandatorycompletion = $data['mandatorycompletion'];
		$entry->parameterslug = \APP\Tools::safeName($data['parametername']);
		$entry->position = $last_parameter_position->position + 1;
		$entry->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	$group->post('/parameter/remove', function (Request $request, Response $response) {
		$data = $request->getParsedBody();

		\Models\FormField
			::where('id', $data['id'])
			->delete()
		;

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	$group->post('/moveupdown', function (Request $request, Response $response) {
		$data = $request->getParsedBody();
		$movefrom = \Models\FormField
			::where('id', $data['id'])
			->first()
		;


		if ($movefrom->position == null) {
			$movefrom->position = $data['position'] + 1;
		}

		$position = $movefrom->position;

		if ($data['direction'] == 'up') {
			$to_position = $movefrom->position - 1;
			$to_position_comparison = '<=';
			$to_position_order = 'DESC';
		} else if ($data['direction'] == 'down') {
			$to_position = $movefrom->position + 1;
			$to_position_comparison = '>=';
			$to_position_order = 'ASC';
		}

		$moveto = \Models\FormField
			::where('position', $to_position_comparison, $to_position)
			->where('learning_module_types_id', $movefrom->learning_module_types_id)
			->orderBy('position', $to_position_order)
			->first()
		;

		$movefrom->position = $moveto->position;
		$moveto->position = $position;
		$movefrom->save();
		$moveto->save();

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'insert'));

	// Update
	$group->POST('/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$slug = \APP\Tools::safeName($data['name']);

		$entry = \Models\Form::find($data['id']);
		$entry->name = $data['name'];

		// If name/slug changed, check if slug already exists, if not, change it!
		if ($entry->slug != $slug) {
			$check_for_slug = \Models\Form
				::where('slug', $slug)
				->where('id', '!=', $data['id'])
				->first()
			;
			if ($check_for_slug) {
				return \APP\Tools::returnCode($request, $response, 500, 'This Type already exists, please choose a different name.');
			}
		}
		$entry->slug = \APP\Tools::safeName($data['name']);

		$entry->save();


		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'update'));

	$group->get('/parameter/{type_id:[0-9]+}/all', function (Request $request, Response $response, $args) {
		$data = \Models\FormField
			::where("learning_module_types_id", $args['type_id'])
			->get()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));



	$group->post('/list', function (Request $request, Response $response, $args) {

		try{
		$params = $request->getParsedBody();

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
        }



		$query= \Models\Form
			::where("id",">",0)->where('display_in_list',1);
			if (isset($params["search"]["schedule_id"])) {
				if (\APP\Auth::isManager()) {
					$query=$query->where('forms.system_form', 0)->nonArchived();
				}
			}else{
				if(!isset($params['search']['display_all']))
				{
                    $query->where('restricted_form',0)->nonArchived();
				}else
				{
					unset($params['search']['display_all']);
				}
			}



		// If schedule ID is passed, return count of schedules resource is assigned to (target should be 1)
		//Also to identify the event page
		$query = \Models\Schedule::countAndConditions($query, $params);


		$data = \APP\SmartTable::searchPaginate($params, $query, false);

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	}catch(\Exception $e){
		print_r($e->getMessage());die;
	}
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	$group->get('/category/{slug}',function (Request $request,Response $response,$args){
		$formType = \Models\Field
			::select("slug as key","name","option_group","custom")
			->where('field_category_id', $args['slug'])
			->where("status","1")
		;

		if($this->get('settings')['licensing']['isApprentix']){
			$formType->where('isApprentix',"!=","1");
		}

		 $formType=$formType->get();
		 //TODO:ILR fields need to work on this
		if($args['slug']==1 && ($this->get('settings')['licensing']['isApprentix'])) {
			$ilr = $this->get('settings')['ilr_fields'];
			$ilr_constant = ['', 'Accom', 'CampId', 'OTJHours', 'PlanLearnHours', 'PlanEEPHours', 'DPOutcome', 'employment-statuses', 'UKPRN', 'LearnRefNumber', 'PrevUKPRN', 'PrevLearnRefNumber', 'ULN', 'exclude_from_ilr_export', 'PostcodePrior','ContactPreference', 'LLDDHealthProb','MathGrade', 'EngGrade'];
			foreach ($ilr as $key => $field) {
				if (false != array_search($key, $ilr_constant)) {
					$itr_row = [
						"key" => $key,
						"name" => $field['name'],
						"option_group" => "ILR"
					];
					$formType->push($itr_row);
				}
//            foreach ($ilr->templates as $key => $value) {
//                $value->template = json_decode($value->template, true);
//            }

			}
		}
		$response
			->getBody()
			->write(json_encode($formType))
		;
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	$group->get('/category',function (Request $request,Response $response){
		$category= \Models\FieldCategory::where('status',true)->get();

		$hideLearningProgrammes = \APP\Tools::getConfig('HideCustomFieldLearningProgrammes');
		$hideLearningResource = \APP\Tools::getConfig('HideCustomFieldLearningResource');
		$hideLessons = \APP\Tools::getConfig('HideCustomFieldLessons');

		$category = $category->reject(function ($item) use ($hideLearningProgrammes, $hideLearningResource, $hideLessons) {
		return ($hideLearningProgrammes && $item->slug == 'programme') ||
			($hideLearningResource && $item->slug == 'learning_resource') ||
			($hideLessons && $item->slug == 'lesson');
		});

		$response
			->getBody()
			->write(\GuzzleHttp\json_encode($category))
		;
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));

	// get all users from standard using pagination
	$group->post('/users/{form:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		if (isset($params["search"]["form_id"])) {
			unset($params["search"]["form_id"]);
		}

		$query = \Models\User::selectRaw("users.*")
			->where("users.status", ">", "0")
			->with(['UserForms' => function($query) use ($args) {
				$query
					->where("form_id",$args["form"])
					->where("type","direct")
					;
				;
			}])
			->select('id', 'fname', 'lname', 'email');
		if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
			$query->whereHas('UserForms', function ($query) use ($args) {
				$query
					->where("form_id",$args["form"]);
				;
			});
			unset($params["search"]["show_assigned"]);
		}

		// if (\APP\Auth::isManager() && !\APP\Auth::isAdmin() && !\APP\Auth::accessAllLearners()) {
		// 	$query = $query
		// 		->whereIn("users.id", function($query) {
		// 			$query
		// 				->select("user_id")
		// 				->from("manager_users")
		// 				->where("manager_id", "=", \APP\Auth::getUserId())
		// 			;
		// 		})
		// 	;
		// }

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));


	// get all departments from standard using pagination
	$group->post('/departments/{form:[0-9]+}', function (Request $request, Response $response, $args) {
try{
		$params = $request->getParsedBody();

		if (isset($params["search"]["form_id"])) {
			unset($params["search"]["form_id"]);
		}

		$query = \Models\Department
			::where("departments.status", 1)
			->with(["company" => function($query)  {
				$query->select(["companies.id", "companies.name"]);
			}])
			->with(['DepartmentForms' => function($query) use ($args) {
				$query
					->where("form_id",$args["form"]);
				;
			}])
		;

		if (isset($params["search"]) && isset($params["search"]["show_assigned"])) {
			$query->whereHas('DepartmentForms', function ($query) use ($args) {
				$query
					->where("form_id",$args["form"]);
				;
			});
			unset($params["search"]["show_assigned"]);
		}

		// if (
		// 	\APP\Auth::isManager() &&
		// 	!\APP\Auth::isAdmin() &&
		// 	!\APP\Auth::accessAllLearners()
		// ) {
		// 	$query = $query
		// 		->whereIn("departments.id",
		// 			\Models\ManagerDepartment
		// 				::select('department_id')
		// 				->where("manager_id", \APP\Auth::getUserId())
		// 				->get()
		// 		)
		// 	;
		// }

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		}catch(\Exception $e){
			print_r($e->getMessage());die;
		}

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));

	// Get all groups from standard
	$group->post('/groups/{form_id:[0-9]+}', function (Request $request, Response $response, $args) {
try{
		$params = $request->getParsedBody();

		if (isset($params["search"]["form_id"])) {
			unset($params["search"]["form_id"]);
		}

		$query = \Models\Group
			::where("groups.status", 1)
			->with(['GroupForms' => function($query) use ($args) {
				$query
					->where("form_id",$args["form_id"]);
				;
			}])
		;

		if (
			isset($params["search"]) &&
			isset($params["search"]["show_assigned"])
		) {
			$query->whereHas('GroupForms', function($query) use ($args) {
				$query
					->where("form_id",$args["form_id"]);
				;
			});
			unset($params["search"]["show_assigned"]);
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		}catch(\Exception $e){
			print_r($e->getMessage());
		}

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'select'));



	// Assign user to standard
	$group->put("/{form_id:[0-9]+}/user/{user_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		try{
			$data = $request->getParsedBody();

			$passData=['user_id'=>$args['user_id'],
				'form_id'=>$args['form_id'],
				'type'=>'direct',
				'type_id'=>NULL
			];
			$form=\Models\Form::find($args['form_id']);
			$userAssignedForm=\Models\UserForm::assignUserToForms($passData,$this->get('settings'));

			if(!$userAssignedForm){
				$response
					->getBody()
					->write(json_encode(["status"=>false,
						"form" => $form,
						'formattedFormName' => $form?$form->name." ".\Carbon\Carbon::now()->format(\APP\Tools::getConfig('defaultDateFormat')):null
					]))
				;
				return $response
					->withHeader('Content-Type', 'application/json')
				;
			}

			$user = \Models\User::find($args['user_id']);
			$form = \Models\Form::find($args['form_id']);
			if($user && $form && !$form->has_sign_off_order){
				// \Models\UserForm::sendFormAssignMailToManagers($user, $form);
			}
			$relationArr= [
				'user_form_id' => $userAssignedForm->id,
				'type' => 'direct',
				'assigned_method' => 'form_direct_assign'
			];
			\Models\UserFormTemplateWorkflowRelations::addUserFormTemplateWorkFlow($relationArr);

			return
				$response
			;
		}catch(\Exception $e){
			print_r($e->getMessage());
		}

	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Self assign user to standard
	$group->post("/{form_id:[0-9]+}/user/{user_id:[0-9]+}/self", function (Request $request, Response $response, $args) {
		try {

			if (\APP\Auth::isLearner()) {
				$args["user_id"] = \APP\Auth::getUserId();
			}

			$data = $request->getParsedBody();

			$form = Form::where('id', $args['form_id'])->where('has_learner_to_assign', 1)->first();
			if (!$form) {
				$response
					->getBody()
					 ->write(json_encode(['status' => 'failed', 'msg' => "Form is not self assignable"]))
				;
				return $response->withHeader('Content-Type', 'application/json');
			}

			$slug = \APP\Tools::safeName($form->name);
			$formCount = \Models\Form::where("slug", 'like', '%' . $slug . '%')->count() + 1;

			$form_clone = $form->replicate()->fill([
				'name' => $data['form_name'],
				'slug' => $slug . $formCount,
				'display_in_list' => $data['assignStatus'] ? 0 : 1, //Forms that are duplicated from user programs are not to be listed in drop down
				'parent_id' => $form->id
			]);
			$form_clone->save();

			/*Replicate Form Field*/
			$form_fields = $form->FormField;
			if (count($form_fields) > 0) {
				foreach ($form_fields as $field) {
					$form_field = \Models\FormField::find($field->id);

					if ($form_field) {
						$form_field_clone = $form_field->replicate()->fill([
							'form_id' => $form_clone->id,
						]);
						$form_field_clone->save();
						$form_field_auth_permissions = $form_field->FormFieldAuthorPermission;
						if (count($form_field_auth_permissions) > 0) {
							// remove the array as permission should be a array
							// $form_field_auth_permissions = array($form_field_auth_permissions);
							foreach ($form_field_auth_permissions as $permission) {
								$form_field_auth_permission = \Models\FormFieldAuthorPermission::find($permission->id);
								if ($form_field_auth_permission) {
									$form_field_auth_permission_clone = $form_field_auth_permission->replicate()->fill([
										'form_field_id' => $form_field_clone->id,
									]);
									$form_field_auth_permission_clone->save();
								}
							}
						}
					}
				}
			}

			/*Replicate Form Sign Off Roles*/
			$form_signoff_roles = $form->FormSignOffRoles;
			if (count($form_signoff_roles) > 0) {
				foreach ($form_signoff_roles as $role) {
					$form_signoff_role = \Models\FormSignoffRole::find($role->id);
					if ($form_signoff_role) {
						$form_signoff_role_clone = $form_signoff_role->replicate()->fill([
							'form_id' => $form_clone->id,
						]);
						$form_signoff_role_clone->save();
					}
				}
			}

			$passData = [
				'user_id' => $args['user_id'],
				'form_id' => $form_clone->id,
				'type' => 'direct',
				'type_id' => NULL,
			];
			$userAssignedForm = \Models\UserForm::assignUserToForms($passData, $this->get('settings'));

			if (!$userAssignedForm) {
				$response
					->getBody()
					->write(json_encode(["status" => false]))
				;
				return $response->withHeader('Content-Type', 'application/json');
			}

			$user = \Models\User::find($args['user_id']);
			$form = \Models\Form::find($args['form_id']);
            if ($user && $form && !$form->has_sign_off_order) {
				\Models\UserForm::sendFormAssignMailToManagers($user, $form);
			}
			$relationArr = [
				'user_form_id' => $userAssignedForm->id,
				'type' => 'direct',
				'assigned_method' => 'form_direct_assign'
			];
			\Models\UserFormTemplateWorkflowRelations::addUserFormTemplateWorkFlow($relationArr);

			$response
				->getBody()
				->write(json_encode(['user_form' => $userAssignedForm, 'form' => $form_clone]))
			;
			return $response->withHeader('Content-Type', 'application/json');
		} catch (\Exception $e) {
			print_r($e->getMessage());
			die;
		}
	})->add(\APP\Auth::getSessionCheck());

	$group->put("/{form_id:[0-9]+}/department/{department_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$userAssignedForm=new \Models\DepartmentForm();
		$userAssignedForm->department_id=$args['department_id'];
		$userAssignedForm->form_id=$args['form_id'];
		$userAssignedForm->status="1";
		$userAssignedForm->save();

		// Get all department users.
		$department = \Models\Department
			::where('id', $args["department_id"])
			->where('status', true)
			->with(["employees" => function($query)  {
				$query
					->where('status', true)
				;
			}])
			->first()
		;


		// Huge resource problem, need to optimise down the line
		if ($department) {
			$d_user_ids = \Models\User::getUserIdsByManagerAndDepartment(\APP\Auth::getUserId(), $department->id, \APP\Auth::isAdmin());

			foreach ($d_user_ids as $employee_id) {
				$userFormData=\Models\UserForm::where("user_id",$employee_id)->where("form_id",$args["form_id"])->first();
				if(!$userFormData){
					$userForm=new \Models\UserForm();
					$userForm->user_id=$employee_id;
					$userForm->form_id=$args['form_id'];
					$userForm->status="1";
					$userForm->save();
				}
			}
		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	$group->put("/{form_id:[0-9]+}/group/{group_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();

		$userAssignedForm=new \Models\GroupForm();
		$userAssignedForm->group_id=$args['group_id'];
		$userAssignedForm->form_id=$args['form_id'];
		$userAssignedForm->status="1";
		$userAssignedForm->save();

		$group = \Models\Group
			::where('id', $args["group_id"])
			->where('groups.status', true)
			->with(["users" => function($query)  {
				$query
					->where('users.status', true)
				;
			}])
			->first()
		;
		if ($group) {
			$g_user_ids = \Models\GroupUser::getUserIdsByManagerAndGroup(\APP\Auth::getUserId(), $group->id, \APP\Auth::isAdmin());
			foreach ($g_user_ids as $user_id) {
				$userFormData=\Models\UserForm::where("user_id",$user_id)->where("form_id",$args["form_id"])->first();
				if(!$userFormData){
					$userForm=new \Models\UserForm();
					$userForm->user_id=$user_id;
					$userForm->form_id=$args['form_id'];
					$userForm->status="1";
					$userForm->save();
				}
			}
		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// Remove user from standard
	$group->delete("/{form_id:[0-9]+}/user/{user_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		try{
			$data = $request->getParsedBody();
			$userFormData=\Models\UserForm::where("user_id",$args["user_id"])->where("form_id",$args["form_id"])->first();
			if ($userFormData) {
				$user_form_id = $userFormData->id;
				$userFormData->delete();
				\Models\UserCustomFormValue::where("id",$userFormData->user_custom_form_value_id)->delete();
				\Models\UserFormTemplateWorkflowRelations::where([['user_form_id', '=', $user_form_id],['type', '=', 'direct'],['assigned_method', '=', 'form_direct_assign']])->delete();
			}
			return $response;
		}catch(\Exception $e){
			print_r($e->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// Remove department and users from standard
	$group->delete("/{form_id:[0-9]+}/department/{department_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$response=\Models\DepartmentForm::where("form_id",$args["form_id"])->where("department_id",$args["department_id"])
		->delete();
		$department = \Models\Department
			::where('id', $args["department_id"])
			->where('status', true)
			->with(["employees" => function($query)  {
				$query
					->where('status', true)
				;
			}])
			->first()
		;
		if($department){
			$d_user_ids = \Models\User::getUserIdsByManagerAndDepartment(\APP\Auth::getUserId(), $department->id, \APP\Auth::isAdmin());

			foreach ($d_user_ids as $employee_id) {
				$userFormData=\Models\UserForm::where("user_id",$employee_id)->where("form_id",$args["form_id"])->first();
				$userFormData->delete();
				\Models\UserCustomFormValue::where("id",$userFormData->user_custom_form_value_id)->delete();
			}
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));


	// Remove group and users from standard
	$group->delete("/{form_id:[0-9]+}/group/{group_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		\Models\GroupForm::where("form_id",$args["form_id"])->where("group_id",$args["group_id"])->delete();
		$group = \Models\Group
			::where('id', $args["group_id"])
			->where('groups.status', true)
			->with(["users" => function($query)  {
				$query
					->where('users.status', true)
				;
			}])
			->first()
		;

		if ($group) {
			$g_user_ids = \Models\GroupUser::getUserIdsByManagerAndGroup(\APP\Auth::getUserId(), $group->id, \APP\Auth::isAdmin());
			foreach ($g_user_ids as $user_id){
				$userFormData=\Models\UserForm::where("user_id",$user_id)->where("form_id",$args["form_id"])->first();
				if ($userFormData) {
					$userFormData->delete();
					\Models\UserCustomFormValue::where("id",$userFormData->user_custom_form_value_id)->delete();
				}
			}
		}
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'disable'));

	// Get all groups from standard
	$group->post('/user-forms/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		try {
				if (!\Models\Role::getRoleParam('lfp_show_assigned_forms')) return $response->withStatus(403)->withHeader('Content-Type', 'text/html');

				if (\APP\Auth::isLearner()) {
					$args["user_id"] = \APP\Auth::getUserId();
				}

				$user_id=$args["user_id"];
				$params = $request->getParsedBody();
				unset($params['search']['refresh']);


				$query=\Models\UserForm::
				select("forms.*", "forms.id AS form_id_number", "user_forms.*","user_forms.id AS id", "user_forms.id AS user_form_id","schedules.name AS schedule_name",
				"schedules.type AS schedule_type","event_types.name AS event_type_name",
				"learning_modules.name AS learning_module_name", 'user_forms.updated_at as completed_date')
				->join("forms", function($join) {
					$join
						->on("forms.id", "=", "user_forms.form_id")
						->where("forms.status", "=", 1)
					;

				})
				->leftjoin("schedules", function($join) {
					$join
						->on("schedules.id", "=", "user_forms.type_id")
						->where("user_forms.type", "=", 'schedule')
					;
				})

				->leftjoin("learning_modules", function($join) {
					$join
						->on("learning_modules.id", "=", "user_forms.type_id")
						->where("user_forms.type", "=", 'lesson')
					;
				})

				->leftjoin("event_types", function($join) {
					$join
						->on("schedules.type", "=", "event_types.slug")
					;
				})
				->with(['Form'=>function($query){
					$query->with('FormSignOffRoles.role');
					if (\APP\Auth::isLearner()) {
						$query->where('forms.system_form', 0);
					}
				}])
				->with(['formLearningModule' => function($query) {
					$query->select('learning_modules.id', 'learning_modules.name')
						->join('user_forms', 'user_forms.type_id', '=', 'learning_modules.id')
						->where('user_forms.type', 'learning_module');
				}])
				// ->join('users', function($join) {
				// 	$join
				// 		->on("users.id", "=", "user_forms.user_id")
				// 		->where("users.status", "=", 1)
				// 	;
				// })
				->with('scheduleLinkManagers')
				->where("user_id",$user_id);

				if (\APP\Auth::isLearner()) {
					$query->where('forms.system_form', 0);
				}

                if (\APP\Auth::isManager() && !\APP\Auth::isAdmin() && !\APP\Auth::accessAllLearners()) {
                    // for restricted forms - if manager is assigned in the event then only able to see those forms
                    $query = $query
                        ->where(
                            function ($query) {
                                $query
                                    ->where(function ($query) {
                                        $query->where(function ($subquery) {
                                            $subquery->whereExists(function ($existsQuery) {
                                                $existsQuery->select(DB::raw(1))
                                                    ->from('schedule_links')
                  																	->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
									                                  ->where('user_forms.type','schedule')
                                                    ->where('link_id', \APP\Auth::getUserId())
                                                    ->whereNull('deleted_by')
                                                    ->where('schedule_links.type', 'managers')
                                                    ->whereNull('schedule_links.deleted_at');
                                            })
                                            ->where(function ($innerSubquery) {
                                                $innerSubquery->whereExists(function ($nestedSubquery) {
                                                    $nestedSubquery->from('schedules')
                                                        ->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
                    																		->whereColumn('user_forms.type_id', '=', 'schedules.id')
                                                        ->where('user_forms.type', 'schedule')
                                                        ->where('forms.restricted_form', 1);
                                                });
                                            });
                                        })
                                            ->orWhere(function ($subquery) {
                                                $subquery->whereNotExists(function ($notExistsQuery) {
                                                    $notExistsQuery->select(DB::raw(1))
                  																			->from('schedule_links')
									                                    	->where('user_forms.type','schedule')
                                                        ->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
                                                        ->where('schedule_links.type', 'managers');
                                                })
                                                    ->whereIn('user_forms.user_id', function ($whereInQuery) {
                                                        $whereInQuery->select('user_id')
                                                            ->from('manager_users')
                                                            ->where('manager_id', \APP\Auth::getUserId())
															->whereNull('manager_users.deleted_at');
                                                    });
                                            })

                                            ->orWhere(function ($innerSubquery) {
                                                $innerSubquery->whereExists(function ($nestedSubquery) {
                                                    $nestedSubquery->from('schedules')
                                                        ->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
                                                        ->whereColumn('user_forms.type_id', '=', 'schedules.id')
                                                        ->where('user_forms.type', 'schedule')
                                                        ->where('forms.restricted_form', 0);
                                                })
                                                    ->whereIn('user_forms.user_id', function ($query) {
                                                        $query->select('user_id')
                                                            ->from('manager_users')
                                                            ->where('manager_id', \APP\Auth::getUserId())
															->whereNull('manager_users.deleted_at');
                                                    });
                                            })
                                        ;


									});
							});
				}

				// if (\APP\Auth::isManager() && \APP\Auth::isAdmin() && $user_id == \APP\Auth::getUserId()) {
				//   $query
				// 		->orwhereIn("user_id", function($query) use ($user_id) {
				// 			$query
				// 				->select("user_id")
				// 				->from("manager_users")
				// 				->where("manager_id", "=", $user_id)
				// 			;
				// 		})
				// 		//Here check the managers include schedule
				// 		->orWhereHas('scheduleLinkManagers',function ($query) use ($params,$user_id) {
				// 			$query
				// 				->where("link_id",$user_id)
				// 			;
				// 		})
				// 	;

				// }

				// if (isset($params["search"])) {
				// 	if (isset($params["search"]["schedule_name"])) {
				// 		$query->
				// 			where('schedules.name', 'LIKE', '%'.$params["search"]["schedule_name"].'%');
				// 		unset($params["search"]["schedule_name"]);
				// 	}
				// 	if (isset($params["search"]["refresh"])) {
				// 		unset($params["search"]["refresh"]);
				// 	}
				// }



				$p = \APP\SmartTable::searchPaginate($params, $query);

				$safe_thumbnail = $this->get('settings')['LMSUrl'] . 'images/default_type/form.jpg';
				$custom_safe_thumbnail = $this->get('settings')['LMSDefaultTypeCustomPath'] . 'form.jpg';
				if (is_file($custom_safe_thumbnail)) {
					$safe_thumbnail = $this->get('settings')['LMSUrl'] . 'images/default_type_custom/form.jpg';
				}

				foreach ($p as $key => $value) {
					if($value->type == 'work_flow'){
						$workflow = \Models\UserForm::select('fw.*')
													->join('form_workflow as fw', function($join){
														$join->on('user_forms.type_id', 'fw.id')
															 ->where('user_forms.type', 'work_flow');
													})
													->where('user_forms.id', $value->id)
													->first();

						$value->workflow = $workflow;
					}else{
						$value->workflow = '';
					}

					if($value->type == 'programme'){
						$programme = \Models\UserForm::select('aps.*')
													->join('apprenticeship_standards as aps', function($join){
														$join->on('user_forms.type_id', 'aps.id')
															 ->where('user_forms.type', 'programme');
													})
													->where('user_forms.id', $value->id)
													->first();

						$value->programme = $programme;
					}else{
						$value->programme = '';
					}

					$assigned_workflow = \Models\UserWorkflowForm::join('form_workflow as fw', 'fw.id', 'user_workflow_form.form_workflow_id')
																 ->where([['user_workflow_form.type_id', '=', $value->type_id], ['user_workflow_form.user_id', '=', $value->user_id], ['user_workflow_form.type', '=', $value->type]])
																 ->select('fw.*')
																 ->first();

					$value->assigned_workflow = $assigned_workflow;
					$value->safe_thumbnail = $safe_thumbnail;
					if (
						$value->thumbnail &&
						is_file($this->get('settings')['LMSPublicPath'] . 'images/thumbnails/' . $value->thumbnail)
					) {
						$value->safe_thumbnail = $this->get('settings')['LMSUrl'] . 'images/thumbnails/' . $value->thumbnail;
					}
				}

				$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

				}catch(\Exception $e){
					print_r($e->getMessage());
				}


			})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


	// get forms of the workflow of the user
	$group->get('/user-forms/{user_id:[0-9]+}/{form_workflow_id:[0-9]+}', function(Request $request, Response $response, array $args) {

		$user_id = $args["user_id"];
		$formWorkflowId = $args['form_workflow_id'];

		$query=\Models\UserForm::query()
			->select("forms.*","user_forms.*","user_forms.id AS id", "user_forms.id AS user_form_id",
				"schedules.name AS schedule_name",
			"schedules.type AS schedule_type","event_types.name AS event_type_name",
			"learning_modules.name AS learning_module_name", 'user_forms.updated_at as completed_date')
			->whereHas('UserWorkflowForm', function ($query) use ($formWorkflowId) {
				$query->whereHas('form_workflow', function($query) use ($formWorkflowId) {
					$query->where('id', '=', $formWorkflowId);
				});
			})
			->with(['UserWorkflowForm' => function($query) use ($formWorkflowId) {
				$query->with(['form_workflow' => function($query) use ($formWorkflowId) {
					$query->where('id', '=', $formWorkflowId);
				}]);
			}])
			->join("forms", function($join) {
				$join
					->on("forms.id", "=", "user_forms.form_id")
					->where("forms.status", "=", 1)
				;

			})
			->leftjoin("schedules", function($join) {
				$join
					->on("schedules.id", "=", "user_forms.type_id")
					->where("user_forms.type", "=", 'schedule')
				;
			})
			->leftjoin("learning_modules", function($join) {
				$join
					->on("learning_modules.id", "=", "user_forms.type_id")
					->where("user_forms.type", "=", 'lesson')
				;
			})
			->leftjoin("event_types", function($join) {
				$join
					->on("schedules.type", "=", "event_types.slug")
				;
			})
			->with(['Form'=>function($query){
				$query->with('FormSignOffRoles.role');
				if (\APP\Auth::isLearner()) {
					$query->where('forms.system_form', 0);
				}
			}])
			->with('scheduleLinkManagers')
			->where("user_id", '=', $user_id);

		if (\APP\Auth::isLearner()) {
			$query->where('forms.system_form', 0);
		}

		$userWorkflowForm = \Models\UserWorkflowForm::getUserWorkflowForm($user_id, $formWorkflowId);

		$data = ['user_forms' => $query->get(), 'user_workflow_form' => $userWorkflowForm];

//        $query->userWorkflowForm = $userWorkflowForm;

//        $data = $query->get();

//        if (\APP\Auth::isManager() && !\APP\Auth::isAdmin() && !\APP\Auth::accessAllLearners()) {
//            // for restricted forms - if manager is assigned in the event then only able to see those forms
//            $query = $query
//                ->where(
//                    function ($query) {
//                        $query
//                            ->where(function ($query) {
//                                $query->where(function ($subquery) {
//                                    $subquery->whereExists(function ($existsQuery) {
//                                        $existsQuery->select(DB::raw(1))
//                                            ->from('schedule_links')
//                                            ->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
//                                            ->where('user_forms.type','schedule')
//                                            ->where('link_id', \APP\Auth::getUserId())
//                                            ->whereNull('deleted_by')
//                                            ->where('schedule_links.type', 'managers')
//                                            ->whereNull('schedule_links.deleted_at');
//                                    })
//                                        ->where(function ($innerSubquery) {
//                                            $innerSubquery->whereExists(function ($nestedSubquery) {
//                                                $nestedSubquery->from('schedules')
//                                                    ->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
//                                                    ->whereColumn('user_forms.type_id', '=', 'schedules.id')
//                                                    ->where('user_forms.type', 'schedule')
//                                                    ->where('forms.restricted_form', 1);
//                                            });
//                                        });
//                                })
//                                    ->orWhere(function ($subquery) {
//                                        $subquery->whereNotExists(function ($notExistsQuery) {
//                                            $notExistsQuery->select(DB::raw(1))
//                                                ->from('schedule_links')
//                                                ->where('user_forms.type','schedule')
//                                                ->whereColumn('user_forms.type_id', 'schedule_links.schedule_id')
//                                                ->where('schedule_links.type', 'managers');
//                                        })
//                                            ->whereIn('user_forms.user_id', function ($whereInQuery) {
//                                                $whereInQuery->select('user_id')
//                                                    ->from('manager_users')
//                                                    ->where('manager_id', \APP\Auth::getUserId());
//                                            });
//                                    })
//
//                                    ->orWhere(function ($innerSubquery) {
//                                        $innerSubquery->whereExists(function ($nestedSubquery) {
//                                            $nestedSubquery->from('schedules')
//                                                ->leftJoin('forms', 'forms.id', '=', 'user_forms.form_id')
//                                                ->whereColumn('user_forms.type_id', '=', 'schedules.id')
//                                                ->where('user_forms.type', 'schedule')
//                                                ->where('forms.restricted_form', 0);
//                                        })
//                                            ->whereIn('user_forms.user_id', function ($query) {
//                                                $query->select('user_id')
//                                                    ->from('manager_users')
//                                                    ->where('manager_id', \APP\Auth::getUserId());
//                                            });
//                                    })
//                                ;
//
//
//                            });
//                    });
//        }


		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get('/programs/{user_id:[0-9]+}/{program_id:[0-9]+}', function (Request $request, Response $response, array $args): Response {
		try {
			// If user is a learner, force their user_id
			if (\APP\Auth::isLearner()) {
				$args["user_id"] = \APP\Auth::getUserId();
			}

			// Query for the program
			$program = \Models\ApprenticeshipStandardUser::select(
					'completion_status as status_programme',
					'apprenticeship_standards_users.start_at',
					'apprenticeship_standards_users.paused',
					'apprenticeship_standards_users.paused_start',
					'apprenticeship_standards_users.paused_end',
					'apprenticeship_standards_users.working_hours',
					'apprenticeship_standards_users.due_at',
					'apprenticeship_standards.completion_months',
					'apprenticeship_standards_users.time_spent',
					'apprenticeship_standards_users.percentage',
					'apprenticeship_standards_users.criteria_completion'
				)
				->leftJoin('apprenticeship_standards', function($query) {
					$query->on('apprenticeship_standards.id', '=', 'apprenticeship_standards_users.standard_id');
				})
				->where([
					'standard_id' => $args['program_id'],
					'user_id'     => $args['user_id']
				])
				->first();


			// If not found, return 404
			if (!$program) {
				$response->getBody()->write(json_encode(['error' => 'Program not found']));

				return $response
					->withStatus(404)
					->withHeader('Content-Type', 'application/json');
			}

			// Return found program as JSON
			$response
				->getBody()
				->write($program->toJson())
			;
			return $response
				->withHeader('Content-Type', 'application/json')
			;

		} catch (\Exception $e) {
			$response->getBody()->write(json_encode(['error' => $e->getMessage()]));

			return $response
				->withStatus(500)
				->withHeader('Content-Type', 'application/json')
			;
		}

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	 $group->get('/list[/{filter}]',function(Request $request,Response $response, $args){
        $params = $request->getQueryParams();
	FormField::$ilr= $this->get('settings')['ilr_fields'];
	 $filter = isset($args["filter"])?$args["filter"]:'ss';
       $forms = Form::where('status', 1)
           ->where('display_in_list', '=', 1);
        if($params && !isset($params['all'])){
            $forms->nonArchived();
        }
		 if ($filter == 'parent-list-basic') {
      $forms->select('name', 'id','is_archived');
      $forms->where('is_archived',0);
		 } else {
			 $forms->with(['FormField' => function ($query) {
				 $query->with('Field');
			 }])
				 ->with('FormSignOffRoles.role')
               ->select('name', 'id', 'system_form','is_archived');
		 }
		 $response
			->getBody()
			->write(json_encode($forms->get()))
		 ;
		 return $response->withHeader('Content-Type','application/json');
	 })->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'select'));


	$group->get('/events/{user_id:[0-9]+}/{event_id:[0-9]+}', function (Request $request, Response $response, $args) {
		try{

			if (\APP\Auth::isLearner()) {
				$args["user_id"] = \APP\Auth::getUserId();
			}

			$events=ScheduleLink::select(
			'is_authorised',
			'completed_at',
			'authorisation_notes',
			'completion_status AS status_event'
				)
		 ->where(['schedule_id'=>$args['event_id'],'link_id'=>$args['user_id'],'type'=>'users'])->first();

		 //Fetch event
		// $modelFormField= /Models/FormField::where("category_id","2")->where()

		//  $custom_field_model=/Models/CustomFieldQuery::where('')''
		//  if()
		 $response
			->getBody()
			->write($events)
		 ;
		 return $response
				->withHeader('Content-Type', 'application/json');
		}catch(\Exception $e){
			print_r($e->getMessage());
		}

		})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

		$group->get('/schedules/{event_id:[0-9]+}', function (Request $request, Response $response, $args) {

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));



	$group->put('/form-duplicate/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		try{
			$data = $request->getParsedBody();
		$form = \Models\Form::find($args["id"]);
		$slug = \APP\Tools::safeName($form->name);
		$formCount=\Models\Form::where("slug", 'like', '%'.$slug.'%')->count() + 1;
			/*If assignStatus is true*/

		$form_clone = $form->replicate()->fill(
			[
				'name' => ($data['assignStatus'] && isset($data['formName']) && !empty($data['formName'])) ? $data['formName']:"copy of ".$form->name,
				'slug'=> $slug.$formCount,
				'display_in_list' => $data['assignStatus'] ? 0:1, //Forms that are duplicated from user programs are not to be listed in drop down
				'parent_id' => $args["id"]
			]
		);

		$form_clone->save();

		/*Replicate Form Field*/
		$form_fields= $form->FormField;
		if(count($form_fields)>0){
			foreach ($form_fields as $field){
				$form_field =  \Models\FormField::find($field->id);

				if($form_field){
					$form_field_clone = $form_field->replicate()->fill(
						[
							'form_id' => $form_clone->id,
						]
					);
					$form_field_clone->save();

					$form_field_auth_permissions=$form_field->FormFieldAuthorPermission;

					if(count($form_field_auth_permissions)>0){

						// remove the array as permission should be a array
						// $form_field_auth_permissions = array($form_field_auth_permissions);

						foreach ($form_field_auth_permissions as $permission){
							$form_field_auth_permission =  \Models\FormFieldAuthorPermission::find($permission->id);
							if($form_field_auth_permission){
								$form_field_auth_permission_clone = $form_field_auth_permission->replicate()->fill(
									[
										'form_field_id' => $form_field_clone->id,
									]
								);
								$form_field_auth_permission_clone->save();
							}
						}
					}
				}
			}
		}

		/*Replicate Form Sign Off Roles*/
		$form_signoff_roles= $form->FormSignOffRoles;
		if(count($form_signoff_roles)>0){
			foreach ($form_signoff_roles as $role){
				$form_signoff_role =  \Models\FormSignoffRole::find($role->id);
				if($form_signoff_role){
					$form_signoff_role_clone = $form_signoff_role->replicate()->fill(
						[
							'form_id' => $form_clone->id,
						]
					);
					$form_signoff_role_clone->save();
				}
			}
		}

		$response
			->getBody()
			->write(
				json_encode(
					[
						'l_name' => $form_clone->name,
						'l_id' => $form_clone->id,
					]
				)
			)
		;
		return $response->withStatus(200)
			->withHeader('Content-Type', 'text/html');
	}catch(\Exception $e){
		print_r($e->getMessage());
	}
	});

	$group->put('/user_form/{status:[\/a-z]*}/{user_form_id:[0-9]+}', function (Request $request, Response $response, $args) {
		if(
			$args['status'] == 'disable' &&
			\Models\UserForm::find($args["user_form_id"])
		) {
			\Models\UserForm::where('id', $args["user_form_id"])->update(['status' => 0]);
			\Models\UserForm::find($args["user_form_id"])->delete();
			$note = 'User form disabled';
		} else if (\Models\UserForm::where('id', $args["user_form_id"])->withTrashed()->first()) {
			\Models\UserForm::where('id', $args["user_form_id"])->withTrashed()->update(['deleted_at' => null, 'status' => 1]);
			$note = 'User form enabled';
		}

		// -----Adding log------------
		$user_form = \Models\UserForm::withTrashed()->find($args["user_form_id"]);
		if (!$user_form) {
			return \APP\Tools::returnCode($request, $response, '404');
		}
		$user = \Models\User::find($user_form->user_id);
		$data_arr = [
			'user_id' => $user_form->user_id,
			'username' => $user ? $user->username : '',
			'learner_name' => $user ? $user->fname .' '. $user->lname : '',
			'form_id' => $user_form->form_id,
			'user_form_id' => $args["user_form_id"],
			'form_field' => null,
			'edited_by_user' => \APP\Auth::getUserId() ?? 0,
			'date_time' => date("Y-m-d h:i:s", time()),
			'notes' => $note
		];
		\Models\FormLog::addEntry($data_arr);
		// ---------------------------

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck(['standards-and-other-programmes', 'trainee-standards'], 'disable'));

	#====================save form====================
	$group->post('/save-form/{user_form_id:[0-9]+}',function (Request $request,Response $response,$args){
		try{
		$data = $request->getParsedBody();
		// $signature=null;
		// if(array_key_exists('signature',$data))
		// {
		// $signature=$data['signature'];
		// unset($data['signature']);
		// }

		//return $args['user_id'];
		 $user_form=\Models\UserForm::where("id",$args['user_form_id'])
		 ->with('Form')
		// ->where("form_id",$args['form_id'])
		->first();
		$user=\APP\Auth::getUser();
		$user_form->snooze_target_date = isset($data['snooze_target_date'])?$data['snooze_target_date']:NULL;
		$user_form->save();
		//Here chekc the user form  and userform value and update or save
		if($user_form->user_custom_form_value_id!=null){
			$user_custom_from_value=UserCustomFormValue::where('id',$user_form->user_custom_form_value_id)->first();
			$user_custom_from_value->values=$data;
			$user_custom_from_value->save();
		}else{
		$user_custom_from_value=new UserCustomFormValue();
			$user_custom_from_value->values=$data;
			//Check Admin or Manager
			$user_custom_from_value->save();
			$user_form->user_custom_form_value_id=$user_custom_from_value->id;
			$user_form->save();
		}

		if(!\APP\Auth::isLearner()){


		}

		if(\APP\Auth::isLearner()){


		}else{
			$user=\Models\User::find($user_form['user_id']);
		}




	// if(\APP\Auth::isAdmin() || \APP\Auth::isManager() || \APP\Auth::accessAllLearners()){
	//         \APP\Form::saveToMaster($user_form->form,$data,$user);
	// 	}
		$response
			->getBody()
			->write("done")
		;
		return $response;
	}catch(\Exception $e){
		print_r($e->getMessage());
	}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources','trainee-standards'], 'select'));
	#====================save form====================

	$group->get('/sign-off-details/{user_form_id:[0-9]+}', function (Request $request, Response $response,$args) {
		try {
			$data = \Models\UserForm::whereHas('Form.FormSignOffRoles')
									->with(['Form.FormSignOffRoles.Role', 'UserFormSignoff'])
									->find($args['user_form_id']);
			if($data){
				$response->getBody()->write($data->toJson());
		return $response->withHeader('Content-Type', 'application/json');

			}else{
				return \APP\Tools::returnCode($request, $response, '204');
			}
		} catch (Exception $exception) {
			// dd($exception->getMessage());
			return \APP\Tools::returnCode($request, $response, '500');
		}

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get('/amend-signoff-data', function () {
		$user_forms = \Models\UserForm::whereHas('Form.FormSignOffRoles')
										->with(['UserFormSignOff', 'Form.FormSignOffRoles.role'])
										->withCount('UserFormSignOff')
										->get();

		foreach ($user_forms as $user_form) {
			if($user_form->UserFormSignOff){
				$role_ids = $user_form->Form->FormSignOffRoles->pluck('role_id')->toArray() ?? [];
				$delete_status = false;
				$user_signoff_ids = [];
				foreach ($user_form->UserFormSignOff as  $user_form_signoff) {
					if($user_form_signoff->signoff_role == null || ! in_array($user_form_signoff->signoff_role, $role_ids)){
						\Models\UserFormSignOff::where('id', $user_form_signoff->id)->delete();
						$delete_status = true;
					}

					if($user_form_signoff->signoff_role != null){
						$user_signoff_ids[] = $user_form_signoff->signoff_role;
					}
				}

				if($delete_status){
					\Models\UserForm::where('id', $user_form_signoff->user_form_id)->update(['user_form_status' => 'In Progress']);
				}
			}

			$roles_count = $user_form->Form->FormSignOffRoles ?  $user_form->Form->FormSignOffRoles->count() : 0;
			$signed_roles_count = $user_form->user_form_sign_off_count;

			if(($roles_count > $signed_roles_count) && $user_form->user_form_status == 'Completed'){
				\Models\UserForm::where('id', $user_form->id)->update(['user_form_status' => 'Awaiting Sign-off']);
			}

			$update_status = false;
			foreach ($user_form->Form->FormSignOffRoles as $role) {
				if($role->role->slug == 'trainee' && !in_array($role->role_id, $user_signoff_ids) && $user_form->user_form_status == 'Awaiting Sign-off'){
					$update_status = true;
				}
			}

			if($update_status){
				\Models\UserForm::where('id', $user_form->id)->update(['user_form_status' => 'In Progress']);
			}
		}

		return 'Data updated successfully';
	});

	$group->post('/refresh-form',function (Request $request,Response $response){

		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::roleAllowRefreshForms()
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$data = $request->getParsedBody();

		if (empty($data['user_form_id'])) {
			return \APP\Tools::returnCode($request, $response, 403, 'Invalid Request');
		}

		$userFormId  = $data["user_form_id"];

		$userForm = UserForm::query()
			->where('id', '=', $userFormId)
			->whereHas('Form.FormSignOffRoles')
			->with(['FormValue','UserFormSignOff', 'Form.FormSignOffRoles'])
			->first();

		if(empty($userForm)) {
			return \APP\Tools::returnCode($request, $response, 403, 'Invalid Request');
		}

		if (!in_array($userForm->user_form_status, ['Awaiting Sign-off', 'In Progress'])) {
			return \APP\Tools::returnCode($request, $response, 403, 'Invalid Request');
		}

		$userForm->UserFormSignOff->each(function ($form) {
			$form->delete();
		});

		if($userForm->FormValue) {
			$userForm->FormValue->delete();
		}

		$userForm->user_form_status = 'In Progress';
		$userForm->user_custom_form_value_id = null;
		$userForm->save();

		$data_arr = [
			'user_id' => $userForm->user_id,
			'username' => $userForm->user->username,
			'learner_name' => $userForm->user->fname .' '. $userForm->user->lname,
			'form_id' => $userForm->form_id,
			'user_form_id' => $userForm->id,
			'edited_by_user' => \APP\Auth::getUserId() ?? 0,
			'date_time' => date("Y-m-d h:i:s", time()),
			'notes' => 'Form Refreshed'
		];

		\Models\FormLog::query()
			->create($data_arr);
		$response->getBody()->write(json_encode(['message' => 'Form Refreshed']));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	$group->post('/upload-file', function (Request $request, Response $response, $args) {
		$path = $this->get('settings')["LMSPrivatePath"] . "form_files/";
		$storage = new \Upload\Storage\FileSystem($path);
		$import_file = new \Upload\File('file', $storage);
		$import_file_id = uniqid();
		$original_name = $import_file->getNameWithExtension();
		$import_file->setName($import_file_id);

		try {
			$import_file->upload();
			$uploaded_file = $import_file->getNameWithExtension();
			$payload = json_encode(['file' => $uploaded_file, 'file_name' => $original_name]);

			$response->getBody()->write($payload);
			return $response->withHeader('Content-Type', 'application/json');
		} catch (Exception $e) {
			$payload = json_encode(['error' => $e->getMessage()]);

			$response->getBody()->write($payload);
			return $response
				->withHeader('Content-Type', 'application/json')
				->withStatus(400);
		}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-standards'], 'select'));

	$group->get('/download-file/{file_name}', function (Request $request, Response $response, $args) {
		$data = $request->getQueryParams();

		// Clean the file name to remove any path
		$args['file_name'] = basename($args['file_name']);
		$file = $this->get('settings')["LMSPrivatePath"] . "form_files/" . $args['file_name'];
		$all_files = UserCustomFormValue::where('values', "like", "%%" . $args['file_name'] . "%%")->first();
		$files = [];

		if ($data && isset($data['compress'])) {
			if ($all_files && $all_files->values && $data['compress'] && isset($all_files->values[$data['compress']])) {
				foreach ($all_files->values[$data['compress']] as $file_path) {
					$files[] = $file_path['dataURL'];
				}
			} else {
				$payload = json_encode(["error" => "File not found"]);
				$response->getBody()->write($payload);
				return $response->withHeader('Content-Type', 'application/json')->withStatus(404);
			}

			// Create ZIP using the files and download
			if ($files) {
				$zip = new \ZipArchive();
				$zip_file = $this->get('settings')["LMSTempPath"] . $data['compress'] . '.zip';
				if (is_file($zip_file)) {
					unlink($zip_file);
				}

				if ($zip->open($zip_file, \ZipArchive::CREATE) === TRUE) {
					foreach ($files as $file) {
						$zip->addFromString(basename($file), file_get_contents($file));
					}
					$zip->close();

					$response = $response->withHeader('Content-Type', 'application/octet-stream')
						->withHeader('Content-Disposition', 'attachment; filename="' . basename($zip_file) . '"')
						->withHeader('Content-Length', (string) filesize($zip_file));

					$response->getBody()->write(file_get_contents($zip_file));
					return $response;
				}
			}
		} else {
			if (file_exists($file)) {
				$filename = basename($file);

				if ($all_files) {
					foreach ($all_files->values as $value) {
						if (is_array($value)) { // Check for multiple file uploads
							foreach ($value as $file_details) { // Loop through and check file name
								if (isset($file_details['dataURL']) && $file_details['dataURL'] === $args['file_name']) {
									$filename = $file_details['name'];
								}
							}
						}
					}
				}

				$response = $response->withHeader('Content-Type', mime_content_type($file))
					->withHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
					->withHeader('Content-Length', (string) filesize($file));

				$response->getBody()->write(file_get_contents($file));
				return $response;
			} else {
				$payload = json_encode(["error" => "File not found"]);
				$response->getBody()->write($payload);
				return $response->withHeader('Content-Type', 'application/json')->withStatus(404);
			}
		}
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-standards'], 'select'));


	// Route for updating the user form status from completed to in progress
	$group->put('/user_form_status/{user_form_id:[0-9]+}', function (Request $request, Response $response, $args) {
		DB::beginTransaction();
		try {
			$userForm  = \Models\UserForm::find($args["user_form_id"]);
			$userForm->user_form_status = 'In Progress';
			$userForm->learner_completion_status = false;
			$userForm->save();
			$userFormSignoff = $userForm->UserFormSignoff;
			foreach ($userFormSignoff as $value) {
				if (file_exists($signoff->e_signature)) {
					unlink($value->e_signature);
				}
				$value->delete();
			}

			$user_form = \Models\UserForm::withTrashed()->find($args["user_form_id"]);
			if (!$user_form) {
				return \APP\Tools::returnCode($request, $response, '404');
			}
			$user = \Models\User::find($user_form->user_id);
			$data_arr = [
				'user_id' => $user_form->user_id,
				'username' => $user ? $user->username : '',
				'learner_name' => $user ? $user->fname .' '. $user->lname : '',
				'form_id' => $user_form->form_id,
				'user_form_id' => $args["user_form_id"],
				'form_field' => '',
				'edited_by_user' => \APP\Auth::getUserId() ?? 0,
				'date_time' => date("Y-m-d h:i:s", time()),
				'notes' => 'User form status updated to in progress from awaiting sign off',
			];
			\Models\FormLog::addEntry($data_arr);
			DB::commit();
			$response->getBody()->write(json_encode(["success" => true]));
			return $response->withHeader('Content-Type', 'application/json')->withStatus(200);
		} catch (\Throwable $th) {
			DB::rollback();
			return \APP\Tools::returnCode($request, $response, 500, "Something went wrong: " . $th->getMessage());
		}
	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-forms-data'], 'insert'));

	$group->post('/form-submit-permission', function(Request $request, Response $response) {
		$params = $request->getParsedBody();
		$form_id = $params['form_id'];
		$form = Form::where('id', $form_id)->with('FormField')->first();
		$current_user = Auth::getUser();
		$role_id = $current_user->shadow_role_id ? $current_user->shadow_role_id : $current_user->role_id;

		$has_write_permission = false;

		foreach ($form->FormField as $field) {
			// Start by assuming the global permission
			$field_permission = $field->permission;

			foreach ($field->FormFieldAuthorPermission as $author_permission) {
				if ($author_permission->role_id == $role_id) {
					// Override with the user-specific permission
					$field_permission = $author_permission->permission;
					break;
				}
			}

			if ($field_permission === 'write') {
				$has_write_permission = true;
				break; // Exit early if write permission is found for any field
			}
		}

		$data = ['permission' => $has_write_permission];
		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-standards'], 'select'));

	$group->post('/module/list/{form_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$formId = $args['form_id'];
		$params = $request->getParsedBody();

		$query = \Models\LearningModule
			::select([
				"learning_modules.*",
				DB::raw("
					(SELECT COUNT(*)
					FROM form_learning_module_mapping
					WHERE form_learning_module_mapping.module_id = learning_modules.id
					AND form_learning_module_mapping.form_id = " . (int) $formId . "
					) AS is_assigned
				")
			])
			->with([
				"category",
				"FPCategory",
				"type",
				"competencies",
				"company",
				"CreatedBy",
			])
			->validresource()
			->whereNull('created_by_event')
			->where('open_in_events_only', 0)
			->where('created_in_learner_interface', 0)
		;

		if (isset($params['search']['is_assigned']) && $params['search']['is_assigned'] !== '') {
			$query = DB::table(DB::raw("({$query->toSql()}) as subquery"))
						->mergeBindings($query->getQuery())
						->where("is_assigned", (int) $params['search']['is_assigned']);

			unset($params['search']['is_assigned']); // Remove to avoid double filtering in searchPaginate
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);
		$json = $p->toJson();

		$response->getBody()->write($json);
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-standards'], 'select'));

	$group->post('/assign/module/{form_id:[0-9]+}/{module_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$formId = $args['form_id'];
		$moduleId = $args['module_id'];
		$form = Form::find($formId);
		// Check if the module is already assigned
        if ($form->modules()->where('module_id', $moduleId)->exists()) {
            // Unassign module
            $form->modules()->detach($moduleId);
			$assigned = false;
        } else {
            // Assign module
            $form->modules()->attach($moduleId);
			$assigned = true;
        }
		$response->getBody()->write(json_encode(["assigned" => $assigned]));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-standards'], 'select'));

	$group->post('/image/{form_id:[0-9]+}', function ($request, $response, $args) {
		$data = $request->getParsedBody();
		$form = \Models\Form::find($args['form_id']);
		$image_response = \Models\LearningModule::uploadImages($response, $data, $_FILES, $this->get('settings'), $form);
		$response->getBody()->write(json_encode($image_response));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-forms-data'], 'insert'));

	$group->delete('/image/{form_id:[0-9]+}', function ($request, $response, $args) {
		$data = $request->getParsedBody();
		$form = \Models\Form::find($args['form_id']);
		\Models\LearningModule::deleteImage($form, 'thumbnail', $this->get('settings'));

		return $response;


	})->add(\APP\Auth::getStructureAccessCheck(['library-learning-forms-data'], 'insert'));

});
