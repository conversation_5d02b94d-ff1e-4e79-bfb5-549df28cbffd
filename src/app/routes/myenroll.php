<?php

use APP\Auth;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\CreditUsage;
use Models\LearningModule;
use Models\LearningResult;
use Models\LinkedLearningModule;

$app->group("/myenroll",  function ($group) {

	$group->put("/add", function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		$user = \APP\Auth::getUser();

		$module_ids = \Models\LearningModule::returnValidResourcesIDs($params["module_ids"]);

		if (count($module_ids) == 0) {
			return \APP\Tools::returncode($request, $response, 404);
        }
        $creditUsers = \Models\CreditUsage::checkCreditExist($module_ids, [$user->id]);
        if ($creditUsers === false) {
            return \APP\Tools::returncode($request, $response, 403, 'Not enough credits');
        }

		// Link and assign resources to user.
		\Models\UserLearningModule::linkResources($user->id, $module_ids, 'user enrolled to this resource');

		// If any of these resources need approval by manager, mark learning result entry as not approved.
		\Models\LearningResult
			::where("learning_results.user_id", $user->id)
			->where('refreshed', 0)
			->whereIn("learning_results.learning_module_id", function($query) use ($module_ids) {
				$query
					->select("id")
					->from("learning_modules")
					->whereIn("id", $module_ids)
					->where("approval", true)
				;
			})
			->update([
				"approved" => false
			])
		;

		/** For Sending Mail for approval learning resource */

		foreach ($module_ids as $key => $resource_id) {
			$learning_module = \Models\LearningModule
				::where("id", $resource_id)
				->first()
			;
			if ($learning_module) {
				if ($learning_module->approval) {
					$recipients = [];
					$managerList = \Models\ManagerUser
						::where('user_id', \APP\Auth::getUserId())
						->select('manager_id')
						->get()
					;

					foreach($managerList AS $managerListVal){
						$recipients[] = $managerListVal->manager_id;
					}

					// Send e-mail to Manager that status changes to approval
					$template = \Models\EmailTemplate::getTemplate('Resource Approval Request');
					if (
						$template &&
						count($recipients) > 0
					) {
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->recipients = $recipients;
						$email_queue->from = \APP\Auth::getUserId();
						$email_queue->custom_variables = json_encode([
							'TRAINEE_FNAME' => $user->fname,
							'TRAINEE_LNAME' => $user->lname,
							'RESOURCE_NAME' => $learning_module->name,
							'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
							'MANAGER_APPROVAL_LINK'=>'app/dashboard/manage-learning/manage-learning-resources/lessons-and-learning-resources',
							'REGARDS'=>$GLOBALS["CONFIG"]->Regards,
						]);
						$email_queue->save();
					}
				} else if ($learning_module->is_course == 1) {
					$module_ids = \APP\Learning::getAllModuleIds($module_ids);
					$module_ids = \Models\LearningModule::returnValidResourcesIDs($module_ids);

					\Models\UserLearningModule::linkResources(\APP\Auth::getUserId(), $module_ids, 'Learner enrolled to lesson ' . $learning_module->id . ', assigning linked resources.');
				}
			}
		}
        CreditUsage::updateCredit($module_ids,$creditUsers);
		$learningResults = LearningResult
			::select('id', 'learning_module_id', 'user_id', 'approved')
			->whereIn('learning_module_id', $module_ids)
			->where('user_id', $user->id)
			->where('refreshed', 0)
			->get()
		;
		$response->getBody()->write(json_encode(['learningResults' => $learningResults, 'userId' => $user->id]));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['misc-permissions-enroll-learning-modules'], 'update'));

	$group->post('/list', function (Request $request, Response $response) {

        $params = $request->getParsedBody();
        $user = \APP\Auth::getUser();
        $makeAllEnrollable = false;
        $catelogList = '';
        if($user->company){
            $makeAllEnrollable = $user->company->make_all_enrollable?1:0;
            $catelog = array_map(function($name) {
                    return "'" . addslashes($name) . "'";
            }, LearningModule::$catelog);
            $catelogList = implode(",", $catelog);
        }

		// Handle empty catalog list
		if (empty($catelogList)) {
			$catelogList = "''"; // Empty string in SQL
		}

		$companyId = \APP\Auth::getUser()->company_id;
		$isEnterPriseVersion = !\APP\Tools::getConfig('sharedClients');

		$query = \Models\LearningModule
			::select([
				'learning_modules.id',
				'learning_modules.name',
				'learning_modules.description',
                'learning_modules.keywords',
				'learning_modules.category_id',
				'learning_modules.f_p_category_id',
				'learning_modules.type_id',
				'learning_modules.thumbnail',
				'learning_modules.do_prerequisite',
				'learning_modules.track_progress',
				'learning_modules.is_course',
				'learning_modules.created_by',
				'learning_modules.jackdaw_resource',
                'learning_modules.created_by_group',
                DB::raw("
                    CASE
                        WHEN '{$makeAllEnrollable}' = '1' OR learning_modules.name IN ({$catelogList})
                    THEN true
                        ELSE learning_modules.self_enroll
                    END AS self_enroll
                "),
				DB::raw("count(user_learning_modules.id) > 0 AS assigned"),
                'learning_modules.created_by_event',
                'learning_modules.refresh_period',
				'learning_modules.refresh_date',
                'learning_modules.open_in_events_only',
			])
			->with("competencies", "type", "category", "linked")
            ->where(function($query) {
                if(!Auth::getUser()?->company?->make_all_enrollable){
					$query
						->where("learning_modules.self_enroll", true)
					;
                } else {
                    $query->where(function($query) {
                        $query
							->where("learning_modules.self_enroll", true)
                        	->orWhereIn("learning_modules.name",LearningModule::$catelog)
						;
                    });
                }

				// If events are not supposed to be seen on learning view, then there needs to be lesson displayed if eligable that learner can click on to and enroll.
				if (!\APP\Tools::getConfig('ShowEventsineLearningView')) {
					$query = $query
						// Lessons, that are linked to events, that are available for enrollment to user, if lesson is not assigned to user

						// If ShowEventsineLearningView is false
						->orWhere(function($query) {
							$query
								->where("learning_modules.is_course", 1)
								->whereNotIn('learning_modules.id',
									\Models\UserLearningModule
										::select('learning_module_id')
										->where('user_id', \APP\Auth::getUserId())
										->get()
								)
								// All we need is lesson that has at least one event that learner can enroll to
								->whereHas('ScheduleLesson', function ($query) {
									$query
										->whereHas('Schedule', function ($query) {
											$query = $query
												->where('schedules.enrole_any_learner', true)
												->where('schedules.status', true)
												->where('schedules.start_date', '>', \Carbon\Carbon::now()) // Only if there are event in future
												->where(function($query) {
													$query = $query
														->whereHas('Queries', function ($query) {
															$query = $query
																->whereRaw("FIND_IN_SET(?, resource_queries.user_ids)", [\APP\Auth::getUserId()])
															;
														})
														->orDoesntHave('Queries')
													;
												})
											;
										})
										->where('schedule_links.status', true)
									;
								})
							;
						})
					;
				}
			})
			->where("status", true)
			->where(function($query ) use ($companyId, $isEnterPriseVersion) {
				$query
					->where(function($query ) use ($companyId, $isEnterPriseVersion) {
						// permission based on the company  on self enrollments
						$query
							->where('learning_modules.self_enroll', 1)
							->where(function($query) use ($companyId, $isEnterPriseVersion) {
								// used to get the existing  data
								$query->where(function($subQuery) use ($companyId, $isEnterPriseVersion) {
									if ($isEnterPriseVersion) {
										$subQuery->where('learning_modules.self_enroll', 1)
												->whereNull('learning_modules.self_enroll_access');
									} else {
										$subQuery->where('learning_modules.self_enroll', 1)
												->whereNull('learning_modules.self_enroll_access')
												->where('learning_modules.company_id', $companyId);
									}
								});
								// end of used to get the existing  data

								// Case 1: Open to all companies (self_enroll_access = 0)
								$query->orWhere('learning_modules.self_enroll_access', 0);

								// // Case 2: Accessible only to selected companies (self_enroll_access = 1)
								$query->orWhere(function($subQuery) use ($companyId) {
									$subQuery->where('learning_modules.self_enroll_access', 1)
											->whereHas('companyModuleEnrollments', function($q) use ($companyId) {
												$q->where('company_module_enrollment.company_id', $companyId)->where('company_module_enrollment.type',1);
									});
								});

								// // Case 3: Accessible to all except selected companies (self_enroll_access = 2)
								$query->orWhere(function($subQuery) use ($companyId) {
									$subQuery
										->where('learning_modules.self_enroll_access', 2)
										->whereDoesntHave('companyModuleEnrollments', function($q) use ($companyId) {
											$q->where('company_module_enrollment.company_id', $companyId)->where('company_module_enrollment.type',1);
										})
									;
								});
							});
						})

					->orWhereIn('learning_modules.name',LearningModule::$catelog)
				;
			})
			->leftjoin("user_learning_modules", function($join) {
				$join
					->on("user_learning_modules.learning_module_id", "=", "learning_modules.id")
					->where("user_learning_modules.user_id", "=", \APP\Auth::getUserId())
					->whereNull("user_learning_modules.deleted_at")
				;
			})
			->validresource()
			->groupBy("learning_modules.id")
		;
        if (\APP\Auth::isLearner()) {
             $query->with("isLinked");
            if (\APP\Tools::getConfig('Hide100PercentDiscountedEnrollableLearningResources')) {
                if (
                	\APP\Auth::getUser() &&
                	\APP\Auth::getUser()->company &&
                	\APP\Auth::getUser()->company->discount_percentage &&
                	\APP\Auth::getUser()->company->discount_percentage == 100
                ) {
                    $query->where('created_by_event',1);
                }
            }
			$query->orWhere(function($query){
				$query->whereIn('learning_modules.id',LinkedLearningModule::getAvailableLearning());
			});
        }
		$p = \APP\SmartTable::searchPaginate($params, $query);

		if (\APP\Auth::isLearner()) {
			foreach($p as $result) {
				$result->setAppends(['safe_thumbnail', 'highlight', 'isLocked']);
			}
		}
		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

});
