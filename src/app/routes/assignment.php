<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;

$app->group("/assignment", function ($group){
	// add link
	$group->put(
		'/link/{source_table}/{source_table_id:[0-9]+}/{link_table}/{link_table_id:[0-9]+}',
		\APP\Controllers\AssignmentController::class.':link'
	)->add(
		\APP\Auth::getStructureAccessCheck('misc-permissions-assignments', 'insert')
	);

	// Remove link
	$group->put(
		'/delete/{source_table}/{source_table_id:[0-9]+}/{link_table}/{link_table_id:[0-9]+}',
		\APP\Controllers\AssignmentController::class.':delete'
	)->add(
		\APP\Auth::getStructureAccessCheck('misc-permissions-assignments', 'disable')
	);

	// list links
	$group->post(
		'/{source_table}/{link_table}/{link_table_id:[0-9]+}',
		\APP\Controllers\AssignmentController::class.':list'
	)->add(
		\APP\Auth::getStructureAccessCheck('misc-permissions-assignments', 'select')
	);
});
