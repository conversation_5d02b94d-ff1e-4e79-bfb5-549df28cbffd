<?php

use APP\Auth;
use APP\Controllers\GovUKPayController;
use APP\Form;
use APP\Teams;
use APP\Tools;
use Carbon\Carbon;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\OutlookSubscription;
use Models\ResourceQuery;
use Models\ScheduleLink;
use Models\ScheduleLinkedFormDetails;
use Models\UserWorkflowForm;
use Models\Schedule;
use Models\UserPaymentTransaction;
use Models\Venue;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Middleware\RateLimitMiddleware;
use APP\Controllers\CustomReportController;
use Models\LearningModule;
use Models\UserScheduleWaitingList;
use Models\User;

$app->group("/schedule", function ($group) {

	$group->post('/list/excel', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();
		$query = \Models\Schedule
			::select(
				'schedules.id',
				DB::raw("(SELECT COUNT('id') FROM schedule_links ct
					WHERE ct.schedule_id = schedules.id and ct.type='users' AND  ct.deleted_at IS NULL and ct.link_id is not null) as assigned"),
				'schedules.name',
				'schedules.start_date',
				'schedules.type',
				//'schedules.minclass',
				//'schedules.maxclass',
				'schedules.enrole_any_learner',
								DB::raw("TIME_FORMAT(SEC_TO_TIME(schedules.duration * 60), '%H:%i') AS duration_hours")
			)
			->selectRaw("DATE_ADD(start_date, INTERVAL duration minute) AS end_date")
			->selectRaw("(
					CASE
						WHEN
							schedules.enrole_any_learner = 1
						THEN
							schedules.minclass
						ELSE
							null
					END
				) as minclass
			")
			->selectRaw("(
					CASE
						WHEN
							schedules.enrole_any_learner = 1
						THEN
							schedules.maxclass
						ELSE
							null
					END
				) as maxclass
			")
			->with(['Comments' => function ($query) {
				$query
					->with(['AddedBy' => function ($query) {
						$query
							->validuser();
					}])
					->whereHas('AddedBy', function ($query) {
						$query
							->validuser()
							->whereHas('Role', function ($query) {
								$query
									->where('is_admin', 1)
									->orWhere('is_manager', 1);
							});
					})
					->orderBy('comments.created_at', 'desc')
					->take(1); // Limit to only the latest comment
			}])
		;
		$query = \Models\Schedule::countAndConditions($query, $params);

		/*For excluding current event, for https://emil-reisser-weston.atlassian.net/browse/SCOR-2331*/
		if (isset($args["event_id"]) && !empty($args["event_id"])) {
			$query = $query->where('schedules.id', '!=', $args["event_id"]);
		}

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["search"]["schedule_user_name"])) {
			$query
				->whereHas('Users', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["schedule_user_name"] . "%'")
						->where('users.status', 1)
					;
					// if hideNotAssignedLearnerInformationToManagersInEvents is true, return only learners assigned to manager
					if (
						!\APP\Auth::isAdmin() &&
						!\APP\Auth::isCd() &&
						!\APP\Auth::accessAllLearners() &&
						\APP\Tools::getConfig('hideNotAssignedLearnerInformationToManagersInEvents')
					) {
						$query = $query
							->whereIn('users.id',
								\Models\ManagerUser
									::select('manager_users.user_id')
									->where('manager_id', \APP\Auth::getUserId())

									->get()
							)
						;
					}
				})
			;
			unset($params["search"]["schedule_user_name"]);
		}

		if (isset($params["search"]["schedule_manager_name"])) {
			$query
				->whereHas('Managers', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["schedule_manager_name"] . "%'")
						->where('users.status', 1)
					;
				})
			;
			unset($params["search"]["schedule_manager_name"]);
		}

		if (isset($params["search"]["schedule_department_name"])) {
			$query
				->whereHas('Departments', function ($query) use ($params) {
					$query
						->whereRaw("departments.name LIKE '%" . $params["search"]["schedule_department_name"] . "%'")
						->where('departments.status', 1)
					;
				})
			;
			unset($params["search"]["schedule_department_name"]);
		}

		if (isset($params["search"]["schedule_group_name"])) {
			$query
				->whereHas('Groups', function ($query) use ($params) {
					$query
						->whereRaw("groups.name LIKE '%" . $params["search"]["schedule_group_name"] . "%'")
						->where('groups.status', 1)
					;
				})
			;
			unset($params["search"]["schedule_group_name"]);
		}

		if (isset($params["search"]["interface"])) {
			if ($params["search"]["interface"] == 'manage') {
				if (!\APP\Auth::isCd()) {
					$query = $query
						->where(function($query) {
							if (!\APP\Tools::getConfig('listAllEventsToManagers')) {
								$query
									->whereHas('Permissions', function ($query) {
										$query
											->where('user_id', \APP\Auth::getUserId())
										;
									})
									->orWhereHas('Managers', function ($query) {
										$query
											->where('users.id', \APP\Auth::getUserId())
										;
									})
								;
							}
						})
					;
				}
				$query = $query
					->whereNull('parent_id')
					->with(['Departments' => function ($query) {
						$query
							->select(
								'departments.id',
								'departments.name'
							)
							->where('departments.status', 1)
						;
					}])
					->with(['Groups' => function ($query) {
						$query
							->select(
								'groups.id',
								'groups.name'
							)
							->where('groups.status', 1)
						;
					}])
					->with(['Managers' => function($query) use ($args) {
						$query = $query
							->select(
								'users.id',
								'users.fname',
								'users.lname',
								'users.email',
								'users.department_id'
							)
							->selectRaw("CONCAT(users.fname, ' ', users.lname) as full_name")
							->where('users.status', true)
							->with(['Groups' => function ($query) {
								$query
									->select(
										'groups.id',
										'groups.name'
									)
									->where('groups.status', 1)
								;
							}])
						;
					}])
					->with(['Users' => function ($query) use ($args) {
						$query = $query
							->select(
								'users.id',
								'users.fname',
								'users.lname',
								'users.email'
							)
							->selectRaw("CONCAT(users.fname, ' ', users.lname) as full_name")
							->where('users.status', true)
						;
					}])
					->withCount('Waiting')
					->withCount(['Attended' => function ($query) use ($args) {
						// https://emil-reisser-weston.atlassian.net/browse/SCOR-3005
							$query
								//->where('users.status', true)
							;
					}])
				;
			}
			unset($params["search"]["interface"]);
		}

		// Completed at filter for learning results
		$query = \APP\Tools::datePeriod($params, $query, 'start_date');

		$pages = \APP\SmartTable::searchPaginate($params, $query, false, false);

		// if hideNotAssignedLearnerInformationToManagersInEvents is true, filter out learners not assigned to manager
		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isCd() &&
			!\APP\Auth::accessAllLearners() &&
			\APP\Tools::getConfig('hideNotAssignedLearnerInformationToManagersInEvents')
		) {
			foreach ($pages as $page_key => $page) {
				foreach ($page->users as $user_key => $user) {
					$manager_user = \Models\ManagerUser
						::where('manager_id', \APP\Auth::getUserId())
						->where('user_id', $user->id)
						->first()
					;
					if (!$manager_user) {
						$pages[$page_key]->users[$user_key] = new \stdClass;
						$pages[$page_key]->users[$user_key]->id = 0;
						$pages[$page_key]->users[$user_key]->fname = 'Learner';
						$pages[$page_key]->users[$user_key]->lname = '';
						$pages[$page_key]->users[$user_key]->schedule_link = $user->schedule_link;
					}
				}
			}
		}
		$data = [];

		foreach ($pages as $page_key => $page) {
			$event =  new stdClass;
			$event->id = $page->id;
			$event->name = $page->name;
			$event->type = $page->type;

			// Limit concatenated fields to prevent Excel cell character limit (32,767)
			$users_text = $page->users->implode('full_name',', ');
			$event->users = strlen($users_text) > 32000 ? substr($users_text, 0, 32000) . '...' : $users_text;

			$event->minclass = $page->minclass??'N/A';
			$event->maxclass = $page->maxclass?? 'N/A';
			$event->required = $page->minclass ? ((($page->minclass - $page->users->count()) < 0 ) ? '0' : $page->minclass - $page->users->count()) : 'N/A';
			$event->assigned =  $page->assigned;
			$event->attended_count =  $page->attended_count;
			$event->duration_hours =  $page->duration_hours;

			$departments_text = $page->departments->implode('name',', ');
			$event->departments = strlen($departments_text) > 32000 ? substr($departments_text, 0, 32000) . '...' : $departments_text;

			$groups_text = $page->groups->implode('name',', ');
			$event->groups = strlen($groups_text) > 32000 ? substr($groups_text, 0, 32000) . '...' : $groups_text;

			$managers_text = $page->managers->implode('full_name',', ');
			$event->managers = strlen($managers_text) > 32000 ? substr($managers_text, 0, 32000) . '...' : $managers_text;

			$event->start_date = $page->start_date ? \Carbon\Carbon::parse($page->start_date)->format(\APP\Tools::getConfig('defaultDateFormat') . " H:i") : "--";
			$event->waiting_count = $page->waiting_count;
			if ($page->Comments->count() > 0) {
				$comment_text = strip_tags($page->Comments[0]->comment);
				$event->comments = strlen($comment_text) > 32000 ? substr($comment_text, 0, 32000) . '...' : $comment_text;
			}
			$event->special_requirements = 'No';
			if ($page->Users->count() > 0) {
				foreach ($page->Users as $user) {
					if ($user->schedule_link->learner_requirement) {
						$event->special_requirements = 'Yes';
						break;
					}
				}
			}
			$data [] = $event;
		}

		$export_fields = [
			"Event ID" => "id",
			"Name" => "name",
			"Type" => "type",
			"Users" => "users",
			"Min" => "minclass",
			"Max" => "maxclass",
			"Required" => "required",
			"Assigned" => "assigned",
			'Waiting List' => 'waiting_count',
			'Attended' => 'attended_count',
			'Hours' => 'duration_hours',
			"Departments"=>"departments",
			"Groups"=>"groups",
			"Managers"=>"managers",
			"Date/Time"=>"start_date",
			"Comments" => "comments",
			"Special Requirements" => "special_requirements",
		];


		$download_file_name = uniqid("events.list.") . ".xlsx";

		\APP\Tools::generateExcelDownload(
			$data,
			$export_fields,
			$this->get('settings')["LMSTempPath"] . $download_file_name
		);

		$response->getBody()->write($download_file_name);
		return $response;

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));

	// List all events and if schedule_id is provided, check if event is linked with provided schedule_id
	$group->post('/list[/{event_id:\d+}]', function (Request $request, Response $response, array $args) {
        $params = $request->getParsedBody();

		$query_id = 'scheduleList';
		$QueryBuilder = "\\APP\\QueryBuilder\\" . $query_id;
		$query = $QueryBuilder::generate($params, $args);

		$pages = \APP\SmartTable::searchPaginate($params, $query);


		// if hideNotAssignedLearnerInformationInEvents is true, filter out learners not assigned to manager
		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isCd() &&
			!\APP\Auth::accessAllLearners() &&
			\APP\Auth::hideNotAssignedLearnerInformationInEvents()
		) {
			foreach ($pages as $page_key => $page) {
				foreach ($page->users as $user_key => $user) {
					$manager_user = \Models\ManagerUser
						::where('manager_id', \APP\Auth::getUserId())
						->where('user_id', $user->id)
						->first()
					;
					if (!$manager_user) {
						$pages[$page_key]->users[$user_key] = new \stdClass;
						$pages[$page_key]->users[$user_key]->id = 0;
						$pages[$page_key]->users[$user_key]->fname = 'Learner';
						$pages[$page_key]->users[$user_key]->lname = '';
						$pages[$page_key]->users[$user_key]->schedule_link = $user->schedule_link;
					}
				}
			}
		}


		$response->getBody()->write($pages->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));

	$group->get('/{base:all|min}[/{date}[/{period}]]', function (Request $request, Response $response, array $args) {

		$events = \Models\Schedule
			::where('schedules.status', true)
			->where('parent_id', null)
		;
        $isEnterprise = !\APP\Tools::getConfig('sharedClients');
        if(!$isEnterprise && (Auth::isManagerInterface() || Auth::isLearningInterface())){
        $events =  $events->join('users as created_user',function($join){
                        $join->on('created_user.id','schedules.created_by');
                        $join->where('created_user.company_id',\APP\Auth::getUserCompanyId());
                    });
        }
		if ($args['base'] == 'all') {
			$events = $events
				->with('Children.VenueDeatils')
								->with('VenueDeatils')
				->with(['Category' => function ($query) {
					$query
						->select(
							'learning_module_categories.id',
							'learning_module_categories.name'
						)
						->where('learning_module_categories.status', 1)
					;
				}])
				->with(['Lessons' => function($query) {
					$query = $query
						->select(
							'learning_modules.id',
							'learning_modules.name'
						)
						->where('learning_modules.status', true)
					;
				}])
			;
		}


		if (isset($args['date'])) {
			$date_obj = \Carbon\Carbon::parse($args['date']);
			if (isset($args['period'])) {
				switch ($args['period']) {
					case 'day':
							$events = $events
								->where(function ($query) use ($date_obj) {
									$query
										->where(function ($query) use ($date_obj) {
											$query
												->whereDate('start_date', $date_obj)
											;
										})
										->orWhere(function ($query) use ($date_obj) {
											$query
												->whereDate('start_date', '<=', $date_obj) // Check if the event starts before or on the given date
												->whereDate('end_date', '>=', $date_obj)
											;
										})
									;
								})
							;
						break;

					case 'week':
							$events = $events
								->where(function ($query) use ($date_obj) {
									$query
										->where(function ($query) use ($date_obj) {
											$query
												->whereBetween('start_date', [$date_obj->copy()->startOfWeek(), $date_obj->copy()->endOfWeek()])
											;
										})
										->orWhere(function ($query) use ($date_obj) {
											$query
												->whereDate('start_date', '<=', $date_obj->copy()->format('Y-m-d')) // Check if the event starts before or on the given date
												->whereDate('end_date', '>=', $date_obj->copy()->format('Y-m-d'))
											;
										})
									;
								})
							;
						break;

					case 'month':
							$events = $events
								->where(function ($query) use ($date_obj) {
									$query
										->where(function ($query) use ($date_obj) {
											$query
												->whereMonth('start_date', $date_obj->copy()->month)
												->whereYear('start_date', $date_obj->copy()->year)
											;
										})
										->orWhere(function ($query) use ($date_obj) {
											$query
												->whereMonth('end_date', $date_obj->copy()->month)
												->whereYear('end_date', $date_obj->copy()->year)
											;
										})
									;
								})
							;
						break;

					case 'year':
							$events = $events
								->whereYear('start_date', $date_obj->copy()->year)
							;
						break;
				}
			} else {
				$events = $events
					->whereDate('start_date', $date_obj);
				;
			}
		}

		if (\APP\Auth::isCd()) {
			$events = $events
				->select(
					'schedules.id',
					'schedules.name',
					'schedules.type',
					'schedules.category_id',
					'schedules.visit_type_id',
					'schedules.start_date',
					'schedules.duration',
					'schedules.all_day_event',
					'schedules.parent_id',
					'schedules.created_by',
					'schedules.minclass',
					'schedules.maxclass',
					'schedules.type'
				)
			;
		} else {
			$events = $events
				->select('schedules.*')
			;
			// Show events to manager list only if this is true!
			if (!\APP\Auth::isLearner()) {
				$events = $events
					->where('visible_schedule', true)//New condition added for visible schedule
				;
			}

		}
		$events = $events
			->selectRaw("IF (end_date is null, DATE_ADD(start_date, INTERVAL duration minute), end_date) as end_date")
		;


		// If learner, show only those schedules this learner is linked with!
		if (\APP\Auth::isLearner()) {
			$events = $events
				->where(function ($query) {
					$query
						->where('enrole_any_learner', true)
						->where(function($query) {
							$query = $query
								->whereHas('Queries', function ($query) {
									$query = $query
										->whereRaw("FIND_IN_SET(?, resource_queries.user_ids)", [\APP\Auth::getUserId()])
									;
								})
								->orDoesntHave('Queries')
							;
						})
						->whereNotIn('schedules.id',
							\Models\ScheduleLink
								::select('schedule_id')
								->where('schedule_links.type', 'users')
								->where('schedule_links.link_id', \APP\Auth::getUserId())
								->get()
						)
					;
					$query = \Models\Schedule::filterVisibleDays($query);
				})
				->orWhere(function ($query) {
					$query
						->whereIn('schedules.id',
							\Models\ScheduleLink
								::select('schedule_id')
								->where('schedule_links.type', 'users')
								->where('schedule_links.link_id', \APP\Auth::getUserId())
								->get()
						)
						->where(function($query) {
							$query = $query
								->where('schedules.type','!=','lesson')
								->orWhereIn('schedules.id',
									\Models\ScheduleLink
										::select('schedule_id')
										->where('schedule_links.type', 'lesson')
										->whereIn('link_id',
											\Models\UserLearningModule
												::select('learning_module_id')
												->where('user_id', \APP\Auth::getUserId())
												->get()
										)
										->get()
								)
							;
						})
					;
				})
				->with(['Files' => function ($query) {
					$query
						->where('status', true)
						->with(['AddedBy' => function ($query) {
							$query
								->select(
									'id',
									'fname',
									'lname',
									'role_id'
								)
								->with(['role' => function ($query) {
									$query
										->select(
											'id',
											'name'
										)
									;
								}])
							;
						}])
					;
				}])
				->with(['Resources' => function ($query) {
					$query = $query
						->select(
							'learning_modules.id',
							'learning_modules.name',
							'learning_modules.description',
							'learning_modules.type_id',
							'learning_modules.is_course',
							'learning_modules.material'
						)
						->where('learning_modules.status', true)
						->with('Type');
				}])
				->with(['Visitors' => function ($query) {
					$query
						->select(
							'users.id',
							'role_id'
						)
						->where('users.status', true)
					;

				}])
				->with(['Users' => function ($query) {
					$query
						->select(
							'users.id',
							'role_id',
							'schedule_links.approved'
						)
						->where('users.status', true)
					;
				}])
				->with(['Waiting' => function ($query) {
					$query
						->select(
							'users.id',
							'role_id',
							'schedule_links.approved',
							'schedule_links.is_paid'
						)
						->where('users.status', true)
					;
				}])
				->with(['Comments' => function ($query) {
					$query
						->where('status', true)
						->where('visible_learner', true)
						->with(['AddedBy' => function ($query) {
							$query
								->select(
									'id',
									'role_id',
									'image'
								)
								->with(['role' => function ($query) {
									$query
										->select(
											'id',
											'name'
										);
								}]);
						}]);
				}])
			;
		} else {
			// For manager, show schedules that were added by manager
			$events = \Models\Schedule::filterVisibleDays($events);
			if (!\APP\Auth::isCd()) {
				$events = $events
					->where(function($query) {
						if (!\APP\Tools::getConfig('listAllEventsToManagers')) {
							$query
								->whereHas('Permissions', function ($query) {
									$query
										->where('user_id', \APP\Auth::getUserId())
									;
								})
								->orWhereHas('Managers', function ($query) {
									$query
										->where('users.id', \APP\Auth::getUserId())
									;
								})
							;
						}
					})
				;
			}

			if ($args['base'] == 'all') {
				$events = $events
					->with(['Departments' => function ($query) {
						$query
							->select(
								'departments.id',
								'departments.name'
							)
							->where('departments.status', 1)
						;
					}])
					->with(['Groups' => function ($query) {
						$query
							->select(
								'groups.id',
								'groups.name'
							)
							->where('groups.status', 1)
						;
					}])
					->with(['Managers' => function($query) use ($args) {
						$query = $query
							->select(
								'users.id',
								'users.fname',
								'users.lname',
								'users.email',
								'users.department_id'
							)
							->selectRaw("CONCAT(users.fname, ' ', users.lname) as full_name")
							->where('users.status', true)
							->with(['Groups' => function ($query) {
								$query
									->select(
										'groups.id',
										'groups.name'
									)
									->where('groups.status', 1)
								;
							}])
						;
					}])
					->with(['Users' => function ($query) use ($args) {
						$query = $query
							->select(
								'users.id',
								'users.fname',
								'users.lname',
								'users.email'
							)
							->selectRaw("CONCAT(users.fname, ' ', users.lname) as full_name")
							->where('users.status', true)
						;
					}])
				;
			}
		}

		$events = $events
			->get()
		;
		if (\APP\Auth::isLearner())  {
			$events->makeHidden(['outlook_refresh_token','outlook_event_response','outlook_event_id']);
		}

		$response->getBody()->write(json_encode($events));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-standards'], 'select'));

	// Export all events as spreadsheet.
	$group->post('/list/download', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();
		if(isset($params['search']))
		{
				if(array_key_exists('status',$params['search']))
				{
						$status = $params['search']['status'];
						$params['search']['schedules__status']=$status;
						unset($params['search']['status']);
				}
				if(array_key_exists('interface',$params['search']))
				{
						unset($params['search']['interface']);
				}
		}
		// need to be CD/Admin/Access to all learners to download this data
		if (
			!\APP\Auth::isCd() &&
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::accessAllLearners()
		) {
			\APP\Tools::returnCode($request, $response, '403');
		}

		$query = \Models\Schedule
			::where('schedules.status', true)
			->select([
				'schedules.id',
				'schedules.name',
				'schedules.type',
				'schedules.start_date',
				DB::raw("DATE_FORMAT(schedules.start_date,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS start_date_uk"),
				'learning_module_categories.name as category_name',
				'users.username as created_by_username',
				'schedules.location',
				'schedules.created_at',
				DB::raw("DATE_FORMAT(schedules.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS created_at_uk"),
				'schedules.created_by',
			])
			->whereNull('schedules.parent_id')
			->leftJoin("learning_module_categories", function ($join) {
				$join
					->on("learning_module_categories.id", "schedules.category_id")
				;
			})
			->leftJoin("users", function ($join) {
				$join
					->on("users.id", "schedules.created_by")
				;
			})->withCount('Waiting')
		;

		$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

		$export_fields = [
			"Event ID" => "id",
			"Name" => "name",
			"Type" => "type",
			"Start Date" => "start_date_uk",
			"Category" => "category_name",
			"Location" => "location",
						"Waiting List" => "waiting_count",
			"Entry Created at" => "created_at_uk",
			"Entry Created by" => "created_by_username",
		];


		$download_file_name = uniqid("events.list.") . ".xlsx";

		\APP\Tools::generateExcelDownload(
			$data,
			$export_fields,
			$this->get('settings')["LMSTempPath"] . $download_file_name
		);

		$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	$group->get('/enrollable/all', function (Request $request, Response $response, array $args) {
        $isEnterprise = !\APP\Tools::getConfig('sharedClients');
		$events = \Models\Schedule
			::select(
				'schedules.*',
				DB::raw("CONCAT(DATE_FORMAT(start_date,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i'), '-', DATE_FORMAT(DATE_ADD(start_date, INTERVAL duration MINUTE),'%H:%i')) AS event_date_range")
			)
            ->where('schedules.status', true)
            ->where(function($query)use($isEnterprise){
                if(!$isEnterprise && (Auth::isManagerInterface() || Auth::isLearningInterface())){
                    $query->join('users as created_user',function($join){
                        $join->on('created_user.id','schedules.created_by');
                        $join->where('created_user.company_id',\APP\Auth::getUserCompanyId());
                    });
                }
                $query->where(function($query){
                $query->where('enrole_any_learner', 1);
                $query->OrWhereIn('schedules.id',
					\Models\ScheduleLink
						::select('schedule_id')
						->whereIn('type', ['users_queue'])
						->where('schedule_links.status', 1)
						->where('link_id', \APP\Auth::getUserId())
						->whereNull('deleted_at')
						->get()
			);
            });
            })
			->where('parent_id', null)
			->with('Children')
			->with('VenueDeatils')
			->with(['Users' => function ($query) {
				$query
					->select(
						'users.id',
						'role_id',
						'schedule_links.approved'
					)
					->where('users.status', true)
				;
			}])
			->with(['Waiting' => function ($query) {
				$query
					->select(
						'users.id',
						'role_id',
						'schedule_links.approved',
						'schedule_links.is_paid'
					)
					->where('users.status', true)
					->where('users.id', \APP\Auth::getUserId())
				;
			}])
			->with(['Category' => function ($query) {
				$query
					->select(
						'learning_module_categories.id',
						'learning_module_categories.name'
					)
					->where('learning_module_categories.status', 1)
				;
			}])
			->with(['Lessons' => function($query) {
				$query = $query
					->select(
						'learning_modules.id',
						'learning_modules.name',
						'learning_modules.keywords',
						'learning_modules.language',
						'learning_modules.thumbnail',
						'learning_modules.promo_image',
						'learning_modules.created_by_event',
						'learning_modules.refresh_period',
						'learning_modules.refresh_date',
						'learning_modules.open_in_events_only',
						'learning_modules.is_course'
					)
					->where('learning_modules.status', true)
					->where(function($query) {
						$query = $query
							->whereHas('Queries', function ($query) {
								$query = $query
									->where(function($query) {
										$query = $query
											->whereRaw("FIND_IN_SET(?, resource_queries.user_ids)", [\APP\Auth::getUserId()])
											->where('resource_queries.action', 'add')
										;
									})
									->orWhere(function($query) {
										$query = $query
											->whereRaw("NOT FIND_IN_SET(?, resource_queries.user_ids)", [\APP\Auth::getUserId()])
											->where('resource_queries.action', 'remove')
										;
									})
								;
							})
							->orDoesntHave('Queries')
						;
					})
				;
			}])
			->where(function($query) {
				$query = $query
					->where('type', '!=', 'lesson')
					->orWhere(function($query) {
						$query = $query
							->whereHas('Lessons', function ($query) {
								$query = $query
									->where('learning_modules.status', true)
									->whereHas('Queries', function ($query) {
										$query = $query
											->where(function($query) {
												$query = $query
													->whereRaw("FIND_IN_SET(?, resource_queries.user_ids)", [\APP\Auth::getUserId()])
													->where('resource_queries.action', 'add')
												;
											})
											->orWhere(function($query) {
												$query = $query
													->whereRaw("NOT FIND_IN_SET(?, resource_queries.user_ids)", [\APP\Auth::getUserId()])
													->where('resource_queries.action', 'remove')
												;
											})
										;
									})
									->orDoesntHave('Queries')
								;
							})
						;
					})
				;
			})
			->with(['Waiting' => function ($query) {
				$query
					->select(
						'users.id',
						'role_id',
						'schedule_links.approved',
						'schedule_links.is_paid'
					)
					->where('users.status', true)
					->where('users.id', \APP\Auth::getUserId())
				;
			}])
			->selectRaw("DATE_ADD(start_date, INTERVAL duration minute) AS end_date")
			->whereNotIn('schedules.id',
				\Models\ScheduleLink
					::select('schedule_id')
					->whereIn('type', ['users'])
					->where('schedule_links.status', 1)
					->where('link_id', \APP\Auth::getUserId())
					->whereNull('deleted_at')
					->get()
			)
		;

		$events = $events
			->where(function($query) {
				$query = $query
					->whereHas('Queries', function ($query) {
						$query = $query
							->whereRaw("FIND_IN_SET(?, resource_queries.user_ids)", [\APP\Auth::getUserId()])
						;
					})
					->orDoesntHave('Queries')
				;
		})
		;

		if (\APP\Auth::isManager()) {
			$events = $events
				->where('visible_schedule', true)
			;
		}

		if (\APP\Auth::isLearner()) {
			$events = $events
				->where('visible_learner', true)
			;
		}

		$events = \Models\Schedule::filterVisibleDays($events);
		$events = $events
			->get()
		;

		$events->makeHidden(['outlook_refresh_token','outlook_event_response','outlook_event_id']);
		$totaldiscount = 0;
		$totaldiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
		$totaldiscount = ($totaldiscount > 100) ? 100 : $totaldiscount;
		foreach($events as $event) {
			if (
				$totaldiscount &&
				$event->cost
			) {
				$event->discounted_cost = $event->cost - round(($event->cost * $totaldiscount) / 100, 2);
			} else {
				$event->discounted_cost = $event->cost;
			}
			if (
				$event->Lessons->isNotEmpty() &&
				$event->Lessons[0]
			) {
				$event->Lessons[0]->setAppends(['safe_thumbnail', 'highlight', 'safe_promo']);
			}
		}

		$response->getBody()->write(json_encode($events));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-standards'], 'select'));

	$group->put('/update-lesson-name/{lesson_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$lesson = \Models\LearningModule::find($args['lesson_id']);

		if (
			$lesson->is_course &&
			isset($data['name']) &&
			$data['name']
		) {
			$lesson->name = $data['name'];
			$lesson->save();
		}


		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	$group->put('/toggle-lesson-order_modules/{lesson_id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$lesson = \Models\LearningModule::find($args['lesson_id']);
		$lesson->order_modules = !$lesson->order_modules;
		$lesson->save();


		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->put('/update-lesson-resource-order/{schedule_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$event = \Models\Schedule::find($args['schedule_id']);

		if (
			$event &&
			$data['new_order'] &&
			is_array($data['new_order'])
		) {
			// This will trigger for managers who created lesson(created_by) and event(created_by)
			$lessons = $event->Lessons;
			if (
				$lessons &&
				$lessons[0]
			) {
				$lesson = $lessons[0];
				\Models\ScheduleLink::processAction(
					'create',
					$lesson->id,
					'lesson',
					[
						'link' => 'resources',
						'entries' => $data['new_order']
					]
				);
			}
		}


		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	$group->put('/update-user-order/', function (Request $request, Response $response, array $args) {

		$data = $request->getParsedBody();

		// Update enrolled user sort order and move them from quueue to enrolled list if needed
		if (
			$data['users'] &&
			is_array($data['users']) &&
			sizeof($data['users']) > 0
		) {
			foreach ($data['users'] as $user) {
				$user_link = \Models\ScheduleLink::find($user['schedule_link_id']);
				if ($user_link) {
					$user_link->type = 'users';
					$user_link->order = $user['order'];
					$user_link->approved = $user['approved'];
					$user_link->save(); // This save needs to be used to trigger attached events to model
				}
			}
		}

		// Update waiting users sort order
		if (
			$data['waiting']
			&& is_array($data['waiting'])
			&& sizeof($data['waiting']) > 0
		) {
			DB::beginTransaction();
				foreach ($data['waiting'] as $user) {
					DB::table('schedule_links')
						->where('id', '=', $user['schedule_link_id'])
						->update([
							'type' => 'users_queue',
							'order' => $user['order']
						]);
				}
			DB::commit();
		}

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	// Get resources assigned to lesson
	$group->get('/lesson-resources/{lesson_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$resources = \Models\LearningModule
			::select('learning_modules.*')
			->where('status', true)
			->whereHas('Course', function ($query) use ($args) {
				$query
					->where('learning_course_id', $args['lesson_id']);
			})
			->join("learning_course_modules", function ($join) use ($args) {
				$join
					->on("learning_course_modules.learning_module_id", "learning_modules.id")
					->where("learning_course_modules.learning_course_id", $args['lesson_id']);
			})
			->orderBy('learning_course_modules.id', 'ASC')
			->get();

		foreach($resources as $resource) {
			$resource->setAppends(['safe_thumbnail', 'safe_promo']);
		}

		$response->getBody()->write(json_encode($resources));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->put('/update-from-lesson/{event_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::roleAllowRefreshEventResources()
		) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$lesson = \Models\LearningModule
			::where('is_course', 1)
			->whereIn('learning_modules.id',
				\Models\ScheduleLink
					::select('link_id')
					->where('schedule_id', $args['event_id'])
					->where('type', 'lesson')
					->get()
			)
			->first()
		;

		$event = \Models\Schedule
			::with('Resources')
			->find($args['event_id'])
		;


		if (
			!$lesson ||
			!$event
		) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		if (
			isset($data['type']) &&
			$data['type'] == 'remove'
		) {

			// Find all linked resources to event and remove them
			foreach ($event->Resources as $key => $resource) {
				$link = \Models\ScheduleLink
					::where('schedule_id', $event->id)
					->where('type', 'resources')
					->where('link_id', $resource->id)
					->first()
				;
				$link->status = false;
				$link->cron_task = true;
				$link->save();
			}
		}

		$data = [
			'schedule_id' => $args['event_id'],
			'type' => 'resources'
		];
		foreach ($lesson->Resources as $key => $resource) {
			$data["link_id"] = $resource->id;
			\Models\ScheduleLink::addNewLink($data);
		}


		return
			$response
		;
	})->add(\APP\Auth::getSessionCheck());


	// Restore deleted event!
	$group->get('/restore/{event_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$event = \Models\Schedule
			::where('id', $args['event_id'])
			->onlyTrashed()
			->first()
		;

		if ($event) {
			// Find all schedule links, restore them!
			$links = \Models\ScheduleLink
				::where('schedule_id', $event->id)
				->onlyTrashed()
				->get()
			;

			foreach ($links as $key => $link) {
				$link->status = true;
				$link->cron_task = true;
				$link->ignore_email = true; // Mark link to not send email about event creation.
				$link->save();
				$link->restore();
			}
			//restore children
			$children = \Models\Schedule
				::where('parent_id', $event->id)
				->onlyTrashed()
				->get()
			;
			foreach ($children as $key => $child) {
				$child->status = true;
				$child->cron_task = false;
				$child->save();
				$child->restore();
			}

			$event->status = true;
			$event->cron_task = true;
			$event->save();
			$event->restore();

		// Restore User Forms

		$user_form_list=\Models\UserFormTemplateWorkflowRelations::where("type","schedule")
		->where("type_id", $event->id)->withTrashed()->get();
		if($user_form_list){
			foreach($user_form_list AS $user_form_list_val){
				\Models\UserForm::deleteUserForms($user_form_list_val->user_form_id,false);
			}
		}


		} else {
			return \APP\Tools::returnCode($request, $response, 404);
		}



		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-deleted-events', 'disable'));

	// Return comments against schedule
	$group->get('/{schedule_id:[0-9]+}/comment', function (Request $request, Response $response, $args) {

		if (!\APP\Auth::isAdminInterface()) {
			return false;
		}

		$comments = \Models\Comment::where([['table_row_id', '=', $args['schedule_id']],
			['table_name', '=', "schedules"]])
			->with(['AddedBy' => function ($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id',
						'image'
					)
					->with('role');
			}])->orderBy('id', "DESC")->get();

		$response->getBody()->write(json_encode($comments));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	/*
		V2, proper schedule events, not tied to specific relationship
	*/

	// Get scheduled event, get event
	$group->get('/{event_id:[0-9]+}{type:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		if (!\APP\Auth::isAdminInterface()) {

			$event = \Models\Schedule
				::where('id', $args['event_id'])
				->where('parent_id', null)
				->with(['Children' => function ($query) use ($args) {
					if ($args['type'] == '/deleted') {
						$query = $query
							->withTrashed()
						;
					}
				}])
				->with('VenueDeatils')
				->first()
			;
			$ids = \Models\Schedule::getChild($event->id);
			$schedules = \Models\Schedule::whereIn("id", $ids)
				->where('enrole_any_learner',1)
				->with(['Users' => function ($query) {
					$query = $query
						->select(
							'users.id'
						)
					;
				}])
				->get()
			;
			$event->schedules=$schedules;
						$totaldiscount = 0;
						$totaldiscount = \Models\User::calculateTotalDiscount(\APP\Auth::getUserId());
						$totaldiscount = ($totaldiscount > 100) ? 100 : $totaldiscount;
						if ($totaldiscount && $event->cost) {
								$event->discounted_cost = $event->cost - round(($event->cost * $totaldiscount) / 100, 2);
						} else {
								$event->discounted_cost = $event->cost;
						}

			$response->getBody()->write(json_encode($event));
			return $response->withHeader('Content-Type', 'application/json');
		}

		$event = \Models\Schedule
			::where('id', $args['event_id'])
			->where('parent_id', null)
			->with(['Children' => function ($query) use ($args) {
				if ($args['type'] == '/deleted') {
					$query = $query
						->withTrashed()
					;
				}
			}])
			->with('Category')
			->with('Departments')
			->with('Groups')
			->with('VisitType')
			->with(['Lessons' => function($query) use ($args) {
				$query = $query
					->select(
						'learning_modules.id',
						'learning_modules.name',
						'learning_modules.description',
						'learning_modules.type_id',
						'learning_modules.is_course',
						'learning_modules.category_id',
						'learning_modules.created_by'
					)
					->where('learning_modules.status', true)
					->with('Type')
					->with(['Files' => function($query) {
						$query
							->where('status', true)
							->with(['AddedBy' => function ($query) {
								$query
									->select(
										'id',
										'fname',
										'lname',
										'role_id'
									)
									->with('role');
							}])
						;
					}])
				;
				if ($args['type'] == '/deleted') {
					$query = $query
						->withTrashed('schedule_links.deleted_at')
					;
				}
			}])
			->with(['Files' => function ($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function ($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id'
							)
							->with('role')
						;
					}])
				;
			}])
			->with(['Comments' => function ($query) {
				$query
					->where('status', true)
					->with(['AddedBy' => function ($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id',
								'image'
							)
							->with('role');
					}]);
			}])
			/*Forum Added by managers*/
			->with(['forum' => function ($query) {
				$query
					->select('id', 'schedule_id', 'added_by', 'visible_learner', 'created_at', 'updated_at')
					->with(['AddedBy' => function ($query) {
						$query
							->select(
								'id',
								'fname',
								'lname',
								'role_id',
								'image'
							)
							->with('role');
					}])
					->with(['topics' => function ($query) {
						$query
							->select('id', 'forum_id', 'added_by', 'created_at', 'updated_at', 'name', 'content')
							->with(['AddedBy' => function ($query) {
								$query
									->select(
										'id',
										'fname',
										'lname',
										'role_id',
										'image'
									)
									->with('role');
							}])
							->first();
					}])
				;
			}])
			/*Forum ends here*/
			->with(['Programmes' => function($query) use ($args) {
				$query = $query
					->select(
						'apprenticeship_standards.id',
						'apprenticeship_standards.name'
					)
					->where('apprenticeship_standards.status', true)
				;
				if ($args['type'] == '/deleted') {
					$query = $query
						->withTrashed('schedule_links.deleted_at')
					;
				}
			}])
			->with(['resources' => function($query) use ($args) {
				$query = $query
					->select(
						'learning_modules.id',
						'learning_modules.name',
						'learning_modules.description',
						'learning_modules.type_id',
						'learning_modules.is_course',
						'learning_modules.material',
						'learning_modules.thumbnail',
						'learning_modules.promo_image'
					)
					->where('learning_modules.status', true)
					->with(['type' => function ($query) {
						$query = $query
							->withoutGlobalScope('type_filter')
						;
					}])
					// This is only for instructor lead lesson
					->with(['LearningResult' => function ($query) {
						$query
							->where('user_id', \APP\Auth::getUserId())
							->where('refreshed', 0)
							->select(
								'id',
								'completion_status',
								'learning_module_id'
							)
						;
					}])
					->with(['ScheduleLink' => function ($query) use ($args) {
						$query = $query
							->where('schedule_id', $args['event_id'])
						;
						if ($args['type'] == '/deleted') {
							$query = $query
								->withTrashed()
							;
						}
					}])
				;
				if ($args['type'] == '/deleted') {
					$query = $query
						->withTrashed('schedule_links.deleted_at')
					;
				}
			}])
			->with(['Users' => function ($query) use ($args) {
				$query = $query
					->select(
						'users.id',
						'users.fname',
						'users.lname',
						'users.email',
						'users.phone',
						'users.image',
						'users.company_id',
						'users.designation_id',
						'users.location_id',
						'users.department_id',
						'users.status'
					)
					->with('company')
					->with('department')
					->with('location')
					->with('Designation')
				//	->where('users.status', true)
					->with(['ScheduleLink' => function ($query) use ($args) {
						$query = $query
							->where('schedule_id', $args['event_id'])
						;
					}])
					// Need to overwrite withIntermediate linked with Users in Schedule.php model
					->withIntermediate('Models\ScheduleLink', ['id', 'completion_status', 'approved', 'authorisation_notes', 'authorisation_notes_by', 'authorisation_notes_at', 'is_authorised', 'learner_requirement', 'is_paid', 'order', 'completed_at'])
				;
				if (
					\APP\Auth::isManager() &&
					!\APP\Auth::accessAllLearners()
				) {
					$query->with(['Managers'=>function($query){
						$query->where('manager_id',\APP\Auth::getUserId());
					}]);
				}
				if ($args['type'] == '/deleted') {
					$query = $query
						->withTrashed('schedule_links.deleted_at')
					;
				}

			}])
			->with('forms')
			->with(['Waiting' => function ($query) use ($args) {
				$query = $query
					->select(
						'users.id',
						'users.fname',
						'users.lname',
						'users.email',
						'users.phone',
						'users.image',
						'users.company_id',
						'users.designation_id',
						'users.location_id',
						'users.department_id',
						'users.status'
					)
					->with('company')
					->with('department')
					->with('location')
					->with('Designation')
					->where('users.status', true)
					->with(['WaitingScheduleLink' => function ($query) use ($args) {
						$query = $query
							->where('schedule_id', $args['event_id'])
						;
					}])
				;
				if ($args['type'] == '/deleted') {
					$query = $query
						->withTrashed('schedule_links.deleted_at')
					;
				}

			}])
			->with(['Managers' => function ($query) use ($args) {
				$query = $query
					->select(
						'users.id',
						'users.fname',
						'users.lname',
						'users.email',
						'users.status'
					)
					->where('users.status', true)
					->with(['ManagerScheduleLink' => function ($query) use ($args) {
						$query = $query
							->where('schedule_id', $args['event_id']);
					}])
				;
				if ($args['type'] == '/deleted') {
					$query = $query
						->withTrashed('schedule_links.deleted_at')
					;
				}
			}])
			->with('CancelledUsers')
						->with('VenueDeatils')
					 // rejected users details
					 ->with(['LinkedUsers' => function($query) use ($args) {
							 $query->with(['ScheduleLinkOfUser' => function ($query) use ($args) {
									 $query
											 ->with('rejectRequestItems')
											 ->where('schedule_id', $args['event_id'])
											 ->whereIn('type', ['users', 'users_queue'])
						 ->has('rejectRequestItems')
									 ;
							 }]);
					 }])
						->with(['RejectedUsers' => function($query) {
								$query->with(['User' => function($query) {
										$query->select('username', 'fname', 'lname', 'id', 'email', 'status');
								}]);
						}])
			->select('schedules.*')
			->selectRaw("IF (end_date is null, DATE_ADD(start_date, INTERVAL duration minute), end_date) as end_date")
		;

		if ($args['type'] == '/deleted') {
			$event = $event
				->onlyTrashed()
			;
		}

	$userCount = ScheduleLink::where(function($query){
		$query->where('type','users');
		$query->orWhere('type','users_queue');
	})->where('schedule_id',$args['event_id'])->count();
	$workflows=[];
	if($userCount>0)
	{
	 $workflows = UserWorkflowForm::with('lesson','programme','schedule')->with(['form_workflow' => function ($query) use ($args) {
				$query->with(['form_workflow_templates' => function ($query) use ($args) {
					$query->with(['document_templates.document_template_bindings.form' => function ($query) use ($args) {
						$query->with(['UserForms' => function ($query) use ($args) {
							$query->where('type_id', $args['event_id']);
							$query->where('type','schedule');
						}]);
					}]);
				}]);
			}])
				->select("*", "user_workflow_form.id AS id", "form_workflow.name as name", "user_workflow_form.created_at")
				->join('form_workflow', function ($join) {
					$join
						->on('form_workflow.id', 'user_workflow_form.form_workflow_id')
						->where("form_workflow.status", "=", 1);
				})
				->join('users', function ($join) {
					$join
						->on("users.id", "=", "user_workflow_form.user_id")
						->where("users.status", "=", 1);
				})
				->where('type','schedule')->where('type_id',$args['event_id'])->get();
	}
		$event = $event
		->first();

		$event->form_workflow = $workflows;

		// if hideNotAssignedLearnerInformationInEvents is true, return only information for learners assigned to manager
		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isCd() &&
			!\APP\Auth::accessAllLearners() &&
			\APP\Auth::hideNotAssignedLearnerInformationInEvents()
		) {
			//loop user list and anonimyze data.
			foreach ($event->Users as $key => $event_user) {
				$assigned = \Models\ManagerUser
					::where('user_id', $event_user->id)
					->where('manager_id', \APP\Auth::getUserId())
					->first()
				;
				if ($assigned) {
					continue;
				}
				$event_user->disabled = true;
				$event_user->fname  = 'Learner';
				$event_user->lname  = '';
				$event_user->phone  = '';
				$event_user->email  = '';
				if ($event_user->company) {
					$event_user->company->name = '';
				}
				if ($event_user->department) {
					$event_user->department->name = '';
				}
				if ($event_user->designation) {
					$event_user->designation->name = '';
				}
				if ($event_user->location) {
					$event_user->location->name = '';
				}
			}
			foreach ($event->Waiting as $key => $event_user) {
				$assigned = \Models\ManagerUser
					::where('user_id', $event_user->id)
					->where('manager_id', \APP\Auth::getUserId())
					->first()
				;
				if ($assigned) {
					continue;
				}
				$event_user->disabled = true;
				$event_user->fname  = 'Learner';
				$event_user->lname  = '';
				$event_user->phone  = '';
				$event_user->email  = '';
				if ($event_user->company) {
					$event_user->company->name = '';
				}
				if ($event_user->department) {
					$event_user->department->name = '';
				}
				if ($event_user->designation) {
					$event_user->designation->name = '';
				}
				if ($event_user->location) {
					$event_user->location->name = '';
				}
			}
		;
	}


		\Models\TableExtension::returnAllFields('schedules', $event->id, $event);
		if (!$event) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		foreach($event->resources as $resource) {
			$resource->setAppends(['safe_thumbnail', 'safe_promo']);
		}

		// I need to find events that overlap with this event, then find out if any user/manager from this event overlaps as well!
		// Also all repeat lessons!!! <- NOT I MPLEMENTED

		// Generate end date!
		//$event->end_date = \Carbon\Carbon::parse($event->start_date)->addMinutes($event->duration)->format('Y-m-d H:i:s');

		// Pluck current event manager and user id's
		$user_id_list = [];
		$manager_id_list = [];
		if ($event->Users){
			$user_id_list = $event->Users->pluck('id')->toArray();
		}
		if ($event->Managers){
			$manager_id_list = $event->Managers->pluck('id')->toArray();
		}

		// Find events that overlap current event!
		$overlapping_cnt = 0;
		if (
			$event->start_date &&
			$event->start_date > 0
		) {
			$start_date = \Carbon\Carbon::parse($event->start_date)->addMinute();
			$end_date = \Carbon\Carbon::parse($event->end_date)->subMinute();
			$over_lap_events = \Models\Schedule
				::where('status', true)
				->select('schedules.*')
				//->selectRaw("DATE_ADD(start_date, INTERVAL duration minute) AS end_date")
				->whereNotIn('schedules.id', [$event->id])
				->where('duration', '>', 0) // Any event with duration 0, will lead to error in below query
				->where(function ($query) use ($start_date,$end_date) {
					$query
						->where('start_date', '<', $end_date)
						->where('end_date', '>', $start_date)
					;
				})
				->with(['Users' => function($query) use ($user_id_list) {
					$query = $query
						->select(
							'users.id',
							'users.fname',
							'users.lname',
							'users.email'
						)
						->where('users.status', true)
						->whereIn('users.id', $user_id_list)
					;
				}])
				->with(['Managers' => function($query) use ($manager_id_list) {
					$query = $query
						->select(
							'users.id',
							'users.fname',
							'users.lname',
							'users.email'
						)
						->where('users.status', true)
						->whereIn('users.id', $manager_id_list)
					;
				}])
				->get()
			;



			// Loop current users and compare with overlap users, if found add  overlapping event to entry against current user
			foreach ($event->Users as $key => $event_user) {
				$event_user->overlap_events = [];
				$event_user->notification = true;
				$event_user->checked = false;

				$event_userArray = $event_user->toArray();
				$event_user->schedule_link?->load('AuthorisationNotesBy');
				if (
					isset($event_userArray['schedule_link']['ignore_email']) &&
					$event_userArray['schedule_link']['ignore_email']
				) {
					$event_user->notification = false;
				}
				$event_user->schedule_link_id = $event_userArray['schedule_link']['id'];

				foreach ($over_lap_events as $key => $over_lap_event) {
					foreach ($over_lap_event->Users as $key => $overlap_user) {
						if ($overlap_user->id == $event_user->id) {
							$overlapping_cnt++;
							$temp_event = new \stdClass();
							$temp_event->name = $over_lap_event->name;
							$temp_event->start_date = \Carbon\Carbon::parse($over_lap_event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat') . " H:i");
							$event_user->overlap_events = array_merge($event_user->overlap_events, [$temp_event]);
						}
					}
				}
			}

			// Same for managers are users
			foreach ($event->Managers as $key => $event_manager) {
				$event_manager->overlap_events = [];
				foreach ($over_lap_events as $key => $over_lap_event) {
					foreach ($over_lap_event->Managers as $key => $overlap_manager) {
						if ($overlap_manager->id == $event_manager->id) {
							$overlapping_cnt++;
							$temp_event = new \stdClass();
							$temp_event->name = $over_lap_event->name;
							$temp_event->start_date = \Carbon\Carbon::parse($over_lap_event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat') . " H:i");
							$event_manager->overlap_events = array_merge($event_manager->overlap_events, [$temp_event]);
						}
					}
				}
			}

		}

		if(\APP\Auth::isManager()) {
			$event->is_loggedin_manager_assigned = $event->Managers && $event->Managers->where('id', '=', \APP\Auth::getUserId())->isNotEmpty();
		}

		// Set boolean if there is at least one overlap event.

		$event->over_lap_event = $overlapping_cnt > 0 ? true : false;
		$ids = \Models\Schedule::getChild($event->id);

		$schedules = \Models\Schedule
			::whereIn("id", $ids)
			->with(['ScheduleParents' => function ($query) {
				$query = $query
					->select(
						'schedules.id',
						'schedules.name'
					)
					->where('schedules.status', true)
				;
			}])
			->get()
		;
		$event->schedules = $schedules;
		$event->hasPermission = \Models\SchedulePermission::hasOwnerPermission($event->id);
		$event->userPermission = \APP\Auth::accessAllLearners();

		$response->getBody()->write(json_encode($event));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	// Create new schedule event!
	$group->post('/new', function (Request $request, Response $response, array $args) {


		$data = $request->getParsedBody();
		/*Logic for limiting the functionality to owner manager, CD and Admin*/
		if (array_key_exists('parent_id',$data) && !\Models\SchedulePermission::hasOwnerPermission($data['parent_id'])){
			return false;
		}
		$create_response = '';
		//Check if there is already venue booked on same time only applicable to outlook venue;
		if (Tools::getConfig('useOutlookVenues') && isset($data['room_email']) && !empty($data['room_email'])) {
			$schedule = Schedule::where(function ($query) use ($data) {
				$startTime = Carbon::parse($data['start_date'])->addMinute(1);
				$endTime = Carbon::parse($data['end_date'])->subMinutes(1);
				$query->where(function ($q) use ($startTime, $endTime) {
					$q->where('start_date', '>=', $startTime)
						->where('start_date', '<=', $endTime);
				})->orWhere(function ($q) use ($startTime, $endTime) {
					$q->where('end_date', '>=', $startTime)
						->where('end_date', '<=', $endTime);
				})->orWhere(function ($q) use ($startTime, $endTime) {
					$q->where('start_date', '<=', $startTime)
						->where('end_date', '>=', $endTime);
				});
			})
				->where('room_email', $data['room_email']);
			$schedule = $schedule->first();
			if ($schedule) {
				/*
					https://openelms.atlassian.net/browse/OPENHELP-1851
					Now check is performed at front end and user is informed about venue conflict, then he can proceed or cancel at front end.
				 */
				//return $response->withStatus(409, "venue");
			}
		}
        if (
			\DB\LicenseFeatures::checkFeatureLimitReached(Auth::getUser(),'smart_classroom_events') &&
			\APP\Tools::getConfig('sharedClients')
		) {
            return \APP\Tools::returnCode($request, $response, 403, 'Licence Limit reached');
        }

		$create_response = \Models\Schedule::createEvent($data);

		$response->getBody()->write(json_encode(['data' => $create_response]));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(new RateLimitMiddleware())->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'insert'));


	// Update event!
	$group->put('/{event_id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$data = $request->getParsedBody();
		$user_ids = [];
		$changed_date = false; //check end date changed or not
		/*Logic for limiting the functionality to owner manager, CD and Admin*/
		if (!\Models\SchedulePermission::hasOwnerPermission($args['event_id'])) {
			return false;
		}
		$data = \Models\Schedule::setAllDayEventTime($data);
		//Check if there is already venue booked on same time only applicable to outlook venue;
		if (Tools::getConfig('useOutlookVenues') && isset($data['room_email']) && !empty($data['room_email'])) {
			$schedule = Schedule::where(function ($query) use ($data) {
				$startTime = Carbon::parse($data['start_date'])->addMinute();
				$endTime = Carbon::parse($data['end_date'])->subMinute(1);
				$query->where(function ($q) use ($startTime, $endTime) {
					$q->where('start_date', '>=', $startTime)
						->where('start_date', '<=', $endTime);
				})->orWhere(function ($q) use ($startTime, $endTime) {
					$q->where('end_date', '>=', $startTime)
						->where('end_date', '<=', $endTime);
				})->orWhere(function ($q) use ($startTime, $endTime) {
					$q->where('start_date', '<=', $startTime)
						->where('end_date', '>=', $endTime);
				});
			})
				->where('room_email', $data['room_email']);
			$schedule = $schedule->where('id', '!=', $args['event_id']);
			$schedule = $schedule->first();
			if ($schedule) {
				/*
					https://openelms.atlassian.net/browse/OPENHELP-1851
					Now check is performed at front end and user is informed about venue conflict, then he can proceed or cancel at front end.
				*/
				//return $response->withStatus(409, "venue");
			}
		}
		$event = \Models\Schedule::find($args['event_id']);
		$event->category_id = isset($data["category_id"]) ? $data["category_id"] : null;
		// Check if category is valid
		if ($event->category_id) {
			$category = \Models\LearningModuleCategory
				::where('id', $event->category_id)
				->where('status', 1)
				->first()
			;
			if (!$category) {
				$event->category_id = null;
			}
		}
		if(isset($data['name']) && $event->name !=$data["name"]){
			$event->update_outlook = "all";
		}
		if(isset($data['description']) && $event->description !=$data["description"]){
			$event->update_outlook = "all";
		}
		if(isset($data['room_email']) && $event->room_email !=$data["room_email"]){
			$event->update_outlook = "all";
		}
		if(isset($data['start_date']) && $event->start_date !=$data["start_date"]){
			$event->update_outlook = "all";
		}
		if(isset($data['end_date']) && $event->end_date !=$data["end_date"]){
			$event->update_outlook = "all";
		}
		$event->name = isset($data["name"]) ? $data["name"] : null;
		$event->cost = isset($data["cost"]) ? $data["cost"] : null;
		$event->description = isset($data["description"]) ? $data["description"] : '';
		$event->location = isset($data["location"]) ? $data["location"] : '';
		$event->visit_type_id = isset($data["visit_type_id"]) ? $data["visit_type_id"] : null;
		$event->start_date = isset($data["start_date"]) ? \Carbon\Carbon::parse($data["start_date"]) : null;
		$event->duration = isset($data["duration"]) ? $data["duration"] : null;
		if (isset($data['end_date'])) {

			if (Carbon::parse($event->end_date)->notEqualTo(Carbon::parse($data['end_date']))) {

				$changed_date = true;
			}
		}
		if (
			isset($data['update_custom_completion_dates']) &&
			$data['update_custom_completion_dates']
		) {
			$changed_date = true;
		}
		$event->end_date = isset($data["end_date"]) ? \Carbon\Carbon::parse($data["end_date"]) : null;
		$event->parent_id = isset($data["parent_id"]) ? $data["parent_id"] : null;
		$event->visible_learner = isset($data["visible_learner"]) ? $data["visible_learner"] : false;
		$event->visible_learner_task = isset($data["visible_learner_task"]) ? $data["visible_learner_task"] : false;
		$event->visible_schedule = isset($data["visible_schedule"]) ? $data["visible_schedule"] : true;
		$event->enrole_any_learner = isset($data["enrole_any_learner"]) ? $data["enrole_any_learner"] : false;
		$event->approval = isset($data["approval"]) ? $data["approval"] : false;
		$event->minclass = isset($data["minclass"]) ? $data["minclass"] : 0;
		$event->maxclass = isset($data["maxclass"]) ? $data["maxclass"] : 0;
		$event->room_email = isset($data["room_email"])?$data["room_email"]:"";
		if(Tools::getConfig('useOutlookVenues')){
				if(!isset($data['extended'])){
					$data['extended']=[];
			}
			$data['extended']['venue_id'] = "";
		}
		$event->save();
		$deadlineAt = $dropOffDeadlineAt = null;
		if(!empty($data['deadline_at']) && $event->enrole_any_learner) {
			$deadlineAt = \Carbon\Carbon::parse($data["deadline_at"]);
			if(\Carbon\Carbon::parse($data["start_date"])->lessThan($deadlineAt)) {
				$deadlineAt = \APP\Tools::calculateDeadline($data['start_date']);
			}
		}

		if(!empty($data['drop_off_deadline_at']) && $event->enrole_any_learner) {
			$dropOffDeadlineAt = \Carbon\Carbon::parse($data["drop_off_deadline_at"]);
			if(\Carbon\Carbon::parse($data["start_date"])->lessThan($dropOffDeadlineAt)) {
				$dropOffDeadlineAt = \APP\Tools::calculateDeadline($data['start_date']);
			}
		}

		$event->deadline_at = $deadlineAt;
		$event->drop_off_deadline_at = $dropOffDeadlineAt;

		if ($event->lesson) {
			$event->lesson->save();
		}

		if (!$event->parent_id) {
			$event->cron_task = true;
		}

		if ($event->outlook_integration && \APP\Tools::getConfig("enableGlobalOutlookIntegration")) {
			$event->outlook_refresh_token = "use_global";
		}

		$event->save();


		if (!empty($data['custom_field'])) {
			Form::saveCustomForm($data['custom_field'],'event',$event->id,0);
		}

		if (isset($data['room_email']) && $data['room_email'] && Tools::getConfig('useOutlookVenues')) {
			$teams = new Teams();
			if (!isset($data['end_date'])) {
				$end_date = Carbon::create($data['start_date'])->addMinute($data['duration']);
			} else {
				$end_date = $data['end_date'];
			}
			$defaultTimeZone =  Tools::getConfig('defaultTimeZone');
			if($event->venue && $event->venue->id){
				$venue_details = Venue::where('id',$event->venue->value)->first();
			}
			$teams->token = Venue::getToken();
			$locations = Venue::getLocation($data['room_email']);
			if($locations)
			{
				$locations = $locations->value;
				$venue = Venue::where('room_email',$data['room_email'])->first();
				if(!$venue){
					$address = "";
					$postcode = "";
					if($locations[0]->address)
					{
						$address = $locations[0]->address->street."\n".$locations[0]->address->city."\n".$locations[0]->address->state."\n".$locations[0]->address->countryOrRegion;
						$postcode = $locations[0]->address->postalCode;
					}
					$venue = Venue::create(['room_email'=>$data['room_email'],'name'=>$locations[0]->displayName,'address'=>$address,'postcode'=>$postcode,'capacity'=>$locations[0]->capacity,'status'=>1]);
				}
				if(!isset($data['extended'])){
					$data['extended']=[];
				}
				$data['extended']['venue_id']=$venue->id;

			}
		}

		// IF extension fields are present loop them and update data accordingly.
		if (isset($data["extended"])) {
			foreach ($data["extended"] as $field_name => $value) {
				\Models\TableExtension::updateField('schedules', $event->id, $field_name, $value);
			}
		}


		// Update relevant lesson name also!
		if ($event->type == 'lesson') {
			$lesson = \Models\LearningModule
				::where('is_course', 1)
				->whereIn('learning_modules.id',
					\Models\ScheduleLink
						::select('link_id')
						->where('schedule_id', $event->id)
						->where('type', 'lesson')
						->get()
				)
				->first();

			if (
				$lesson &&
				$lesson->is_course
			) {
				//$lesson->name = $event->name;
				if ($event->category_id) {
					$lesson->category_id = $event->category_id;
				}
				$lesson->save();
			}


			// This is triggered when lesson is changed in event.
			$existingLessonId = $event->lesson->id ?? null;
			if (
				$event->lesson &&
				$data["lesson_id"] != $existingLessonId
			) {

				//Remove existing Lesson
				\Models\ScheduleLink
					::where("type","lesson")
					->where("schedule_id", $event->id)
					->where('link_id', $existingLessonId)
					->update(
						[
							'deleted_by' => \APP\Auth::getUserId(),
							'cron_task' => true,
							'status' => false
						]
					)
				;

				//Add new lesson to event
				$link = \Models\ScheduleLink
					::firstOrNew(
						[
							"schedule_id" => $event->id,
							"type" => 'lesson',
							"link_id" => $data["lesson_id"],
						]
					)
				;

				$link->status = true;
				$link->cron_task = true;
				$link->deleted_by = NULL;
				$link->save();

				$lesson_id = $data["lesson_id"];

				// Get all resources assigned to lesson and link them with event
				$resources = \Models\LearningModule
					::where('status', true)
					->whereIn('id',
						\Models\LearningCourseModule
							::select('learning_module_id')
							->where('learning_course_id', $lesson_id)
							->get()
					)
					->get()
				;
				foreach ($resources as $key => $resource) {
					$link = \Models\ScheduleLink::firstOrNew(
						[
							"schedule_id" => $event->id,
							"type" => 'resources',
							"link_id" => $resource->id,
						]
					);
					$link->status = true;
					$link->cron_task = true;
					$link->save();
				}



				//fetch existing users of events
				foreach($event->users AS $user) {
					$user_ids[]=$user->id;
				}

				// IF Attendees have Attended the event at some point, then mark the newly added lesson as attended!

				\Models\Schedule::processEvents(false, $event->id, $user_ids, true);

			}

			// This is not needed here as "schedule/update-user-order" is sending user order on every event save, if something has changed there.
			//\Models\ScheduleLink::resetUsers($args['event_id']);
		}

		if ($changed_date && \APP\Tools::getConfig('changeResourceDateWhenEventDateChange')) {
			ScheduleLink::where('schedule_id', $event->id)->whereIn('type', ['resources', 'lesson'])->update(['cron_task' => true]);
		}

		// Check and delete event if marked for deletion.
		if (
			isset($data["delete"]) &&
			$data["delete"] &&
			\APP\Auth::checkStructureAccess(['lessons-and-learning-resources'], 'disable')
		) {
			\Models\Schedule::deleteEvent($event->id);
		}

		$event->cron_task = true;
		$event->save();
		$response->getBody()->write(json_encode($event));
		return $response->withHeader('Content-Type', 'application/json');

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	// delete event child
	$group->delete('/{event_child_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		/*Logic for limiting the functionality to owner manager, CD and Admin*/

		$event = \Models\Schedule::find($args['event_child_id']);

		if (
			$event &&
			$event->parent_id
		) {
			if(! \Models\SchedulePermission::hasOwnerPermission($event->parent_id)){
				return false;
			}
			\Models\Schedule::deleteEvent($event->id);
		}
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	$group->put('/property/{event_id:[0-9]+}', function (Request $request, Response $response, array $args) {

		$data = $request->getParsedBody();

		$event = \Models\Schedule::find($args['event_id']);
		if ($event) {
			foreach ( $data as $key => $value ) {
				$event->$key = $value;
			}

			$event->save();
		}
		$response->getBody()->write(json_encode($event));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	// Change link completion status
	$group->put('/update-completion-status/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (
			isset($data['type'])
		) {
			switch ($data['type']) {
				case 'user':
					$link = \Models\ScheduleLink
						::where('id', $args['id'])
						->with('Schedule')
                        ->first();
                    SChedule::processEvents(false, [$link->schedule_id], [$link->link_id]);
					if (
						$link &&
						isset($data['completion_status'])
					) {

						// If there is no %% at the end or beggining, strip % out and add at the end and at the beggining
						if (!preg_match('/^%%.*%%$/', $data['completion_status'])) {
							$data['completion_status'] =  '%%' . str_replace('%', '', $data['completion_status']) . '%%';
						}

						if (
							$link->completion_status != '%%event_completion_state_not_attempted%%' &&
							$data['completion_status'] == '%%event_completion_state_completed%%' &&
							$link->Schedule->type == 'meeting'
						) {
							\Models\User::updateLastContactDate($link->link_id, $link->Schedule->start_date);
						}
						$link->completion_status = $data['completion_status'];
						$link->authorisation_notes = isset($data['authorisation_notes']) ? $data['authorisation_notes'] : null;
						if (
							$link->isDirty('authorisation_notes') &&
							$link->authorisation_notes != null &&
							\APP\Auth::getUserId()
						) {
							$link->authorisation_notes_by = \APP\Auth::getUserId();
							$link->authorisation_notes_at = \Carbon\Carbon::now();
						} else {
							$link->authorisation_notes_by = null;
							$link->authorisation_notes_at = null;
						}
						if ($data['completion_status'] == '%%event_completion_state_not_attempted%%') {
							$link->is_authorised = $data['is_authorised'];
						} else {
							$link->is_authorised = null;
						}
						// In case something is messed up!
						if (!$link->completion_status) {
							$link->completion_status = '%%event_completion_state_not_attempted%%';
						}
						if (isset($data['completed_at'])) {
							$link->completed_at = \Carbon\Carbon::parse($data['completed_at']);
						}

						$link->save();
					}
					break;

				case 'users':
					if (isset($data['completion_status'])) {
						$links = \Models\ScheduleLink
							::where('schedule_id', $args['id'])
							->where('type', 'users')
							->with('Schedule')
							->where('status', true)
							->get();
                        foreach ($links as $key => $link) {
                            SChedule::processEvents(false, [$link->schedule_id], [$link->link_id]);
							if (
								$link->completion_status != '%%event_completion_state_completed%%' &&
								$data['completion_status'] == '%%event_completion_state_completed%%' &&
								$link->Schedule->type == 'meeting'
							) {
								\Models\User::updateLastContactDate($link->link_id, $link->Schedule->start_date);
							}
							$link->completion_status = $data['completion_status'];
							if (
								isset($data['completed_at']) &&
								$data['completed_at']
							) {
								$link->completed_at = \Carbon\Carbon::parse($data['completed_at']);
							}
							$link->save();
						}
					}
				break;
			}
		}
		$response->getBody()->write(json_encode(['status' => true]));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	$group->post('/update-completion-date', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$link = \Models\ScheduleLink
			::where('id', $data['schedule_link_id'])
			->where('link_id', $data['schedule_link']['link_id'])
			->first()
		;
		$link->completed_at = \Carbon\Carbon::parse($data['completed_at']);
		$link->save();

		return
			$response
		;

	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));


	// Change manager visitor status
	$group->put('/update-visitor-status/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$link = \Models\ScheduleLink
			::where('id', $args['id'])
			->where('type', 'managers')
			->with('Schedule')
			->first();

		if (
		$link
		) {
			$link->manager_visitor = !$link->manager_visitor;
			$link->save();
		}


	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->post('/link/update', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (
			isset($data["id"])
		) {
			\Models\ScheduleLink::updateLink($data);
		}

		return
			$response//->write($event->id)
			;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->post('/import-attendees', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		if (isset($_FILES['importFile'])) {

			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSTempPath"]);
			$import_file = new \Upload\File('importFile', $storage);
			$import_file_name = $import_file->getNameWithExtension();
			$extension = $import_file->getExtension();
			$import_file_id = uniqid();
			$import_file->setName($import_file_id);

			try {
				$import_file->upload();
			} catch (\Exception $e) {
				$errors = $import_file->getErrors();
				return \APP\Tools::returnCode($request, $response, 503, implode("\n", $errors));
			}
			\Models\LogExportImport::insertRecord(file_get_contents($this->get('settings')["LMSTempPath"] . $import_file->getNameWithExtension()), '.' . $import_file->getExtension(), $import_file_name, false, 'imports');

			$php_excel = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->get('settings')["LMSTempPath"] . $import_file->getNameWithExtension());
			$sheet = $php_excel->getActiveSheet();
			$rows = $sheet->getRowIterator(1);
			$fields = [
				"A" => "username",
				"B" => "completion_status",
				"C" => "is_authorised",
				"D" => "authorisation_notes",
			];

			$data = [];

			foreach ($rows as $row) {
				$row_i = $row->getRowIndex();
				$cells = $row->getCellIterator();
				$cells->setIterateOnlyExistingCells($row_i == 1);
				if ($row_i > 1) {
					$data[$row_i] = [];
				}
				foreach ($cells as $cell_i => $cell) {
					if ($row_i != 1) {
						if (
							empty($fields[$cell_i])
						) {
							continue;
						}
						$data[$row_i][$fields[$cell_i]] = $cell->getFormattedValue();
					}
				}
			}

			foreach ($data as $key => $entry) {
				if (isset($params["eventid"])) {
					$user = \Models\User
						::where('username',$entry['username'])
						->select('id')
						->first()
					;
					if (!$user) {
						continue;
					}

					$savedata = [];
					$savedata['schedule_id'] = $params["eventid"];
					$savedata['link_id'] = $user->id;
					$savedata['type'] = 'users';
					$savedata['completion_status'] = $entry['completion_status'];
					$savedata['authorisation_notes'] = $entry['authorisation_notes'];
					$savedata['is_authorised'] = $entry['is_authorised'];

					$schedule = \Models\Schedule
						::where('id', $params["eventid"])
						->with(['users' => function ($query) {
							$query = $query
								->select(
									'users.id'
								)
								->where('users.status', true)
							;
						}])->first()
					;

					if (isset($schedule->users)) {
						if ($schedule->maxclass > 0) {
							if ($schedule->maxclass <= count($schedule->users)) {
								$savedata["type"] = 'users_queue';
							}
						}
					}

					\Models\ScheduleLink::addNewLink($savedata);
				}
			}
		}



		return
			$response//->write($event->id)
		;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'insert'));


	// Link schedule with whatever entries
	$group->post('/link/new', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$user = \APP\Auth::getUser();

		$response_data = new \stdClass();
		$response_data->linked_events = [];

		if (
			empty($data["schedule_id"]) ||
			empty($data['type']) ||
			empty($data['link_id'])
		) {
			return \APP\Tools::returnCode($request, $response, '404');
		}

		$event = \Models\Schedule::find($data["schedule_id"]);

		if (!$event) {
			return \APP\Tools::returnCode($request, $response, '404');
		}

		// check deadline date for enrolling has passed
		if(!empty($event->deadline_at)) {
			$deadlineDateTime = \Carbon\Carbon::parse($event->deadline_at);
			if ($deadlineDateTime->isPast() && \APP\Auth::isLearner()) {
				$bookingDeadlineConfig = \APP\Tools::getConfig('BookingDeadline');
				$bookingDeadlineConfigMessage = \APP\Tools::subArrayKeyValue($bookingDeadlineConfig, 'name', 'DeadlinePassedMessage', 'value');
				return \APP\Tools::returnCode($request, $response, 422, ['error_type' => 'deadline_passed', 'message' => $bookingDeadlineConfigMessage]);
			}
		}

		$created_by = \Models\User::find($event->created_by);

		/*Logic for limiting the functionality to owner manager, CD and Admin*/
		$permission = \Models\SchedulePermission::hasOwnerPermission($data["schedule_id"]);
		$user_permisssion = \Models\SchedulePermission::hasUserPermission($data['link_id']);
		$user_type = in_array($data['type'], ['users_queue','users']);
		if (
			(
				!$permission &&
				!$user_type
			) ||
			(
				$user_type &&
				!$user_permisssion &&
				!\APP\Auth::isLearner()
			) ||
			(
				\APP\Auth::isLearner() &&
				$event->enrole_any_learner != 1 &&
				\APP\Auth::getUserId() != $data["link_id"] &&
				!$user_type
			)
		) {
			return \APP\Tools::returnCode($request, $response, '403');
		}

		if (
			isset($data["schedule_id"]) &&
			$event &&
			isset($data["type"]) &&
			(
				(\APP\Auth::isAdminInterface() && isset($data["link_id"])) ||
				// Or learner who is enrolling
				(
					\APP\Auth::isLearner() &&
					$event->enrole_any_learner == 1 &&
					(
						$data["type"] == 'users' ||
						$data["type"] == 'users_queue'
					) &&
					$data["link_id"] == \APP\Auth::getUserId()
				)
			)
		) {
			if (
				$data["type"] == 'users' ||
				$data["type"] == 'users_queue'
			) {
				$schedule = \Models\Schedule
					::where('id', $data["schedule_id"])
					->with(['Schedules' => function ($query) {
						$query = $query
							->with(['users' => function ($query) {
								$query = $query
									->select(
										'users.id'
									)
									->where('users.status', true)
								;
							}])
						;
					}])
					->with(['users' => function ($query) {
						$query = $query
							->select(
								'users.id'
							)
							->where('users.status', true)
						;
					}])
					->first()
				;

				// If learners enrolls to free event or manager adds user to any event
				if (
					(
						$event->cost == 0 ||
						$event->cost == "" ||
						$event->cost == NULL
					) ||
					\APP\Auth::isAdminInterface()
				) {
					if (
						// if event is enrolleable and all places taken, put user on queue
						(
							$schedule->enrole_any_learner && // Maxclass only if event is enrolleable
							$schedule->maxclass > 0 &&
							$schedule->maxclass <= count($schedule->users)
						) ||
						// If event has cost, put on queue
						(
							\APP\Tools::getConfig('isCivicaPaymentsEngine') &&
							$schedule->cost != NULL && // If cost is 0, users were put in users_queue
							$schedule->cost != 0
						) ||
						// If event can be enrolled and needs approval, and is learner, queue
						(
							$schedule->enrole_any_learner &&
							$schedule->approval == '1' && // Learners added by managers do not need to be approved
							\APP\Auth::isLearner()
						)
					) {
						if(Auth::isAdminInterface()){
							if(
							$schedule->enrole_any_learner && // Maxclass only if event is enrolleable
							$schedule->maxclass > 0 &&
								$schedule->maxclass <= count($schedule->users)){
								$user["type"] = 'users_queue';
							}else{
								$user["type"] = 'users';
							}
						}else{
							$data["type"] = 'users_queue';
						}

					}

					$ids = \Models\Schedule::getChild($data["schedule_id"]);
					$schedules = \Models\Schedule
						::whereIn("id", $ids)
						->select([
							'id',
							'name',
							'enrole_any_learner',
							'maxclass',
							'cost',
							'approval',
							'start_date',
						])
						->with(['users' => function ($query) {
							$query = $query
								->select(
									'users.id'
								)
								->where('users.status', true)
							;
						}])
						->get()
					;

					$waitingList = 0;

					foreach ($schedules as $key => $linked_schedule) {
						if ($linked_schedule->id != $schedule->id) {
							$insert["link_id"] = $data['link_id'];
							$insert["schedule_id"] = $linked_schedule->id;
							$insert["type"] = 'users'; // reset user type for each linked event!

							if (isset($linked_schedule->users)) {
								if (
									$linked_schedule->enrole_any_learner && // Check if this is enrolleable first, then check for maxclass
									$linked_schedule->maxclass > 0
								) {
									if ($linked_schedule->maxclass <= count($linked_schedule->users)) {
										$insert["type"] = 'users_queue';
										$waitingList = 1;
									}
								}
							}

							if (
								$linked_schedule->cost > 0 &&
								\APP\Tools::getConfig('isCivicaPaymentsEngine')
							) {
								$insert["is_paid"] = '0';
							}
							$insert["order"] = \Models\ScheduleLink::where("schedule_id", $insert["schedule_id"])->where("type", $insert["type"])->where("status", 1)->max("order")+1;
							if(isset($data['learner_requirement'])){
								$insert['learner_requirement']=$data['learner_requirement'];
							}
							// Sometimes event have approval set to true even if enrole_any_learner is not set, check againt both flags.
							$insert["approval"] = ($linked_schedule->enrole_any_learner && $linked_schedule->approval);

							// Manager interface approves automatically all linked events.
							if (\APP\Auth::isAdminInterface()) {
								$insert["approval"] = 0;
							}
							if((Auth::isLearningInterface() && $schedule->approval != 1) || Auth::isAdminInterface()){
								\Models\ScheduleLink::addNewLink($insert);
							}
							// If learner enrolls, process his event assignment!
							if (
								\APP\Auth::isLearner() &&
								$event->enrole_any_learner == 1 &&
								(
									$insert["type"] == 'users' ||
									$insert["type"] == 'users_queue'
								) &&
								$insert["link_id"] == \APP\Auth::getUserId()
							) {
								// TODO, processEvents for just this event, for just this user!
								\Models\Schedule::processEvents(false, $insert['schedule_id'], $insert["link_id"]);
								$response_data->linked_events[] = $linked_schedule;
							}
						}
					}
				}


			}

			$data["order"] = \Models\ScheduleLink::where("schedule_id", $data["schedule_id"])->where("type", $data["type"])->where("status", 1)->max("order")+1;

			// if events are linked, reverse link as well
			if ($data["type"] == 'schedules') {
				\Models\ScheduleLink::addNewLink([
					'schedule_id' => $data["link_id"],
					'link_id' => $data["schedule_id"],
					'type' => $data["type"],
					'order' => $data["order"]
				]);
			}

			// Managerial interface will approve all events by default
			$data['approval'] = 0;

			if (\APP\Auth::isLearner()) {
				$data['approval'] = $schedule->approval;
			}
			\Models\ScheduleLink::addNewLink($data);

			/**
			 * Add Workflow from Learning Resource
			 */
			if($data['type']=="programmes"){
				$learning_modules=\Models\ApprenticeshipStandard::fetchFormIdsUsingProgrammes($data["link_id"]);
				$form_ids=\Models\FormWorkflow::getFormFromLearningModuleSchedule($learning_modules);

				if($form_ids){
					foreach($form_ids AS $formId){
						\Models\ScheduleLink::addNewLink([
							'schedule_id' => $data["schedule_id"],
							'link_id' => $formId['form_id'],
							'type' => "forms"
						]);
						$data['schedule_linked_type']='form_workflow';
						$data["schedule_linked_type_id"]=$formId['workflow_id'];
						$data["assigned_method"]='workflow_schedule_programme_assign';
						$data["reference_type_id"]=$data["link_id"];
						$data["reference_type"]="programme";
						$data["schedule_type"]='forms';
						$data["schedule_type_id"]=$formId['form_id'];
						$data=array_merge($data,$formId);
						\Models\Schedule::assignFormsToUser($data);
					}
				}
			}

			/**
			 * type is form
			 * Here check when add form - existing users in events added to current form
			 */
			$data["event_date"]=$event->start_date;
			//used for track the direct event and workflowevents
			$data['schedule_linked_type']='direct';
			if($data["type"]=="forms"){
				\Models\Schedule::assignFormsToUser($data);
			}

			/*****************************************************************
			 * WorkFlow, Templates and Forms are assigned though this section *
			 * ***************************************************************/
			if($data["type"]=="users"){
				\Models\Schedule::assignUsersToForm($data);
			}

			// If learner enrolls, process his event assignment!
			if (
					\APP\Auth::isLearner() &&
					$event->enrole_any_learner == 1 &&
					(
						$data["type"] == 'users' ||
						$data["type"] == 'users_queue'
					) &&
					$data["link_id"] == \APP\Auth::getUserId()
			) {
				// TODO, processEvents for just this event, for just this user!
				\Models\Schedule::processEvents(false, $data['schedule_id'], $data["link_id"]);
			}

			if (
				$event->enrole_any_learner == 1 &&
				$event->approval == 1 &&
				\APP\Auth::isLearner()
			) {
				// Send e-mail to Manager that status changes to approval
				$template = \Models\EmailTemplate::getTemplate('Event Approval Request');
				if ($template) {

					$managerList = \Models\ManagerUser
						::select('manager_id', 'users.fname', 'users.lname')
						->where('manager_users.user_id', \APP\Auth::getUserId())
						->join("users", function($join) {
							$join->on("manager_id", "users.id");
						})
						->get()
					;

					foreach($managerList AS $managerListVal){
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->recipients = [$managerListVal->manager_id];
						$email_queue->from = \APP\Auth::getUserId();
						$email_queue->custom_variables = json_encode([
							'USER_FNAME' => $managerListVal->fname,
							'USER_LNAME' => $managerListVal->lname,
							'TRAINEE_FNAME' => $user->fname,
							'TRAINEE_LNAME'=> $user->lname,
							'EVENT_NAME' => $event->name,
							'EVENT_DESCRIPTION' => $event->description,
							'EVENT_LOCATION' => $event->location,
							'EVENT_DATE' => $event->start_date ? \Carbon\Carbon::parse($event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat')) : "--",
							'CONFIG_LMSUrl' => $GLOBALS["CONFIG"]->LMSUrl,
							'MANAGER_APPROVAL_LINK' => 'app/dashboard/manage-learning/manage-learning-resources/lessons-and-learning-resources',
							'REGARDS' => $GLOBALS["CONFIG"]->Regards,
						]);
						$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($event,$email_queue->custom_variables);
						$email_queue->save();
					}
				}
			}
		}

		$response_data->type = $data["type"];

		if (!\APP\Auth::isAdminInterface()) {
			if ($data['approval'] == 1) {
				$response_data->type = 'not_approved';
			}
		} else {
			\Models\ScheduleLink::resetUsers($event->id);

			if (
				isset($waitingList) &&
				$waitingList
			) {
				$response_data->type = 'waiting_list';
			}
		}

		$response->getBody()->write(json_encode($response_data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(new RateLimitMiddleware(60, 100))->add(\APP\Auth::getSessionCheck());

	$group->post('/link/getlink', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (
			isset($data["schedule_id"]) &&
			isset($data["type"]) &&
			isset($data["link_id"])
		) {
			$link = \Models\ScheduleLink
				::where('schedule_id', $data["schedule_id"])
				->where('type', $data["type"])
				->where('link_id', $data["link_id"])
				->first();

			$response->getBody()->write($link);
			return $response;

		}

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	$group->post('/link/delete', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		if (
			empty($data["schedule_id"]) ||
			empty($data["type"]) ||
			empty($data["link_id"])
		) {
			return \APP\Tools::returnCode($request, $response, '404');
		}
		$event = \Models\Schedule::find($data["schedule_id"]);
		if (!$event) {
			return \APP\Tools::returnCode($request, $response, '404');
		}

		// If learner and he is not the one canceling his assignment, fail
		$permission = \Models\SchedulePermission::hasOwnerPermission($data["schedule_id"]);
		$user_permisssion = \Models\SchedulePermission::hasUserPermission($data['link_id']);
		$user_type = in_array($data['type'],['users_queue','users']);
		if (
			(
				!$permission &&
				!$user_type
			) ||
			(
				$user_type &&
				!$user_permisssion &&
				!\APP\Auth::isLearner()
			) ||
			(
				\APP\Auth::isLearner() &&
				$event->enrole_any_learner != 1 &&
				\APP\Auth::getUserId() != $data["link_id"] &&
				!$user_type
			)
		) {
			return \APP\Tools::returnCode($request, $response, '403');
		}

		// check deadline date for cancellation/drop-off has passed
		if(!empty($event->drop_off_deadline_at)) {
			$dropOffDeadlineDateTime = \Carbon\Carbon::parse($event->drop_off_deadline_at);
			if ($dropOffDeadlineDateTime->isPast() && \APP\Auth::isLearner()) {
				$dropOffDeadlineConfig = \APP\Tools::getConfig('EventDropOffDeadline');
				$dropOffDeadlineConfigMessage = \APP\Tools::subArrayKeyValue($dropOffDeadlineConfig, 'name', 'EventDropOffDeadlineMessage', 'value');
				return \APP\Tools::returnCode($request, $response, 422, ['error_type' => 'drop_off_deadline_passed', 'message' => $dropOffDeadlineConfigMessage]);
			}
		}
		##########################################################
		if ($data['type'] == 'users') {
			$refund_status = \APP\Controllers\GlobalPaymentController::refund('schedules', $data['schedule_id'], $data['link_id']);
			GovUKPayController::refund('schedules', $data['schedule_id'], $data['link_id']);
		}

		if ($data["type"] == 'linkevents') {
			$link_events = \Models\LinkedEvent
				::where('event_id', $data["schedule_id"])
				->where('link_id', $data["link_id"])
				->get()
			;
			foreach ($link_events as $key => $link_event) {
				if (\APP\Auth::getUserId()) {
					$link_event->deleted_by = \APP\Auth::getUserId();
					$link_event->delete();
				}
			}
		} else {

			$link = \Models\ScheduleLink
				::where('schedule_id', $data["schedule_id"])
				->where(function ($query) use ($data) {
					$query
						->where('type', $data["type"])
					;
					// Workaround for issue where user wants to cancel event and he is as users_queue, permanent temporaty solution
					if ($data["type"] == 'users') {
						$query = $query
							->orWhere('type', 'users_queue')
						;
					}
				})
				->where('status', true)
				->where('link_id', $data["link_id"])
				->first()
			;
			// Fail all ordeal if link is not found.
			if (!$link) {
				return \APP\Tools::returnCode($request, $response, 404);
			}
			if($link->type=="users"){
				//Update linked schedules update_outlook
				Schedule::where('id',$data["schedule_id"])->where('update_outlook','!=','all')->update(['update_outlook'=>'user','cron_task'=>true]);
			}
			// Remove linked events as well, but not for learner
			if (
				\APP\Auth::isAdminInterface() &&
				(
					(
						in_array($data['type'],['users','users_queue']) &&
						array_key_exists("remove_child", $data) &&
						$data['remove_child']
					) ||
					(
						in_array($data['type'],['users']) &&
						array_key_exists("schedule_id", $data) &&
						array_key_exists("link_id", $data) &&
						$data['link_id'] &&
						$data['schedule_id']
					)
				)
			) {
				$schedules = \Models\Schedule
					::getChild($data['schedule_id'],[$data['schedule_id']])
				;
				//Update linked schedules update_outlook
				Schedule::where('id',$schedules)->where('update_outlook','!=','all')->update(['update_outlook'=>'user','cron_task'=>true]);
				\Models\ScheduleLink
					::whereIn('schedule_id', $schedules)
					->whereIn('type', ['users','users_queue'])
					->where('link_id', $data["link_id"])
					->update(
						[
							'status' => false,
							'deleted_by' => \APP\Auth::getUserId(),
							'cron_task' => true
						]
					)
				;
				foreach($schedules as $id) {
					\Models\ScheduleLink::resetUsers($id);
                    \Models\Schedule::setForCron($id);
                    Schedule::where('id',$id)->where('update_outlook','!=','all')->update(['update_outlook'=>'user']);
				}
			}
			if(\APP\Auth::isLearningInterface()){
				$schedules = \Models\Schedule
					::getChild($data['schedule_id'],[$data['schedule_id']])
				;
				//Update linked schedules update_outlook
				Schedule::where('id',$schedules)->where('update_outlook','!=','all')->update(['update_outlook'=>'user','cron_task'=>true]);
				\Models\ScheduleLink
					::whereIn('schedule_id', $schedules)
					->whereIn('type', ['users','users_queue'])
					->where('link_id', $data["link_id"])
					->update(
						[
							'status' => false,
							'deleted_by' => \APP\Auth::getUserId(),
							'cron_task' => true
						]
					)
				;
				foreach($schedules as $id) {
					\Models\ScheduleLink::resetUsers($id);
                    \Models\Schedule::setForCron($id);
                    Schedule::where('id',$id)->where('update_outlook','!=','all')->update(['update_outlook'=>'user']);
				}
			}

			$link->status = false;
			$link->deleted_by = \APP\Auth::getUserId();
			$link->cron_task = true;
			$link->cancellation_reason = isset($data["cancellation_reason"]) ? $data["cancellation_reason"] : '';
			$link->save();

						// add rejection reason by manager
						if(isset($data['rejection_reason_id'])) {
								$rejectionReason = new \Models\RejectRequestItems();
								$rejectionReason->user_id = \APP\Auth::getUserId();
								$rejectionReason->item()->associate($link);
								$rejectionReason->rejection_reason_id = $data['rejection_reason_id'];
								$rejectionReason->rejection_reason = $data['rejetion_reason_comment'];
								$rejectionReason->save();
						}


			$created_by = \Models\User::find($link->link_id);

			\Models\ScheduleLink::resetUsers($data["schedule_id"]);
            \Models\Schedule::setForCron($data["schedule_id"]);
            Schedule::where('id',$data['schedule_id'])->where('update_outlook','!=','all')->update(['update_outlook'=>'user']);

			// Detach resources from event lesson
			if ($data["type"] == 'resources') {
				$lesson = \Models\LearningModule
					::where('is_course', 1)
					->whereIn('learning_modules.id',
						\Models\ScheduleLink
							::select('link_id')
							->where('schedule_id', $data["schedule_id"])
							->where('type', 'lesson')
							->get()
					)
					->first()
				;

				// if lesson has been created by same person who created event/schedule!
				// And user is not CD
				if (
					!\APP\Auth::isCd() &&
					$lesson &&
					$lesson->created_by == $event->created_by
				) {
					$lesson->modules()->detach([$data["link_id"]]);
				}
			}

			// If departments or groups are detached, detach users in those collections
			if (
				$data["type"] == 'departments' ||
				$data["type"] == 'groups'
			) {
				if ($data["type"] == 'departments') {
					$user_collection = \Models\Department
						::where('id', $data["link_id"]);
				}
				if ($data["type"] == 'groups') {
					$user_collection = \Models\Group
						::where('id', $data["link_id"]);
				}
				$user_collection = $user_collection
					->with(['Users' => function ($query) {
						$query = $query
							->where('users.status', true);
					}])
					->first()
				;
				if (
					$user_collection &&
					$user_collection->users
				) {
					foreach ($user_collection->users as $key => $user) {
						$user_link = \Models\ScheduleLink::firstOrNew(
							[
								"schedule_id" => $data["schedule_id"],
								"type" => 'users',
								"link_id" => $user->id,
							]
						);
						$user_link->status = false;
						$user_link->cron_task = true;
						$user_link->save();
						//Update linked schedules update_outlook
						Schedule::where('id',$data["schedule_id"])->where('update_outlook','!=','all')->update(['update_outlook'=>'user','cron_task'=>true]);
					}
				}
			}

			if (
				$data["type"] == 'users' &&
				$link->approved == 1 &&
				isset($data["cancellation_type"]) &&
				$data["cancellation_type"] == 'cancelled_by_user'
			) {
				$managers = \Models\ScheduleLink
					::where('schedule_id', $data["schedule_id"])
					->where('type', 'managers')
					->get()
					->pluck('link_id')
					->toArray()
				 ;

				 $template = \Models\EmailTemplate
					::where('name', 'Event Cancellation By Learner')
					->where('status', true)
					->first();

					if ($template && $template->id) {
							$user = \APP\Auth::getUser();
							$email_queue = new \Models\EmailQueue;
							$email_queue->email_template_id = $template->id;
							$email_queue->recipients = $managers;
							$email_queue->from = \APP\Auth::getUserId();
							$email_queue->custom_variables = json_encode([
								'FIRSTNAME' => $user->fname,
								'SURNAME' => $user->lname,
								'EVENT_NAME'=> $event->name,
								'CANCELLATION_MESSAGE' => $link->cancellation_reason,
								'EVENT_DATE' => $event->start_date ? \Carbon\Carbon::parse($event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat')) : "--",
							]);
							$email_queue->save();
					}
				}

			if (
				$data["type"] == 'users'&&
				$link->approved == 0 &&
				!\APP\Auth::isLearner() // Do not trigger this if learner cancels attendance
			) {
				// Send e-mail to Manager that status changes to approval
				$template = \Models\EmailTemplate
					::where('name', 'Event Not Approved')
					->where('status', true)
					->first()
				;

				$vars = [
					'USER_FNAME' => $created_by->fname,
					'USER_LNAME' => $created_by->lname,
					'EVENT_NAME' => $event->name,
					'EVENT_LOCATION' => $event->location,
					'EVENT_DATE' => $event->start_date ? \Carbon\Carbon::parse($event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat')) : "--",
					'REGARDS' => $GLOBALS["CONFIG"]->Regards,
				];

				$vars['REJECT_REASON'] = "";
				if (isset($data['rejection_reason_id'])) {
					$vars['REJECT_REASON'] = $rejectionReason->rejection_reason;
				}

				if ($template && $template->id) {
					$email_queue = new \Models\EmailQueue;
					$email_queue->email_template_id = $template->id;
					$email_queue->recipients = [$created_by->id];
					$email_queue->from = \APP\Auth::getUserId();
					$email_queue->custom_variables = json_encode($vars);
					$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($event,$email_queue->custom_variables);
					$email_queue->save();
				}
			}


			if($data['type']=="programmes"){
					$form_programme_ids=[];
					$getUserFormIds=\Models\UserFormTemplateWorkflowRelations::where("type",'schedule')
					->where("assigned_method","workflow_schedule_programme_assign")
					 ->where('type_id',$data['schedule_id'])->get();

					foreach($getUserFormIds AS $getUserFormVal){
						$userFormModel=\Models\UserForm::where("id",$getUserFormVal->user_form_id)->first();
						$formLink=\Models\ScheduleLink::where("type","forms")
						 ->where('schedule_id',$data['schedule_id'])->where('link_id',$userFormModel->form_id)->first();
						\Models\ScheduleLinkedFormDetails::where("schedule_link_id",$formLink['id'])
						->where("type","form_workflow")->delete();
						\Models\UserForm::deleteUserForms($getUserFormVal->user_form_id);
					}


			}

			//Detach users from froms when remove from schedule forms and users type
			if($data["type"]=="forms"){
				$formLink=\Models\ScheduleLink::where("type","forms")
							->where('schedule_id',$data['schedule_id'])->where('link_id',$data['link_id'])->first();
				$schedule_link_id = $formLink->id;
				$work_flow_id = \Models\ScheduleLinkedFormDetails::where([['schedule_link_id', '=', $schedule_link_id],
																			['type', '=', 'form_workflow']])
																 ->first()
																 ->type_id ?? 0;

				if($work_flow_id){
					# Removing user from workflow
					\Models\UserWorkflowForm::where([['type_id', '=', $data['schedule_id']],
													 ['form_workflow_id', '=', $work_flow_id],
													 ['type', '=', 'schedule']])
											->delete();

					# Updating forms in that workflow from 'schedule' to 'direct' to users
					\Models\UserForm::where([['type_id', '=', $data['schedule_id']],
											 ['type', '=', 'schedule']])
									->update(['type' => 'direct']);
				}

				\Models\ScheduleLinkedFormDetails::where("schedule_link_id",$schedule_link_id)->delete();


				$existUsers=\Models\ScheduleLink::where("type","users")
										->where('schedule_id',$data['schedule_id'])->get();
				 foreach($existUsers AS $user){
					 $userFormModel=\Models\UserForm::where("user_id",$user['link_id'])
					 ->where("form_id",$data["link_id"])
					 ->where("type_id",$data['schedule_id'])->where("type","schedule")->get();
					 if(!empty($userFormModel)){
						foreach($userFormModel AS $userFormModelVal){
							\Models\UserForm::deleteUserForms($userFormModelVal->id);
						}
					}

					//Delete tracked schedule formd
				 }
			}

			if($data["type"]=="users"){
				$existForms=\Models\ScheduleLink::where("type","forms")
				->where('schedule_id',$data['schedule_id'])->get();
				foreach($existForms AS $form){
				//    \Models\ScheduleLinkedFormDetails::where("schedule_link_id",$form['id'])->delete();

					$userFormModel=\Models\UserForm::where("form_id",$form['link_id'])->where("user_id",$data["link_id"])
					->where("type_id",$data['schedule_id'])->where("type","schedule")->get();
					if(!empty($userFormModel)){
						foreach($userFormModel AS $userFormModelVal){
							\Models\UserForm::deleteUserForms($userFormModelVal->id);
						}


					}
					//Delete tracked schedule formd
					// \Models\ScheduleLinkedFormDetails::where("schedule_link_id",$form['id'])->delete();
				}
				// \Models\UserWorkflowForm::where("type","schedule")->where("type_id",$data['schedule_id'])
				// 			->where("user_id",$data["link_id"])->delete();
			}

			if (
				(
					$data["type"] == 'users_queue'
				) &&
				$link->approved == 1
			) {
				// Send e-mail to Manager that status changes to approval
				$template = \Models\EmailTemplate
					::where('name', 'Event Waiting List')
					->where('status', true)
					->first()
				;
				if ($template && $template->id) {
					$email_queue = new \Models\EmailQueue;
					$email_queue->email_template_id = $template->id;
					$email_queue->recipients = [$created_by->id];
					$email_queue->from = \APP\Auth::getUserId();
					$email_queue->custom_variables = json_encode([
						'USER_FNAME' => $created_by->fname,
						'USER_LNAME' => $created_by->lname,
						'EVENT_NAME' => $event->name,
						'EVENT_LOCATION' => $event->location,
						'EVENT_DATE' => $event->start_date ? \Carbon\Carbon::parse($event->start_date)->format(\APP\Tools::getConfig('defaultDateFormat')) : "--",
						'REGARDS' => $GLOBALS["CONFIG"]->Regards,
					]);
					$email_queue->custom_variables = \APP\Tools::eventVenueToCustomVariables($event,$email_queue->custom_variables);
					$email_queue->save();
				}
			}

			if ($data["type"] == "schedules") {
				// In this case, need to check if event has linked events and lesson that can be completed only when all linked events are completed, check if all linked events are completed and complete lesson for users.
				\Models\Schedule::completionCheck($data["schedule_id"]);
			}

		}
		if (!empty($refund_status)) {
			$response->getBody()->write(json_encode(['refund_status' => $refund_status]));
		} else {
			$response->getBody()->write(json_encode([]));
		}

		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	//send custom email
	$group->post('/send-custom-email', function (Request $request, Response $response, array $args) {

		if (!\APP\Auth::roleAllowSendCustomEventEmail()) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		try {
			$data = $request->getParsedBody();
			$email_queue_ids = [];
			if (!isset($data['listAction']['email'])) {
				$response->getBody()->write(json_encode(['message'=>'Email data required']));
				return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
			}

			if (!isset($data['listAction']['email']['title']) || empty(trim($data['listAction']['email']['title']))) {
				if (!isset($data['listAction']['email']['description']) || empty(trim($data['listAction']['email']['description']))) {
					$response->getBody()->write(json_encode(['message'=>'Subject and Description required']));
					return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
				}
				$response->getBody()->write(json_encode(['message'=>'Subject required']));
				return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
			}

			if (!isset($data['listAction']['email']['description']) || empty(trim($data['listAction']['email']['description']))) {
				$response->getBody()->write(json_encode(['message'=>'Description required']));
				return $response->withStatus(400)->withHeader('Content-Type', 'application/json');
			}

			$template = \Models\EmailTemplate::getTemplate('Schedule Custom Mail');
			if ($template) {

				if(isset($data['listAction']['email']['copy_manager']) && $data['listAction']['email']['copy_manager']=='1'){
					$managers=\Models\ScheduleLink::where('schedule_id',$data['schedule_id'])->where("type","managers")->get();
					foreach($managers AS $manager){
						$user_details=\Models\User::find($manager->link_id);
						$email_queue = new \Models\EmailQueue;
						$email_queue->email_template_id = $template->id;
						$email_queue->recipients = [$manager->link_id];
						$email_queue->from = \APP\Auth::getUserId();
						$email_queue->custom_variables = json_encode([
							'USER_FNAME' => $user_details['fname'],
							'USER_LNAME' => $user_details['lname'],
							'SUBJECT' => $data['listAction']['email']['title'],
							'BODY' => $data['listAction']['email']['description'],
							'attachments'=>[],
							'REGARDS' => $GLOBALS["CONFIG"]->Regards,
						]);
						$email_queue->save();
						$email_queue_ids[]=$email_queue->id;
					}
				}

				foreach($data['users'] AS $user){
					$user_details=\Models\User::find($user);
					$email_queue = new \Models\EmailQueue;
					$email_queue->email_template_id = $template->id;
					$email_queue->recipients = [$user];
					$email_queue->from = \APP\Auth::getUserId();
					$email_queue->custom_variables = json_encode([
						'USER_FNAME' => $user_details['fname'],
						'USER_LNAME' => $user_details['lname'],
						'SUBJECT' => $data['listAction']['email']['title'],
						'BODY' => $data['listAction']['email']['description'],
						'attachments'=>[],
						'REGARDS' => $GLOBALS["CONFIG"]->Regards,
					]);
					$email_queue->save();
					$email_queue_ids[]=$email_queue->id;
				}
			}
			$response->getBody()->write(json_encode($email_queue_ids));
			return $response->withHeader('Content-Type', 'application/json');
		}catch(\Exception $e){
			$response->getBody()->write(json_encode(['error' => $e->getMessage()]));
			return $response->withStatus(500)->withHeader('Content-Type', 'application/json');
		}
	})->add(\APP\Auth::getSessionCheck());


	$group->post('/upload-schedule-email-attachement', function (Request $request, Response $response, array $args) {
		try{

			 $data = $request->getParsedBody();

			$data["queue_ids"]=json_decode($data["queue_ids"]);
			//Make directory
			if (!file_exists($this->get('settings')["LMSEventEmailAttachmentPath"].'/'.$data['schedule_id'])) {
				mkdir($this->get('settings')["LMSEventEmailAttachmentPath"].'/'.$data['schedule_id'], 0777, true);
			}

			$storage = new \Upload\Storage\FileSystem($this->get('settings')["LMSEventEmailAttachmentPath"].'/'.$data['schedule_id'].'/', true);
			$upload = new \Upload\File('file', $storage);

			 $hash = bin2hex(random_bytes(16));

			 $file_name = $upload->getNameWithExtension();
			 $extension = $upload->getExtension();
			 $upload->setName($hash);

			 $fileSizeValidation = new \Upload\Validation\Size('800M');
			 $fileTypeValidation = new \Upload\Validation\Mimetype(\APP\Tools::documentMime());
			 $fileExtValidation = new \Upload\Validation\Extension(\APP\Tools::allowExtensions());

			 $fileTypeValidation->setMessage("Invalid file type: " . $upload->getMimetype());
			 $upload->addValidations([
				 $fileTypeValidation,
				 $fileSizeValidation,
				 $fileExtValidation
			 ]);
			 $upload->upload();

			 foreach($data["queue_ids"] AS $email_queue_id){
				$email_queue=Models\EmailQueue::where("id",$email_queue_id)->first();
				$custom_variables=json_decode($email_queue->custom_variables);
				array_push($custom_variables->attachments,$data['schedule_id'].'/'.$hash.'.'.$extension);
				$email_queue->custom_variables = json_encode($custom_variables);
				$email_queue->save();
			 }
		}catch(\Exception $e){
			print_r($e->getMessage());
		}


	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'insert'));

	// Remove all non zoom/teams resources and reset learner status.
	$group->put('/new-topic/{schedule_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$schedule = \Models\Schedule::find($args['schedule_id']);

		if (isset($data['name'])) {
			$schedule->name = $data['name'];
		}
		$schedule->cron_task = true;
		$schedule->save();

		/*
		$old_lesson = \Models\LearningModule
			::where('is_course', 1)
			->whereIn('id',
				\Models\ScheduleLink
					::select('link_id')
					->where('type', 'lesson')
					->where('schedule_id', $schedule->id)
					->where('status', true)
					->get()
			)
			->first()
		;
		*/

		// Create new lesson!
		$new_lesson = new \Models\LearningModule;
		$new_lesson->is_course = 1;
		$new_lesson->name = $schedule->name;
		$new_lesson->status = true;
		$new_lesson->save();

		// Replace old lesson in link with new one.
		\Models\ScheduleLink
			::where('schedule_id', $schedule->id)
			->where('status', true)
			->where('type', 'lesson')
			->update(['link_id' => $new_lesson->id]);

		$schedule_links = \Models\ScheduleLink
			::where('schedule_id', $schedule->id)
			->where('status', true)
			->get();

		foreach ($schedule_links as $key => $schedule_link) {
			if ($schedule_link->type == 'resources') {
				// Check if resource is zoom or teams, if not, remove!
				$resource = \Models\LearningModule::find($schedule_link->link_id);

				if (
					!$resource->type->slug == 'zoom_meeting' &&
					!$resource->type->slug == 'microsoft_teams'
				) {
					$schedule_link->status = false;
					$schedule_link->cron_task = true;
				} else {
					// attach these resources to new lesson!
					\Models\LearningCourseModule::firstOrCreate(
						[
							'learning_course_id' => $new_lesson->id,
							'learning_module_id' => $schedule_link->link_id
						]
					);
				}
			} else {
				$schedule_link->completion_status = '%%event_completion_state_not_attempted%%';
			}
			$schedule_link->save();
		}

		$response->getBody()->write($schedule->id);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'insert'));


	$group->put('/set-homework/{resource_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$link = \Models\ScheduleLink
			::where('link_id', $args['resource_id'])
			->where('schedule_id', $data['schedule_id'])
			->where('status', true)
			->where('type', 'resources')
			->first();
		if (isset($data['completion_date_custom'])) {
			$link->completion_date_custom = \Carbon\Carbon::parse($data['completion_date_custom']);
		} else {
			$link->completion_date_custom = null;
		}

		$link->cron_task = true;
		$link->save();

		\Models\Schedule::setForCron($data['schedule_id']);

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'update'));


	// Resource can not be played by learner the normal way, it will sync with managers progress.
	$group->put('/set-instructor-lead/{resource_id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		$link = \Models\ScheduleLink
			::where('link_id', $args['resource_id'])
			->where('schedule_id', $data['schedule_id'])
			->where('status', true)
			->where('type', 'resources')
			->first()
		;
		$link->instructor_lead = !$link->instructor_lead;
		$link->cron_task = true;
		$link->save();

		\Models\Schedule::setForCron($data['schedule_id']);

		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'update'));

	$group->get('/visit-types/all', function (Request $request, Response $response) {
		session_write_close();
		$query = \Models\ScheduleVisitType
			::where("status", true)
		;

		$query = $query
			->get()
		;

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());


	// Update issue "VisibilityStatus from.
	$group->POST('/change_visibility/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$event = \Models\Schedule
			::findOrFail($args['id']);
		if ($data['type'] == "visible_learner_task") {
			$event->visible_learner_task = $data['val'];
		} else if ($data['type'] == "visible_learner") {
			$event->visible_learner = $data['val'];
		} else if ($data['type'] == "visible_schedule") {
			$event->visible_schedule = $data['val'];
		}
		$event->save();

		return
			$response;
	})->add(\APP\Auth::getStructureAccessCheck('standards-and-other-programmes', 'update'));

	// This will get data from learning_results, assessment tables and scorm_data table for logged in user and update all users assigned to this schedule/event/resource with same progress state.
	$group->get('/sync-resource/{schedule_id:[0-9]+}/{resource_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$event = \Models\Schedule
			::with('users')
			->find($args['schedule_id'])
		;

		// Loop all users in schedule/event, create array
		$user_ids = [];
		foreach ($event->users as $key => $user) {
			$user_ids[] = $user->id;
		}


		// Get related assessment Data
		$assesment_data = \Models\Assessment\Data
			::where('user_id', \APP\Auth::getUserId())
			->where('course_id', $args['resource_id'])
			->get()
		;

		if ($assesment_data) {
			foreach ($assesment_data as $key => $assesment) {
				// Look into assessment_tasks, if there is anything that needs to be synced.
				$assessment_tasks = \Models\Assessment\Task
					::where('user_id', \APP\Auth::getUserId())
					->where('course_id', $args['resource_id'])
					->where('assessment_data_id', $assesment->id)
					->get()
				;

				// Create new assessment data for users
				foreach ($user_ids as $key => $user_id) {

					// Delete assessment_tasks for user, if unfinished!
					\Models\Assessment\Task
						::where('user_id', $user_id)
						->where('course_id', $args['resource_id'])
						->whereIn('assessment_data_id',
							\Models\Assessment\Data
								::select('id')
								->where('user_id', $user_id)
								->where('course_id', $args['resource_id'])
								->where('status', '!=', 4)
								->get()
						)
						->delete()
					;
					// Delete assessment_data for all users
					\Models\Assessment\Data
						::where('user_id', $user_id)
						->where('course_id', $args['resource_id'])
						->where('status', '!=', 4)
						->delete()
					;

					// how to work out duplicates?
					$new_assesment = new \Models\Assessment\Data;
					$new_assesment->user_id = $user_id;
					$new_assesment->course_id = $args['resource_id'];
					$new_assesment->status = true;
					$new_assesment->save();

					foreach ($assessment_tasks as $key => $assessment_task) {
						$new_assesment_task = new \Models\Assessment\Task;
						$new_assesment_task->assessment_data_id = $new_assesment->id;
						$new_assesment_task->user_id = $user_id;
						$new_assesment_task->course_id = $args['resource_id'];
						$new_assesment_task->reporter_id = $assessment_task->reporter_id;
						$new_assesment_task->question_id = $assessment_task->question_id;
						$new_assesment_task->answer_id = $assessment_task->answer_id;
						$new_assesment_task->user_comment = $assessment_task->user_comment;
						$new_assesment_task->weighting = $assessment_task->weighting;
						$new_assesment_task->status = true;
						$new_assesment_task->submitted_at = $assessment_task->submitted_at;
						$new_assesment_task->ignore = true;
						$new_assesment_task->save();
					}
				}
			}
		}
		// EOF assessment Data sync


		// Get managers learning_result
		$learning_result = \Models\LearningResult
			::where('learning_module_id', $args['resource_id'])
			->where('user_id', \APP\Auth::getUserId())
			->where('refreshed', 0)
			->with('Module')
			->first()
		;
		// Mass update learning result for all users assigned to this event/resource
		\Models\LearningResult
			::where('learning_module_id', $args['resource_id'])
			//->where('user_id', $user->id)
			->whereIn('user_id', $user_ids)
			->where('refreshed', 0)
			->where('completion_status', '!=', 'completed')
			->with('Module')
			->update([
				'completion_status' => $learning_result->completion_status,
				'passing_status' => $learning_result->passing_status,
				'grade' => $learning_result->grade,
				'score' => $learning_result->score,
				'duration_hours' => $learning_result->duration_hours,
				'duration_minutes' => $learning_result->duration_minutes,
				'duration_scorm' => $learning_result->duration_scorm,
			])
		;
		// EOF Mass update learning result

		// Mass update SCORM \Models\Scorm\Track
		/// Get managers latest attempt data from scorm_scorm_scoes_track table
		$max_attempt_no = \Models\Scorm\Track
			::where("userid", \APP\Auth::getUserId())
			->where("scormid", $args['resource_id'])
			->max("attempt")
		;
		$max_attempt_no = $max_attempt_no > 0 ? $max_attempt_no : 1;
		// Get all data in scorm tracking table to sync it with all users!
		$scorm_scorm_scoes_track = \Models\Scorm\Track
			::where("userid", \APP\Auth::getUserId())
			->where("scormid", $args['resource_id'])
			->where("attempt", $max_attempt_no)
			->get()
		;

		// Replace SCROM TRACK data with manager data
		foreach ($user_ids as $key => $user_id) {
			// Find max attempt for user
			$user_max_attempt_no = \Models\Scorm\Track
				::selectRaw("IFNULL(MAX(attempt), 0) + 1 AS maxAttempt")
				->where("userid", \APP\Auth::getUserId())
				->where("scormid", $args['resource_id'])
				->where("element", "LIKE", "%-r")
				->first()
			;
			// Delete data from \Models\Scorm\Track
			\Models\Scorm\Track
				::where("userid", $user_id)
				->where("scormid", $args['resource_id'])
				->where("attempt", $user_max_attempt_no->maxAttempt)
				->delete()
			;
			// Add manager data!
			foreach ($scorm_scorm_scoes_track as $key => $scoes_track) {
				$new_scorm_track = new \Models\Scorm\Track;
				$new_scorm_track->userid = $user_id;
				$new_scorm_track->scormid = $args['resource_id'];
				$new_scorm_track->attempt = $user_max_attempt_no->maxAttempt;
				$new_scorm_track->scoid = $scoes_track->scoid;
				$new_scorm_track->element = $scoes_track->element;
				$new_scorm_track->value = $scoes_track->value;
				$new_scorm_track->timemodified = $scoes_track->timemodified;
				$new_scorm_track->save();
			}
		}
		// EOF Mass update SCORM


		return
			$response
		;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'update'));

	$group->post('/deleted/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {

		$params = $request->getParsedBody();
		$query = \Models\Schedule
			::onlyTrashed()
			->select(
				'schedules.*',
				DB::raw("DATE_FORMAT(schedules.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS created_at_uk"),
				DB::raw("DATE_FORMAT(schedules.start_date,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS start_date_uk"),
				DB::raw("DATE_FORMAT(schedules.deleted_at,'" . \APP\Tools::defaultDateFormatMYSQL() . "') AS deleted_at_uk")
			)
			->whereNull('parent_id')
			->leftJoin("learning_module_categories", function ($join) {
				$join
					->on("learning_module_categories.id", "schedules.category_id")
				;
			})

			->leftJoin("users as deleted_by_user", function ($join) {
				$join
					->on("deleted_by_user.id", "schedules.deleted_by")
				;
			})

			->leftJoin("users as created_by_user", function ($join) {
				$join
					->on("created_by_user.id", "schedules.created_by")
				;
			})

			->with(['DeletedBy' => function ($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id'
					)
					->with('role')
				;
			}])
			->with(['CreatedBy' => function ($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id'
					)
					->with('role')
				;
			}])
			->with('Category')
		;


		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-deleted-events', 'select'));

	$group->post('/link/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		$params = $request->getParsedBody();

		$query = \Models\ScheduleLink
			::withTrashed()
			->select(
				'schedule_links.*',
				'schedules.name',
				'schedules.id as event_id',
				'schedules.deleted_at as event_deleted_at',
				'schedules.start_date',
				'schedules.type as event_type',
				'schedules.category_id',
				'learning_module_categories.name as category_name',
				DB::raw("DATE_FORMAT(schedules.start_date,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS start_date_formatted"),
				DB::raw("DATE_FORMAT(schedule_links.created_at,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS created_at_formatted"),
				DB::raw("DATE_FORMAT(schedule_links.updated_at,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS updated_at_formatted"),
				DB::raw("DATE_FORMAT(schedule_links.deleted_at,'" . \APP\Tools::defaultDateFormatMYSQL() . " %H:%i') AS deleted_at_formatted")
			)

			->leftJoin("schedules", function ($join) {
				$join
					->on("schedules.id", "schedule_links.schedule_id")
				;
			})

			->leftJoin("learning_module_categories", function ($join) {
				$join
					->on("learning_module_categories.id", "schedules.category_id")
				;
			})

			->leftJoin("users as deleted_by_user", function ($join) {
				$join
					->on("deleted_by_user.id", "schedule_links.deleted_by")
				;
			})

			->leftJoin("users as created_by_user", function ($join) {
				$join
					->on("created_by_user.id", "schedule_links.created_by")
				;
			})

			->with(['DeletedBy' => function ($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id'
					)
					->with('role')
				;
			}])
			->with(['CreatedBy' => function ($query) {
				$query
					->select(
						'id',
						'fname',
						'lname',
						'role_id'
					)
					->with('role')
				;
			}])
			->with(['User' => function ($query) {
				$query
					->select(
						'users.id',
						'usercode',
						'fname',
						'lname',
						'email',
						'role_id'
					)
					->with('role')
				;
			}])
			->with(['Resource' => function ($query) {
				$query
					->select(
						'id',
						'name',
						'type_id',
						'is_course'
					)
					->with('type')
				;
			}])
		;


		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		if (isset($params["search"]["item_name"])) {
			// Look for User, resource name here
			$query
				->where(function($query) use ($params) {
					$query
						->whereHas('User', function ($query) use ($params) {
							$query
								->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["item_name"] . "%'")
							;
						})
						->orWhereHas('Resource', function ($query) use ($params) {
							$query
								->whereRaw("learning_modules.name LIKE '%" . $params["search"]["item_name"] . "%'")
							;
						})
					;
				})
			;
			unset($params["search"]["item_name"]);
		}


		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-event-items', 'select'));

	// Add event to user's Outlook
	$group->post('/{user_id: [0-9]+}/outlook', function (Request $request, Response $response, array $args) {
		$user_id = $args["user_id"];
		$data = $request->getParsedBody();


		if (
			isset($data["event_id"]) &&
			isset($data["event_type"]) &&
			isset($data["start_date"]) &&
			isset($data["duration"]) &&
			\APP\Tools::getConfig("enableGlobalOutlookIntegration")
		) {
			$user = \Models\User::find($user_id);

			$teams = new \APP\Teams(false);
			$title = isset($data["title"]) && !empty($data["title"]) ? $this->get('settings')["LMSName"] . ": ". $data["title"] : $this->get('settings')["LMSName"] . " event";
			$teams->refresh_global_calendar_oauth_token(\APP\Tools::getConfig("GlobalOutlookIntegrationSecurityToken"));

			$link = isset($data["link"]) && !empty($data["link"]) ? $this->get('settings')["LMSUrl"] . $this->get('settings')["LMSAppUri"] . $data["link"] : false;
			$description = $data["description"] ?? "";

			$teams->createUserEvent($user, $title, $description, $data["start_date"], $data["duration"], $link, $data["event_id"], $data["event_type"]);
		};

		return
			$response
			;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	/*Import events*/
	$group->post('/import-event', function (Request $request, Response $response) {
		$params = $request->getParsedBody();

		if (isset($_FILES['importFile'])) {
			 $uploadDir = $this->get('settings')["LMSTempPath"];
			 $adapter = new LocalFilesystemAdapter($uploadDir);
			 $filesystem = new Filesystem($adapter);
			 $uploadedFile = $_FILES['importFile'];

			 $import_file_id = uniqid();
			 $import_file_name = $import_file_id . '.' . pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
			 $filePath = $uploadDir . DIRECTORY_SEPARATOR . $import_file_name;

			 // Validate file type and size
			 $allowedMimeTypes = \APP\Tools::documentMime('excel');
			 $allowedExtensions = \APP\Tools::allowExtensions('excel');
			 $maxFileSize = 100 * 1024 * 1024; // 100MB

			 $mimeType = mime_content_type($uploadedFile['tmp_name']);
			 $fileSize = $uploadedFile['size'];
			 $fileExtension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);

			 if (!in_array($mimeType, $allowedMimeTypes)) {
				  return \APP\Tools::returnCode($request, $response, 400, "Invalid file type: $mimeType.");
			 }

			 if (!in_array($fileExtension, $allowedExtensions)) {
				  return \APP\Tools::returnCode($request, $response, 400, "Invalid extension: $fileExtension.");
			 }

			 if ($fileSize > $maxFileSize) {
				  return \APP\Tools::returnCode($request, $response, 400, "File size exceeds the limit of 100MB.");
			 }

			 try {
				  // Upload the file
				  $stream = fopen($uploadedFile['tmp_name'], 'r+');
				  $filesystem->writeStream($import_file_name, $stream);

				  if (is_resource($stream)) {
						fclose($stream);
				  }

				  // Log import files!
				  \Models\LogExportImport::insertRecord(file_get_contents($filePath), '.' . $fileExtension, $import_file_name, false, 'imports');

				  $n_records = \APP\Import::events($filePath, $this->get('settings'));

				  // Clean up temporary file after processing
				  $filesystem->delete($import_file_name);

				  // Return the response
				  $response->getBody()->write(
						json_encode(
							 [
								  'updated' => $n_records['n_record_updated'],
								  'inserted' => $n_records['n_record_inserted'],
								  'disabled' => $n_records['n_record_disabled'],
								  'rejected' => $n_records['n_record_rejected'],
								  'deleted' => $n_records['n_record_deleted'],
								  'message' => $n_records['message'],
								  'log' => $n_records['log']
							 ]
						)
				  );
				  return $response->withHeader('Content-Type', 'application/json');
			 } catch (\Exception $e) {
				  // Clean up temporary file in case of exception
				  if ($filesystem->fileExists($import_file_name)) {
						$filesystem->delete($import_file_name);
				  }
				  return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
			 }
		} else {
			 return \APP\Tools::returnCode($request, $response, 400, 'No import file provided.');
		}
  })->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'insert'));

	/*Import Event/Schedule data*/
	$group->post('/import-event-data', function (Request $request, Response $response) {
		$params = $request->getParsedBody();
		if (isset($_FILES['importFile'])) {

			 // Define the upload directory
			 $uploadPath = $this->get('settings')["LMSTempPath"];
			 $adapter = new LocalFilesystemAdapter($uploadPath);
			 $filesystem = new Filesystem($adapter);

			 // Get the uploaded file information
			 $uploadedFile = $_FILES['importFile'];
			 $importFileName = uniqid() . '.' . pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);

			 try {
				  // Check file size and type
				  if ($uploadedFile['size'] > 100 * 1024 * 1024) { // 100MB
						throw new \Exception("File size exceeds the limit of 100MB.");
				  }

				  $mimeType = mime_content_type($uploadedFile['tmp_name']);
				  $allowedMimeTypes = \APP\Tools::documentMime('excel');
				  if (!in_array($mimeType, $allowedMimeTypes)) {
						throw new \Exception("Invalid file type: $mimeType.");
				  }

				  // Upload the file
				  $stream = fopen($uploadedFile['tmp_name'], 'r+');
				  $filesystem->writeStream($importFileName, $stream);
				  fclose($stream);

			 } catch (\Exception $e) {
				  return \APP\Tools::returnCode($request, $response, 500, $e->getMessage());
			 }

			 // Log import files!
			 \Models\LogExportImport::insertRecord(file_get_contents($uploadPath . '/' . $importFileName), '.' . pathinfo($importFileName, PATHINFO_EXTENSION), $importFileName, false, 'imports');

			 $n_records = \APP\Import::userEventData($uploadPath . '/' . $importFileName);

			 $response->getBody()->write(
				json_encode(
					[
						'updated' => $n_records['n_record_updated'],
						'inserted' => $n_records['n_record_inserted'],
						'disabled' => $n_records['n_record_disabled'],
						'rejected' => $n_records['n_record_rejected'],
						'deleted' => $n_records['n_record_deleted'],
						'message' => $n_records['message'],
						'log' => $n_records['log']
					]
				)
			 );
			 return $response->withHeader('Content-Type', 'application/json');
		};

	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'insert'));

	$group->get('/import-template', function (Request $request, Response $response) {
		$spreadsheet = new Spreadsheet();
		$spreadsheet->getProperties()
			 ->setCreator("")
			 ->setLastModifiedBy("")
			 ->setTitle("")
			 ->setSubject("")
			 ->setDescription("");

		// Create the Template sheet
		$templateSheet = $spreadsheet->setActiveSheetIndex(0);
		$templateSheet->setTitle("Template");
		$templateSheet->setCellValue('A1', 'Username');
		$templateSheet->setCellValue('B1', 'MappedEventID');
		$templateSheet->setCellValue('C1', 'Status (Not Started, In Progress, Completed)');
		$templateSheet->setCellValue('D1', 'isWaiting List (Yes or No)');
		$templateSheet->setCellValue('E1', 'isReceive Email Notifications (Yes or No)');
		$templateSheet->setCellValue('F1', 'isAuthorised Absence');
		$templateSheet->setCellValue('G1', 'Absence Notes');
		$templateSheet->setCellValue('H1', 'Completed at');

		// Create the LookupData sheet
		$spreadsheet->createSheet();
		$spreadsheet->setActiveSheetIndex(1);
		$mapDataSheet = $spreadsheet->getActiveSheet();
		$mapDataSheet->setTitle('LookupData');

		$events = \Models\Schedule::select('id', 'name')->orderBy('created_at')->get();
		$eventsArray = $events->prepend(['ID', 'Name'])->toArray();
		$mapDataSheet->fromArray($eventsArray);

		// Set back to the first sheet
		$spreadsheet->setActiveSheetIndex(0);

		// Generate the file content
		$tempFilePath = tempnam(sys_get_temp_dir(), 'template');
		$writer = new Xlsx($spreadsheet);
		$writer->save($tempFilePath);

		$fileName = 'user_event_template.xlsx';

		// Return the response to trigger file download
		$response = $response->withHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
									->withHeader('Content-Disposition', 'attachment; filename="' . $fileName . '"')
									->withHeader('Content-Length', filesize($tempFilePath));

		$stream = new \Slim\Psr7\Stream(fopen($tempFilePath, 'r'));

		return $response->withBody($stream);
	})->add(\APP\Auth::getStructureAccessCheck(['system-setup-organisation-users'], 'select'));

	$group->post('/custom-field/{schedule_id:[0-9]+}', function (Request $request, Response $response,$args) {
		$data=$request->getParsedBody();
		$user_id=Auth::getUserId();
		Form::saveCustomForm($data,'event',$args['schedule_id'],$user_id);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('lessons-and-learning-resources', 'select'));

	$group->post('/list/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if(empty($args['user_id'])) {
			return;
		}

		$userId = $args['user_id'];

		$params = $request->getParsedBody();

		// get all the events of the learner
		$query = ScheduleLink::query()
				->select([
						'schedule_links.*',
						'schedules.name as schedule_name',
						'schedules.description as schedule_description',
						'schedules.start_date as schedule_start_date',
						'schedules.type as schedule_type',
						'venues.name as venue_name'
				])
				->join('schedules', 'schedules.id', '=', 'schedule_links.schedule_id')
				->leftJoin('table_extensions', function ($join) {
						$join->on('table_extensions.table_id', '=', 'schedules.id')
								->where('table_extensions.table', '=', 'schedules');
				})
				->leftJoin('venues', 'venues.id', '=', 'table_extensions.value')
				->with(['Schedule' => function($query) {
						$query
								->with('Lessons')
								->with(['Users' => function ($query) {
										$query
												->select(
														'users.id',
														'fname',
														'lname',
														'role_id',
														'schedule_links.approved'
												)
												->where('users.status', true);
								}])
								->with(['Visitors' => function ($query) {
										$query
												->select(
														'users.id',
														'fname',
														'lname',
														'role_id'
												)
												->where('users.status', true);
								}])
								->with('Programmes')
								->with('VenueDeatils')
								->select(['schedules.*'])
								->where('status', true)
								->where('visible_learner', true);
				}])
				->with(['CreatedBy' => function ($query) {
						$query
								->select(
										'users.id',
										'fname',
										'lname'
								)
								->where('users.status', true)
						;
				}])
				->where('schedule_links.type', '=', 'users')
				->where('schedule_links.link_id', '=', $userId)
		;

		if(isset($params['search']['completion_status'])) {
				switch($params['search']['completion_status']):
						case "In Progress":
								$params['search']['completion_status'] = "in_progress";
								break;
						case "Not Attended":
								$params['search']['completion_status'] = "not_attempted";
								break;
						default:
								$params['search']['completion_status'] = ScheduleLink::link2lr_completion_status($params['search']['completion_status']);
				endswitch;
		}

		unset($params['search']['refresh']);

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->get('/link/{event_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$link = \Models\ScheduleLink
			::where('schedule_id',$args['event_id'])
			->where('link_id',Auth::getUserId())->whereIn('type',['users','users_queue'])->first();

		$response
			->getBody()
			->write(json_encode($link))
		;

		return $response
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

    $group->post('/check-waiting-list', function (Request $request, Response $response) {
		$body = $request->getParsedBody();
		if (isset($body['id'])) {
			$schedule = Schedule::withCount('Users')->find($body['id']);
			$count = UserPaymentTransaction::checkPaymentProgress($body['id']);
		if ($schedule &&((($schedule->users_count+$count) < $schedule->maxclass) || ($schedule->maxclass==0))) {

			$response->getBody()->write(json_encode(['status'=>true]));
			return $response->withHeader('Content-Type', 'application/json');
			}
		}
        $response->getBody()->write(json_encode(['status'=>false]));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources'], 'select'));

	$group->post('/payment',function(Request $request,Response $response){
		$data = $request->getParsedBody();
		return $response;
	});

	$group->post('/event-alerts/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {

		if(empty($args['user_id'])) {
				return ;
		}

		$userId = $args['user_id'];

		$params = $request->getParsedBody();

		$query = UserScheduleWaitingList::select('learning_modules.name','learning_modules.description','learning_modules.category_id','learning_modules.type_id','learning_module_categories.name as category_name','user_schedule_waiting_lists.id','user_schedule_waiting_lists.status')->join('learning_modules',function($join)use($userId){
			$join->on('user_schedule_waiting_lists.learning_module_id','=','learning_modules.id')
			->where('user_schedule_waiting_lists.user_id','=',$userId);
		})->join('learning_module_categories','learning_module_categories.id','=','learning_modules.category_id');

		unset($params['search']['refresh']);

		if(isset($params['sort']) && isset($params['sort']['predicate']) && $params['sort']['predicate']=='user_schedule_waiting_lists__status'){
			$params['sort']['reverse']=!$params['sort']['reverse'];
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response
			->getBody()
			->write($p->toJson())
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->put('/schedule-alerts/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		if(!isset($args['id'])){
			return $response->withStatus(404);
		}
		$scheduleWaitingList=UserScheduleWaitingList::find($args['id']);
		if(!$scheduleWaitingList){
			return $response->withStatus(404);
		}
		$scheduleWaitingList->status = !$scheduleWaitingList->status;
		$scheduleWaitingList->save();

		$response
			->getBody()
			->write(json_encode(['status'=>$scheduleWaitingList->status]))
		;
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

    $group->post('/outlook-webhook', function (Request $request, Response $response, $args) {
        $params = $request->getQueryParams();
		$logger = \APP\LoggerHelper::getLogger();

        // Enhanced: Initial webhook entry logging
        $logger->info("WEBHOOK: Outlook webhook notification received", [
            'timestamp' => Carbon::now()->toDateTimeString(),
            'user_agent' => $request->getHeader('User-Agent')[0] ?? 'unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
        $logger->info('WEBHOOK: Query parameters', $params);

        // Handle validation token (initial webhook setup)
        if ($params && isset($params['validationToken'])) {
            $logger->info("WEBHOOK: Validation token request received", ['token' => $params['validationToken']]);
			$response->getBody()->write($params['validationToken']);
			return $response->withHeader('Content-Type', 'text/plain');
        }

        try {
            $data = $request->getParsedBody();
            if(!$data){
                $data = [];
            }

            // Enhanced: Log raw webhook data
            $logger->info('WEBHOOK: Raw notification data', $data);

            if ($data && isset($data['value'])
                && isset($data['value'])
                && isset($data['value'][0])
                && isset($data['value'][0]['resourceData'])) {

                $subscription_id = $data['value'][0]['subscriptionId'] ?? null;
                $changeType = $data['value'][0]['changeType'] ?? 'unknown';
                $resourceData = $data['value'][0]['resourceData'];

                // Enhanced: Log notification details
                $logger->info("WEBHOOK: Processing notification", [
                    'subscription_id' => $subscription_id,
                    'change_type' => $changeType,
                    'resource_id' => $resourceData['id'] ?? 'unknown',
                    'etag' => $resourceData['@odata.etag'] ?? 'unknown'
                ]);

				if(!$subscription_id){
					$logger->warning("WEBHOOK: No subscription ID found in notification data", $data);
					return $response->withStatus(200);
				}

				$subscription = OutlookSubscription::where('subscription_id', $subscription_id)->first();
				if(!$subscription){
					$logger->warning("WEBHOOK: No subscription found in database", [
                        'subscription_id' => $subscription_id,
                        'searched_table' => 'outlook_subscriptions'
                    ]);
					return $response->withStatus(200);
				}

				// Enhanced: Log subscription details
                $logger->info("WEBHOOK: Subscription found", [
                    'subscription_db_id' => $subscription->id,
                    'subscription_email' => $subscription->email,
                    'subscription_expires' => $subscription->expire_at
                ]);

				$schedule = Schedule::where('outlook_subscription_id', $subscription->id)->orderBy('created_at', 'desc')->first();
				if(!$schedule){
					$logger->warning("WEBHOOK: No schedule found for subscription", [
                        'subscription_id' => $subscription_id,
                        'subscription_db_id' => $subscription->id
                    ]);
					return $response->withStatus(200);
				}

				// Enhanced: Log schedule details
                $logger->info("WEBHOOK: Schedule found", [
                    'schedule_id' => $schedule->id,
                    'schedule_name' => $schedule->name,
                    'outlook_event_id' => $schedule->outlook_event_id,
                    'created_by' => $schedule->created_by,
                    'start_date' => $schedule->start_date,
                    'end_date' => $schedule->end_date
                ]);

                $teams = new Teams();

                // Enhanced: Log authentication method selection
				if (
					\APP\Tools::getConfig("enableGlobalOutlookIntegration", false, $schedule->created_by) &&
					$schedule->outlook_refresh_token == "use_global"
				) {
				    $logger->info("WEBHOOK: Using global Outlook integration authentication");
					$teams->refresh_global_calendar_oauth_token(\APP\Tools::getConfig("GlobalOutlookIntegrationSecurityToken", false, $schedule->created_by));
				} else if (
					Tools::getConfig('useOutlookVenues', false, $schedule->created_by) &&
					Tools::getConfig('OutlookRoomAccessToken', false, $schedule->created_by) &&
					Tools::getConfig('OutLookRoomRefreshToken', false, $schedule->created_by)
				) {
				    $logger->info("WEBHOOK: Using Outlook venues authentication");
					$teams->token = Venue::getToken();
				} else {
				    $logger->info("WEBHOOK: Using standard calendar authentication");
					$teams->refresh_calendar_oauth_token($schedule->outlook_refresh_token);
				}

                // Enhanced: Log API call details
                $logger->info("WEBHOOK: Fetching event data from Microsoft Graph", [
                    'event_id' => $resourceData['id'],
                    'api_method' => 'getOutlook'
                ]);

                $eventData = $teams->getOutlook($resourceData['id']);

                // Enhanced: Verify schedule matches the event ID to prevent mismatches
                $eventSchedule = Schedule::where('outlook_event_id', $resourceData['id'])->first();
                if ($eventSchedule && $eventSchedule->id !== $schedule->id) {
                    $logger->warning("WEBHOOK: Schedule ID mismatch detected", [
                        'subscription_schedule_id' => $schedule->id,
                        'subscription_schedule_name' => $schedule->name,
                        'event_schedule_id' => $eventSchedule->id,
                        'event_schedule_name' => $eventSchedule->name,
                        'outlook_event_id' => $resourceData['id'],
                        'using_schedule' => 'event_based_lookup'
                    ]);
                    // Use the event-based lookup as it's more accurate
                    $schedule = $eventSchedule;
                } elseif (!$eventSchedule) {
                    $logger->warning("WEBHOOK: No schedule found with matching outlook_event_id", [
                        'outlook_event_id' => $resourceData['id'],
                        'subscription_schedule_id' => $schedule->id,
                        'subscription_schedule_outlook_event_id' => $schedule->outlook_event_id
                    ]);
                } else {
                    $logger->info("WEBHOOK: Schedule lookup confirmed", [
                        'schedule_id' => $schedule->id,
                        'outlook_event_id' => $resourceData['id']
                    ]);
                }

                try{
                    $eventData = json_decode($eventData,true);
                    $logger->info("WEBHOOK: Event data successfully parsed", [
                        'event_id' => $eventData['id'] ?? 'unknown',
                        'subject' => $eventData['subject'] ?? 'unknown',
                        'attendee_count' => isset($eventData['attendees']) ? count($eventData['attendees']) : 0
                    ]);
                }catch(Exception $e){
                    $logger->error("WEBHOOK: Failed to parse event data", [
                        'error' => $e->getMessage(),
                        'raw_data' => $eventData
                    ]);
                }

                if (isset($eventData['id']) && $schedule) {
                    $logger->info("WEBHOOK: Processing event update", [
                        'schedule_id' => $schedule->id,
                        'event_operation' => 'update'
                    ]);

                    $timeZone = Tools::getConfig('defaultTimezone');
                    $old_name = $schedule->name;
                    $old_start_date = $schedule->start_date;
                    $old_end_date = $schedule->end_date;

                    // Handle description updates from Outlook (now receiving HTML format)
                    $newDescription = $eventData['body']['content'] ?? '';
                    $currentDescription = $schedule->description ?? '';

                    // Clean and normalize both descriptions for comparison
                    // This handles HTML entities, extra whitespace, and formatting differences
                    $normalizedNew = trim(html_entity_decode(strip_tags($newDescription), ENT_QUOTES | ENT_HTML5, 'UTF-8'));
                    $normalizedCurrent = trim(html_entity_decode(strip_tags($currentDescription), ENT_QUOTES | ENT_HTML5, 'UTF-8'));

                    // Only update description if there's a meaningful content difference
                    if ($normalizedNew !== $normalizedCurrent && !empty($normalizedNew)) {
                        $logger->info("WEBHOOK: Schedule description updated from Outlook", [
                            'old_description_length' => strlen($currentDescription),
                            'new_description_length' => strlen($newDescription),
                            'content_type' => 'html',
                            'normalized_old_preview' => substr($normalizedCurrent, 0, 100),
                            'normalized_new_preview' => substr($normalizedNew, 0, 100)
                        ]);
                        $schedule->description = $newDescription;
                    } else {
                        $logger->info("WEBHOOK: Description unchanged, skipping update", [
                            'reason' => empty($normalizedNew) ? 'empty_new_description' : 'content_equivalent',
                            'content_type' => 'html',
                            'normalized_length' => strlen($normalizedCurrent)
                        ]);
                    }
                    $schedule->name = $eventData['subject'];
                    $start_date = Carbon::parse($eventData['start']['dateTime'],$eventData['start']['timeZone'])
                                  ->setTimezone($timeZone);
                    $end_date = Carbon::parse($eventData['end']['dateTime'],$eventData['end']['timeZone'])
                        ->setTimezone($timeZone);
                    $schedule->start_date = $start_date;
                    $schedule->end_date = $end_date;

                    // Enhanced: Log schedule changes
                    $changes = [];
                    if($old_name !== $schedule->name) $changes['name'] = ['from' => $old_name, 'to' => $schedule->name];
                    if($old_start_date !== $schedule->start_date->toDateTimeString()) $changes['start_date'] = ['from' => $old_start_date, 'to' => $schedule->start_date->toDateTimeString()];
                    if($old_end_date !== $schedule->end_date->toDateTimeString()) $changes['end_date'] = ['from' => $old_end_date, 'to' => $schedule->end_date->toDateTimeString()];

                    if(!empty($changes)) {
                        $logger->info("WEBHOOK: Schedule changes detected", $changes);
                    }

                    // Enhanced: Log venue processing
                    if(Tools::getConfig('useOutlookVenues')){
                        $logger->info("WEBHOOK: Processing venue information");
                        if(isset($eventData['locations']) && isset($eventData['locations'][0])){
                            $logger->info("WEBHOOK: Location found in event", [
                                'location_id' => $eventData['locations'][0]['uniqueId'] ?? 'unknown',
                                'display_name' => $eventData['locations'][0]['displayName'] ?? 'unknown'
                            ]);
                            $locations = Venue::getLocation($eventData['locations'][0]['uniqueId']);
                            if($locations){
                                $venue = Venue::where('room_email',$eventData['locations'][0]['uniqueId'])->first();
                                if (!$venue) {
                                    $logger->info("WEBHOOK: Creating new venue", [
                                        'room_email' => $eventData['locations'][0]['uniqueId'],
                                        'name' => $locations[0]->displayName
                                    ]);
                                    $address = "";
                                    $postcode = "";
                                    if ($locations[0]->address) {
                                        $address = $locations[0]->address->street . "\n" . $locations[0]->address->city . "\n" . $locations[0]->address->state . "\n" . $locations[0]->address->countryOrRegion;
                                        $postcode = $locations[0]->address->postalCode;
                                    }
                                    $venue = Venue::create(['room_email' => $data['room_email'], 'name' => $locations[0]->displayName, 'address' => $address, 'postcode' => $postcode, 'capacity' => $locations[0]->capacity, 'status' => 1]);
                                } else {
                                    $logger->info("WEBHOOK: Using existing venue", [
                                        'venue_id' => $venue->id,
                                        'venue_name' => $venue->name
                                    ]);
                                }
                                \Models\TableExtension::updateField('schedules', $schedule->id,'venue_id', $venue->id);
                            }
                        }else{
                            $logger->info("WEBHOOK: No location in event, clearing venue");
                            \Models\TableExtension::updateField('schedules', $schedule->id,'venue_id', NULL);
                        }
                    }

					$attendees = [];
					if (isset($eventData['attendees']) && is_array($eventData['attendees'])) {
					    $logger->info("WEBHOOK: Processing attendees", [
                            'total_attendees' => count($eventData['attendees'])
                        ]);
						foreach ($eventData['attendees'] as $attendee) {
							if (isset($attendee['emailAddress']['address'])) {
								$email = $attendee['emailAddress']['address'];
								$status = $attendee['status']['response'] ?? 'none';
								$user = User::where('email', $email)->first();
								if ($user) {
								    $logger->info("WEBHOOK: Attendee found", [
                                        'email' => $email,
                                        'user_id' => $user->id,
                                        'status' => $status
                                    ]);
									$attendees[] = [
										'type' => 'users',
										'link_id' => $user->id,
										'status' => $status,
										'approved' => true
									];
								} else {
								    $logger->info("WEBHOOK: Attendee not found in system", [
                                        'email' => $email,
                                        'status' => $status
                                    ]);
								}
							}
						}
					}

					// Enhanced: Process declined attendees with comprehensive logging
					$declined_count = 0;
					foreach ($attendees as $attendee) {
						if ($attendee['status'] == 'declined') {
						    $declined_count++;
						    $user = User::find($attendee['link_id']);
						    $logger->info("WEBHOOK: CANCELLATION - Processing declined attendee", [
                                'user_id' => $attendee['link_id'],
                                'user_email' => $user->email ?? 'unknown',
                                'user_name' => $user ? $user->FullName() : 'unknown',
                                'schedule_id' => $schedule->id,
                                'schedule_name' => $schedule->name
                            ]);

							$existingLink = ScheduleLink::where('schedule_id', $schedule->id)
								->whereIn('type', ['users', 'users_queue', 'managers'])
								->where('link_id', $attendee['link_id'])
								->get();

							$logger->info("WEBHOOK: CANCELLATION - Found existing links", [
                                'user_id' => $attendee['link_id'],
                                'links_found' => $existingLink->count(),
                                'link_types' => $existingLink->pluck('type')->toArray()
                            ]);

							foreach ($existingLink as $link) {
								$cancellation_data = [
									'type' => $link->type,
									'link_id' => $link->link_id,
									'schedule_id' => $link->schedule_id,
								];

								$logger->info("WEBHOOK: CANCELLATION - Processing link cancellation", [
                                    'link_id' => $link->id,
                                    'link_type' => $link->type,
                                    'user_id' => $link->link_id,
                                    'schedule_id' => $link->schedule_id,
                                    'current_status' => $link->status
                                ]);

								// Enhanced: Log refund processing
								if ($cancellation_data['type'] == 'users') {
								    $logger->info("WEBHOOK: CANCELLATION - Processing refunds for user", [
                                        'user_id' => $cancellation_data['link_id'],
                                        'schedule_id' => $cancellation_data['schedule_id']
                                    ]);

									$refund_status = \APP\Controllers\GlobalPaymentController::refund('schedules', $cancellation_data['schedule_id'], $cancellation_data['link_id']);
									$govuk_refund = GovUKPayController::refund('schedules', $cancellation_data['schedule_id'], $cancellation_data['link_id']);

									$logger->info("WEBHOOK: CANCELLATION - Refund processing completed", [
                                        'user_id' => $cancellation_data['link_id'],
                                        'global_payment_refund' => $refund_status ?? 'no_status',
                                        'govuk_refund' => $govuk_refund ?? 'no_status'
                                    ]);
								}

								$schedules = \Models\Schedule::getChild($cancellation_data['schedule_id'], [$cancellation_data['schedule_id']]);

								$logger->info("WEBHOOK: CANCELLATION - Updating schedule links", [
                                    'user_id' => $cancellation_data['link_id'],
                                    'affected_schedules' => $schedules,
                                    'operation' => 'bulk_update_schedule_links'
                                ]);

								\Models\ScheduleLink
									::whereIn('schedule_id', $schedules)
									->whereIn('type', ['users', 'users_queue'])
									->where('link_id', $cancellation_data["link_id"])
									->update([
										'status' => false,
										'deleted_by' => $cancellation_data['link_id'],
                                        'cron_task' => true,
                                        'deleted_at' => \Carbon\Carbon::now(),
                                        'cancellation_reason' => 'Declined from Outlook'
									]);

								foreach ($schedules as $id) {
								    $logger->info("WEBHOOK: CANCELLATION - Processing schedule cleanup", [
                                        'schedule_id' => $id,
                                        'user_id' => $cancellation_data['link_id'],
                                        'operations' => ['resetUsers', 'setForCron', 'update_outlook_flag']
                                    ]);
									\Models\ScheduleLink::resetUsers($id);
									\Models\Schedule::setForCron($id);
									Schedule::where('id', $id)->where('update_outlook', '!=', 'all')->update(['update_outlook' => 'user']);
								}

								$link->status = false;
								$link->deleted_by = $cancellation_data['link_id'];
								$link->cron_task = true;
								$link->deleted_at = \Carbon\Carbon::now();
								$link->cancellation_reason = 'Declined from Outlook';
								$link->save();

								$logger->info("WEBHOOK: CANCELLATION - Link cancellation completed", [
                                    'link_id' => $link->id,
                                    'user_id' => $link->link_id,
                                    'cancellation_reason' => 'Declined from Outlook',
                                    'cron_task_flagged' => true
                                ]);
							}
						}
					}

					if($declined_count > 0) {
                        $logger->info("WEBHOOK: CANCELLATION - Summary", [
                            'total_declined_attendees' => $declined_count,
                            'schedule_id' => $schedule->id,
                            'schedule_name' => $schedule->name
                        ]);
                    }

                    $schedule->save();

                    $logger->info("WEBHOOK: Event update completed successfully", [
                        'schedule_id' => $schedule->id,
                        'changes_applied' => !empty($changes),
                        'cancellations_processed' => $declined_count
                    ]);

                }elseif($schedule){
                    $logger->info("WEBHOOK: EVENT DELETION - Processing event deletion", [
                        'schedule_id' => $schedule->id,
                        'schedule_name' => $schedule->name,
                        'outlook_event_id' => $schedule->outlook_event_id,
                        'reason' => 'Event deleted in Outlook'
                    ]);

                    Schedule::deleteEvent($schedule->id);

                    $logger->info("WEBHOOK: EVENT DELETION - Event deletion completed", [
                        'schedule_id' => $schedule->id,
                        'operation' => 'Schedule::deleteEvent'
                    ]);
                }
                else{
                    $logger->warning("WEBHOOK: No matching event found", [
                        'resource_id' => $resourceData['id'],
                        'subscription_id' => $subscription_id,
                        'searched_field' => 'outlook_event_id'
                    ]);
                }
            }else{
                $logger->warning("WEBHOOK: Invalid notification data structure", [
                    'has_value' => isset($data['value']),
                    'has_value_array' => isset($data['value'][0]),
                    'has_resource_data' => isset($data['value'][0]['resourceData']),
                    'raw_data' => $data
                ]);
            }
        } catch (\Exception $e) {
            $logger->error("WEBHOOK: Critical error processing notification", [
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);
        }

        $logger->info("WEBHOOK: Request processing completed", [
            'timestamp' => Carbon::now()->toDateTimeString(),
            'response_status' => 200
        ]);

        return $response->withStatus(200);
    });

    $group->post('/outlook-lifecycle',function(Request $request, Response $response, $args){
        $params = $request->getQueryParams();
		$logger = \APP\LoggerHelper::getLogger();
        $logger->info("lifecycle");
        $logger->info('params',$params);
        if($params && isset($params['validationToken'])){
			$response->getBody()->write($params['validationToken']);
			$response = $response->withHeader('Content-Type', 'text/plain');
        }
        $data = $request->getParsedBody();
        if(!$data){
            $data = [];
        }
        $logger->info("Data",$data);
        return $response->withStatus(200);
    });

    $group->get('/delete-webhook/{subscription}',function(Request $request, Response $response, $args){
        if(!isset($args['subscription'])){
            return $response->withStatus(404);
        }
        $subscription = OutlookSubscription::where('subscription_id',$args['subscription'])->first();
        if(!$subscription){
            return $response->withStatus(404);
        }
        $teams = new Teams();
        $res = $teams->deleteSubscription($subscription->subscription_id,$subscription->email);
		$response->getBody()->write(json_encode($res));
		return $response->withHeader('Content-Type', 'application/json');
    });

	$group->post('/get/position',function(Request $request, Response $response, $args){
        $params = (array)$request->getParsedBody();
		$userId = $params['userId'];
		$scheduleId = $params['scheduleId'];

		$queue = ScheduleLink::where('schedule_id', $scheduleId)
			->where('type', 'users_queue')
			->orderBy('id')
			->get();

		$position = $queue->search(function ($item) use ($userId) {
			return $item->link_id == $userId;
		});
		if ($position !== false) {
			$position += 1;
		} else {
			$position = 0;
		}
		$response->getBody()->write(json_encode(['position' => $position]));
		return $response->withHeader('Content-Type', 'application/json');
    })->add(\APP\Auth::getSessionCheck());


});
