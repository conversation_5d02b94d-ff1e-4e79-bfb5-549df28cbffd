<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/register",  function ($group) {

	$group->get("",  function (Request $request, Response $response, array $args) {

		// Check if registraton is allowed
		$register = \APP\Tools::getConfig('allowRemoteRegistration');
		if (!$register) {
			return \APP\Tools::returnCode($request, $response, 403, '403 Forbidden');
		}

		return $this->get('view')->render(
			$response,
			'html/register.html',
			[
				"version" => \Models\Version::orderBy('created_at', 'desc')->first()->id,
				"LMSUrl" => $this->get('settings')["LMSUrl"],
				"LMSUri" => $this->get('settings')["LMSUri"],
				"licensing" => $this->get('settings')["licensing"],
				"usernameLabel" => \APP\Tools::getConfig('defaultUsernameLabel') ? \APP\Tools::getConfig('defaultUsernameLabel') : 'Username',
				"LMSAppUri" => $this->get('settings')["LMSAppUri"],
				"LMSTplsUri" => $this->get('settings')["LMSTplsUri"],
				"LMSTplsUriHTML" => $this->get('settings')["LMSTplsUriHTML"],
				"LMSPasswordPattern" => $this->get('settings')["LMSPasswordPattern"],
				"olarkCode" => \APP\Tools::getConfig('enableOlark') ? \APP\Tools::getConfig('olarkCode') : '',
				"googleAnalyticsCode" => \APP\Tools::getConfig('enableGoogleAnalytics') ? \APP\Tools::getConfig('googleAnalyticsCode') : '',
			]
		);
	});

	// api point to retrieve field list for the registration form
	$group->get("/field-list",  function (Request $request, Response $response, array $args)
	{
		$defaultFieldList = $this->get('settings')['defaultRegistrationFieldList'];
		$translator = \APP\Templates::getTranslator();
		$customConfigData = (object) json_decode(\APP\Tools::getConfig('registrationFormFields'));

		if (json_last_error() === JSON_ERROR_NONE)	// if no errors, merging custom configuration in to the default one
		{
			$customFieldList = $customConfigData->fields;
			foreach ($defaultFieldList as $key => $firstItem)
			{

				$matchingItem = array_filter($customFieldList, function ($secondItem) use ($firstItem) { return $secondItem->slug == $firstItem['slug']; });
				if (!empty($matchingItem))
				{
					$matchingItem = reset($matchingItem);
					$defaultFieldList[$key] = array_merge((array) $firstItem, (array) $matchingItem);
				}
			}
		}

		foreach ($defaultFieldList as $key => $field) {
			if ($field['slug'] == 'company') {
				if (\APP\Tools::getConfig('registerShowCompany')) {
					$defaultFieldList[$key]['enabled'] = true;
				} else {
					$defaultFieldList[$key]['enabled'] = false;
				}
			}
		}

		$fieldList = [];

		if (\APP\Auth::isAdmin()) $fieldList = $defaultFieldList;
		else
		{
			
			foreach($defaultFieldList as $key => $field)	// filtering out values to show
			{
				if ((isset($field['enabled']) && $field['enabled']) || (isset($field['baseField']) && $field['baseField']))
				{
					$item = [
						'name' => $field['name'],
						'slug' => $field['slug'],
						'required' => (isset($field['required']) && $field['required']) || (isset($field['baseField']) && $field['baseField']),
						'pattern' => isset($field['pattern']) ? $field['pattern'] : false,
						'pattern_error' => isset($field['pattern_error']) ? $field['pattern_error'] : false
					];
					if (isset($field['customName'])) $item['customName'] = $field['customName'];
					if (isset($field['table']) && !isset($field['parent'])) $item['options'] = DB::table($field['table'])->select('id', 'name')->where('status', 1)->get();

					array_push($fieldList, $item);
				}
			}
		}
		$formData = new stdClass();
		$formData->fields = $fieldList;
		if (isset($customConfigData->formAboveText)) $formData->formAboveText = $customConfigData->formAboveText;
		if (isset($customConfigData->formBelowText)) $formData->formBelowText = $customConfigData->formBelowText;

		// replacing version labels
		foreach ($formData->fields as $key => $field) {
			if (isset($formData->fields[$key]['name'])) {
				$formData->fields[$key]['name'] = $translator->replaceVersionLabels($field['name']);
			}
			if (isset($formData->fields[$key]['parent'])) {
				$formData->fields[$key]['parent'] = $translator->replaceVersionLabels($field['parent']);
			}
			if (isset($formData->fields[$key]['pattern_error'])) {
				$formData->fields[$key]['pattern_error'] = $translator->replaceVersionLabels($field['pattern_error']);
			}
		}
		
		if (isset($customConfigData->enableCustomRegistrationForm)) 
			$formData->enableCustomRegistrationForm = $customConfigData->enableCustomRegistrationForm;
		if (isset($customConfigData->customRegistrationFormHTML)) 
			$formData->customRegistrationFormHTML = $customConfigData->customRegistrationFormHTML;
		$response->getBody()->write(json_encode($formData));
		return $response->withHeader('Content-Type', 'application/json');
		
	})->add(\APP\Auth::getSessionRegisterCheck());


	$group->put("/update-field-list",  function (Request $request, Response $response, array $args)
	{
		\APP\Tools::updateConfig('registrationFormFields', json_encode($request->getParsedBody()));
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-roles', 'insert'));


	$group->get("/field-options/{table:[a-z_]+}/{parent_id:[0-9]+}",  function (Request $request, Response $response, array $args)
	{
		// hard coded cases to avoid unauthorized access to db
		if ($args['table'] == 'cities')
		{
			$resp = DB::table($args['table'])->select('id', 'name')->where('country_id', $args['parent_id'])->where('status', 1)->get();
		}
		else if ($args['table'] == 'departments')
		{
			$resp = DB::table($args['table'])->select('id', 'name')->where('company_id', $args['parent_id'])->where('status', 1)->get();
		}
		else die('406 Not Acceptable');

		$response
			->getBody()
			->write(json_encode($resp))
		;
		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getSessionRegisterCheck());


	// This is only for NRAS, this will return I am list from configuration option
	$group->get("/i-am/list",  function (Request $request, Response $response, array $args) {
		$i_am_list = \APP\Tools::getConfig('registerIAm');
		if ($i_am_list) {
			$response->getBody()->write(json_encode($i_am_list));
		return $response->withHeader('Content-Type', 'application/json');
		}
	});

	// Get company information by slug for auto-population
	$group->get("/company-by-slug/{slug}",  function (Request $request, Response $response, array $args) {
		$slug = $args['slug'];
		
		if (empty($slug)) {
			return \APP\Tools::returnCode($request, $response, 400, 'Slug is required');
		}

		try {
			$company = \Models\Company::where('urlextension', $slug)
				->where('status', 1)
				->first();

			if (!$company) {
				return \APP\Tools::returnCode($request, $response, 404, 'Company not found');
			}

			$companyData = [
				'id' => $company->id,
				'name' => $company->name,
				'slug' => $company->urlextension
			];

			$response->getBody()->write(json_encode($companyData));
			return $response->withHeader('Content-Type', 'application/json');

		} catch (Exception $e) {
			return \APP\Tools::returnCode($request, $response, 500, 'Internal server error');
		}
	});
});