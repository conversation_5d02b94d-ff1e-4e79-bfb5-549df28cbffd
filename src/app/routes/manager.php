<?php

use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Models\ManagerUser;

$app->group("/manager", function ($group) {

	$group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$manager = \Models\User::find($args["id"]);

		$response->getBody()->write(json_encode($manager));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	// get list of managers for given user, full list
	$group->get("/user/{user_id:[0-9]+}" , function (Request $request, Response $response, $args) {
		$query = \Models\User::managerList($args);
		$query = $query->get();

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	// get list of managers for given user in pagination format
	$group->post("/user/{user_id:[0-9]+}" , function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\User::managerList($args);

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
			foreach ($params["search"] as $field => $value) {
				if (is_int($value)) {
					$query->where($field, "=", $value);
				} else {
					$query->where($field, "LIKE", "%{$value}%");
				}
			}
		}

		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"])) {
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getSessionCheck());


	$group->get("/all", function (Request $request, Response $response, $args) {
		session_write_close();
		$query = [];



		if (\APP\Auth::isAdminInterface()) {
			$query = \Models\User
				::select('users.id', 'users.fname', 'users.lname', 'users.username', 'users.email', 'users.role_id')
				->where('users.status', true)
				->join("roles", function ($join) {
					$join
						->on("roles.id", "=", "users.role_id")
						->where("roles.is_manager", 1)
					;
					if (\APP\Auth::isQa()) {
						$join = $join
							->where("roles.show_user_role_in_qa_list", true)
						;
					}
				})
				->with(['role'=>function($query){
					$query
						->select(
							'roles.id',
							'roles.is_manager',
							'roles.is_admin',
							'roles.exclude_manager_from_schedule'

						)
					;
				}])
				->orderBy('users.fname')
			;

			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::isCd() &&
				!\APP\Auth::isFa() &&
				!\APP\Auth::isQa()
			) {
				$query = $query
					->where('users.id', \APP\Auth::getUserId())
				;
			}


			if (
				!\APP\Auth::accessAllCompanies() &&
				!\APP\Auth::isAdmin()
			) {
				$query->where("users.company_id", "=", \APP\Auth::getUser()->company_id);
			}
			if (\APP\Auth::isAdmin()) {
				$query = cache()->remember('manager_role_all', 600, function () use ($query) {
					return $query->get()->toArray();
				});
			} else {
				$query = $query->get();
			}

		}

		$response->getBody()->write(gzencode(json_encode($query)));
		return $response
			->withHeader('Content-Encoding', 'gzip')
			->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getSessionCheck());

	// Assign individual user to manager directly
	$group->put('/{manager_id:[0-9]+}/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		\Models\ManagerUser::link([$args["user_id"]], [$args["manager_id"]], 'direct assign');

		//Updated same email users and managers when assignAllLinkedUsersToAllLinkedManagers condition true
		ManagerUser::assignAllLinkedUsersToAllLinkedManagers($args['manager_id'],$args['user_id']);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'update'));

	// Delete individual user from manager
	$group->delete('/{manager_id:[0-9]+}/user/{user_id:[0-9]+}', function (Request $request, Response $response, $args) {

		\Models\ManagerUser::unLink([$args["user_id"]], [$args["manager_id"]], 'direct remove');

        //Delete same email users and manager when assignAllLinkedUsersToAllLinkedManagers set true
        ManagerUser::assignAllLinkedUsersToAllLinkedManagers($args['manager_id'],$args['user_id'],true);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'disable'));

	// List all users and add "assigned" option to each user if it is assigned to manager id passed in args
	$group->post('/{id:[0-9]+}/users', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\User
			::selectRaw("users.*, MAX(`manager_users`.manager_id  IS NOT NULL) AS assigned")
			->leftjoin('manager_users', function ($join) use ($args) {
				$join
					->on('manager_users.user_id', '=', 'users.id')
					->where("manager_users.manager_id", "=", $args["id"])
					->whereNull('manager_users.deleted_at')
				;
			})
			->where('users.status', true)
			->groupBy("users.id")
			->with('role')
		;

		if (isset($params["search"]) && isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query->whereRaw("`manager_users`.manager_id IS NOT NULL");
			} elseif ($params["search"]["assigned"] == "0") {
				$query->whereRaw("`manager_users`.manager_id IS NULL");
			}
			unset($params["search"]["assigned"]);
		}

		$query = \Models\Role::userAccessList($query);

		$p = \APP\SmartTable::searchPaginate($params, $query);


		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	// Assign company to manager with all users
	$group->put('/{manager_id:[0-9]+}/company/{company_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		$manager = \Models\User::find($args["manager_id"]);
		$company = \Models\Company::find($args["company_id"]);

		$managerDepartment = new \Models\ManagerCompany;
		$managerDepartment->manager_id = $args["manager_id"];
		$managerDepartment->company_id = $args["company_id"];
		$managerDepartment->save();

        $comp_user_ids = $company->employees()->where('status', true)->get()->pluck("id")->toArray();

		\Models\ManagerUser::link($comp_user_ids, [$manager->id], 'Assign company to manager with all users');
        ManagerUser::assignAllLinkedUsersToAllLinkedManagers($manager->id, $comp_user_ids);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'update'));

	// unassign company from manager with all the users
	$group->delete('/{manager_id:[0-9]+}/company/{company_id:[0-9]+}', function (Request $request, Response $response, $args) {

		\Models\ManagerCompany
			::where("manager_id", $args["manager_id"])
			->where("company_id", $args["company_id"])
			->delete()
		;

		$c_users = \Models\User::where("company_id", $args["company_id"])->get();

		\Models\ManagerUser::unLink($c_users->pluck('id')->toArray(), [$args["manager_id"]], 'unassign company from manager with all the users');

        ManagerUser::assignAllLinkedUsersToAllLinkedManagers($args['manager_id'],$c_users->pluck('id')->toArray(),true);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'disable'));

	// Get all companies for manager and show witch ones are assigned to manager.
	$group->post('/{id:[0-9]+}/companies', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\Company
			::selectRaw("companies.*, (`manager_companies`.manager_id  IS NOT NULL) AS assigned")
			->leftjoin('manager_companies', function ($join) use ($args) {
				$join
					->on('manager_companies.company_id', '=', 'companies.id')
					->where("manager_companies.manager_id", "=", $args["id"])
				;
			})
			->where('companies.status', true)
		;

		if (isset($params["search"]) && isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query->whereRaw("`manager_companies`.manager_id IS NOT NULL");
			} elseif ($params["search"]["assigned"] == "0") {
				$query->whereRaw("`manager_companies`.manager_id IS NULL");
			}
			unset($params["search"]["assigned"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	// Assign department to manager with all users
	$group->put('/{manager_id:[0-9]+}/department/{department_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		$manager = \Models\User::find($args["manager_id"]);
		$department = \Models\Department::find($args["department_id"]);

		$managerDepartment = new \Models\ManagerDepartment;
		$managerDepartment->manager_id = $args["manager_id"];
		$managerDepartment->department_id = $args["department_id"];

		$managerDepartment->save();

		$dep_user_ids = $department->employees()->where('status', true)->get()->pluck("id")->toArray();
		\Models\ManagerUser::link($dep_user_ids, [$manager->id], 'Assign department to manager with all users');

        ManagerUser::assignAllLinkedUsersToAllLinkedManagers($manager->id, $dep_user_ids);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'update'));

	// unassign users from manager for specific department
	$group->delete('/{manager_id:[0-9]+}/department/{department_id:[0-9]+}', function (Request $request, Response $response, $args) {

		\Models\ManagerDepartment
			::where("manager_id", "=", $args["manager_id"])
			->where("department_id", "=", $args["department_id"])
			->delete()
		;

		$d_users = \Models\User::where("department_id", "=", $args["department_id"])->get();

		\Models\ManagerUser::unLink($d_users->pluck('id')->toArray(), [$args["manager_id"]], 'unassign users from manager for specific department');
        ManagerUser::assignAllLinkedUsersToAllLinkedManagers($args['manager_id'],$d_users->pluck('id')->toArray(),true);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'disable'));

	// Get all departments for manager and show witch ones are assigned to manager.
	$group->post('/{id:[0-9]+}/departments', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\Department
			::selectRaw("departments.*, (`manager_departments`.manager_id  IS NOT NULL) AS assigned")
			->leftjoin('manager_departments', function ($join) use ($args) {
				$join->on('manager_departments.department_id', '=', 'departments.id')
					->where("manager_departments.manager_id", "=", $args["id"]);
			})
			->where('departments.status', true);

		if (isset($params["search"]) && isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query->whereRaw("`manager_departments`.manager_id IS NOT NULL");
			} elseif ($params["search"]["assigned"] == "0") {
				$query->whereRaw("`manager_departments`.manager_id IS NULL");
			}
			unset($params["search"]["assigned"]);
		}

		$query = \Models\Role::departmentAccessList($query);

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	/*Get all Groups for manager and show witch ones are assigned to manager*/
	$group->post('/{id:[0-9]+}/groups', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\Group
			::selectRaw("groups.*, (`manager_groups`.manager_id  IS NOT NULL) AS assigned")
			->with(['employees'=>function($query){
				$query
					->select(
						'users.id'
					)
				;
			}])
			->leftjoin('manager_groups', function ($join) use ($args) {
				$join->on('manager_groups.group_id', '=', 'groups.id')
					->where("manager_groups.manager_id", "=", $args["id"]);
			})
			->where('groups.status', true);

		if (isset($params["search"]) && isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query->whereRaw("`manager_groups`.manager_id IS NOT NULL");
			} elseif ($params["search"]["assigned"] == "0") {
				$query->whereRaw("`manager_groups`.manager_id IS NULL");
			}
			unset($params["search"]["assigned"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	// Assign group to manager with all users
	$group->put('/{manager_id:[0-9]+}/group/{group_id:[0-9]+}', function (Request $request, Response $response, $args) {
		\Models\Group::assignToManager(
			$args["group_id"],
			$args["manager_id"]
		);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'update'));

	// unassign users from manager for specific group
	$group->delete('/{manager_id:[0-9]+}/group/{group_id:[0-9]+}', function (Request $request, Response $response, $args) {

		\Models\ManagerGroup
			::where("manager_id", "=", $args["manager_id"])
			->where("group_id", "=", $args["group_id"])
			->delete()
		;

		$d_users = \Models\GroupUser
			::where("group_id", $args["group_id"])
			->whereHas('User', function ($query) {
				$query
					->where('users.status', true)
				;
				$query = \Models\Role::userAccessList($query);
			})
			->get()
		;


		\Models\ManagerUser::unLink($d_users->pluck('user_id')->toArray(), [$args["manager_id"]], 'unassign users from manager for specific group');
        ManagerUser::assignAllLinkedUsersToAllLinkedManagers($args['manager_id'],$d_users->pluck('user_id')->toArray(),true);
		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'disable'));
	/*Groups ends here*/

	// Assign category to manager
	$group->put('/{manager_id:[0-9]+}/category/{category_id:[0-9]+}', function (Request $request, Response $response, $args) {
		$params = $request->getParsedBody();

		\Models\ManagerLearningModuleCategory::firstOrCreate([
			'manager_id' => $args['manager_id'],
			'learning_module_category_id' => $args["category_id"]
		]);

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'update'));

	// unassign manager from category
	$group->delete('/{manager_id:[0-9]+}/category/{category_id:[0-9]+}', function (Request $request, Response $response, $args) {

		\Models\ManagerLearningModuleCategory
			::where("manager_id", $args["manager_id"])
			->where("learning_module_category_id", $args["category_id"])
			->delete();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'disable'));

	// Get all categories for manager and show witch ones are assigned to manager.
	$group->post('/{id:[0-9]+}/categories', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\LearningModuleCategory
			::selectRaw("learning_module_categories.*, (`manager_learning_module_categories`.manager_id  IS NOT NULL) AS assigned")
			->leftjoin('manager_learning_module_categories', function ($join) use ($args) {
				$join
					->on('manager_learning_module_categories.learning_module_category_id', 'learning_module_categories.id')
					->where("manager_learning_module_categories.manager_id", $args["id"]);
			})
			->where('learning_module_categories.status', true);

		if (isset($params["search"]) && isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query
					->whereRaw("`manager_learning_module_categories`.manager_id IS NOT NULL");
			} elseif ($params["search"]["assigned"] == "0") {
				$query
					->whereRaw("`manager_learning_module_categories`.manager_id IS NULL");
			}
			unset($params["search"]["assigned"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	// Assign learning resource to manager
	$group->put('/{manager_id:[0-9]+}/learning/{learning_id:[0-9]+}', function (Request $request, Response $response, $args) {

		$manager_learning_module = new \Models\ManagerLearningModule;
		$manager_learning_module->manager_id = $args['manager_id'];
		$manager_learning_module->learning_module_id = $args['learning_id'];
		$manager_learning_module->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'insert'));

	// Delete learning resource from manager
	$group->delete('/{manager_id:[0-9]+}/learning/{learning_id:[0-9]+}', function (Request $request, Response $response, $args) {

		\Models\ManagerLearningModule
			::where("manager_id", "=", $args['manager_id'])
			->where("learning_module_id", "=", $args['learning_id'])
			->delete();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'disable'));


	// List all learning resources and hilight ones assigned to manager.
	$group->post('/{id:[0-9]+}/learning', function (Request $request, Response $response, $args) {

		$params = $request->getParsedBody();

		$query = \Models\LearningModule
			::selectRaw("learning_modules.*")
			->selectRaw("MAX((manager_learning_modules.manager_id  IS NOT NULL)) AS assigned")
			->leftjoin('manager_learning_modules', function ($join) use ($args) {
				$join->on('manager_learning_modules.learning_module_id', '=', 'learning_modules.id')
					->where("manager_learning_modules.manager_id", "=", $args["id"]);
			})
			->groupBy("learning_modules.id");

		if (isset($params["search"]) && isset($params["search"]["assigned"])) {
			if ($params["search"]["assigned"] == "1") {
				$query->whereRaw("`manager_learning_modules`.manager_id IS NOT NULL");
			} elseif ($params["search"]["assigned"] == "0") {
				$query->whereRaw("`manager_learning_modules`.manager_id IS NULL");
			}
			unset($params["search"]["assigned"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

	// list all managers and show assigned user count
	$group->post('/list{download:[\/a-z]*}', function (Request $request, Response $response, array $args) {
		try {
			// List only for managers/admins
			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::isCd() &&
				!\APP\Auth::isManager()
			) {
				return
					$response
						->withStatus(403)
						->write('403 Forbidden')
				;
			}

			$params = $request->getParsedBody();

			$query = \Models\User
				::select(
					'users.id',
					'users.fname',
					'users.username',
					'users.lname',
					'users.email',
					'users.company_id',
					'users.last_login_dt',
					'users.role_id',
					DB::raw("datediff(NOW(), users.last_contact_date) AS last_contact_date_days")
				)
				->selectRaw("COUNT(linked_users.id) AS n_employees")
				->with(['company' => function ($query) {
					$query->select('id', 'name');
				}])
				->with('role')
				->join("roles", function ($join) {
					$join
						->on("roles.id", "=", "users.role_id")
						->where("roles.is_manager", "=", 1);
				})
				->leftJoin("manager_users", function ($join) {
					$join
						->on("manager_users.manager_id", "=", "users.id")
						->whereNull("manager_users.deleted_at")
					;
				})
				->leftJoin("users as linked_users", function ($join) {
					$join
						->on("linked_users.id", "=", "manager_users.user_id")
						->where('linked_users.status', 1)
						->where(function($query) {
							$query
								->whereNull("linked_users.expiration_dt")
								->orWhere("linked_users.expiration_dt", ">", \Carbon\Carbon::now())
							;
						})
					;
				})
				->validuser()
				->groupBy("users.id")
			;

			// If not CD or Admin, and user had no access to all companies, list managers only from same company
			if (
				!\APP\Auth::isAdmin() &&
				!\APP\Auth::isCd() &&
				!\APP\Auth::accessAllCompanies()
			) {
				$query = $query
					->where("users.company_id", \APP\Auth::getUserCompanyId())
				;
			}


			if (isset($params["search"]["relationship"]) && $params["search"]["relationship"]=="batch-report-managers") {
				$query = \Models\BatchReport::countAndConditions($query, $params);
			} else {
				$query = \Models\Schedule::countAndConditions($query, $params);
			}


			if (isset($params["search"]) && is_array($params["search"])) {
				if (isset($params["search"]["relationship"])) {
					unset($params["search"]["relationship"]);
				}
				if (isset($params["search"]["status"])) {
					unset($params["search"]["status"]);
				}

				if (isset($params["search"]["refresh"])) {
					unset($params["search"]["refresh"]);
				}

				if (isset($params["search"]["manager"])) {
					unset($params["search"]["manager"]);
				}
				/*
				should not be here, but need more testing
				foreach ($params["search"] as $field => $value) {
					if (is_int($value)) {
						$query->where($field, "=", $value);
					} else {
						$query->where($field, "LIKE", "%{$value}%");
					}
				}
				*/
			}


			if (isset($args["download"]) && $args["download"] == "/download") {
				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				$export_fields = [
					"ID" => "id",
					"First Name" => "fname",
					"Last Name" => "lname",
					"Email" => "email",
					"Company Name" => "company.name",
					"Employees" => "n_employees"
				];


				$download_file_name = uniqid("groups.list.") . ".xlsx";

				\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				);

				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
			} else {
				$p = \APP\SmartTable::searchPaginate($params, $query);

				$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

			}
		}catch(\Exception $e){
			print_r($e->getMessage());
		}

	})->add(\APP\Auth::getSessionCheck());

	$group->post('/missing-users', function (Request $request, Response $response, $args) {
		if (
			!\APP\Auth::isAdmin() &&
			!\APP\Auth::isCd() &&
			!\APP\Auth::isManager()
		) {
			return
				$response
					->withStatus(403)
					->write('403 Forbidden');
		}

		$params = $request->getParsedBody();
		$query = \Models\User
			::whereHas('role', function ($query) {
				$query
					->where('is_admin', false)
					->where('is_manager', false)
					->where('is_demo', false)
					->where('access_all_companies', false)
					->where('access_all_learners', false)
					->where('status', true);
			})
			->where('status', true)
			->whereDoesntHave('Managers')
			->select('id', 'username', 'fname', 'lname', 'email', 'role_id')
		;


		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));

    $group->get("/processing-manager/list[/{user_id:.*}]", function (Request $request, Response $response, array $args) {
        $query = [];
            $query = \Models\User
                ::select('users.id', 'users.fname', 'users.lname', 'users.username', 'users.email', 'users.role_id')
                ->where('users.status', true)
                ->join("roles", function ($join) {
                    $join
                        ->on("roles.id", "=", "users.role_id")
                        ->where("roles.is_manager", 1)
                    ;
                })
                ->with(['role'=>function($query){
                    $query
                        ->select(
                            'roles.id',
                            'roles.is_manager',
                            'roles.is_admin',
                            'roles.exclude_manager_from_schedule'

                        )
                    ;
                }])
                ->orderBy('users.fname')
                ->orderBy('users.lname');


//            if (
//                !\APP\Auth::accessAllCompanies() &&
//                !\APP\Auth::isAdmin()
//            ) {
//                $query->where("users.company_id", "=", \APP\Auth::getUser()->company_id);
//            }
        if(\APP\Tools::getConfig('AttachManagerstoUploads') == "attach_assigned_managers" || \APP\Tools::getConfig('AttachManagerstoUploads') =="attach_assigned_managers_by_manager_only")
        {
            $assigned_managers =array();
            if(isset($args['user_id']) && $args['user_id']){
                $assigned_managers = \Models\ManagerUser::select('manager_id')->where('user_id',$args['user_id'])->get()->toArray();
            }else{
                $assigned_managers = \Models\ManagerUser::select('manager_id')->where('user_id',APP\Auth::getUserId())->get()->toArray();
            }
            $query->whereIn('users.id', $assigned_managers);
        }
            $query = $query->get();


        $response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

    })->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));


	// Get all entries on manager_users table for ertain manager
	$group->post('/history/{manager_id:[0-9]+}', function (Request $request, Response $response, $args) {
		if (!\APP\Auth::isAdmin()) {
			return \APP\Tools::returnCode($request, $response, 403);
		}

		$params = $request->getParsedBody();

		$query = \Models\ManagerUser
			::select('manager_users.*')
			->with('user')
			->with('manager')
			->with('createdby')
			->with('deletedby')
			->where('manager_id', $args['manager_id'])
		;



		if (isset($params["search"]["status"])) {
			if ($params["search"]["status"] == 'deleted') {
				$query = $query
					->onlyTrashed()
				;
			}
			unset($params["search"]["status"]);
		} else {
			$query = $query
				->withTrashed()
			;
		}

		if (isset($params["search"]["users_name"])) {
			$qyery = $query
				->whereHas('user', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["users_name"] . "%'")
						->orWhere('email', 'LIKE', "%{$params["search"]["users_name"]}%")
					;
				})
			;
			unset($params["search"]["users_name"]);
		}

		if (isset($params["search"]["createdby_name"])) {
			$qyery = $query
				->whereHas('createdby', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["createdby_name"] . "%'")
						->orWhere('email', 'LIKE', "%{$params["search"]["createdby_name"]}%")
					;
				})
			;
			unset($params["search"]["createdby_name"]);
		}

		if (isset($params["search"]["deletedby_name"])) {
			$qyery = $query
				->whereHas('deletedby', function ($query) use ($params) {
					$query
						->whereRaw("CONCAT( users.fname,  ' ', users.lname ) LIKE '%" . $params["search"]["deletedby_name"] . "%'")
						->orWhere('email', 'LIKE', "%{$params["search"]["deletedby_name"]}%")
					;
				})
			;
			unset($params["search"]["deletedby_name"]);
		}

		if (isset($params["search"]["refresh"])) {
			unset($params["search"]["refresh"]);
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-organisation-coach-trainers', 'select'));
});
