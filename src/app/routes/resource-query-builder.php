<?php

use Models\ApprenticeshipStandardUser;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;
use Slim\Http\Response as HttpResponse;
use Middleware\RateLimitMiddleware;

$app->group("/resource-query-builder", function ($group) {

	/*Fetching all variables with condition and parameters => /resource-query-builder/variables*/

	$group->get('/variables', function (Request $request, Response $response, $args) {
		$variable = \Models\ResourceQueryVariable::
		with(['conditions' => function ($condition) {
			$condition->select('id', 'key', 'value', 'variable_key', 'multiple', 'resource_query_variable_id');
		}])
			->with(['parameters' => function ($condition) {
				$condition->select('id', 'key', 'value', 'variable_key', 'resource_query_variable_id');
			}])
			->get();

       $response
            ->getBody()
            ->write(json_encode($variable))
        ;
        return $response;

	})->add(\APP\Auth::getSessionCheck());

	/*Create raw Query => /resource-query-builder/variables*/

	$group->post('/save_created_raw_query', function (Request $request, Response $response, $args) {
		// if (!\APP\Auth::isAdminInterface()) {
		// 	\APP\Tools::returnCode($request, $response, '403');
		// }
		$data = $request->getParsedBody();
		if (
			(!empty($data['type']) &&
			!empty($data['type_id']) &&
			!empty($data['query']) ) || !empty($data['query_direct'])
		) {

			// Regenerate raw_query every time query builder changes.
			// https://emil-reisser-weston.atlassian.net/browse/SCOR-3681
			if ($data['query_builder_type'] == 'query_builder') {
				// Uncommeting this, but there will be problems possibly
				//$data['query_direct'] = false;
			}

			if(!empty($data['query_direct'])){
				$user_ids=DB::select($data['query_direct']);
			}else{
				$user_ids=DB::select(\DB\ResourceQuery::generateRawQuery($data['query']));
			}

			$user_data=[];
			foreach ($user_ids as $user_id)
			{
			 $user_data[]=$user_id->id;
			}
			$user_ids=implode(',',$user_data);

			// regenerate query every time query_builder_type is query_builder
			if ($data['query_builder_type'] == 'query_builder') {
				$data['query_direct'] = null;
			}

			if (
				!empty($data['query']) ||
				$data['query_builder_type'] !== 'query_builder'
			) {
				$queryBuilder = \Models\ResourceQuery
					::updateOrCreate([
						'type' => $data['type'],
						'type_id' => $data['type_id']?$data['type_id']:0,
						'action' => $data['action']?$data['action']:'add',
						'type_parent_id' => $data['type_parent_id'],
					], [
						'query_variable' => json_encode($data['query']),
						'user_ids' => $user_ids,
						'raw_query' => !empty($data['query_direct']) ? $data['query_direct'] : \DB\ResourceQuery::generateRawQuery($data['query']),
						'query_builder_type' => $data['query_builder_type']
					])
				;

				if (!$queryBuilder->created_by) {
					$queryBuilder->created_by = \APP\Auth::getUserId();
				}
				$queryBuilder->save();

				// Check if type is "lessons" or "resources" and action is "add".
					// Retrieve resource
						//check if "refresh_only_if_learning_meets_query"
							// If "lessons||resources"_refresh type does not exist, create it in resource_queries table, a copy of this query
							// If "lessons||resources"_refresh type exists and !further_customise_this_query, update it with this query.
				if (
					in_array($queryBuilder->type, ['lessons', 'resources']) &&
					$queryBuilder->action == 'add'
				) {
					$resource = \Models\LearningModule::find($queryBuilder->type_id);
					if (
						$resource &&
						$resource->refresh &&
						$resource->refresh_period > 0 &&
						$resource->refresh_only_if_learning_meets_query
					) {
						$refresh_query = \Models\ResourceQuery
							::where('type_id', $queryBuilder->type_id)
							->where('type', $queryBuilder->type . '_refresh')
							->first()
						;
						if (!$refresh_query) {
							$refresh_query = $queryBuilder->replicate();
							$refresh_query->type = $queryBuilder->type . '_refresh';
							$refresh_query->save();
						} else {
							if (!$resource->further_customise_this_query) {
								$refresh_query_replicate = $queryBuilder->replicate();
								$refresh_query_replicate->type = $queryBuilder->type . '_refresh';
								$refresh_query->fill($refresh_query_replicate->getAttributes())->save();
							}
						}
					}
				}

			} else {
				$queryBuilderObj = \Models\ResourceQuery::where(
				[
					['type_id',"=",$data['type_id']],
					['type',"=",$data['type']],
					['type_parent_id',"=",$data['type_parent_id']],
					['action',"=",$data['action']],
				]
				)->first();
				if ($queryBuilderObj) $queryBuilderObj->delete();
			}
		} elseif (
			!empty($data['type']) &&
			!empty($data['type_id']) &&
			empty($data['query'])
		) {
			$queryBuilderObj = \Models\ResourceQuery
				::where(
					[
						['type_id',"=",$data['type_id']],
						['type',"=",$data['type']],
						['type_parent_id',"=",$data['type_parent_id']],
						['action',"=",$data['action']],
					]
				)->first()
			;


			if ($queryBuilderObj) {
				$queryBuilderObj->deleted_by = \APP\Auth::getUserId();
				$queryBuilderObj->save();
				$queryBuilderObj->delete();
			}

		}

		if (empty($user_list)) {
			$user_list = [];
		}
		$cron = \Models\Cron::where('function','processQueryUsers')->first();
		if (
			$cron &&
			!$cron->locked
		) {
			$cron->force_run=true;
			$cron->save();
		}
		$cron = \Models\Cron::where('function','queryBuilderItrator')->first();
		if (
			$cron &&
			!$cron->locked
		) {
			$cron->force_run=true;
			$cron->save();
		}

        $response
            ->getBody()
            ->write(json_encode([
            "status"=>true,
            "user_list"=>$user_list
        ]));
        return $response;


    })->add(\APP\Auth::getStructureAccessCheck('misc-permissions-query-builders', 'update'));

	/*Route to retrieve the query variable*/
	$group->get('/get-query/{type:.+}/{type_id:[0-9]+}/{type_parent_id:[0-9]+}/{action:.+}', function (Request $request, Response $response, $args) {
		$queryBuilder = \Models\ResourceQuery::where('type', '=', $args['type'])
			->where('type_id', '=', $args['type_id'])
			->where('type_parent_id', '=', $args['type_parent_id'])
			->where('action', '=', $args['action'])
			->first()
		;

		// If "lessons_refresh" or "resources_refresh" and there is no response, check if entry exists for "lessons" or "resources", clone that entry for refresh entries and return.
		if (
			(
				$args['type'] == 'lessons_refresh' ||
				$args['type'] == 'resources_refresh'
			) &&
			!$queryBuilder
		) {
			$check_query = \Models\ResourceQuery
				::where('type', str_replace("_refresh", "", $args['type']))
				->where('type_id', $args['type_id'])
				->where('type_parent_id', $args['type_parent_id'])
				->where('action', $args['action'])
				->first()
			;
			if ($check_query) {
				$queryBuilder = $check_query->replicate();
				$queryBuilder->type = $args['type']; // the new project_id
				$queryBuilder->save();
			}
		}
		if ($queryBuilder) {
			$response->getBody()->write(json_encode($queryBuilder));
		}
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	/*ONLY FOR TESTING*/
	/*Create raw Query => /resource-query-builder/variables*/

	$group->post('/get-query-result', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user_list = [];
		$error= '';
		if ($data['query']) {

			$raw_query = \DB\ResourceQuery::generateRawQuery($data['query'],$data['limit'], $data['key'] );

			try {
				$users = DB::select($raw_query);
				if ($users) {
					foreach ($users as $user) {

						$userObj = \Models\User
							::where('id', $user->id)
							->select('id', 'fname', 'lname', 'role_id','email', 'usercode')
							->with('role')
							->first()
						;

						if (
							!empty($data['role_learner']) &&
							$userObj->role &&
							$userObj->role->is_learner
						) {
							array_push($user_list, $userObj);
						}

						if (
							empty($data['role_learner'])
						) {
							array_push($user_list, $userObj);
						}
					}
				}
			} catch (Exception $e) {
				$error = "The generated query is invalid and throws a system exception: " . $e->getMessage();
			}
		}

        $response
            ->getBody()
            ->write(json_encode([
                "raw_query" => $raw_query,
                "users" => $users,
                "user_list" => $user_list,
                "error" => $error,
            ]
        ));
        return $response;

	})->add(new RateLimitMiddleware())->add(\APP\Auth::getSessionCheck());


	 $group->post('/get-sql-query-result', function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();
		$user_list = [];
		$error= '';
		if ($data['query']) {
			if(\APP\Form::isSelect($data['query']) && \APP\Form::isFromUsers($data['query'])){
				try {
					$users = DB::select($data['query']);
					if ($users) {
						foreach ($users as $user) {

							$userObj = \Models\User
								::where('id', $user->id)
								->select('id', 'fname', 'lname', 'role_id')
								->with('role')
								->first()
							;

							if (
								!empty($data['role_learner']) &&
								$userObj->role &&
								$userObj->role->is_learner
							) {
								array_push($user_list, $userObj);
							}

							if (
								empty($data['role_learner'])
							) {
								array_push($user_list, $userObj);
							}
						}
					}
				} catch (Exception $e) {
					$error = "The generated query is invalid and throws a system exception: " . $e->getMessage();
				}
			}else{
				$error = "Invalid SQL Query.";
			}
		}

		$response
			->getBody()
			->write(json_encode([
				// "users" => $users,
				"user_list" => $user_list,
				"error" => $error,
			]))
		;
		return $response;
	})->add(\APP\Auth::getSessionCheck());



    $group->post('/get-permission', function (Request $request, Response $response, $args) {
        $data = $request->getParsedBody();

        if(!isset($data['type_obj']) && !isset($data['type_obj']['type'])){
            return $response;
        }
        $can_inline = false;
        if(isset($data['type_obj']['inline_save'])){
            $can_inline = $data['type_obj']['inline_save'];
        }
        $type = $data['type_obj']['type'];
        if ($type == "resources" || $type == "resources_refresh"){
            $canEdit = \APP\Auth::checkStructureAccess('library-learning-resources-and-lessons', 'update');
        }else if ($type == "events") {
            $canEdit = \APP\Auth::checkStructureAccess('lessons-and-learning-resources', 'update');
        }else if ($type == "programmes" || $type == "criteria" || $type == "outcome" || $type == "subcriteria") {
            $canEdit = \APP\Auth::checkStructureAccess('standards-and-other-programmes', 'update');
        }else if ($type == "skill_library") {
            $canEdit = \APP\Auth::checkStructureAccess('library-learning-resources-and-lessons', 'update');
        }else if ($type == "form-workflow") {
            // $canEdit = \APP\Auth::checkStructureAccess('standards-and-other-programmes', 'update');
            $canEdit = \APP\Auth::checkSession();  // the save only checks if the user is logged in
        }else if ($type == "forms") {
            $canEdit = \APP\Auth::checkStructureAccess(['standards-and-other-programmes', 'trainee-standards'], 'update');
        }else if ($type == "reports") {
            $canEdit = true;
        }else if ($type == "lessons" || $type == "lessons_refresh") {
            $canEdit = \APP\Auth::checkStructureAccess('library-learning-resources-and-lessons', 'update');
        }else {
            // if type is not handled should we show edit or not ?
            $canEdit = true;
        }

        $canViewQueryBuilder = \APP\Auth::checkStructureAccess('misc-permissions-query-builders', 'select');
        $canEditQueryBuilder = \APP\Auth::checkStructureAccess('misc-permissions-query-builders', 'update');

        $canView = $canViewQueryBuilder;
        $canEdit = $canEdit && $canEditQueryBuilder;

		$response
			->getBody()
			->write(json_encode(['can_edit' => $canEdit, 'can_view' => $canView,'inline_save' => $can_inline]))
		;
		return $response;

    })->add(\APP\Auth::getSessionCheck());

});
