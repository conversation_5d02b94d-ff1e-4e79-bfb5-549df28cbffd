<?php

use APP\Tools;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/grading", function ($group) {

	$group->post("/generate-question", function (Request $request, Response $response) {

		if (!\APP\Auth::permission('view_learning_results__grade_work')) {
			return \APP\Tools::returnCode($request, $response, 403, "You do not have permission to use this feature");
		}
		$data = $request->getParsedBody();
		if (
			!empty($data['evidence_list']) &&
			is_array($data['evidence_list']) &&
			count($data['evidence_list']) > 0
		) {
			$fileIds = [];
			$grading = new \APP\Grading();

			// take first evidence that is file type
			foreach ($data['evidence_list'] as $key => $entry) {
				if ($entry['evidence_type'] == 'file') {
					$evidence_id = $entry['id'];
					$evidence = \Models\LearningModuleEvidence::find($evidence_id);
					if ($evidence) {
						$filename = $this->get('settings')["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension;
						if (file_exists($filename)) {
							if (
								!$evidence->openai_file_id ||
								!$evidence->openai_file_uploaded_at ||
								$evidence->openai_file_uploaded_at < \Carbon\Carbon::now()->subDays(6)
							) {
								$evidence->openai_file_id = $grading->uploadFile($filename);
								$evidence->openai_file_uploaded_at = \Carbon\Carbon::now();
								$evidence->save();
							}
							$fileIds[] = $evidence->openai_file_id;
						}
					}
				}
			}

			if (count($fileIds) > 0) {

				if (count($fileIds) == 1 && $evidence) {
					if (!$evidence->openai_thread_id) {
						// Step 2: Create thread
						$threadId = $grading->createThread();
						$evidence->openai_thread_id = $threadId;
						$evidence->save();
					}
					$threadId = $evidence->openai_thread_id;
					if (!$evidence->openai_message_id) {
						// Step 3: Add message
						$messageId = $grading->createMessage($threadId, $fileIds, \APP\Tools::getConfig('AIGradingQuestionPrompt'));
						$evidence->openai_message_id = $messageId;
						$evidence->save();
					}

					if (!$evidence->openai_run_id) {
						// Step 4: Run assistant
						$runId = $grading->createRun($threadId);
						$evidence->openai_run_id = $runId;
						$evidence->save();
					}
					$runId = $evidence->openai_run_id;

				} else {
					$threadId = $grading->createThread();
					$grading->createMessage($threadId, $fileIds, \APP\Tools::getConfig('AIGradingQuestionPrompt'));
					$runId = $grading->createRun($threadId);
				}

				// Step 5: Poll run status
				$grading->learning_result_id = $data['learning_result_id'] ?? null;
				$grading->user_id = $data['user_id'] ?? null;
				$completed = $grading->poolRun($threadId, $runId, 60);
				// run 2nd time to get AI response, as on first pool run I only get my own message back
				//$grading->poolRun($threadId, $runId);

				if (!$completed) {
					return \APP\Tools::returnCode($request, $response, 500, "AI grading run did not complete in time, please try again later or chose different documents");
				}

				// Step 6: Retrieve messages
				$content = $grading->getMessages($threadId, true);
				$response->getBody()->write(json_encode(['summary' => $content]));
				return $response->withHeader('Content-Type', 'application/json');
			} else {
				return \APP\Tools::returnCode($request, $response, 400, "No file evidence found");
			}

		} else {
			return \APP\Tools::returnCode($request, $response, 400, "Evidence list is empty");
		}

		return $response;
	})->add(\APP\Auth::getSessionCheck());

	$group->post("/grade", function (Request $request, Response $response) {

		if (!\APP\Auth::permission('view_learning_results__grade_work')) {
			return \APP\Tools::returnCode($request, $response, 403, "You do not have permission to use this feature");
		}
		$data = $request->getParsedBody();


		if (
			!empty($data['evidence_list']) &&
			is_array($data['evidence_list']) &&
			count($data['evidence_list']) > 0 &&
			!empty($data['prompt'])
		) {

			$fileIds = [];
			$grading = new \APP\Grading();

			// take first evidence that is file type
			foreach ($data['evidence_list'] as $key => $entry) {
				if ($entry['evidence_type'] == 'file') {
					$evidence_id = $entry['id'];
					$evidence = \Models\LearningModuleEvidence::find($evidence_id);
					if ($evidence) {
						$filename = $this->get('settings')["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension;
						if (file_exists($filename)) {
							if (
								!$evidence->openai_file_id ||
								!$evidence->openai_file_uploaded_at ||
								$evidence->openai_file_uploaded_at < \Carbon\Carbon::now()->subDays(6)
							) {
								$evidence->openai_file_id = $grading->uploadFile($filename);
								$evidence->openai_file_uploaded_at = \Carbon\Carbon::now();
								$evidence->save();
							}
							$fileIds[] = $evidence->openai_file_id;
						}
					}
				}
			}

			if (count($fileIds) > 0) {

				$threadId = $grading->createThread();


				// Step 3: Add message
				$maxLength = 2000;
				$prompt = trim($data['prompt']);
				$prompt = preg_replace('/[\x00-\x1F\x7F]/u', ' ', $prompt);
				$prompt = str_replace('```', '', $prompt);
				$prompt = mb_substr($prompt, 0, $maxLength, 'UTF-8');
				$structuredPrompt = $prompt . "\n\n" .
				"Now carefully assess the **relevance** and **quality** of the uploaded evidence. First, determine whether the file clearly addresses the topic below. If the evidence is off-topic, unrelated, or does not demonstrate understanding of the specific learning objective, assign a low grade and score even if the writing is well-structured.\n\n" .
				"Format the \"grade_text\" value as HTML. Wrap the section headings \"Strengths\", \"Areas for Improvement\", and \"Tips for Progression\" in `<strong></strong>` tags for bold styling.\n" .
				"Return the evaluation in the following JSON format (only the JSON, with no explanation):\n\n" .

<<<EOT
```json
{
"grade_text": "Your detailed feedback here.",
"grade_score": 0-100,
"grade_level": "Fail | Pass | Merit | Distinction"
}
EOT;

				$messageId = $grading->createMessage($threadId, $fileIds, $structuredPrompt);

				// Retry if vector store expired
				if (!$messageId && count($fileIds) === 1 && isset($evidence)) {
					// Re-upload file
					$filename = $this->get('settings')["LMSEvidencePath"] . $evidence->hash . '.' . $evidence->extension;
					$evidence->openai_file_id = $grading->uploadFile($filename);
					$evidence->openai_file_uploaded_at = \Carbon\Carbon::now();
					$evidence->save();

					$fileIds = [$evidence->openai_file_id];

					// Retry message creation
					$messageId = $grading->createMessage($threadId, $fileIds, $structuredPrompt);
				}

				$runId = $grading->createRun($threadId);


				// Step 5: Poll run status
				$grading->learning_result_id = $data['learning_result_id'] ?? null;
				$grading->user_id = $data['user_id'] ?? null;
				$completed = $grading->poolRun($threadId, $runId, 100);
				// run 2nd time to get AI response, as on first pool run I only get my own message back
				//$grading->poolRun($threadId, $runId);

				if (!$completed) {
					return \APP\Tools::returnCode($request, $response, 500, "AI grading run did not complete in time, please try again later or chose different documents");
				}

				// Step 6: Retrieve messages
				$content = $grading->getMessages($threadId, false);



				// Extract JSON from markdown block
				if (preg_match('/```json\s*(\{.*?\})\s*```/s', $content, $matches)) {
					$jsonString = $matches[1];
					$content = json_decode($jsonString, true);
				}

				$response->getBody()->write(
					json_encode([
						'grade_text' => $content['grade_text'] ?? '',
						'grade_score' => $content['grade_score'] ?? 0,
						'grade_level' => $content['grade_level'] ?? '',
					])
				);

				return $response;


			}

		} else {
			return \APP\Tools::returnCode($request, $response, 400, "Evidence list is empty");
		}

		return $response->withAddedHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getSessionCheck());
});
