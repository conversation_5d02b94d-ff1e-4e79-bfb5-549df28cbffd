<?php

use APP\Tools;
use GuzzleHttp\Client;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Slim\Psr7\Stream;

$app->group("/backend",  function ($group) {

	$group->get("/users/fields",  function (Request $request, Response $response) {
		$data = [];

		foreach(["Sex", "LLDDHealthProb", "Ethnicity"] as $field_name){
			$data[$field_name] = [];
			foreach($this->get('settings')["ilr_fields"][$field_name]["choices"] as $ch){
				$data[$field_name][] = [
					"value" => $ch["value"],
					"label" => $ch["name"],
				];
			}
		}

		$data["EmployerX_EmpStat"] = [];
		foreach($this->get('settings')["ilr_fields"]["LearnerEmploymentStatus"]["children"]["EmpStat"]["choices"] as $ch){
			$data["EmployerX_EmpStat"][] = [
				"value" => $ch["value"],
				"label" => $ch["name"],
			];
		}

		$esm_codes = ["SEI", "PEI", "SEM", "LOU", "BSI", "EII", "LOE"];

		foreach($esm_codes as $esm_code){
			$data["EmployerX_{$esm_code}"] = [];
			foreach(
				$this->get('settings')["ilr_fields"]["LearnerEmploymentStatus"]["children"]["EmploymentStatusMonitoring"]["children"]["ESMCode"]["choices"] as $code){

					if ($code["condition_value"] == $esm_code){

						$data["EmployerX_{$esm_code}"][] = [
							"value" => $code["value"],
							"label" => $code["name"],
						];
					}
				}
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));;

	$group->get("/users/ilr{offset:[\/0-9]*}", function (Request $request, Response $response, array $args) {

		$offset =  isset($args['offset']) ? str_replace("/","", $args['offset']): false;
		$limit = 1000;

		$users_query = \Models\User
			::where("status", "=", 1);

		$data = [];
		foreach($users_query->offset($offset * $limit)->limit($limit)->get() as $user){
			$data[$user->id] = [];
			$user_fields = [
					"username" => $user->username,
					"employee_id" => $user->usercode,
					"email" => $user->email,
					"fname" => $user->fname,
					"lname" => $user->lname,
				];

			foreach($user_fields as $field => $value){
				$data[$user->id][$field] = $value;
			}

			$data[$user->id]["ilr"] = [];
			foreach($this->get('settings')["ilr_fields"] as $ilr_field_name => $ilr_field_details){
				$data[$user->id]["ilr"][$ilr_field_name] = $user->$ilr_field_name;
			}
		}

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	// Add a user using API, create user, create a new user, create new user
	$group->post("/users",  function (Request $request, Response $response) {
		$data = $request->getParsedBody();


		try {
			foreach([
				//"password" => "password", Password should not be mandatory
				"fname" => "first name",
				"lname" => "last name",
				"username" => "username",
				"email" => "email",
			] as $field_id => $field_name) {
				if (empty($data[$field_id])) {
					throw new Exception("No {$field_name} specified");
				}
			}

			$optional_fields = \APP\Autoregister::processUserOptionalFields($data, $this->get('settings'));

			$user = \APP\Autoregister::registerUser(
				$data["username"],
				$data["email"],
				empty($data["password"]) ? "Ul" . bin2hex(openssl_random_pseudo_bytes(10)) . "1" : $data["password"],
				$data["fname"],
				$data["lname"],
				"api",
				$optional_fields)
			;


			//set up groups
			if (isset($data["groups"])){
				$groups = array_map('trim', explode(",", $data["groups"]));

				foreach($groups as $group_name){
					if (!empty($group_name)){
						if (!($group = \Models\Group::where("name", "=", $group_name)->first())){
							$group = new \Models\Group;
							$group->name = $group_name;
							$group->status = 1;
							$group->save();
						}

						$group_user = new \Models\GroupUser;
						$group_user->user_id = $user->id;
						$group_user->group_id = $group->id;
						$group_user->status = 1;
						$group_user->save();

						if ($group->add_remove_resources) {
							\Models\GroupLearningModule::AssingToUser($group->id, $user);
						}
					}
				}
			}

			// add managers
			if (isset($data["managers"])) {
				$manager_emails = array_map('trim', explode(",", $data["managers"]));
				if (!empty($manager_emails)){
					$manager_ids = \Models\User
						::whereIn("email", $manager_emails)
						->validuser()
						->whereHas('role', function ($query) {
							$query
								->where('is_manager', true)
								->where('status', true)
							;
						})
						->pluck('id')
						->toArray()
					;
					$user_ids = [$user->id];
					if (\APP\Tools::getConfig('assignAllLinkedUsersToAllLinkedManagers')) {
						$user_ids = \Models\User
							::where('email', $user->email)
							->validuser()
							->pluck('id')
							->toArray()
						;
					}
					if (count($manager_ids) > 0) {
						\Models\ManagerUser::link($user_ids, $manager_ids, 'User added by API.');
					}
				}
			}

			if (
				isset($data['send_instruction_email']) &&
				$data['send_instruction_email']
			) {
				\APP\Email::sendInstructionEmail($user->id);
			}

			\Models\Log::addEntry($request, $response, 201, ['type' => 'api', 'message' => 'User ' . $user->id . ' added by API call.']);

			return $response;

		} catch(Exception $ex) {
			return \APP\Tools::returnCode($request, $response, 500, 'Unable to register user. ' . $ex->getMessage(), ['type' => 'api']);
		}
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	// Get userlist(100 limit), or if "user_id" provided, get that specific user details, user list
	$group->get("/users{user_id:[\/0-9]*}", function (Request $request, Response $response, array $args) {

		//$time_start = microtime(true);

		$user_id =  isset($args['user_id']) ? str_replace("/","", $args['user_id']): false;

		$users = \Models\User
			::where('id', '>', 0)
			->with("Department")
			->with("Company")
			->with("City")
			->with("Country")
			->with("Location")
			->with("AccountType")
			->with("Role")
		;

		if (\APP\Tools::getConfig('version') == 'smcr') {
			$users = $users
				->with("StaffType")
			;
		}

		if ($user_id) {
			$users->where("users.id" , "=", $user_id);
		}

		// Those can be passed as url parameters for filtered search
		$search_params = [
			"fname", "lname", "username", "usercode", "email", "company_id", "department_id",
			"country_id", "city_id", "location_id", "group_id", "phone", "status", "id", "employee_id",
			"list_resources", "list_groups", "list_managers", "account_type", "role_extra_details",
			"offset", "limit", "position_ref", "watch", "watch_id", "list_learning_results"
		];
		$limit = 10;
		$offset = 0;
		$list_resources = true;
		$list_learning_results = false;
		$list_groups = true;
		$list_managers = false;
		$role_extra_details = false;

		$convert_to_boolean_list = [
			'list_resources',
			'list_learning_results',
			'list_groups',
			'list_managers',
			'role_extra_details',
		];

		foreach($request->getQueryParams() as $query_param_key => $query_param_value) {
			$query_param_key = strtolower($query_param_key);
			if (in_array($query_param_key, $search_params)){
				if ($query_param_key == "group_id") {
					$users->whereIn("id", function($query) use ($query_param_value) {
						$query->select("user_id")->from("group_users")->where("group_id", "=", $query_param_value);
					});
				} else if (
					$query_param_key == "limit" ||
					$query_param_key == "offset"
				) {
					if (is_numeric($query_param_value)) {
						$$query_param_key = intval($query_param_value);
					}
				} else if ($query_param_key == "employee_id") {
					$users->where("users.usercode", "=", $query_param_value);
				} else if ($query_param_key == "account_type") {
					$users = $users
						->whereIn('users.account_type_id',
							\Models\Picklist
								::select('id')
								->where('value', trim($query_param_value))
								->where('type', 'account_type')
								->get()
						)
					;
				} else if (in_array($query_param_key, $convert_to_boolean_list)) {
					$$query_param_key = filter_var($query_param_value, FILTER_VALIDATE_BOOLEAN);
				} else {
					$users->where("users.{$query_param_key}", "=", $query_param_value);
				}
			}
		}

		if ($list_resources) {
			$users = $users
				->with(['Modules' => function ($query) {
					$query
						->where('learning_modules.status', true)
						->where('learning_modules.track_progress', true)
						->where('learning_modules.visible_learner', true)
						->where(function($query) {
							$query = $query
								->where(function($query) {
									$query = $query
										->where('learning_modules.is_course', 1)
										->where('open_in_events_only', false)
										->where('created_by_event', 0)
									;
								})
								->orWhere('learning_modules.is_course', 0)
							;
						})

						->select([
							'learning_modules.id',
							'learning_modules.name',
							'learning_modules.type_id',
						])
						->with('Type')
					;
				}])
			;
		}

		if ($list_managers) {
			$users = $users
				->with(['Managers' => function ($query) {
					$query
						->where('users.status', 1)
						->select([
							'users.id',
							'users.fname',
							'users.lname',
							'users.username',
							'users.email',
							'users.status',
							'users.account_type_id',
						])
						->with('AccountType')
					;
				}])
			;
		}

		if ($list_groups) {
			$users = $users
				->with(['Groups' => function ($query) {
					$query
						->where('groups.status', 1)
						->select([
							'groups.id',
							'groups.name',
						])
					;
				}])
			;
		}


		$data = [];
		$entries_processed = 0;
		$id_limits = \APP\Tools::minMaxIdForThrottlingData($limit, $offset, $users);

		$users = $users
			->orderBy('id', 'asc')
			->where('id', '>=', $id_limits->minId)
			->where('id', '<=', $id_limits->maxId)
			->chunk(10, function ($users) use (&$data, $list_resources, $list_groups, $list_managers, $role_extra_details, &$entries_processed, $list_learning_results) {
				foreach ($users as $user) {
					$entries_processed++;
					$modules = [];
					if (
						$list_resources &&
						$user->Modules
					) {
						foreach($user->Modules as $module) {
							$modules[$module->id] = [
								"name" => $module->name,
								"type" => $module->Type ? $module->Type->name : null,
								"type_id" => $module->type_id ? $module->type_id : null,
							];
							if ($list_learning_results) {
								$learning_results = \Models\LearningResult
									::where("learning_module_id", $module->id)
									->where("refreshed", 0)
									->where("user_id", $user->id)
									->get()
								;
								$learning_result_data = [];
								foreach ($learning_results as $key => $lr) {
									$learning_result_data[] = [
										"id" => $lr->id,
										"user_id" => $lr->user_id,
										"learning_module_id" => $lr->learning_module_id,
										"completion_status" => $lr->completion_status,
										"passing_status" => $lr->passing_status,
										"grade" => $lr->grade,
										"status" => $lr->passing_status == "failed" ? "failed" : $lr->completion_status,
										"score" => $lr->score,
										"completed_version" => $lr->completed_version,
										"completed_by" => $lr->completed_by,
										"created_by" => $lr->created_by,

										"completed_at" => $lr->completed_at  ? $lr->completed_at . "" : null,
										"created_at" => $lr->created_at  ? $lr->created_at . "" : null,
										"due_at" => $lr->due_at  ? $lr->due_at . "" : null,
										"grace_at" => $lr->grace_at  ? $lr->grace_at . "" : null,
										"completion_date_custom" => $lr->completion_date_custom  ? $lr->completion_date_custom . "" : null,

										"duration_hours" => $lr->duration_hours,
										"duration_minutes" => $lr->duration_minutes,

									];
								}
								$modules[$module->id]['learning_results'] = $learning_result_data;
							}
						}
					}
					$groups = [];
					if (
						$list_groups &&
						$user->Groups
					) {
						foreach($user->Groups as $group) {
							$groups[$group->id] = [
								"name" => $group->name,
							];
						}
					}

					$managers = [];
					if (
						$list_managers &&
						$user->Managers
					) {
						foreach($user->Managers as $manager) {
							$managers[$manager->id] = [
								'fname' => $manager->fname,
								'lname' => $manager->lname,
								'username' => $manager->username,
								'email' => $manager->email,
								'status' => $manager->status,
								'created_by' => $manager->pivot->created_by,
								'comment_link' => $manager->pivot->comment_link,
								'account_type' => $manager->AccountType ? $manager->AccountType->value : '',
							];
						}
					}

					$role = $user->Role ? [
							"id" => $user->Role->id,
							"name" => $user->Role->name,
						] : null
					;
					if ($role_extra_details) {
						if ($user->Role) {
							$role = [];
							foreach ($user->Role->getAttributes() as $role_key => $role_value) {
								$role[$role_key] = $role_value;
							}
						}
					}

					$data[] = [
						$user->id =>
							[
								"username" => $user->username,
								"employee_id" => $user->usercode,
								"email" => $user->email,
								"fname" => $user->fname,
								"lname" => $user->lname,
								"phone" => $user->phone,
								"account_type" => $user->AccountType ? $user->AccountType->value : '',
								"manager_name" => $user->emergency_name,
								"manager_job_title" => $user->emergency_relationship,
								"manager_email" => $user->emergency_contact_numbers,
								"status" => $user->status,
								"country" => $user->Country ?
									["id" => $user->Country->id, "name" => $user->Country->name] : null,
								"city" => $user->City ?
									["id" => $user->City->id, "name" => $user->City->name, "country_id" => $user->City->country_id] : null,
								"company" =>  $user->Company ?
									["id" => $user->Company->id, "name" => $user->Company->name] : null,
								"department" =>  $user->Department ?
									["id" => $user->Department->id, "name" => $user->Department->name, "company_id" => $user->Department->company_id] : null,
								"location" =>  $user->Location ?
									["id" => $user->Location->id, "name" => $user->Location->name] : null,
								"job" =>  $user->Designation ?
									["id" => $user->Designation->id, "name" => $user->Designation->name] : null,
								"groups" => $groups,
								"learning" => $modules,
								"managers" => $managers,
								"role" => $role,
								"position_ref" => $user->position_ref,
								//"watch" => $user->watch,
								"watch" =>  $user->Watch ?
									["id" => $user->Watch->id, "name" => $user->Watch->name] : null,
							]
						]
					;
				}
			})
		;

		$response
			->getBody()
			->write(json_encode(["users" => $data]))
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	// Update specific user details, update user
	$group->any("/users/{user_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$user_id =  $args['user_id'];
		$data = $request->getParsedBody();

		try {
			$user = \Models\User
				::where("id", "=", $user_id)
				->firstOrFail()
			;
			foreach($data as $field_name => $field_value){
				if (in_array($field_name, [
					"fname",
					"lname",
					"username",
					"usercode",
					"email",
				])) {
					$user->$field_name = $field_value;
				}
				//set up group
				if (
					$field_name = "groups" &&
					!empty($data["groups"])
				) {

					//remove user from existing groups
					\Models\GroupUser
						::where("user_id", $user->id)
						->delete()
					;

					//set up groups
					$groups = array_map('trim', explode(",", $data["groups"]));

					foreach($groups as $group_name) {
						if (!empty($group_name)) {
							if (!($group = \Models\Group::where("name", "=", $group_name)->first())){
								$group = new \Models\Group;
								$group->name = $group_name;
								$group->status = 1;
								$group->save();
							}

							$group_user = new \Models\GroupUser;
							$group_user->user_id = $user->id;
							$group_user->group_id = $group->id;
							$group_user->status = 1;
							$group_user->save();

							if ($group->add_remove_resources) {
								\Models\GroupLearningModule::AssingToUser($group->id, $user);
							}
						}
					}
				}

				// delete managers
				if (
					$field_name = "managers_delete" &&
					!empty($data["managers_delete"])
				) {
					$manager_emails = array_map('trim', explode(",", $data["managers_delete"]));
					$manager_ids = \Models\User
						::whereIn("email", $manager_emails)
						->validuser()
						/*
						->whereHas('role', function ($query) {
							$query
								->where('is_manager', true)
								->where('status', true)
							;
						})
						*/
						->pluck('id')
						->toArray()
					;

					$user_ids = [$user->id];
					if (\APP\Tools::getConfig('assignAllLinkedUsersToAllLinkedManagers')) {
						$user_ids = \Models\User
							::where('email', $user->email)
							->validuser()
							->pluck('id')
							->toArray()
						;
					}
					if (count($manager_ids) > 0) {
						\Models\ManagerUser::unLink($user_ids, $manager_ids, 'Manager removed from user by API', \APP\Tools::getConfig('API_ignoreManuallyLinked'));
					}
				}

				// add managers
				if (
					$field_name = "managers" &&
					!empty($data["managers"])
				) {
					$manager_emails = array_map('trim', explode(",", $data["managers"]));
					if (!empty($manager_emails)){
						$manager_ids = \Models\User
							::whereIn("email", $manager_emails)
							->validuser()
							->whereHas('role', function ($query) {
								$query
									->where('is_manager', true)
									->where('status', true)
								;
							})
							->pluck('id')
							->toArray()
						;
						$user_ids = [$user->id];
						if (\APP\Tools::getConfig('assignAllLinkedUsersToAllLinkedManagers')) {
							$user_ids = \Models\User
								::where('email', $user->email)
								->validuser()
								->pluck('id')
								->toArray()
							;
						}
						if (count($manager_ids) > 0) {
							\Models\ManagerUser::link($user_ids, $manager_ids, 'User added by API.');
						}
					}
				}

			}

			$optional_fields = \APP\Autoregister::processUserOptionalFields($data, $this->get('settings'));

			foreach($optional_fields as $field_id => $field_value) {
				$user->$field_id = $field_value;
			};

			$user->save();

			\Models\Log::addEntry($request, $response, 204, ['type' => 'api', 'message' => 'User ' . $user->id . ' updated by API call.']);

		} catch(Exception $ex) {
			return \APP\Tools::returnCode($request, $response, 500, 'Unable to update user. ' . $ex->getMessage(), ['type' => 'api']);
		}

		return $response;

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	//Assing learning to user
	$group->put("/assign/{user_id:[0-9]+}/{learning_ids:[0-9,]+}", function (Request $request, Response $response, array $args) {
		$user_id =  $args['user_id'];
		$learning_ids =  $args['learning_ids'];
		$current_learning_id = null;

		try
		{
			$user = \Models\User
				::where("id", "=", $user_id)
				->firstOrFail()
			;

			$module_ids = [];



			//Make sure learnings exists
			foreach(explode(",", $learning_ids) as $learning_id){
				$current_learning_id = trim($learning_id);

				if (!empty($current_learning_id)){
					\Models\LearningModule
						::where("id", "=", $current_learning_id)
						->firstOrFail()
					;
					$module_ids[] = $current_learning_id;
				}
			}

			\Models\UserLearningModule::linkResources($user->id, $module_ids, 'api call - Assing learning to user');
			\APP\Learning::syncUserResults($user->id);

		} catch(Exception $ex) {
			return \APP\Tools::returnCode($request, $response, 500, "Unable to assign learning #{$current_learning_id} to user - learning module doesn't exist. ");
		}

		return $response;

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	//Unassign learning
	$group->delete("/unassign/{user_id:[0-9]+}/{learning_ids:[0-9,]+}", function (Request $request, Response $response, array $args) {
		$user_id =  $args['user_id'];
		$learning_ids =  $args['learning_ids'];
		$current_learning_id = null;

		try
		{
			$user = \Models\User
				::where("id", "=", $user_id)
				->firstOrFail()
			;

			$module_ids = [];

			//Make sure learnings exists
			foreach(explode(",", $learning_ids) as $learning_id){
				$current_learning_id = trim($learning_id);

				if (!empty($current_learning_id)){
					\Models\LearningModule
						::where("id", "=", $current_learning_id)
						->firstOrFail()
					;
					$module_ids[] = $current_learning_id;
				}
			}

			\Models\UserLearningModule::unlinkResources($user->id, $module_ids, 'api call - remove learning from user');

			\APP\Learning::syncUserResults($user->id);

		} catch(Exception $ex) {
			return \APP\Tools::returnCode($request, $response, 500, "Unable to unassign learning #{$current_learning_id} - learning module doesn't exist. ");
		}

		return $response;

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	// update learning result
	$group->put("/learning/{learning_id:[0-9]+}/{user_id:[0-9]+}/{action:[a-z]+}{score:[\/0-9]*}", function (Request $request, Response $response, array $args) {
		$learning_id = $args["learning_id"];
		$user_id = $args["user_id"];
		$action = $args["action"];
		$score =  isset($args['score']) ? str_replace("/","", $args['score']): null;
		$lr = \Models\LearningResult
			::where("user_id", "=", $user_id)
			->where("learning_module_id", "=", $learning_id)
			->where("refreshed", "=", 0)
			->firstOrFail();
		if ($lr) {
			switch($action){
				case "start":
					if ($lr->completion_status == "not attempted") {
						$lr->completion_status = "in progress";
						$lr->updated_at = \Carbon\Carbon::now();
						$lr->save();
					}
				break;
				case "complete":
					if ($lr->completion_status != "completed") {
						$lr->completion_status = "completed";
						$lr->updated_at = \Carbon\Carbon::now();
						$lr->completed_at = \Carbon\Carbon::now();
						$lr->score = $score;
						$lr->save();
					}
				break;
				case "fail":
					$lr->completion_status = "in progress";
					$lr->passing_status = "failed";
					$lr->updated_at = \Carbon\Carbon::now();
					$lr->completed_at = null;
					$lr->score = $score;
					$lr->save();
				break;
			};
		}
		return $response;
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	//get learning results for specific resource, if user_id is specified, get only for that user
	$group->get("/learning/{learning_id:[0-9]+}/trainees{_:\/*}{user_id:[0-9]*}", function (Request $request, Response $response, array $args) {
		$learning_id = $args["learning_id"];
		$user_id = $args["user_id"] ?? false;
		$limit = 10;
		$offset = 0;

		$learning_results = \Models\LearningResult
			::where("learning_module_id", "=", $learning_id)
			->where("refreshed", "=", 0)
		;
		if ($user_id) {
			$learning_results = $learning_results
				->where("user_id", "=", $user_id)
			;
		}

		$search_params = [
			"offset", "limit", "completion_status", "created_at_last_days", "updated_at_last_days", "completed_at_last_days",
		];


		foreach($request->getQueryParams() as $query_param_key => $query_param_value) {
			$query_param_key = strtolower($query_param_key);
			if (in_array($query_param_key, $search_params)) {
				if (
					$query_param_key == "limit" ||
					$query_param_key == "offset"
				) {
					if (is_numeric($query_param_value)) {
						$$query_param_key = intval($query_param_value);
					}
				}
				if ($query_param_key == "completion_status") {
					$learning_results = $learning_results
						->where("completion_status", $query_param_value)
					;
				}
				if (
					$query_param_key == "created_at_last_days" ||
					$query_param_key == "updated_at_last_days" ||
					$query_param_key == "completed_at_last_days"
				) {
					$date_field_name = substr($query_param_key, 0, -10);
					$learning_results = $learning_results
						->whereNotNull($date_field_name)
						->where($date_field_name, '>', \Carbon\Carbon::now()->subDays(intval($query_param_value))->endOfDay())
					;
				}
			}
		}

		$id_limits = \APP\Tools::minMaxIdForThrottlingData($limit, $offset, $learning_results);


		$data = [];

		$entries_processed = 0;
		$learning_results = $learning_results
			->orderBy('id', 'asc')
			->where('id', '>=', $id_limits->minId)
			->where('id', '<=', $id_limits->maxId)
			->chunk(10, function ($entries) use (&$data, &$entries_processed) {
				foreach ($entries as $lr) {
					$entries_processed++;
					$data[] = [
						"id" => $lr->id,
						"user_id" => $lr->user_id,
						"learning_module_id" => $lr->learning_module_id,
						"completion_status" => $lr->completion_status,
						"passing_status" => $lr->passing_status,
						"grade" => $lr->grade,
						"status" => $lr->passing_status == "failed" ? "failed" : $lr->completion_status,
						"score" => $lr->score,
						"completed_version" => $lr->completed_version,
						"completed_by" => $lr->completed_by,
						"created_by" => $lr->created_by,

						"completed_at" => $lr->completed_at  ? $lr->completed_at . "" : null,
						"created_at" => $lr->created_at  ? $lr->created_at . "" : null,
						"due_at" => $lr->due_at  ? $lr->due_at . "" : null,
						"grace_at" => $lr->grace_at  ? $lr->grace_at . "" : null,
						"completion_date_custom" => $lr->completion_date_custom  ? $lr->completion_date_custom . "" : null,

						"duration_hours" => $lr->duration_hours,
						"duration_minutes" => $lr->duration_minutes,

						//"field" => $lr->field,
					];
				}
			})
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));;

	// Create SCORM zip file and return it
	$group->get("/learning/download/{learning_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$learning_id = $args["learning_id"];

		try {
			$learning = \Models\LearningModule
				::where("id", "=", $learning_id)
				->firstOrFail()
			;

			if ($learning->type->id == 1) {
				$scorm_files_path = $this->get('settings')["LMSScormDataPath"] . "{$learning_id}/moddata/scorm/1";
				$scorm_zip_path = $this->get('settings')["LMSTempPath"] . "{$learning_id}.scorm.zip";

				if (!file_exists($scorm_zip_path)) {
					//zip scorm files
					//print($scorm_zip_path);
					$zip = new \ZipArchive();

					$result_code = $zip->open($scorm_zip_path, \ZipArchive::CREATE);
					if (true !== $result_code){
						throw new Exception("Can't open zip file.");
					}

					\APP\Tools::zipRecursive($scorm_files_path, $zip);
					if (!$zip->status == ZIPARCHIVE::ER_OK){
						throw new Exception("Can't open zip file.");
					}

					$zip->close();

				}

				if (!file_exists($scorm_zip_path)) {
					throw new Exception("Can't create SCORM ZIP file.");
				}


				// Prepare the response to download the file
				$response = $response->withHeader('Content-Type', 'application/zip');
				$response = $response->withHeader('Content-Disposition', 'attachment; filename="' . basename($scorm_zip_path) . '"');
				$response->getBody()->write(file_get_contents($scorm_zip_path));

				// Delete the file after sending
				register_shutdown_function(function() use ($scorm_zip_path) {
					unlink($scorm_zip_path);
				});

				return $response;


			} else {
				throw new Exception("This learning resource has no SCORM contents.");
			}
		} catch(Exception $ex) {
			$response->getBody()->write($ex->getMessage());
			$response = $response->withStatus(404);
			return $response;
		}

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	// Create SCORM container zip file and return it
	// curl -H "Authorization: Basic 074daf897c90177597d92d655be19e9e" -k https://openelms/backend/learning/download-container/12439 -O -J -L
	$group->get("/learning/download-container/{learning_id:[0-9]+}", function (Request $request, Response $response, array $args) {
		$learning_id = $args["learning_id"];

		try {
			$learning = \Models\LearningModule
				::where("id", $learning_id)
				->firstOrFail()
			;

			// Check if containers link exist, if not, create new one!
			$container = \Models\LearningModuleContainer
				::firstOrCreate(
					[
						'learning_module_id' => $learning->id
					],
					[
						'name' => $learning->id,
						'token' => bin2hex(random_bytes(14))
					]
				)
			;
			// Enable first container
			$container->status = 1;
			$container->save();

			if ($learning->type->id == 1) {
				$from = $this->get('settings')['LMSPrivatePath'] . "container_template";
				$to = $this->get('settings')['LMSPrivatePath'] . "containers/temp";

				if (is_dir($to)) {
					// remove temp directory, if it was left behind from some other job.
					\APP\Tools::delDirTree($to);
				}
				mkdir($to, 0775);

				if (
					is_dir($from) &&
					is_dir($to)
				) {
					\APP\Tools::recurseCopy($from, $to);
					// Make images directory and include thumb/promo if exists
					mkdir ($to . '/images', 0775);
					$promo =  $this->get('settings')["LMSScormDataPath"] . "/" . $learning->id . "/moddata/scorm/1/images/promo.jpg";
					if (is_file($promo)) {
						copy($promo, $to . '/images/' . 'promo.jpg');
					}
					$thumb =  $this->get('settings')["LMSScormDataPath"] . "/" . $learning->id . "/moddata/scorm/1/images/thumb.jpg";
					if (is_file($thumb)) {
						copy($thumb, $to . '/images/' . 'thumb.jpg');
					}

					//open training.htm file, change %%SCORM_TOKEN%% and %%CONTAINER_URL%%
					$training = file_get_contents($to . "/training.htm");
					$training = str_replace("%%SCORM_TOKEN%%", $container->token, $training);
					$training = str_replace("%%CONTAINER_URL%%", $this->get('settings')['LMSUrl'], $training);
					file_put_contents($to . "/training.htm", $training);

					$imsmanifest = file_get_contents($to . "/imsmanifest.xml");
					$imsmanifest = str_replace("%%LEARNING_NAME%%", $learning->name, $imsmanifest);
					$imsmanifest = str_replace("%%LEARNING_NAME_SAFE%%", \APP\Tools::safeName($learning->name), $imsmanifest);
					$imsmanifest = str_replace("%%LEARNING_DESCRIPTION%%", $learning->description, $imsmanifest);
					file_put_contents($to . "/imsmanifest.xml", $imsmanifest);

					$zip = new ZipArchive();
					$filename = $this->get('settings')["LMSPrivatePath"] . "containers/" . \APP\Tools::safeName($learning->name, "_") . ".zip";

					// Open the ZIP archive for creation/overwrite
					if ($zip->open($filename, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
						throw new RuntimeException("Cannot create ZIP file: <$filename>");
					}

					// Add files to the ZIP archive using a recursive function
					$zip = \APP\Tools::zipRecursive($to, $zip);

					// Verify the ZIP archive status
					if ($zip->status !== ZipArchive::ER_OK) {
						$zip->close();
						throw new RuntimeException("Failed to finalize ZIP file: <$filename>");
					}

					// Close the ZIP file
					$zip->close();

					// Verify the ZIP file exists
					if (!file_exists($filename)) {
						throw new RuntimeException("Failed to create SCORM Container ZIP file: <$filename>");
					}

					$fileStream = new OpenStream($filename, 'r');

					$response = $response
						->withHeader('Content-Length', filesize($filename))
						->withHeader('Content-Type', 'application/zip')
						->withHeader('Content-Disposition', 'attachment; filename="' . \APP\Tools::safeName($learning->name, '_') . '.zip"')
						->withBody($fileStream)
					;
					return $response;

				} else {
					throw new Exception("System settings failure.");
				}
			} else {
				throw new Exception("This learning resource has no SCORM contents.");
			}
		} catch(Exception $ex) {
			return \APP\Tools::returnCode($request, $response, 500, 'Unable to download container. ' . $ex->getMessage(), ['type' => 'api']);
		}

	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	// returns resource list or specific resource with download/container url
	$group->get("/learning{learning_id:[\/0-9]*}", function (Request $request, Response $response, array $args) {
		$learning_id = $args["learning_id"] ? str_replace("/", "", $args["learning_id"]) : false;

		$learnings = \Models\LearningModule
			::where("status", "=", 1)
			->with('category')
			->with('competencies')
		;
		if ($learning_id){
			$learnings->where("id", "=", $learning_id);
		}

		$data = [];

		foreach($learnings->get() as $learning) {
			$category = null;
			$competencies = [];
			if ($learning->category) {
				$category = [
					'id' => $learning->category->id,
					'name' => $learning->category->name,
					'is_mandatory' => $learning->category->is_mandatory,
				];
			}
			foreach ($learning->competencies as $key => $competency) {
				$competencies[$competency->id] = [
					"name" => $competency->name,
					"required_points" => $competency->required_points,
					"description" => $competency->description,
					"created_at" => $competency->created_at,
				];
			}
			$data[$learning->id] = [
					"name" => $learning->name,
					"type" => $learning->type ? $learning->type->name : null,
					"code" => $learning->code,
					"category" => $category,
					"competencies" => $competencies,
					"description" => $learning->description,
					"keywords" => $learning->keywords,
					"created_at" => $learning->created_at,
					"created_by" => $learning->created_by,
					"updated_at" => $learning->updated_at,
					"updated_by" => $learning->updated_by,
/*
    Learning Resource Code *

    Category

    Competencies List

    Description

    Keywords

    when created

and a series of other flags/settings available in the UI.
*/
			];
			if ($learning->type){
				if ($learning->type->id == 1) {
					$data[$learning->id]["scorm_download_url"] = $this->get('settings')["LMSUrl"] . "backend/learning/download/{$learning->id}";
					$data[$learning->id]["scorm_container_download_url"] = $this->get('settings')["LMSUrl"] . "backend/learning/download-container/{$learning->id}";
				}
			}
		}

		$response
			->getBody()
			->write(json_encode(["learning" => $data]))
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	//Get one time URL to load creator for given resource
	$group->get("/learning/creator-url/{learning_id:[0-9]+}", function (Request $request, Response $response, array $args) {

		$learning = \Models\LearningModule
			::where("status", true)
			->where("id", $args["learning_id"])
			->whereIn( 'type_id',
				\Models\LearningModuleType
					::select('id')
					->where('slug', 'e_learning')
					->get()
			)
			->first()
		;

		if (!$learning) {
			return \APP\Tools::returnCode($request, $response, 404);
		}

		$learning->jackdaw_access_token = bin2hex(random_bytes(14));
		$learning->save();

		// Generate new openelms creator token.

		//Create URL
		$access_url = $this->get('settings')["LMSUrl"] . 'creator/token/' . $learning->jackdaw_access_token;

		$response
			->getBody()
			->write($access_url)
		;

		return
			$response
				->withHeader('Content-Type', 'text/html')
		;
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	// Generate new API access Token.
	$group->get("/new/APITokenAccess", function (Request $request, Response $response, array $args) {
		$query = \Models\Configuration
			::firstOrCreate(
				['key' => 'APITokenAccess'],
				[
					'name' => 'API Access Token',
					'type' => 'string',
					'status' => 1,
					'created_by' => 0
				]
			);
		;
		$query->status = 1;
		$query->value = bin2hex(openssl_random_pseudo_bytes(16));
		$query->save();

		$response->getBody()->write(json_encode($query));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-api-setup', 'update'));

	$group->get("/new/APITokenAccess-media-library", function (Request $request, Response $response, array $args) {
		$query = \Models\Configuration
			::firstOrCreate(
				['key' => 'APITokenAccessMediaLibrary'],
				[
					'name' => 'API Access Token for Media Library',
					'type' => 'string',
					'hidden' => true,
					'status' => 1,
					'created_by' => 0
				]
			);
		;
		$query->status = 1;
		$query->value = bin2hex(openssl_random_pseudo_bytes(16));
		
		$baseUri = Tools::getConfig('creatorModuleAPI');
        $apiToken  = Tools::getConfig('creatorModuleAPIKey');

        $headers = [
        'Content-Type' => 'application/json',
        'Authorization' => 'Bearer '. $apiToken
        ];
        $client = new Client([
        'base_uri' => $baseUri,
        'headers' => $headers
        ]);
		$call = $client->post('api/client-token',[
			'json' => [
				'token' => $query->value
			]
			]);
		$res = json_decode($call->getBody(), true);
		$call->getStatusCode() == 200 ? $query->save() : null;

		$response->getBody()->write(json_encode($res));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-api-setup', 'update'));

	/*
		Create new e-learning resource,
		type and name is provided as form data
		will respond with unique ID
		curl --data "name=example&type=elearning" -H "Authorization: Basic 074daf897c90177597d92d655be19e9e" -k https://openelms/backend/learning/new-resource
	*/
	$group->post("/learning/new-resource", function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();

		if (empty($data['name'])) {
			return \APP\Tools::returnCode($request, $response, 412, 'Resource name needs to be specified!');
		}
		// Need to store this list somewhere in more clever way, database or check for directory name.
		$allowed_types = [
			'audit',
			'elearning',
			'elearning_quiz',
			'induction',
			'mp4_quiz',
			'quiz',
			'skillscan',
			'vimeo_quiz',
			'youtube_quiz',
		];
		if (
			empty($data['type']) ||
			!in_array($data['type'], $allowed_types)
		) {
			$data['type'] = 'elearning';
		}

		// Get all needed files to call submodule api, so that I don't need to duplicate code, maybe there is more clever way to call this.
		require_once $this->get('settings')["LMSPublicPath"] . 'api/Util/AbsPath.php';
		require_once $this->get('settings')["LMSPublicPath"] . 'api/Util/RelPath.php';
		require_once $this->get('settings')["LMSPublicPath"] . 'api/Util/CoursesHelper.php';
		require_once $this->get('settings')["LMSPublicPath"] . 'api/Util/File.php';
		require_once $this->get('settings')["LMSPublicPath"] . 'api/Util/Url.php';
		require_once $this->get('settings')["LMSPublicPath"] . 'api/Jackdaw/Courses.php';
		$new_resource = new \Jackdaw\Courses;
		$new_resource->backend_access = true;
		$new_resource_response = $new_resource->post(['name' => $data['name'], 'type' => $data['type']]);

		$response
			->getBody()
			->write(json_encode($new_resource_response['id']))
		;

		return
			$response
		;
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));

	//List groups

	// List roles
	// returns resource list or specific resource with download/container url
	$group->get("/roles{role_id:[\/0-9]*}", function (Request $request, Response $response, array $args) {
		$role_id = $args["role_id"] ? str_replace("/", "", $args["role_id"]) : false;

		$roles = \Models\Role
			::where("id", ">", 0)
		;

		if ($role_id) {
			$roles->where("id", "=", $role_id);
		}


		$roles = $roles
			->get()
		;


		$response
			->getBody()
			->write(json_encode(["roles" => $roles]))
		;

		return
			$response
				->withHeader('Content-Type', 'application/json')
		;
	})->add(\APP\Auth::getBasicTokenCheck("APITokenAccess"));
});
