<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/dashboards",  function ($group) {
	$group->put('/disable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$location = \Models\Dashboard::find($args["id"]);
		$location->status = 0;
		$location->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'disable'));

	$group->put('/enable/{id:[0-9]+}', function (Request $request, Response $response, $args) {
		$location = \Models\Dashboard::find($args["id"]);
		$location->status = 1;
		$location->save();

		return $response;
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'disable'));

	$group->get("/assignments/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$role_ids = \Models\Dashboard
			::select("role_id")
			->where("id", "<>", $args["id"])
			->groupBy("role_id")
			->having('role_id', '<>', 'NULL')
			->get()
			 ->toArray()
		;

		$designation_ids = \Models\Dashboard
			::select("designation_id")
			->where("id", "<>", $args["id"])
			->groupBy("designation_id")
			->having('designation_id', '<>', 'NULL')
			->get()
			->toArray();
		;

		$staff_type_ids = \Models\Dashboard
			::select("staff_type_id")
			->where("id", "<>", $args["id"])
			->groupBy("staff_type_id")
			->having('staff_type_id', '<>', 'NULL')
			->get()
			->toArray();
		;

		$data = [
			"role_ids" => $role_ids,
			"designation_ids" => $designation_ids,
			"staff_type_ids" => $staff_type_ids,
		];
		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'select'));    ;

	$group->post("/new", function (Request $request, Response $response) {

		$data = $request->getParsedBody();

		$dashboard = new \Models\Dashboard;
		$dashboard->name = $data["name"];
		$dashboard->visibility = $data["visibility"];
		$dashboard->role_id = $data["role_id"];

		if (isset($data["apllies_to"]))
		{
			if ($data["apllies_to"] == "job")
			{
				$dashboard->designation_id = $data["designation_id"];
			}
			if ($data["apllies_to"] == "staff_type")
			{
				$dashboard->staff_type_id = $data["staff_type_id"];
			}
		}
		$dashboard->save();

		$statistics = [];
		foreach($data["statistics"] as $statistic)
		{
			$statistics[] = $statistic["id"];
		}
		$dashboard->statistics()->attach($statistics);

		$dataviews = [];
		$dataview_attributes = [];
		foreach($data["dataviews"] as $dataview)
		{
			$dataviews[] = $dataview["id"];
			$dataview_attributes[$dataview["id"]] = [
				"display_size" => $dataview["pivot"]["display_size"],
				"chart_type" => $dataview["pivot"]["chart_type"]
			];
		}
		$dashboard->rawdataviews()->attach($dataviews);

		foreach($dataview_attributes as $dataview_id => $attrs)
		{
			$dashboard->rawdataviews()->updateExistingPivot(
				$dataview_id, $attrs
			);
		}

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'insert'));

	$group->get("/home", function (Request $request, Response $response) {
		if ($dashboard = \APP\Auth::getDashboard())
		{
			$response->getBody()->write($dashboard->toJson());
		return $response->withHeader('Content-Type', 'application/json');

		}
		else
		{
			return $response;
		}
	})->add(\APP\Auth::getSessionCheck());

	$group->get("/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$dashboard = \Models\Dashboard
			::with(["role", "designation", "stafftype"])
			->find($args["id"])
		;

		$response->getBody()->write($dashboard->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'select'));

	$group->post("/check/{id:[0-9]+}", function (Request $request, Response $response, $args) {
		$data = $request->getParsedBody();


		if (isset($data["role_id"]) && ($data["role_id"] > 0))
		{
			$query = \Models\Dashboard
				::where("id", "<>", $args["id"])
				->where("role_id", "=", $data["role_id"]);
			;
		} else {
			return $response;
		}

		if ($data["applies_to"] == "job")
		{
			$query->where("designation_id","=", $data["designation_id"]);
		}

		if ($data["applies_to"] == "staff_type")
		{
			$query->where("staff_type_id","=", $data["staff_type_id"]);
		}

		if ($dashboard = $query->where("status", "=", 1)->first())
		{
			//print_r($dashboard->toArray());
			return $response->withStatus(403);
		}
		else
		{
			return $response;
		}

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'insert'));

	$group->put("/{id:[0-9]+}", function (Request $request, Response $response, $args) {

		$data = $request->getParsedBody();

		$dashboard = \Models\Dashboard::find($args["id"]);
		$dashboard->name = $data["name"];
		$dashboard->visibility = $data["visibility"];
		$dashboard->role_id = $data["role_id"] > 0 ? $data["role_id"] : null;
		$dashboard->designation_id = null;
		$dashboard->staff_type_id = null;

		if (isset($data["applies_to"]))
		{
			switch($data["applies_to"])
			{
				case "job":
					$dashboard->designation_id = $data["designation_id"];
					break;
				case "staff_type":
					$dashboard->staff_type_id = $data["staff_type_id"];
					break;
			}
		}

		$dashboard->save();

		$statistics = [];
		foreach($data["statistics"] as $statistic)
		{
			$statistics[] = $statistic["id"];
		}
		$dashboard->statistics()->sync($statistics);

		// print_r($data["dataviews"]);

		$dataviews = [];
		$dataview_attributes = [];

		foreach($data["dataviews"] as $dataview)
		{
			$dataviews[] = $dataview["id"];
			$dataview_attributes[$dataview["id"]] = [
				"display_size" => $dataview["pivot"]["display_size"],
				"chart_type" => $dataview["pivot"]["chart_type"]
			];
		}

		$dashboard->rawdataviews()->detach();
		$dashboard->rawdataviews()->attach($dataviews);

		//$dashboard->rawdataviews()->sync($dataviews);

		foreach($dataview_attributes as $dataview_id => $attrs)
		{
			$dashboard->rawdataviews()->updateExistingPivot(
				$dataview_id, $attrs
			);
		}

		return $response;

	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'insert'));

	$group->get('/statistics', function (Request $request, Response $response) {

		$data = \Models\Statistic::all()->toArray();

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'select'));

	$group->post('/dataview/update/{id:[0-9]+}', function (Request $request, Response $response, array $args) {
		$data = $request->getParsedBody();
		$dtv =
			\Models\Dataview::find([$args['id']])
		;



		if (isset($data['overall_complience_period']))
		{
			$new_data = \APP\Dataviews::computeOnline("overall_complience", ["period" => $data['overall_complience_period']]);
		} else {
			$new_data = \APP\Dataviews::computeOnline("overall_complience");
		}

		$response->getBody()->write(json_encode($new_data));
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getSessionCheck());

	$group->get('/dataviews', function (Request $request, Response $response) {

		$data =
			\Models\Dataview
				::where("data", "<>", "")
				->orWhere("is_online", "=", 1)
				->get()
				->toArray()
		;

		$response->getBody()->write(json_encode($data));
		return $response->withHeader('Content-Type', 'application/json');
	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'select'));

	$group->post('/list', function (Request $request, Response $response) {

		$params = $request->getParsedBody();

		$query = \Models\Dashboard
				::selectRaw("dashboards.*")
				->selectRaw("roles.name AS role_name")
				->selectRaw("designations.name AS designation_name")
				->selectRaw("smcr_staff_types.name AS staff_type_name")
				->leftJoin("roles", function($join) {
					$join
						->on("dashboards.role_id", "=", "roles.id")
					;
				})
				->leftJoin("designations", function($join) {
					$join
						->on("dashboards.designation_id", "=", "designations.id")
					;
				})
				->leftJoin("smcr_staff_types", function($join) {
					$join
						->on("dashboards.staff_type_id", "=", "smcr_staff_types.id")
					;
				})
		;
		if (isset($params["sort"]) && isset($params["sort"]["predicate"]) && isset($params["sort"]["reverse"]))
		{
			$query->orderBy($params["sort"]["predicate"], $params["sort"]["reverse"] ? "DESC" : "ASC");
		}

		if (isset($params["search"]) && is_array($params["search"])) {
			if (isset($params["search"]["refresh"])) {
				unset($params["search"]["refresh"]);
			}
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);


		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');


	})->add(\APP\Auth::getStructureAccessCheck('system-setup-defaults-dashboards', 'select'));
});
