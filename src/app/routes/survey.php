<?php
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
use \GuzzleHttp\Psr7\LazyOpenStream as OpenStream;
use Illuminate\Database\Capsule\Manager as DB;

$app->group("/survey",  function ($group)
{
	$group->get("/list/{type:[a-z_]+}", function (Request $request, Response $response, array $args)
	{
		$s = \Models\Survey::where('type', $args['type'])
			->select('surveys.learning_module_id', 'learning_modules.name')
			->join("learning_modules", function($join) {
				$join
					->on('learning_modules.id', '=', 'surveys.learning_module_id')
				;
			})
			->distinct()
			->orderBy('learning_modules.name', 'asc')
			->get()
		;

		foreach($s as $surv)
		{
			$u = \Models\Survey::
				select('user_id')
				->where('learning_module_id', $surv->learning_module_id)
				->distinct()
				->get()
			;

			$surv['groups'] = \Models\Group::
				select('groups.id', 'groups.name')
				->whereIn('groups.id',
			        \Models\GroupUser::
			        	select('group_id')
			        	->whereHas('User', function($q) use ($u) {
			        		$q
			        			->whereIn('user_id', $u)
			        		;
			        	})
			            ->get()
				)
				->get()
			;

			$surv['lessons'] = \Models\LearningModule::
				select('learning_modules.id', 'learning_modules.name')
				->where('is_course', 1)
				->whereIn('learning_modules.id',
			        \Models\LearningCourseModule::
			        	select('learning_course_id')
			        	->where('learning_module_id', $surv->learning_module_id)
			            ->get()
				)
				->get()
			;

			$surv['managers'] = \Models\User::
				select('users.id')
				->selectRaw("CONCAT(fname, ' ', lname) as name")
				->whereIn('users.id',
					\Models\ManagerUser::
						select('manager_id')
						->whereHas('User', function($q) use ($u) {
							$q
								->whereIn('user_id', $u)
							;
						})
						->get()
				)
				->get();
			;

			$surv['standards'] = \Models\ApprenticeshipStandard::
				select('apprenticeship_standards.id', 'apprenticeship_standards.name')
				->where('status', 1)
				->whereIn('apprenticeship_standards.id',
					\Models\ApprenticeshipStandardUser::
						select('standard_id')
						->whereHas('User', function($q) use ($u) {
							$q
								->whereIn('user_id', $u)
							;
						})
						->get()
				)
				->get();
			;
		}

		$response->getBody()->write($s->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

	$group->post("/user-list/{learning_module_id:[0-9]+}{option:[\/a-z]*}", function (Request $request, Response $response, array $args)
	{
		$params = $request->getParsedBody();

		if (isset($params["search"]) && is_array($params["search"]))
		{
			unset($params["search"]["refresh"]);
		}

		if (isset($params["search"]["additionalSearchParams"]))
		{
			$additional_search_params = json_decode($params["search"]["additionalSearchParams"], true);
			unset($params["search"]["additionalSearchParams"]);
		}

		$lm = \Models\LearningCourseModule::
			select('learning_course_id')
			->where('learning_module_id', $args['learning_module_id'])
			->get();

		$query = \Models\Survey::
			select('surveys.user_id', 'surveys.updated_at', 'attempt', 'learning_results_id', 'surveys.learning_module_id', 'data', 'type')
			->with(["user" => function($query) use ($lm) {
				$query
					->select([
						"id",
						"department_id",
						"company_id",
						"role_id"
					])
					->selectRaw("CONCAT(fname, ' ', lname) as name")
					->with(['company' => 	function($query) { $query->select('id', 'name'); }])
					->with(['modules' => 	function($query) use ($lm) {
						$query
							->select('learning_modules.id', 'learning_modules.name')
							->where('learning_modules.is_course', 1)
							->whereIn('learning_modules.id', $lm)
						;
					}])
					->with(['department' => function($query) { $query->select('id', 'name'); }])
					->with(['groups' => 	function($query) { $query->select('groups.id', 'groups.name'); }])
					->with(['managers' => 	function($query) { $query->select('manager_id')->selectRaw("CONCAT(fname, ' ', lname) as name"); }])
					->with(['standards' => 	function($query) { $query->select('standard_id', 'name'); }])
					->with(['role' => 		function($query) { $query->select('id', 'name'); }])
				;
			}])

			->join("users", function($join) {
				$join
					->on("surveys.user_id", "=", "users.id")
					->where("users.status", "=", 1)
				;
			})
			->leftJoin("companies", function($join) {
				$join
					->on("users.company_id", "=", "companies.id")
					->where("companies.status", "=", 1)
				;
			})
			->leftJoin("departments", function($join) {
				$join
					->on("users.department_id", "=", "departments.id")
					->where("departments.status", "=", 1)
				;
			})

			->where('surveys.learning_module_id', $args['learning_module_id'])
		;

		if (isset($additional_search_params["group_id"]) && !empty($additional_search_params["group_id"]))
		{
			$query->join("group_users", function($join) use ($additional_search_params) {
				$join
					->on("surveys.user_id", "=", "group_users.user_id")
					->where("group_users.group_id", "=", $additional_search_params["group_id"])
					->where("group_users.status", "=", 1)
				;
			});
		}

		if (isset($additional_search_params["manager_id"]) && !empty($additional_search_params["manager_id"]))
		{
			$query->whereIn('users.id',
					\Models\ManagerUser::
						select('user_id')
						->where('manager_id', $additional_search_params["manager_id"])
						->get()
				)
			;
		}

		if (isset($additional_search_params["lesson_id"]) && !empty($additional_search_params["lesson_id"]))
		{
			$query->join("learning_course_modules", function($join) use ($additional_search_params) {
				$join
					->on('surveys.learning_module_id', 'learning_course_modules.learning_module_id')
					->where('learning_course_modules.learning_course_id', $additional_search_params["lesson_id"])
					// show lessons only that are assigned to user
					->whereExists(function ($query) use ($additional_search_params) {
						$query->select(DB::raw(1))
							->from('user_learning_modules')
							->whereRaw('user_learning_modules.user_id = surveys.user_id')
							->whereRaw('user_learning_modules.learning_module_id = ?', [$additional_search_params["lesson_id"]])
							->whereRaw('user_learning_modules.deleted_at is null')
						;
					})
				;
			});
		}

		if (isset($additional_search_params["standard_id"]) && !empty($additional_search_params["standard_id"]))
		{
			$query->join("apprenticeship_standards_users", function($join) use ($additional_search_params) {
				$join
					->on("surveys.user_id", "=", "apprenticeship_standards_users.user_id")
					->where("apprenticeship_standards_users.standard_id", "=", $additional_search_params["standard_id"])
				;
			});
		}

		if (isset($additional_search_params["period_from"]) || isset($additional_search_params["period_to"]))
		{
			if (isset($additional_search_params["period_from"]))
			{
				$period_from = \Carbon\Carbon::parse($additional_search_params["period_from"])->startOfDay();
			}
			else
			{
				$period_from = \Carbon\Carbon::now()->subYears(100);
			}

			if (isset($additional_search_params["period_to"]))
			{
				$period_to = \Carbon\Carbon::parse($additional_search_params["period_to"])->endOfDay();
			}
			else
			{
				$period_to = \Carbon\Carbon::now()->addYears(100);
			}

			$query
				->whereBetween("surveys.updated_at",[$period_from, $period_to])
			;
		}

		if (!\APP\Auth::accessAllLearners())
		{
			$query->whereIn(
				"surveys.user_id",
				\Models\ManagerUser::
					where('manager_id', '=', \APP\Auth::getUserId())
					->select('user_id')
					->get()
			);
		}

		switch($args["option"])
		{
			case "/download":

				$data = \APP\SmartTable::searchPaginate($params, $query, false, false);

				$question_list = [];
                $type = 'likert';

				if (isset($data[0])) {
                    foreach (json_decode($data[0]->data) as $key => $question) array_push($question_list, $question->question);
                    $type = $data[0]->type;
                }

                if ($type === 'likert') {
                    foreach ($data as $key => $entry)
                    {
                        $manager_list = "";
                        $standard_list = "";
                        $group_list = "";

                        foreach ($entry->user->managers as $key => $manager) {
                            $manager_list .= $manager->name . ', ';
                        }

                        foreach ($entry->user->standards as $key => $standard) {
                            $standard_list .= $standard->name . ', ';
                        }

                        foreach ($entry->user->groups as $key => $group) {
                            $group_list .= $group->name . ', ';
                        }

                        foreach (json_decode($entry->data) as $key => $question) {
                            $entry['answer_'.$key] = '[' . $question->result . '] ' . $question->answer;
                        }

                        $entry['manager_list'] = trim($manager_list, ", ");
                        $entry['standard_list'] = trim($standard_list, ", ");
                        $entry['group_list'] = trim($group_list, ", ");
                    }

                    $export_fields = [
                        "ID" => "learning_results_id",
                        "Participant" => "user.name",
                        "Company" => "user.company.name",
                        "Department" => "user.department.name",
                        "Groups" => "group_list",
                        "Managers" => "manager_list",
                        "Programme"=> "standard_list",
                        "Date Taken" => "updated_at",
                    ];
                }
                else {
                    foreach ($data as $key => $entry)
                    {
                        foreach (json_decode($entry->data) as $key => $question) {
                            $entry['answer_'.$key] = $question->answer;
                        }
                    }

                    $export_fields = [
                        "ID" => "learning_results_id",
                        "Participant" => "user.name",
                        "Company" => "user.company.name",
                        "Department" => "user.department.name",
                        "Date Taken" => "updated_at",
                    ];
                }

				foreach ($question_list as $key => $question) $export_fields['Q'.($key + 1).': '.$question] = 'answer_'.$key;

				$download_file_name = uniqid("survey_" . $type. ".report.") . ".xlsx";

				\APP\Tools::generateExcelDownload(
					$data,
					$export_fields,
					$this->get('settings')["LMSTempPath"] . $download_file_name
				);

				$response->getBody()->write(json_encode($download_file_name));
		return $response->withHeader('Content-Type', 'application/json');
			break;
		}

		$p = \APP\SmartTable::searchPaginate($params, $query);

		foreach ($p as $key => $entry)
		{
			$summ = 0;
			$count = 0;
			foreach (json_decode($entry->data) as $key => $question) { $summ += $question->result; $count++; }
			$entry['average'] = round($summ / $count, 2);
		}

		$response->getBody()->write($p->toJson());
		return $response->withHeader('Content-Type', 'application/json');

	})->add(\APP\Auth::getStructureAccessCheck(['lessons-and-learning-resources', 'trainee-learning-results'], 'select'));

});
