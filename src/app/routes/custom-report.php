<?php

use APP\Controllers\CustomReportController;
$app->group("/custom-report", function ($group){
  $group->get('/fields',CustomReportController::class.':fieldList')->add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->post('/',CustomReportController::class.':create')->add(\APP\Auth::getStructureAccessCheck('review', 'insert'));
  $group->put("/",CustomReportController::class.':edit')->add(\APP\Auth::getStructureAccessCheck('review','update'));
  $group->post('/table/[{id:[0-9]+}]',CustomReportController::class.':table')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->post('/table/{download:[\/a-z]*}',CustomReportController::class.':table')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->post('/table/{download:[\/a-z]*}/[{id:[0-9]+}]',CustomReportController::class.':table')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/{id:[0-9]+}',CustomReportController::class.':get')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/',CustomReportController::class.':list')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/table/{id:[0-9]+}/[{limit:[0-9]+}]',CustomReportController::class.':allTableData')->Add(\APP\Auth::getStructureAccessCheck(['review','custom-report-data'], 'select'));
  $group->put('/edit/data/{id:[0-9]+}',CustomReportController::class.':editData')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/drop-down/{type}/{column_name}',CustomReportController::class.':getDropDown')->Add(\APP\Auth::getStructureAccessCheck('review', 'select'));
  $group->get('/export/{id:[0-9]+}',CustomReportController::class.':export')->Add(\APP\Auth::getStructureAccessCheck('review','insert')); //exporting custom-report with datatable and graphs
  $group->post('/import',CustomReportController::class.':import')->Add(\APP\Auth::getStructureAccessCheck('review','insert')); //importing custom-report with datatable and graphs
  $group->delete('/{id:[0-9]+}',CustomReportController::class.':delete')->Add(\APP\Auth::getStructureAccessCheck('review', 'disable'));
  $group->get('/fix-slug',CustomReportController::class.':fixSlugIssue');
});
