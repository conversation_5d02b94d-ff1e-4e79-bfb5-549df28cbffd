<?php

use Monolog\Logger;
use Monolog\Level;
use Monolog\Handler\StreamHandler;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Log\LoggerInterface;
use Slim\Exception\HttpException;
use Slim\App;

return function (ContainerInterface $c, App $app) {

    /** @var LoggerInterface $logger */
    $logger = $c->get(LoggerInterface::class);

    $c->set(LoggerInterface::class, $logger); // optional, already set via dependencies

    $customErrorHandler = function (
        ServerRequestInterface $request,
        Throwable $exception,
        bool $displayErrorDetails,
        bool $logErrors,
        bool $logErrorDetails
    ) use ($logger, $app) {
        $logger->error($exception->getMessage(), [
            'exception' => $exception,
            'request' => $request->getUri()->__toString(),
        ]);

        $payload = ['error' => $exception->getMessage()];
        $statusCode = ($exception instanceof HttpException) ? $exception->getCode() : 500;

        $response = $app->getResponseFactory()->createResponse();

        return \APP\Tools::returnCode($request, $response, $statusCode, json_encode($payload, JSON_UNESCAPED_UNICODE), ['message' => $exception->getMessage()]);
    };

    // Handle 404 errors (with dedicated log file)
    $notFoundHandler = function ($request, $response) use ($c) {
        // Create dedicated 404 logger
        $notFoundLogger = new Logger('404');
        $notFoundLogger->pushHandler(new StreamHandler($c->get('settings')['LMSRootPath'] . 'logs/404.log', Level::Info));
        
        $notFoundLogger->info('404 Not Found', [
            'request' => $request->getUri()->__toString(),
            'method' => $request->getMethod(),
            'user_agent' => $request->getHeaderLine('User-Agent'),
            'referer' => $request->getHeaderLine('Referer'),
            'ip' => $request->getServerParams()['REMOTE_ADDR'] ?? 'unknown'
        ]);
        
        return \APP\Tools::returnCode($request, $response, 404);
    };

    // Incorrect method logging
    $notAllowedHandler = function ($request, $response, $methods) use ($c) {
        /** @var LoggerInterface $logger */
        $logger = $c->get(LoggerInterface::class);
        $logger->warning('405 Method Not Allowed', [
            'request' => $request->getUri()->__toString(),
            'methods' => $methods,
        ]);
        return \APP\Tools::returnCode($request, $response, 405, false, ['message' => "Not allowed Method, must be one of: " . implode(', ', $methods)]);
    };

    // PHP error overwrite
    $phpErrorHandler = function ($request, $response, $error) use ($c) {
        /** @var LoggerInterface $logger */
        $logger = $c->get(LoggerInterface::class);
        $logger->critical('PHP Error', ['error' => $error]);
        return \APP\Tools::returnCode($request, $response, 500, false, ['message' => "PHP error: " . $error]);
    };

    $c->set('notFoundHandler', $notFoundHandler);
    $c->set('notAllowedHandler', $notAllowedHandler);
    $c->set('phpErrorHandler', $phpErrorHandler);

    return $customErrorHandler;
};
