<?php

declare(strict_types=1);

use Slim\App;
//use RKA\Middleware\IpAddress;
use APP\ClockworkMiddleware;
use Illuminate\Database\Capsule\Manager as Capsule;

return function (App $app) use ($settings, $container) {
	//$ipAddressMiddleware = new IpAddress(true, []);
	//$app->add($ipAddressMiddleware);


	/*
		Fatal error: Uncaught TypeError: Clockwork\DataSource\EloquentDataSource::__construct(): Argument #2 ($eventDispatcher) must be of type Illuminate\Contracts\Events\Dispatcher, null given, called in C:\Users\<USER>\Documents\Darbi\E-learning\html\src\classes\APP\ClockworkMiddleware.php on line 27 and defined in C:\Users\<USER>\Documents\Darbi\E-learning\html\src\vendor\itsgoingd\clockwork\Clockwork\DataSource\EloquentDataSource.php:59 Stack trace: #0 C:\Users\<USER>\Documents\Darbi\E-learning\html\src\classes\APP\ClockworkMiddleware.php(27): Clockwork\DataSource\EloquentDataSource->__construct(Object(Illuminate\Database\DatabaseManager), NULL) #1 C:\Users\<USER>\Documents\Darbi\E-learning\html\src\app\middleware.php(15): APP\ClockworkMiddleware->__construct('C:/Users/<USER>', NULL, Object(Illuminate\Database\Capsule\Manager)) #2 C:\Users\<USER>\Documents\Darbi\E-learning\html\src\public\index.php(55): {closure}(Object(Slim\App)) #3 {main} thrown in C:\Users\<USER>\Documents\Darbi\E-learning\html\src\vendor\itsgoingd\clockwork\Clockwork\DataSource\EloquentDataSource.php on line 59
	*/
	//$capsule = $container->get(Capsule::class);
	//$clockwork = new ClockworkMiddleware($settings['AppFilePath'] . "/../storage", null, $capsule);
	//$app->add($clockwork);
};