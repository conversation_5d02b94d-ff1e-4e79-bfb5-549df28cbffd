<?php
# default config file, rename to lms_config.php if you don't have it already, configure pats and MYSQL/EMAIL credentials below.
date_default_timezone_set('Europe/London'); // new server location, should we use system time instead of setting this up?

class config {
	var $RootPath							=	"https://learning.openelms.com/demo/";
	var $RootUri							=	"/demo/";
	var $wwwRootPath						=	"https://learning.openelms.com/demo/";
	var $AppFilePath						=	"/mnt/openelms/instances/demo/src/";
	var $PublicFilePath					=	"/mnt/openelms/instances/demo/src/public/";
	var $DefaultRoleForBULKUPLOAD		=	"12";
	var $LMSName							=	"e-Learning System"; // Used as name when sending out e-mails
	var $Title								=	"Open eLMS Learning Management System"; // Site's title in browser
	var $CompanyLogosPath				=	"images/company_logos";
	var $DefaultLogo						=	"images/logo.png";
	var $DefaultWelcomeText				=	"Welcome to Open eLMS Pro - the business focussed Learning Content Management System from Open eLMS.";

	// licensing options
	var $isOpenElms					=	true;
	var $isApprentix					=	true;
	var $isJackdawCloud				=	false;
	var $isOmniPrez					=	false;
	var $isOpenElmsLibrary			=	true;
	var $isOpenElmsTMS				=	true;
	var $isVirtualClassrooms		=	false;
	var $isSMCR							=	false;

	// allow to change status of learning results
	var $updateLRS = false;

	//Default non-demo courses
	var $DefaultCourseIDs		=	array();
	//32,44,48,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,88,99,100,102,104,105,106,108,110,111,112,114,116,121,122,123,127,128,129,130,135,137,138,141);

	var $CertificateAlwaysUseDefaultLogo	= true;
	var $DefaultCertificateMessageTop		= "One Stop e-Learning Solution";
	var $DefaultCertificateMessageBottom1	= "Open eLMS, 1 Grove Road, Maidenhead SL6 1LW";
	var $DefaultCertificateMessageBottom2	= "<EMAIL>   www.e-learningwmb.com";

	var $AllowEditEmail		= true;
	var $EmployeeShowName	= "Employee ID";

	var $PasswordPattern = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\w\W]{8,}$"; // without special characters
	//var $PasswordPattern = "^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,}$"; // special characters(only: $@$!%*#?&)

	// This will alow to create/drop databases from URL
	var $DevMode = false;

	//Database Settings
   var $mysqlHostName	= "localhost";
   var $mysqlUserName	= "openelms_";
   var $mysqlPassword	= "";
   var $mysqlDbName		= "openelms_";
   var $mysqlPort			= 3306;

	//Jackdaw Settings
	var $licenceDate		= 140221;
	var $licenceCode		= 137;
	var $licenceNumber	= 134470980;

	//Welcome
	var $userStartPage	= "mycourses.php";
	var $adminStartPage	= "usermaster.php";

	//Single Sign On
	//Shared Key
	var $AutoLoginKey = '36cca1f4235fb4351f2701';
	//Sign On Tokens - each one specifies which user is to be auto signed in.
	//Leave the array empty if you want to disable the single sign on.
	var $AutoLoginTokens = array(
		//"testtoken" => "<EMAIL>",
	);


	//Set this to false (i.e. var $SMTP = false) to use the default PHP mailing function

	var $SMTP = array(
		"host" => "openelms.e-learningwmb.co.uk",
		"port" => 587,
		"auth" => true,
		"username" => "<EMAIL>",
		"password" => "",
		"secure" => "tls",
	);

	// Basic e-mail settings, before changing notify developer(lauris at this moment).
	var $ConfigMail	=	"<EMAIL>";  // Default From: email.


	// Assesment e-mail regards line
	var $Regards		=	"Open eLMS";


	var $overwrite_scores			= false;
	var $sendMailFromUserEmail		= true;
	var $courseLocation				= "/mnt/openelms/courses/";
	var $addUserRegistrationDate	= true;
	var $FixedCourseIDs				= [];
	var $CourseIdStart				= 10000;

	var $ExcludedUpdateFiles = array(
	   "cls_config.php", "include/s.php", "images/inner_logo.jpg", "images/logo.png", "api/data/Sample course/intro.swf", "api/data/Sample course/images/thumbs/1.jpg", "api/data/thumbnails/1.jpg",
	   "scormdata", "scormdata/",
	   "updateinstallations.php", "classes/cls_installations.php",
	);

	var $updateSuperAdminPermissions				= true;
	var $ShowManagerInManagerReport				= false;
	var $SendAssessmentNotificationEmail		= false;
	var $doNotSetCourseRefreshNotifications	= false;
	var $DefaultCourseDescriptions				= [];
	var $DefaultCourseKeywords						= [];

	function load_config_files() {
		if (file_exists(__DIR__ . "/fixed_course_ids.php")) {
			include_once("fixed_course_ids.php");
			if (isset($FixedCourseIDs)) {
				$this->FixedCourseIDs = $FixedCourseIDs;
			}
		}
		if (file_exists(__DIR__ . "/default_course_descriptions.php")) {
			include_once("default_course_descriptions.php");
			if (isset($DefaultCourseDescriptions)) {
				$this->DefaultCourseDescriptions = $DefaultCourseDescriptions;
			}
		}

		if (file_exists(__DIR__ . "/default_course_keywords.php")) {
			include_once("default_course_keywords.php");
			if (isset($DefaultCourseKeywords)) {
				$this->DefaultCourseKeywords = $DefaultCourseKeywords;
			}
		}

	}

	function __construct() {
		require_once(__DIR__ . "/s.php");
		set_options($this);
		$this->load_config_files();
	}
}


?>
