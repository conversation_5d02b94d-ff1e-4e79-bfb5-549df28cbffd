<?php

declare(strict_types=1);

use DI\ContainerBuilder;
use Monolog\Logger;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Events\Dispatcher;
use Illuminate\Queue\Capsule\Manager as Queue;

return function (ContainerBuilder $containerBuilder) {


	/*
		Load legacy config file
	*/
	require __DIR__ . "/lms_config.php";
	$config = new config();

	/*
	Connect to database to get timezone, default is london, very rough connecto to database
	*/
	$timezone = "Europe/London"; // Default timezone

	// MySQL connection
	$conn = new mysqli($config->mysqlHostName, $config->mysqlUserName, $config->mysqlPassword, $config->mysqlDbName, $config->mysqlPort);

	// Query to get the timezone from the database (only if table exists)
	try {
		$sql = "SELECT `value` FROM configuration WHERE `key` = 'defaultTimezone'";
		$result = $conn->query($sql);

		// If a timezone is returned from the database, use it
		if ($result && $result->num_rows > 0) {
			while ($row = $result->fetch_assoc()) {
				$timezone = $row["value"];
			}
		}
	} catch (mysqli_sql_exception $e) {
		// Table doesn't exist yet, use default timezone
	}

	// Check if the timezone is valid, otherwise use default
	if (!in_array($timezone, timezone_identifiers_list())) {
		$timezone = "Europe/London"; // Fallback to default timezone silently
	}

	// Set the valid or default timezone
	$time = new \DateTime('now', new \DateTimeZone($timezone));
	$offset = $time->format('P');
	date_default_timezone_set($timezone);
	/*
		EOF Timezone settings
	*/

	// licensing logic here, should not be different from client to client
	require __DIR__ . "/licensing.php";

	// ILR field logic, maybe movable somewhere else
	require __DIR__ . "/ilr_fields.php";

	// Jackdaw permission logic
	require __DIR__ . "/jackdaw_permissions.php";


	// Overwite RootPath and wwwRootPath with hardcoded known hosts, needs revisiting in future!
	if (
		isset($_SERVER['HTTP_HOST']) &&
		$_SERVER['HTTP_HOST'] &&
		(
			$_SERVER['HTTP_HOST'] == 'openelms.e-learningwmb.co.uk' ||
			$_SERVER['HTTP_HOST'] == 'openelms2.e-learningwmb.co.uk' ||
		$_SERVER['HTTP_HOST'] == 'lms.e-learningwmb.co.uk' || // migration server
		$_SERVER['HTTP_HOST'] == 'learning.openelms.com'
		)
	) {
		define('RootPath', 'https://' . $_SERVER['HTTP_HOST'] . $config->RootUri);
		define('wwwRootPath', 'https://' . $_SERVER['HTTP_HOST'] . $config->RootUri);
		$config->RootPath = 'https://' . $_SERVER['HTTP_HOST'] . $config->RootUri;
		$config->wwwRootPath = 'https://' . $_SERVER['HTTP_HOST'] . $config->RootUri;
	}

	$settings = [
		'settings' => [
			'displayErrorDetails' => true, // Should be set to false in production
			'logError'            => true,
			'logErrorDetails'     => true,
			'database' => [
				'driver' => 'mysql',
				'host' => $config->mysqlHostName,
				'database' => $config->mysqlDbName,
				'username' => $config->mysqlUserName,
				'password' => $config->mysqlPassword,
				'port' => $config->mysqlPort,
				'charset'   => 'utf8',
				'collation' => 'utf8_unicode_ci',
				'prefix'    => '',
				'init' => ["SET sql_mode = ''"],
				'timezone' => $offset
			],
			'lars' => [
				'driver' => 'mysql',
				'host' => $config->mysqlHostName,
				'database' => 'learning_aim_reference_service',
				'username' => 'learning_aim_reference_service_read',
				'password' => 'hMO6an37qC3GdBt7',
				'port' => $config->mysqlPort,
				'charset'   => 'utf8',
				'collation' => 'utf8_unicode_ci',
				'prefix'    => '',
				'init' => ["SET sql_mode = ''"],
				'timezone' => $offset
			],
			'licensing' => [
				'isOpenElms' => $config->isOpenElms,
				'isApprentix' => $config->isApprentix,
				'isJackdawCloud' => $config->isJackdawCloud,
				'isOmniPrez' => $config->isOmniPrez,
				'isOpenElmsLibrary' => $config->isOpenElmsLibrary,
				'isOpenElmsTMS' => $config->isOpenElmsTMS,
				'isVirtualClassrooms' => $config->isVirtualClassrooms,
				'isSMCR' => $config->isSMCR,
				'hiddenMenuItems' => $hide_menu_items,
				'hiddenResourceTypes' => $hide_resource_type,
				'brand' => $licensing_brand,
				'description' => $licensing_description
			],
			'redis' => [
				'host' => $config->redisHostName ?? '127.0.0.1',
				'password' => $config->redisPassword ?? null,
				'port' => $config->redisPort ?? 6379,
				'database' => $config->redisDbName ?? 0,
			],
			'ilr_fields' => $ilr_fields,
			'ilr_groups' => $ilr_groups,
			'ilr_UUID' => $ilr_UUID,
			'ilr_learning_deliveries' => $ilr_learning_deliveries,
			'old_config' => $config,
			// Monolog settings
			'logger' => [
				'name' => 'lms-logger',
				'debug.path' => $config->AppFilePath . 'logs/debug.log',
				'info.path' => $config->AppFilePath . 'logs/info.log',
				'notice.path' => $config->AppFilePath . 'logs/notice.log',
				'warning.path' => $config->AppFilePath . 'logs/warning.log',
				'error.path' => $config->AppFilePath . 'logs/error.log',
				'emergency.path' => $config->AppFilePath . 'logs/emergency.log',
				'cron.errors.path' => $config->AppFilePath . 'crons/errors.log',
				'cron.info.path' => $config->AppFilePath . 'crons/info.log',
				'debug' => isset($config->debug) ? $config->debug : false,
			],
			'logPath' => $config->AppFilePath . 'logs/app.log',
			'logLevel' => \Monolog\Level::Notice, // Log level (e.g., DEBUG, INFO, ERROR)

			'LMSName' => $config->LMSName,
			'LMSTitle' => $config->Title,
			'LMSUrl' => $config->RootPath,
			'LMSUri' => $config->RootUri,
			'LMSAppUri' => $config->RootUri . "app/",
			'LMSTplsUri' => $config->RootUri . "tpl/",
			'LMSTplsUriHTML' => $config->RootUri . "tpl/html/",
			'LMSTplsUriCombined' => $config->RootUri . "tpl/public/",
			'LMSPdfGenPath' => $config->AppFilePath."/pdf_node/",
			'LMSPasswordPattern' => $config->PasswordPattern,

			'AppFilePath' => $config->AppFilePath,

			'LMSDevMode' => $config->DevMode, // This will alow to create/drop databases from URL
			'RegisterManagerialRole' => isset($config->RegisterManagerialRole) ? $config->RegisterManagerialRole : false, // If this is set true in lms_config.php, users can be registered using managerial role.
			'HideOtherRegisterdUsers' => isset($config->HideOtherRegisterdUsers) ? $config->HideOtherRegisterdUsers : false, // If this is set true in lms_config.php, users with role set in defaultRegisterRole won't see others with same role.

			// Setting this to true in lms_config.php will add restrictions and logic to manager roles
			'sharedClients' => isset($config->sharedClients) ? $config->sharedClients : false,
			'clientCompanyUserLimit' => isset($config->clientCompanyUserLimit) ? $config->clientCompanyUserLimit : 10,
			'defaultClientManagerRoleID' => isset($config->defaultClientManagerRoleID) ? $config->defaultClientManagerRoleID : false,

			'LMSPublicPath' => $config->PublicFilePath,
			'LMSTplsPath' => $config->AppFilePath . "tpls/",
			'LMSTempPath' => $config->AppFilePath . "temp/",
			'LMSPublicPath' => $config->AppFilePath . "public/",
			'LMSAppPath' => $config->AppFilePath . "app/",
			'LMSPrivatePath' => $config->AppFilePath . "private/",
			'LMSScormDataPath' => $config->PublicFilePath . "scormdata/",
			'LMSScormVersionPath' => $config->PublicFilePath . "scormversion/",//This path will be used to save uplodaed scorm zip file for version
			'LMSCronPath' => $config->AppFilePath . "crons/",

			'LMSPrivateScormDownloadPath' => $config->AppFilePath . "private/scorm/",
			'LMSImagesPath' => $config->PublicFilePath . "images/",
			'LMSJsPath' => $config->PublicFilePath . "js/",
			'LMSThumbPath' => $config->PublicFilePath . "images/thumbnails/",
			'LMSPromoPath' => $config->PublicFilePath . "images/promo/",
			'LMSHighlightPath' => $config->PublicFilePath . "images/highlight/",
			'LMSIconsUri' => $config->RootUri . "images/icons/",
			'LMSAccreditationPath' => $config->PublicFilePath . "images/accreditation/",
			'LMSDefaultTypePath' => $config->PublicFilePath . "images/default_type/",
			'LMSDefaultTypeCustomPath' => $config->PublicFilePath . "images/default_type_custom/",
			'LMSPrivateImagesPath' => $config->AppFilePath . "private/images/",
			'LMSLearnersInterfaceImagesPath' => $config->AppFilePath . "private/images/learners-interface/",
			'LMSLearnersInterfaceImagesPathDefault' => $config->AppFilePath . "public/images/learner-bg/",
			'LMSUsersImagesPath' => $config->AppFilePath . "private/images/users/",
			'LMSESignatureImagesPath' => $config->AppFilePath . "private/images/e_signature/",
			'LMSReviewsPath' => $config->AppFilePath . "private/reviews/",
			'LMSVideoPath' => $config->PublicFilePath . "video/",
			'LMSPrivateTempPath' => $config->AppFilePath . "private/temp/",
			'LMSEventEmailAttachmentPath' => $config->AppFilePath . "private/events/",
			'LMSPrivateUploadPath' => $config->AppFilePath . "upload/", // Where client will upload files. Jail.
			'LMSVenuePath' => $config->AppFilePath . 'private/venue/',
			'LMSBatchReportData' => $config->AppFilePath . "private/batch_report_data/",
			'LMSFormLogsData' => $config->AppFilePath . "private/form_logs/",

			'LMSEvidencePathOld' => $config->PublicFilePath . "evidence/",
			'LMSEvidencePath' => $config->AppFilePath . "private/evidence/",

			'LMSFilePath' => $config->AppFilePath . "private/files/",

			'LMSRoleImagePath' => $config->AppFilePath . "private/images/roles/",

			'CompanyLogosPath' => $config->PublicFilePath . "images/logos/",
			'LMSCompanyLogosUri' => $config->RootUri . "images/logos/",

			'CompanyLoginBgPath' => $config->PublicFilePath . "images/company/login_bg/",
			'LMSCompanyLoginBgUri' => $config->RootUri . "images/company/login_bg/",

			'CompanyLearnerBgPath' => $config->PublicFilePath . "images/company/learner_bg/",
			'LMSCompanyLearnerBgUri' => $config->RootUri . "images/company/learner_bg/",

			'CompanyThumbnailPath' => $config->PublicFilePath . "images/company/thumbnail/",
			'LMSCompanyThumbnailUri' => $config->RootUri . "images/company/thumbnail/",

			'CategoryLandingImage' => $config->PublicFilePath . "images/category_landing/",
			'CategoryLandingImageUri' => $config->RootUri . "images/category_landing/",
			'DefaultLogo' => $config->RootPath . "images/licensing/" . $licensing_brand . 'logo.png',
			'DefaultWelcomeText' => $config->DefaultWelcomeText,

			'ResetPasswordLogo' => $config->RootPath . "images/licensing/" . $licensing_brand . 'logo.png',
			'ResetPasswordText' => $config->DefaultWelcomeText,

			'CertificateAlwaysUseDefaultLogo' => $config->CertificateAlwaysUseDefaultLogo,
			'DefaultCertificateMessageTop' => $config->DefaultCertificateMessageTop,
			'DefaultCertificateMessageBottom1' => $config->DefaultCertificateMessageBottom1,
			'DefaultCertificateMessageBottom2' => $config->DefaultCertificateMessageBottom2,

			'JackdawTypePath' => $config->courseLocation . 'jackdaw_types/',
			'ApiMediaData' => $config->courseLocation . 'media_data', // without ending slash, that is intended.

			'FixedCourseIds' => isset($config->FixedCourseIDs) ? $config->FixedCourseIDs : [],
			'CourseIdStart' => isset($config->CourseIdStart) ? $config->CourseIdStart : 0,

			'AvailableModulesLocation' => isset($config->courseLocation) ? $config->courseLocation : false,

			'DefaultCourseDescriptions' => isset($config->DefaultCourseDescriptions) ? $config->DefaultCourseDescriptions: [],
			'DefaultCourseKeywords' => isset($config->DefaultCourseKeywords) ? $config->DefaultCourseKeywords: [],

			'LMSTeamsReturnUrl' => $config->RootUri == '/' ? $config->RootPath . 'teams' :  str_replace($config->RootUri, '/msintegration/teams', $config->RootPath),
			'LMSPowerbiReturnUrl' => $config->RootPath . 'powerbi',
			'LMSPowerbiDashboardReturnUrl' => $config->RootPath . 'powerbi-dashboards',
			'LMSJamboardReturnUrl' => $config->RootUri == '/' ? $config->RootPath . 'jamboard' :  str_replace($config->RootUri, '/msintegration/jamboard', $config->RootPath),
			'email' => [
				"Host" => (isset($config->SMTP) && isset($config->SMTP["host"])) ? $config->SMTP["host"] : "localhost",
				"Port" => (isset($config->SMTP) && isset($config->SMTP["port"])) ? $config->SMTP["port"] : 25,
				"Auth" => (isset($config->SMTP) && isset($config->SMTP["auth"])) ? $config->SMTP["auth"] : false,
				"Username" => (isset($config->SMTP) && isset($config->SMTP["username"])) ? $config->SMTP["username"] : "",
				"Password" => (isset($config->SMTP) && isset($config->SMTP["password"])) ? $config->SMTP["password"] : "",
				"Secure" => (isset($config->SMTP) && isset($config->SMTP["secure"])) ? $config->SMTP["secure"] : false,
				"DefaultFromEmail" => isset($config->ConfigMail) ? $config->ConfigMail : false,
				"DefaultFromName" => isset($config->LMSName) ? $config->LMSName : "Administrator",
				"SMTPOptions" => (isset($config->SMTP) && isset($config->SMTP["options"])) ? $config->SMTP["options"] : [],
			],

			'determineRouteBeforeAppMiddleware' => true,

			//Jackdaw settings
			'Jackdaw' => [
				"licenceDate" => $config->licenceDate,
				"licenceCode" => $config->licenceCode,
				"licenceNumber" => $config->licenceNumber,
				"types" => $jackdaw_type
			],

			// Allow for admin to update learning result status
			'updateLRS' => $config->updateLRS,

	'customLoginPage' => isset($config->customLoginPage) ? $config->customLoginPage : false,
	'TokenLoginKey' => isset($config->TokenLoginKey) ? $config->TokenLoginKey : false,
	'DisableFileDownload' => isset($config->DisableFileDownload) ? $config->DisableFileDownload : false,
	'defaultRegistrationFieldList' =>
    [
		[ 'name' => 'Username',			'slug' => 'username',		'db_name' => 'username',			'baseField' => true ],
		[ 'name' => 'Password', 		'slug' => 'password',		'db_name' => 'password',			'baseField' => true, 'pattern_error' => '%%password_pattern_error_message%%' ],
        [ 'name'=> 'Employee ID', 		'slug'=>'usercode' , 		'db_name'=> 'usercode', 			'enabled' => true, 'required' => true ],
		[ 'name' => 'First Name',		'slug' => 'firstName',		'db_name' => 'fname',				'enabled' => true,	'required' => true ],
		[ 'name' => 'Last Name',		'slug' => 'lastName',		'db_name' => 'lname',				'enabled' => true,	'required' => true ],
		// NINumber
		[ 'name' => '%%national_insurance_number%%',			'slug' => 'NINumber',			'db_name' => 'NINumber',				'enabled' => false,	'required' => false, 'pattern' => '^(?:(?![DFIQUV])[A-Z]){1}(?:(?![DFIOQUV])[A-Z]){1}\s?\d{2}\s?\d{2}\s?\d{2}\s?([ABCD]){1}$', 'pattern_error' => '%%national_insurance_pattern_error%%' ],
        [ 'name' => 'Date of Birth',	'slug' => 'dateOfBirth',	'db_name' => 'DateOfBirth',			'enabled' => true,	'required' => false, 'type'=>'date', 'customName'=>'Date of birth(Date picker)' ],
        [ 'name' => 'Adress Line 1',			'slug' => 'address1',		'db_name' => 'AddLine1',			'enabled' => true,	'required' => false ],
        [ 'name' => 'Adress Line 2',			'slug' => 'address2',		'db_name' => 'AddLine2',			'enabled' => true,	'required' => false ],
        [ 'name' => 'Adress Line 3',			'slug' => 'address3',		'db_name' => 'AddLine3',			'enabled' => true,	'required' => false ],
        ['name'=> 'Postcode', 'slug'=> 'Postcode' , 'db_name'=> 'Postcode', 'enabled' => true, 'required'=>false],
		// [ 'name' => 'Sex',				'slug' => 'sex',			'db_name' => 'Sex',					'enabled' => false,	'required' => false ],
		[ 'name' => 'Contact E-mail',	'slug' => 'email',			'db_name' => 'email',				'enabled' => true,	'required' => true ],
		[ 'name' => 'Phone',			'slug' => 'phone',			'db_name' => 'phone',				'enabled' => true,	'required' => true ],
		// [ 'name' => 'School',			'slug' => 'school',			'db_name' => 'school',				'enabled' => false,	'required' => false ],
		[ 'name' => '%%company%%',			'slug' => 'company',		'db_name' => 'company_id',			'enabled' => false,	'required' => false,	'table' => 'companies' ],
		[ 'name' => '%%department%%',		'slug' => 'department',		'db_name' => 'department_id',		'enabled' => false, 'required' => false,	'table' => 'departments', 	'parent' => '%%department%%' ],
		[ 'name' => '%%job%%',				'slug' => 'job',			'db_name' => 'designation_id',		'enabled' => false,	'required' => false,	'table' => 'designations' ],
		[ 'name' => '%%country%%',			'slug' => 'country',		'db_name' => 'country_id',			'enabled' => true,	'required' => true,		'table' => 'countries' ],
		[ 'name' => '%%city%%',				'slug' => 'city',			'db_name' => 'city_id',				'enabled' => true,	'required' => false,	'table' => 'cities', 		'parent' => '%%country%%' ],
				[ 'name' => 'Location',         'slug' => 'location',       'db_name' => 'location_id',         'enabled' => false, 'required' => false,    'table' => 'locations' ],
				[ 'name' => 'Receive Emails',   'slug' => 'receiveEmails',  'db_name' => 'exclude_from_emails', 'enabled' => false, 'required' => false ],
			],
		],
	];

	/* Set up global database connection */
	$capsule = new \Illuminate\Database\Capsule\Manager;
	$capsule->addConnection($settings['settings']['database'], "default");
	$capsule->addConnection($settings['settings']['lars'], "learning_aim_reference_service");
	$capsule->setEventDispatcher(new Dispatcher(new Container));
	$capsule->setAsGlobal();
	$capsule->bootEloquent();
	$capsule->getConnection()->enableQueryLog();

	// Queue configuration
	$queue = new Queue(new Container);
	$queue->addConnection([
		'driver' => 'database',
		'table' => 'jobs',
		'queue' => 'default',
		'retry_after' => 90,
	]);
	$queue->setAsGlobal();
	$queue->getContainer()->bind('events', function () {
		return new Dispatcher(new Container);
	});

	$licensing = \APP\Licensing::setUp($settings['settings']);

	$settings['settings']['licensing'] = $licensing;
	$settings['settings']['DefaultLogo'] = $licensing['logo'] ? $licensing['logo'] : $settings['settings']['LMSUrl'] . "images/licensing/" . $licensing['brand'] . 'logo.png';

	// Global Settings Object
	$containerBuilder->addDefinitions($settings);

	// Add capsule and queue to the container
	$containerBuilder->addDefinitions([
		Capsule::class => function () use ($capsule) {
				return $capsule;
		},
		Queue::class => function () use ($queue) {
				return $queue;
		},
	]);
};
