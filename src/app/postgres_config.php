<?php
/**
 * PostgreSQL Configuration for Vector Database
 * 
 * This configuration is for the code search vector database
 * using PostgreSQL with pgvector extension
 */

return [
    'postgres_vector' => [
        'host' => getenv('POSTGRES_HOST') ?: 'postgres',
        'port' => getenv('POSTGRES_PORT') ?: 5432,
        'database' => getenv('POSTGRES_DB') ?: 'lms_vectors',
        'username' => getenv('POSTGRES_USER') ?: 'lms_vector',
        'password' => getenv('POSTGRES_PASSWORD') ?: 'lms_vector_pass',
        'schema' => 'code_search',
        'charset' => 'utf8',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ],
    
    // OpenAI configuration for embeddings
    'openai' => [
        'api_key' => getenv('OPENAI_API_KEY') ?: '',
        'embedding_model' => 'text-embedding-ada-002',
        'max_tokens' => 8000,
    ],
    
    // Vectorization settings
    'vectorization' => [
        'chunk_size' => 1000, // Maximum characters per chunk
        'chunk_overlap' => 200, // Overlap between chunks
        'batch_size' => 100, // Number of chunks to process at once
        'file_types' => ['php', 'js', 'sql', 'md'],
    ]
];