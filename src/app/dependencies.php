<?php

declare(strict_types=1);

use DI\ContainerBuilder;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Monolog\Processor\UidProcessor;
use APP\Factories\LoggerFactory;
use Psr\Log\LoggerInterface;

return function (\DI\ContainerBuilder $containerBuilder) {
    $containerBuilder->addDefinitions([
        LoggerInterface::class => function (\Psr\Container\ContainerInterface $c) {
            $settings = $c->get('settings')['logger'];
            return LoggerFactory::create($settings);
        }
    ]);
};