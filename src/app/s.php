<?php

function set_options($obj)
{
	$obj->RegsterDefaultMessage		=	"Welcome to Open eLMS's e-learning system.";
	$obj->RegisterAddCompany		= false;
	$obj->RegisterAddDepartment = false;
	$obj->MaxUsersExceedWarning = "The maximum number of users for this company is reached. You can't add more users.";
	$obj->DefaultRolesForAssigningWIthinCompany	=	array(12, 13, 15, 39);
	$obj->SendAutoEmailsonRegister	= false;

	$obj->RegisterAddManager = false;
	$obj->DefaultManagerRole = "Manager";
	$obj->ManagerMissingMessage = "The manager's email given does not exists on the system, please contact the appropriate manager and ask them to register on the system first.";
	$obj->RegisterAddSite = false;
	$obj->LoginAddManager = false;
	$obj->LoginAddSite = false;
	$obj->PrimaryCourseID = 162;
	$obj->HideAssigingManagerToDepartment = true;

	$obj->AssignOneManagerPerEmployee = true;

	$obj->RegisterAddEmployeeCode = false;
	$obj->RegisterAddBorn = false;
	$obj->RegisterAddJob = false;
	$obj->RegisterAddLocation = false;
	$obj->RegisterAddCountry = false;
	$obj->RegisterAddCity = false;
	$obj->RegisterAddPhone = false;
	$obj->RegisterAddDescription = false;
	$obj->RegisterLinkVisible = true;
	$obj->RegisterAddress = "register.php";
}

?>