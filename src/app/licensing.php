<?php
/*
	Use logic to determine what pages to hide, depending on switches in client config file.
	Will use page URL as logic check.
*/

	$hide_resource_type = [1, 2, 3, 4, 5, 6, 7, 8, 9];
	$hide_menu_items = [
		'library-learning-programmes', // Hidden from new dashboard interface
		'review-learning-programmes', // same

		// SMCR stuff
		'system-setup-organisation-staff-type',
		'system-setup-organisation-functions',
		'system-setup-organisation-responsibilities',
		'system-setup-organisation-committees',

		// Jackdaw cloud
		'system-setup-learning-distribution',
		'system-setup-learning-free-jackdaw-resources',
	];


	if ($config->isJackdawCloud) {
		$licensing_brand = 'jackdaw';
		$licensing_description = 'Welcome to Open eLMS AI Editor, the most powerful cloud based e-learning authoring system available today. If this is your first time using the system, please view the video for an overview of development possibilities.';
		$hide_menu_items = array_diff( $hide_menu_items, [
			'system-setup-learning-distribution',
			'system-setup-learning-free-jackdaw-resources'
		]);
	}

	if ($config->isOmniPrez) {
		$licensing_brand = 'omniprez';
		$licensing_description = 'Welcome to <PERSON>mni<PERSON><PERSON>, now you can easily create better presentations for any device. If this is your first time using the system, please view the video for an overview of development possibilities.';
	}

	if ($config->isOpenElms) {
		$licensing_brand = 'openlms';
		$licensing_description = 'Welcome to Open Elms Pro version 8, the most comprehensive Training, Learning Management and Learning Creation System available. Please view the video for an overview of the system to get an insight into the opportunities available to you in using this management side of the system.';
		$hide_resource_type = array_diff( $hide_resource_type, [1, 2, 3]);
	}

	if ($config->isApprentix) {
		$licensing_brand = 'apprentix';
		$licensing_description = 'Welcome to Apprentix, the most comprehensive system for dealing with apprenticeships combing e-portfolio capabilities with learning. Please view the video for an overview of the system to get an insight into the opportunities available to you in using Apprentix, either as a coach, manager, trainer or assessor.';
		$hide_resource_type = array_diff( $hide_resource_type, [4, 5, 6, 7, 9]);
		$hide_menu_items = array_diff( $hide_menu_items, [
			'library-learning-programmes',
			'review-learning-programmes',
		]);

	}


	if ($config->isSMCR) {
		$licensing_brand = 'smcr';
		$licensing_description = 'SMCR Range';
		$hide_resource_type = [];
		$hide_menu_items = array_diff( $hide_menu_items, [
			'system-setup-organisation-staff-type',
			'system-setup-organisation-functions',
			'system-setup-organisation-responsibilities',
			'system-setup-organisation-committees'
		]);
	}


	// Show Classroom, Book and On The Job, import ILR users
	if ($config->isOpenElmsTMS) {
		$hide_resource_type = array_diff( $hide_resource_type, [4, 5, 6, 7, 8]);
	}

	if ($config->isOpenElmsLibrary) {

	}

	if ($config->isVirtualClassrooms) {

	}

?>