# AI-Powered Code Search System

This directory contains the complete AI-powered code search infrastructure for the Open eLMS project.

## Quick Start

Use the master script for all operations:

```bash
# Search for code
./ai-search search "user authentication"
./ai-search search "learning completion" 5

# Show statistics
./ai-search stats

# Full refresh (regenerate + reset + vectorize)
./ai-search refresh

# Incremental update
./ai-search update

# Get help
./ai-search --help
```

## Directory Structure

```
ai-search/
├── ai-search                    # Master script for all operations
├── README.md                    # This file
├── REPOMIX_SECURITY_NOTES.md   # Security warnings reference
├── config/
│   └── repomix.config.json     # Repomix configuration
├── data/
│   ├── repomix-output.xml      # Generated repository summary (81MB)
│   ├── repomix.log             # Repomix generation logs
│   └── *.bak                   # Backup files
└── scripts/
    ├── codesearch.sh           # Search wrapper script
    ├── refresh_code_search.sh  # Full refresh script
    ├── update_code_search.sh   # Incremental update script
    └── refresh_code_search_with_options.sh  # Enhanced refresh with options
```

## Architecture

### Components
- **PostgreSQL with pgvector**: Vector similarity search database
- **Python Vectorizer Service**: FastAPI service for processing and search
- **Repomix**: Repository content aggregation tool
- **Docker Services**: Containerized infrastructure

### Workflow
1. **Repomix** scans the repository and creates `data/repomix-output.xml`
2. **Python service** parses PHP files and extracts functions, classes, routes
3. **OpenAI embeddings** (or local sentence-transformers) create vector representations
4. **PostgreSQL** stores code chunks with their vector embeddings
5. **Search queries** use vector similarity to find relevant code

## Commands Reference

### Master Script (`./ai-search`)

| Command | Description | Example |
|---------|-------------|---------|
| `search <query> [limit]` | Search for code | `./ai-search search "user login" 5` |
| `stats` | Show vectorization statistics | `./ai-search stats` |
| `refresh` | Full refresh (reset everything) | `./ai-search refresh` |
| `update` | Incremental update | `./ai-search update` |
| `repomix` | Generate repomix output only | `./ai-search repomix` |
| `vectorize` | Vectorize existing repomix data | `./ai-search vectorize` |
| `services` | Start/restart services | `./ai-search services` |
| `logs` | Show service logs | `./ai-search logs` |
| `clean` | Stop services and clean data | `./ai-search clean` |

### Options

| Option | Description |
|--------|-------------|
| `--include-suspicious` | Include files flagged by security checks |
| `--no-security-check` | Disable all security checks |
| `-l, --limit NUM` | Limit search results |

## Configuration

### Repomix Configuration (`config/repomix.config.json`)

Controls which files are included in the repository summary:

- **Included**: `src/app/`, `src/classes/`, `src/crons/`, `src/scripts/`, `src/tpls/`, etc.
- **Excluded**: `vendor/`, `node_modules/`, `*.log`, `*.cache`, etc.

### Docker Configuration

Services are defined in `../docker/docker-compose.yml`:
- `postgres`: PostgreSQL with pgvector extension
- `python_vectorizer`: FastAPI service for vectorization

## Search Examples

```bash
# Find authentication code
./ai-search search "user login"
# → Returns functions like createLogAuthentications, failed login handlers

# Find learning functionality
./ai-search search "learning module completion"
# → Returns LearningResult models, completion status functions

# Find database operations
./ai-search search "database migration"
# → Returns schema creation, upgrade functions

# Find API endpoints
./ai-search search "REST API"
# → Returns route definitions, API controllers
```

## Current Status

- **Files Processed**: ~7,877 PHP files
- **Code Chunks**: 6,000+ functions, classes, routes
- **Vector Database**: PostgreSQL with 1536-dimensional embeddings
- **Search Method**: Cosine similarity with 0.3 threshold
- **Fallback**: Local sentence-transformers when OpenAI unavailable

## Troubleshooting

### Services Not Running
```bash
./ai-search services
```

### No Search Results
- Lower the similarity threshold in the Python service
- Check if vectorization completed: `./ai-search stats`
- Try broader search terms

### Repomix Security Warnings
- See `REPOMIX_SECURITY_NOTES.md` for details
- Use `--include-suspicious` flag if needed
- Most warnings are false positives in legitimate codebases

### Performance Issues
- Vectorization takes 5-10 minutes for large codebases
- Search is fast (<1 second) once vectorized
- Use incremental updates (`./ai-search update`) for regular changes

## Integration with Development

### When to Refresh
- **Major refactoring**: `./ai-search refresh`
- **Regular development**: `./ai-search update`
- **New features added**: `./ai-search update`

### IDE Integration
You can create shell aliases for quick access:

```bash
# Add to ~/.bashrc or ~/.zshrc
alias cs="cd /path/to/lms/ai-search && ./ai-search search"
alias css="cd /path/to/lms/ai-search && ./ai-search stats"
```

### CI/CD Integration
Consider adding vectorization updates to your deployment pipeline:

```bash
# In your deployment script
cd ai-search
./ai-search update --skip-repomix  # Skip if no code changes
```