#!/bin/bash

# ai-search - Master script for AI-powered code search system
# This script manages the entire AI code search infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_DIR="$REPO_ROOT/docker"
DATA_DIR="$SCRIPT_DIR/data"
CONFIG_DIR="$SCRIPT_DIR/config"

# Ensure we're in the right place
if [ ! -d "$DOCKER_DIR" ] || [ ! -f "$REPO_ROOT/CLAUDE.md" ]; then
    echo -e "${RED}Error: This script must be run from the ai-search directory in the LMS repository${NC}"
    exit 1
fi

# Function to show help
show_help() {
    echo -e "${GREEN}AI Code Search System${NC}"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  ${YELLOW}search <query> [limit]${NC}      Search for code (default limit: 10)"
    echo "  ${YELLOW}stats${NC}                       Show vectorization statistics"
    echo "  ${YELLOW}refresh${NC}                     Full refresh (regenerate + reset + vectorize)"
    echo "  ${YELLOW}update${NC}                      Incremental update"
    echo "  ${YELLOW}repomix${NC}                     Generate repomix output only"
    echo "  ${YELLOW}vectorize${NC}                   Vectorize existing repomix data"
    echo "  ${YELLOW}services${NC}                    Start/restart AI search services"
    echo "  ${YELLOW}logs${NC}                        Show service logs"
    echo "  ${YELLOW}clean${NC}                       Stop services and clean data"
    echo ""
    echo "Options for search:"
    echo "  -l, --limit NUM               Limit number of results"
    echo ""
    echo "Options for refresh/repomix:"
    echo "  --include-suspicious          Include files flagged by security checks"
    echo "  --no-security-check          Disable all security checks"
    echo ""
    echo "Examples:"
    echo "  $0 search \"user authentication\""
    echo "  $0 search \"learning completion\" 5"
    echo "  $0 refresh --include-suspicious"
    echo "  $0 update"
    echo ""
    echo "File locations:"
    echo "  Repomix output: ${DATA_DIR}/repomix-output.xml"
    echo "  Configuration:  ${CONFIG_DIR}/repomix.config.json"
    echo "  Scripts:        ${SCRIPT_DIR}/scripts/"
}

# Function to ensure services are running
ensure_services() {
    cd "$DOCKER_DIR"
    if ! docker compose ps | grep -q "python_vectorizer.*Up"; then
        echo -e "${YELLOW}Starting AI search services...${NC}"
        docker compose up -d postgres python_vectorizer
        echo "Waiting for services to initialize..."
        sleep 10
    fi
}

# Function to run repomix with current config
run_repomix() {
    local repomix_flags="$1"
    
    cd "$REPO_ROOT"
    
    if [ -f "$DATA_DIR/repomix-output.xml" ]; then
        echo "Backing up existing repomix output..."
        mv "$DATA_DIR/repomix-output.xml" "$DATA_DIR/repomix-output.xml.bak"
    fi
    
    echo "Generating repomix output..."
    local cmd="repomix --config $CONFIG_DIR/repomix.config.json"
    
    if [ ! -z "$repomix_flags" ]; then
        cmd="$cmd $repomix_flags"
    fi
    
    eval $cmd 2>&1 | tee "$DATA_DIR/repomix.log"
    
    if [ -f "$REPO_ROOT/repomix-output.xml" ]; then
        mv "$REPO_ROOT/repomix-output.xml" "$DATA_DIR/"
        echo -e "${GREEN}✓ Repomix output saved to ${DATA_DIR}/repomix-output.xml${NC}"
        
        SIZE=$(ls -lh "$DATA_DIR/repomix-output.xml" | awk '{print $5}')
        FILE_COUNT=$(grep -c '<file path=' "$DATA_DIR/repomix-output.xml" 2>/dev/null || echo "0")
        echo "  File size: $SIZE"
        echo "  Files included: $FILE_COUNT"
    else
        echo -e "${RED}✗ Failed to generate repomix output${NC}"
        return 1
    fi
}

# Parse command line arguments
COMMAND=""
SEARCH_QUERY=""
SEARCH_LIMIT="10"
REPOMIX_FLAGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        search|stats|refresh|update|repomix|vectorize|services|logs|clean)
            COMMAND="$1"
            shift
            ;;
        --include-suspicious)
            REPOMIX_FLAGS="$REPOMIX_FLAGS --include-suspicious"
            shift
            ;;
        --no-security-check)
            REPOMIX_FLAGS="$REPOMIX_FLAGS --no-security-check"
            shift
            ;;
        -l|--limit)
            SEARCH_LIMIT="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            if [ "$COMMAND" = "search" ] && [ -z "$SEARCH_QUERY" ]; then
                SEARCH_QUERY="$1"
            elif [ "$COMMAND" = "search" ] && [ -z "$SEARCH_LIMIT_SET" ]; then
                SEARCH_LIMIT="$1"
                SEARCH_LIMIT_SET=true
            else
                echo -e "${RED}Unknown argument: $1${NC}"
                exit 1
            fi
            shift
            ;;
    esac
done

# Execute command
case $COMMAND in
    search)
        if [ -z "$SEARCH_QUERY" ]; then
            echo -e "${RED}Error: Search query required${NC}"
            echo "Usage: $0 search \"query\" [limit]"
            exit 1
        fi
        ensure_services
        cd "$DOCKER_DIR"
        echo -e "${YELLOW}Searching for: ${NC}$SEARCH_QUERY"
        echo ""
        docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py search "$SEARCH_QUERY" -l "$SEARCH_LIMIT"
        ;;
    
    stats)
        ensure_services
        cd "$DOCKER_DIR"
        docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py stats
        ;;
    
    refresh)
        "$SCRIPT_DIR/scripts/refresh_code_search.sh" $REPOMIX_FLAGS
        ;;
    
    update)
        "$SCRIPT_DIR/scripts/update_code_search.sh"
        ;;
    
    repomix)
        run_repomix "$REPOMIX_FLAGS"
        ;;
    
    vectorize)
        ensure_services
        if [ ! -f "$DATA_DIR/repomix-output.xml" ]; then
            echo -e "${RED}Error: No repomix output found. Run 'repomix' command first.${NC}"
            exit 1
        fi
        
        echo "Copying repomix data for vectorization..."
        cp "$DATA_DIR/repomix-output.xml" "$DOCKER_DIR/"
        
        cd "$DOCKER_DIR"
        echo "Starting vectorization..."
        docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py vectorize
        ;;
    
    services)
        cd "$DOCKER_DIR"
        echo "Starting AI search services..."
        docker compose up -d postgres python_vectorizer
        echo "Services started. Waiting for initialization..."
        sleep 5
        docker compose ps | grep -E "(postgres|python_vectorizer)"
        ;;
    
    logs)
        cd "$DOCKER_DIR"
        docker compose logs -f python_vectorizer postgres
        ;;
    
    clean)
        cd "$DOCKER_DIR"
        echo "Stopping services..."
        docker compose down postgres python_vectorizer
        echo "Removing vector data..."
        docker volume rm docker_postgres_data 2>/dev/null || echo "Volume already removed"
        echo -e "${GREEN}✓ AI search services cleaned${NC}"
        ;;
    
    "")
        show_help
        ;;
    
    *)
        echo -e "${RED}Unknown command: $COMMAND${NC}"
        show_help
        exit 1
        ;;
esac