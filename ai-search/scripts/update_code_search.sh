#!/bin/bash

# update_code_search.sh - Incremental update of code search vectors
# This script updates the vectorized data without a full reset

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Code Search Incremental Update ===${NC}"
echo "This will update the vectorized data with changes from repomix"
echo ""

# Check if we're in the right directory
if [ ! -f "repomix-output.xml" ] && [ ! -d "src" ]; then
    echo -e "${RED}Error: Please run this script from the repository root directory${NC}"
    exit 1
fi

# Parse command line arguments
SKIP_REPOMIX=false
FORCE_FULL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-repomix)
            SKIP_REPOMIX=true
            shift
            ;;
        --force-full)
            FORCE_FULL=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --skip-repomix    Skip regenerating repomix-output.xml"
            echo "  --force-full      Force full re-vectorization (same as refresh_code_search.sh)"
            echo "  -h, --help        Show this help message"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

# If force-full is requested, just run the refresh script
if [ "$FORCE_FULL" = true ]; then
    echo "Running full refresh..."
    exec "$(dirname "$0")/refresh_code_search.sh"
fi

# Step 1: Check current statistics
echo -e "${YELLOW}Current Statistics:${NC}"
cd docker
CURRENT_STATS=$(docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py stats 2>/dev/null || echo "Service not running")
echo "$CURRENT_STATS"
echo ""

# Step 2: Regenerate repomix-output.xml (unless skipped)
if [ "$SKIP_REPOMIX" = false ]; then
    echo -e "${YELLOW}Step 1: Regenerating repomix-output.xml...${NC}"
    cd ..
    
    if command -v repomix &> /dev/null; then
        # Get modification time of current file
        if [ -f "repomix-output.xml" ]; then
            OLD_TIME=$(stat -f %m "repomix-output.xml" 2>/dev/null || stat -c %Y "repomix-output.xml" 2>/dev/null || echo "0")
        else
            OLD_TIME=0
        fi
        
        # Generate new repomix output
        repomix --include "src/app/,src/classes/,src/crons/,src/scripts/,src/tpls/,src/public/index.php,src/public/scorm/,src/public/api/" --ignore "src/public/api/data/"
        
        # Check if file was actually updated
        if [ -f "repomix-output.xml" ]; then
            NEW_TIME=$(stat -f %m "repomix-output.xml" 2>/dev/null || stat -c %Y "repomix-output.xml" 2>/dev/null || echo "0")
            if [ "$NEW_TIME" -gt "$OLD_TIME" ]; then
                echo -e "${GREEN}✓ repomix-output.xml updated${NC}"
                SIZE=$(ls -lh repomix-output.xml | awk '{print $5}')
                echo "  File size: $SIZE"
            else
                echo -e "${BLUE}ℹ No changes detected in repomix output${NC}"
            fi
        fi
    else
        echo -e "${RED}Error: repomix is not installed${NC}"
        echo "Install it with: npm install -g @yamadashy/repomix"
        exit 1
    fi
else
    echo -e "${BLUE}Skipping repomix regeneration (--skip-repomix flag used)${NC}"
fi

# Step 3: Copy repomix file to Docker context
echo -e "${YELLOW}Step 2: Updating Docker context...${NC}"
cp repomix-output.xml docker/
echo -e "${GREEN}✓ File copied to Docker context${NC}"

# Step 4: Ensure services are running
echo -e "${YELLOW}Step 3: Checking services...${NC}"
cd docker
if ! docker compose ps | grep -q "postgres.*Up"; then
    echo "Starting PostgreSQL..."
    docker compose up -d postgres
    sleep 10
fi

if ! docker compose ps | grep -q "python_vectorizer.*Up"; then
    echo "Starting Python vectorizer..."
    docker compose up -d python_vectorizer
    sleep 5
fi

echo -e "${GREEN}✓ Services are running${NC}"

# Step 5: Run incremental vectorization
echo -e "${YELLOW}Step 4: Running incremental vectorization...${NC}"
echo "This will update existing vectors and add new ones..."

# Run vectorization and capture output
docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py vectorize

# Step 6: Show updated statistics
echo ""
echo -e "${YELLOW}Updated Statistics:${NC}"
docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py stats

# Step 7: Test search
echo ""
echo -e "${YELLOW}Testing search functionality:${NC}"
TEST_RESULT=$(docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py search "user authentication" -l 1 2>/dev/null | head -n 1)
if echo "$TEST_RESULT" | grep -q "Found"; then
    echo -e "${GREEN}✓ Search is working correctly${NC}"
else
    echo -e "${RED}✗ Search test failed${NC}"
fi

echo ""
echo -e "${GREEN}=== Incremental Update Complete! ===${NC}"
echo ""
echo "Quick search commands:"
echo '  docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py search "your query"'
echo ""
echo "For a full reset, run: ./scripts/refresh_code_search.sh"