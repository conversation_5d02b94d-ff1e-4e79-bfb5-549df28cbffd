#!/bin/bash

# codesearch.sh - Convenient wrapper for code search functionality
# Usage: ./scripts/codesearch.sh "search query" [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
LIMIT=10
SHOW_HELP=false

# Check if no arguments provided
if [ $# -eq 0 ]; then
    SHOW_HELP=true
fi

# Parse command line arguments
QUERY=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -l|--limit)
            LIMIT="$2"
            shift 2
            ;;
        -h|--help)
            SHOW_HELP=true
            shift
            ;;
        stats|--stats)
            # Show statistics
            cd "$(dirname "$0")/../docker"
            docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py stats
            exit 0
            ;;
        update|--update)
            # Run incremental update
            exec "$(dirname "$0")/update_code_search.sh"
            ;;
        refresh|--refresh)
            # Run full refresh
            exec "$(dirname "$0")/refresh_code_search.sh"
            ;;
        *)
            # Assume it's part of the search query
            if [ -z "$QUERY" ]; then
                QUERY="$1"
            else
                QUERY="$QUERY $1"
            fi
            shift
            ;;
    esac
done

# Show help if requested or no query provided
if [ "$SHOW_HELP" = true ] || ([ -z "$QUERY" ] && [ "$SHOW_HELP" = false ]); then
    echo -e "${GREEN}Code Search Tool${NC}"
    echo ""
    echo "Usage: $0 <query> [options]"
    echo "       $0 <command>"
    echo ""
    echo "Commands:"
    echo "  stats              Show vectorization statistics"
    echo "  update             Run incremental update"
    echo "  refresh            Run full refresh (reset and re-vectorize)"
    echo ""
    echo "Options:"
    echo "  -l, --limit NUM    Limit number of results (default: 10)"
    echo "  -h, --help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 \"user authentication\""
    echo "  $0 \"learning module completion\" -l 5"
    echo "  $0 \"SCORM package\" --limit 20"
    echo "  $0 stats"
    echo "  $0 update"
    echo ""
    echo "Search Tips:"
    echo "  - Use natural language queries for best results"
    echo "  - Search by concept: \"user login\" finds authentication code"
    echo "  - Search by feature: \"email notification\" finds email systems"
    echo "  - Search by technical terms: \"database migration\" finds schema updates"
    exit 0
fi

# Change to docker directory
cd "$(dirname "$0")/../docker"

# Check if services are running
if ! docker compose ps | grep -q "python_vectorizer.*Up"; then
    echo -e "${YELLOW}Vectorizer service is not running. Starting it now...${NC}"
    docker compose up -d postgres python_vectorizer
    echo "Waiting for services to start..."
    sleep 10
fi

# Perform the search
echo -e "${YELLOW}Searching for: ${NC}$QUERY"
echo ""

docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py search "$QUERY" -l "$LIMIT"