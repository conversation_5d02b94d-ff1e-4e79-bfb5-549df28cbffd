#!/bin/bash

# refresh_code_search_with_options.sh - Regenerate repomix output with security options
# This script handles repomix security warnings and provides options to include flagged files

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Code Search Refresh Script (with Security Options) ===${NC}"
echo ""

# Default values
INCLUDE_SUSPICIOUS=false
REPOMIX_FLAGS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --include-suspicious)
            INCLUDE_SUSPICIOUS=true
            shift
            ;;
        --no-security-check)
            REPOMIX_FLAGS="--no-security-check"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --include-suspicious    Include files flagged as suspicious by repomix"
            echo "  --no-security-check     Disable repomix security checks entirely"
            echo "  -h, --help             Show this help message"
            echo ""
            echo "Note: Files flagged as 'suspicious' are usually false positives in legitimate"
            echo "      codebases. Common triggers include:"
            echo "      - eval() or similar dynamic code execution"
            echo "      - Base64 encoded strings"
            echo "      - URLs in test files"
            echo "      - Security-related function names"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

# Check if we're in the right directory
if [ ! -f "repomix-output.xml" ] && [ ! -d "src" ]; then
    echo -e "${RED}Error: Please run this script from the repository root directory${NC}"
    exit 1
fi

# Step 1: Regenerate repomix-output.xml
echo -e "${YELLOW}Step 1: Regenerating repomix-output.xml...${NC}"

if command -v repomix &> /dev/null; then
    # Backup existing file if it exists
    if [ -f "repomix-output.xml" ]; then
        echo "Backing up existing repomix-output.xml to repomix-output.xml.bak"
        mv repomix-output.xml repomix-output.xml.bak
    fi
    
    # Prepare repomix command
    REPOMIX_CMD="repomix"
    REPOMIX_CMD="$REPOMIX_CMD --include \"src/app/,src/classes/,src/crons/,src/scripts/,src/tpls/,src/public/index.php,src/public/scorm/,src/public/api/\""
    REPOMIX_CMD="$REPOMIX_CMD --ignore \"src/public/api/data/\""
    
    if [ "$INCLUDE_SUSPICIOUS" = true ] || [ ! -z "$REPOMIX_FLAGS" ]; then
        REPOMIX_CMD="$REPOMIX_CMD $REPOMIX_FLAGS"
        echo -e "${YELLOW}Note: Security checks modified - including potentially suspicious files${NC}"
    fi
    
    # Show the command being run
    echo "Running: $REPOMIX_CMD"
    
    # Generate new repomix output
    eval $REPOMIX_CMD 2>&1 | tee repomix_output.log
    
    # Check for suspicious files in the output
    if grep -q "suspicious file(s) detected" repomix_output.log; then
        echo ""
        echo -e "${YELLOW}Warning: Some files were excluded as suspicious${NC}"
        echo "These are likely false positives. The flagged files were:"
        grep -A 20 "suspicious file(s) detected" repomix_output.log | grep -E "^\s*[0-9]+\." | sed 's/^[[:space:]]*/  /'
        echo ""
        echo "To include these files, run with: --include-suspicious or --no-security-check"
        echo ""
    fi
    
    if [ -f "repomix-output.xml" ]; then
        echo -e "${GREEN}✓ repomix-output.xml regenerated successfully${NC}"
        # Show file size
        SIZE=$(ls -lh repomix-output.xml | awk '{print $5}')
        echo "  File size: $SIZE"
        
        # Count files included
        FILE_COUNT=$(grep -c '<file path=' repomix-output.xml || echo "0")
        echo "  Files included: $FILE_COUNT"
    else
        echo -e "${RED}✗ Failed to generate repomix-output.xml${NC}"
        exit 1
    fi
else
    echo -e "${RED}Error: repomix is not installed${NC}"
    echo "Install it with: npm install -g @yamadashy/repomix"
    exit 1
fi

# Clean up log file
rm -f repomix_output.log

# Continue with the rest of the refresh process
echo ""
echo -e "${YELLOW}Continuing with vectorization refresh...${NC}"

# Step 2: Stop Docker services
echo -e "${YELLOW}Step 2: Stopping Docker services...${NC}"
cd docker
docker compose stop python_vectorizer postgres
echo -e "${GREEN}✓ Services stopped${NC}"

# Step 3: Reset PostgreSQL data
echo -e "${YELLOW}Step 3: Resetting PostgreSQL vector data...${NC}"
docker compose down postgres
docker volume rm docker_postgres_data 2>/dev/null || true
echo -e "${GREEN}✓ PostgreSQL data reset${NC}"

# Step 4: Copy new repomix file to Docker context
echo -e "${YELLOW}Step 4: Preparing new repomix file for vectorization...${NC}"
cd ..
cp repomix-output.xml docker/
echo -e "${GREEN}✓ File copied to Docker context${NC}"

# Step 5: Restart services
echo -e "${YELLOW}Step 5: Starting services...${NC}"
cd docker
docker compose up -d postgres python_vectorizer

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to initialize..."
sleep 10

# Check if services are running
if docker compose ps | grep -q "postgres.*Up" && docker compose ps | grep -q "python_vectorizer.*Up"; then
    echo -e "${GREEN}✓ Services started successfully${NC}"
else
    echo -e "${RED}✗ Services failed to start${NC}"
    docker compose logs postgres python_vectorizer
    exit 1
fi

# Step 6: Start vectorization
echo -e "${YELLOW}Step 6: Starting vectorization process...${NC}"
echo "This may take several minutes depending on the size of your codebase..."

# Run vectorization with proper TTY handling
echo "Running vectorization (this will take a few minutes)..."
docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py vectorize

# Check if vectorization was successful
FINAL_STATS=$(docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py stats 2>/dev/null | grep "Total Chunks:" | awk '{print $3}' || echo "0")
if [ "$FINAL_STATS" -gt "0" ]; then
    echo -e "${GREEN}✓ Vectorization completed successfully${NC}"
    echo "  Processed chunks: $FINAL_STATS"
else
    echo -e "${RED}✗ Vectorization may have failed - no chunks processed${NC}"
    echo "Check logs with: docker compose logs python_vectorizer"
fi

# Final statistics
echo -e "${YELLOW}Final Statistics:${NC}"
docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py stats

echo ""
echo -e "${GREEN}=== Code Search Refresh Complete! ===${NC}"
echo ""
echo "You can now search your codebase using:"
echo '  ./scripts/codesearch.sh "your search query"'
echo ""