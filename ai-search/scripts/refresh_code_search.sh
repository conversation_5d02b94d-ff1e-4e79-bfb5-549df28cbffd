#!/bin/bash

# refresh_code_search.sh - Regenerate repomix output and reset vectorized data
# This script completely refreshes the AI-powered code search system

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Code Search Refresh Script ===${NC}"
echo "This will regenerate repomix-output.xml and reset all vectorized data"
echo ""

# Get the directory paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AI_SEARCH_DIR="$(dirname "$SCRIPT_DIR")"
REPO_ROOT="$(dirname "$AI_SEARCH_DIR")"
DATA_DIR="$AI_SEARCH_DIR/data"
CONFIG_DIR="$AI_SEARCH_DIR/config"
DOCKER_DIR="$REPO_ROOT/docker"

# Check if we're in the right directory structure
if [ ! -d "$REPO_ROOT/src" ] || [ ! -d "$DOCKER_DIR" ]; then
    echo -e "${RED}Error: Invalid directory structure. Expected to be in ai-search/scripts/${NC}"
    exit 1
fi

# Step 1: Regenerate repomix-output.xml
echo -e "${YELLOW}Step 1: Regenerating repomix-output.xml...${NC}"
if command -v repomix &> /dev/null; then
    # Backup existing file if it exists
    if [ -f "$DATA_DIR/repomix-output.xml" ]; then
        echo "Backing up existing repomix-output.xml to repomix-output.xml.bak"
        mv "$DATA_DIR/repomix-output.xml" "$DATA_DIR/repomix-output.xml.bak"
    fi
    
    cd "$REPO_ROOT"
    
    # Generate new repomix output using config
    echo "Generating repomix output (this may take a moment)..."
    repomix --config "$CONFIG_DIR/repomix.config.json" 2>&1 | tee "$DATA_DIR/repomix_temp.log"
    
    # Check for suspicious files warning
    if grep -q "suspicious file(s) detected" "$DATA_DIR/repomix_temp.log"; then
        echo ""
        echo -e "${YELLOW}Note: Some files were excluded by repomix security checks${NC}"
        echo "This is normal - these are typically false positives for:"
        echo "  - Dynamic code execution patterns (eval, create_function)"
        echo "  - Base64 encoded strings"
        echo "  - Test files with URLs"
        echo "  - Vendor/third-party libraries"
        echo ""
        echo "The excluded files won't affect most searches."
        echo "To include them, use: repomix --no-security-check"
        echo ""
    fi
    
    # Move repomix output to data directory and clean up temp log
    if [ -f "repomix-output.xml" ]; then
        mv "repomix-output.xml" "$DATA_DIR/repomix-output.xml"
        echo -e "${GREEN}✓ repomix-output.xml regenerated successfully${NC}"
        # Show file size and stats
        SIZE=$(ls -lh "$DATA_DIR/repomix-output.xml" | awk '{print $5}')
        FILE_COUNT=$(grep -c '<file path=' "$DATA_DIR/repomix-output.xml" 2>/dev/null || echo "0")
        echo "  File size: $SIZE"
        echo "  Files included: $FILE_COUNT"
        echo "  Location: $DATA_DIR/repomix-output.xml"
    else
        echo -e "${RED}✗ Failed to generate repomix-output.xml${NC}"
        exit 1
    fi
    
    rm -f "$DATA_DIR/repomix_temp.log"
else
    echo -e "${RED}Error: repomix is not installed${NC}"
    echo "Install it with: npm install -g @yamadashy/repomix"
    exit 1
fi

# Step 2: Stop Docker services
echo -e "${YELLOW}Step 2: Stopping Docker services...${NC}"
cd "$DOCKER_DIR"
docker compose stop python_vectorizer postgres
echo -e "${GREEN}✓ Services stopped${NC}"

# Step 3: Reset PostgreSQL data
echo -e "${YELLOW}Step 3: Resetting PostgreSQL vector data...${NC}"
# Remove the volume to completely reset the database
docker compose down postgres
docker volume rm docker_postgres_data 2>/dev/null || true
echo -e "${GREEN}✓ PostgreSQL data reset${NC}"

# Step 4: Copy new repomix file to Docker context
echo -e "${YELLOW}Step 4: Preparing new repomix file for vectorization...${NC}"
cp "$DATA_DIR/repomix-output.xml" "$DOCKER_DIR/"
echo -e "${GREEN}✓ File copied to Docker context${NC}"

# Step 5: Restart services
echo -e "${YELLOW}Step 5: Starting services...${NC}"
docker compose up -d postgres python_vectorizer

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to initialize..."
sleep 10

# Check if services are running
if docker compose ps | grep -q "postgres.*Up" && docker compose ps | grep -q "python_vectorizer.*Up"; then
    echo -e "${GREEN}✓ Services started successfully${NC}"
else
    echo -e "${RED}✗ Services failed to start${NC}"
    docker compose logs postgres python_vectorizer
    exit 1
fi

# Step 6: Start vectorization
echo -e "${YELLOW}Step 6: Starting vectorization process...${NC}"
echo "This may take several minutes depending on the size of your codebase..."

# Run vectorization with proper TTY handling
echo "Running vectorization (this will take a few minutes)..."
docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py vectorize

# Check if vectorization was successful
FINAL_STATS=$(docker compose exec -T python_vectorizer python /app/scripts/vectorizer_cli.py stats 2>/dev/null | grep "Total Chunks:" | awk '{print $3}' || echo "0")
if [ "$FINAL_STATS" -gt "0" ]; then
    echo -e "${GREEN}✓ Vectorization completed successfully${NC}"
    echo "  Processed chunks: $FINAL_STATS"
else
    echo -e "${RED}✗ Vectorization may have failed - no chunks processed${NC}"
    echo "Check logs with: docker compose logs python_vectorizer"
fi

# Final statistics
echo -e "${YELLOW}Final Statistics:${NC}"
docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py stats

echo ""
echo -e "${GREEN}=== Code Search Refresh Complete! ===${NC}"
echo ""
echo "You can now search your codebase using:"
echo '  docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py search "your search query"'
echo ""
echo "Or use the shorter alias by adding this to your ~/.bashrc or ~/.zshrc:"
echo '  alias codesearch="docker compose exec python_vectorizer python /app/scripts/vectorizer_cli.py search"'
echo ""