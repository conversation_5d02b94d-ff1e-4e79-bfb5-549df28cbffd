<?php
include __DIR__ . '/../crons/setup.php';

use Models\Company;
use Models\Department;
use Models\Group;
use Models\Designation;
use Models\Location;
use Models\Venue;
use Models\LinkedSkill;
use Models\ModuleFeedback;

echo "=== Open eLMS Entity Cleanup Script ===\n";

// Check if this is a dry run or actual cleanup
$dryRun = true;
$actualCleanup = false;

if ($argc >= 2 && $argv[1] === '--clean') {
    $dryRun = false;
    $actualCleanup = true;
    echo "LIVE MODE: This script will actually remove entities from the database.\n";
    echo "WARNING: This action cannot be undone!\n\n";
} else {
    echo "DRY RUN MODE: This script will show what would be cleaned without making changes.\n";
    echo "Use --clean parameter to actually perform the cleanup.\n\n";
}

if ($actualCleanup) {
    echo "Starting actual cleanup process...\n\n";
} else {
    echo "Analyzing entities to be cleaned...\n\n";
}

try {
    // 1. Remove all Teams (Groups)
    echo "1. Groups (Teams)...\n";
    $groupCount = Group::count();
    if ($actualCleanup) {
        Group::truncate();
        echo "   ✓ Removed {$groupCount} groups\n";
    } else {
        echo "   → Would remove {$groupCount} groups\n";
    }

    // 2. Remove all Disabled Companies (status = 0)
    echo "2. Disabled Companies...\n";
    $disabledCompanies = Company::where('status', 0)->count();
    if ($actualCleanup) {
        Company::where('status', 0)->delete();
        echo "   ✓ Removed {$disabledCompanies} disabled companies\n";
    } else {
        echo "   → Would remove {$disabledCompanies} disabled companies\n";
    }

    // 3. Remove all Departments
    echo "3. Departments...\n";
    $departmentCount = Department::count();
    if ($actualCleanup) {
        Department::truncate();
        echo "   ✓ Removed {$departmentCount} departments\n";
    } else {
        echo "   → Would remove {$departmentCount} departments\n";
    }

    // 4. Remove all Job Roles (Designations)
    echo "4. Job Roles (Designations)...\n";
    $designationCount = Designation::count();
    if ($actualCleanup) {
        Designation::truncate();
        echo "   ✓ Removed {$designationCount} job roles (designations)\n";
    } else {
        echo "   → Would remove {$designationCount} job roles (designations)\n";
    }

    // 5. Remove all Locations
    echo "5. Locations...\n";
    $locationCount = Location::count();
    if ($actualCleanup) {
        Location::truncate();
        echo "   ✓ Removed {$locationCount} locations\n";
    } else {
        echo "   → Would remove {$locationCount} locations\n";
    }

    // 6. Remove all Venues
    echo "6. Venues...\n";
    $venueCount = Venue::count();
    if ($actualCleanup) {
        Venue::truncate();
        echo "   ✓ Removed {$venueCount} venues\n";
    } else {
        echo "   → Would remove {$venueCount} venues\n";
    }

    // 7. Remove all Skills
    echo "7. Skills...\n";
    $skillCount = LinkedSkill::count();
    if ($actualCleanup) {
        LinkedSkill::truncate();
        echo "   ✓ Removed {$skillCount} skills\n";
    } else {
        echo "   → Would remove {$skillCount} skills\n";
    }

    // 8. Remove all current Feedback
    echo "8. Current Feedback...\n";
    $feedbackCount = ModuleFeedback::count();
    if ($actualCleanup) {
        ModuleFeedback::truncate();
        echo "   ✓ Removed {$feedbackCount} feedback entries\n";
    } else {
        echo "   → Would remove {$feedbackCount} feedback entries\n";
    }

    // Clear related pivot tables and user references
    echo "\n9. Related data cleanup...\n";
    
    // Clear group-user relationships
    $groupUsersCount = \Illuminate\Database\Capsule\Manager::table('group_users')->count();
    if ($actualCleanup) {
        \Illuminate\Database\Capsule\Manager::table('group_users')->truncate();
        echo "   ✓ Cleared {$groupUsersCount} group-user relationships\n";
    } else {
        echo "   → Would clear {$groupUsersCount} group-user relationships\n";
    }

    // Clear group-learning module relationships  
    $groupModulesCount = \Illuminate\Database\Capsule\Manager::table('group_learning_modules')->count();
    if ($actualCleanup) {
        \Illuminate\Database\Capsule\Manager::table('group_learning_modules')->truncate();
        echo "   ✓ Cleared {$groupModulesCount} group-learning module relationships\n";
    } else {
        echo "   → Would clear {$groupModulesCount} group-learning module relationships\n";
    }

    // Clear designation-learning module relationships
    $designationModulesCount = \Illuminate\Database\Capsule\Manager::table('designation_learning_modules')->count();
    if ($actualCleanup) {
        \Illuminate\Database\Capsule\Manager::table('designation_learning_modules')->truncate();
        echo "   ✓ Cleared {$designationModulesCount} designation-learning module relationships\n";
    } else {
        echo "   → Would clear {$designationModulesCount} designation-learning module relationships\n";
    }

    // Clear department-learning module relationships
    $departmentModulesCount = \Illuminate\Database\Capsule\Manager::table('department_learning_modules')->count();
    if ($actualCleanup) {
        \Illuminate\Database\Capsule\Manager::table('department_learning_modules')->truncate();
        echo "   ✓ Cleared {$departmentModulesCount} department-learning module relationships\n";
    } else {
        echo "   → Would clear {$departmentModulesCount} department-learning module relationships\n";
    }

    // Clear designation-competency relationships
    $designationCompetencyCount = \Illuminate\Database\Capsule\Manager::table('designation_competencies')->count();
    if ($actualCleanup) {
        \Illuminate\Database\Capsule\Manager::table('designation_competencies')->truncate();
        echo "   ✓ Cleared {$designationCompetencyCount} designation-competency relationships\n";
    } else {
        echo "   → Would clear {$designationCompetencyCount} designation-competency relationships\n";
    }

    // Update users to remove references to deleted entities
    echo "\n10. User references update...\n";
    $userDepartmentRefs = \Models\User::whereNotNull('department_id')->count();
    $userDesignationRefs = \Models\User::whereNotNull('designation_id')->count();
    $userLocationRefs = \Models\User::whereNotNull('location_id')->count();
    
    if ($actualCleanup) {
        \Models\User::whereNotNull('department_id')->update(['department_id' => null]);
        echo "   ✓ Cleared {$userDepartmentRefs} user department references\n";

        \Models\User::whereNotNull('designation_id')->update(['designation_id' => null]);
        echo "   ✓ Cleared {$userDesignationRefs} user designation references\n";

        \Models\User::whereNotNull('location_id')->update(['location_id' => null]);
        echo "   ✓ Cleared {$userLocationRefs} user location references\n";
    } else {
        echo "   → Would clear {$userDepartmentRefs} user department references\n";
        echo "   → Would clear {$userDesignationRefs} user designation references\n";
        echo "   → Would clear {$userLocationRefs} user location references\n";
    }

    if ($actualCleanup) {
        echo "\n=== Cleanup Complete ===\n";
        echo "All specified entities have been successfully removed from the database.\n";
        echo "Users have been updated to remove references to deleted entities.\n";
    } else {
        echo "\n=== Dry Run Complete ===\n";
        echo "This was a dry run - no changes were made to the database.\n";
        echo "Use --clean parameter to actually perform the cleanup.\n";
    }

} catch (Exception $e) {
    echo "✗ Error during cleanup: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}